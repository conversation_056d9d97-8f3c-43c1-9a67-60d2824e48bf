﻿using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Azure.Storage.Blobs.Models;
using Microsoft.AspNetCore.Http;

namespace OrderTracking.API.Interfaces.Services
{
    /// <summary>
    ///     Interface for the BlobStorageService
    /// </summary>
    public interface IBlobStorageService : ICloudStorageService
    {
        //Task<(string AbsoluteUri, BlobContentInfo Value, string blobName)> UploadStreamAsync(MemoryStream stream,
        //    string blobName, string uploadedByEmail = null);

        //Task DeleteBlobAsync(string fileName);

        //#region Public Methods

        ///// <summary>
        /////     Deletes a blob file, and removes the association to its parent.
        ///// </summary>
        ///// <param name="resourceId"></param>
        ///// <param name="fileName"></param>
        ///// <returns></returns>
        //Task DeleteBlobAsync(string resourceId, string fileName);

        ///// <summary>
        /////     Download a blob file that is associated to a parent record.
        ///// </summary>
        ///// <param name="resourceId"></param>
        ///// <param name="fileName"></param>
        ///// <returns></returns>
        //Task<BlobDownloadInfo> DownloadBlobAsync(string resourceId, string fileName);

        ///// <summary>
        /////     List the blob files associated with a parent record.
        ///// </summary>
        ///// <param name="resourceId"></param>
        ///// <returns></returns>
        //Task<IEnumerable<BlobItem>> ListBlobsAsync(string resourceId);

        ///// <summary>
        /////     Upload a file to a parent record, and create the association for subsequent joined queries.
        ///// </summary>
        ///// <param name="id"></param>
        ///// <param name="stream"></param>
        ///// <param name="uploadedByEmail"></param>
        ///// <returns></returns>
        //Task<BlobContentInfo> UploadAttachmentAsync(string id, IFormFile stream, string uploadedByEmail = null);

        //#endregion
    }
}