import {
    ChangeDetectionStrategy,
    Component,
    OnDestroy,
    OnInit,
    ViewChild
} from '@angular/core';
import { createSafeResizeObserver } from '../../../shared/helpers';
import { ScreenService } from '../../../shared/services';
import {
    ActivityHoursPieChartComponent,
    AssetsDoughnutChartComponent,
    DailyInspectionCountComponent,
    EquipmentByAreaAndTypeComponent,
    InspectionStatusDoughnutChartComponent,
    InspectionsWithoutDueDatesComponent,
    TaskStatusByWeekComponent
} from '../../components';
import { FirebaseProject } from '../../models';
import { DashboardService } from '../../services/dashboard.service';

@Component({
    selector: 'app-dashboard',
    templateUrl: './dashboard.component.html',
    styleUrls: ['./dashboard.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DashboardComponent implements OnInit, OnD<PERSON>roy {
    private _observer: ResizeObserver;

    /**
     * Charts ViewChild references
     */
    @ViewChild(AssetsDoughnutChartComponent)
    assetsDoughnut: AssetsDoughnutChartComponent;
    @ViewChild(InspectionStatusDoughnutChartComponent)
    inspectionStatusDoughnut: InspectionStatusDoughnutChartComponent;
    @ViewChild(DailyInspectionCountComponent)
    dailyInspectionCounts: DailyInspectionCountComponent;
    @ViewChild(TaskStatusByWeekComponent)
    weeklyInspectionCounts: TaskStatusByWeekComponent;
    @ViewChild(EquipmentByAreaAndTypeComponent)
    equipment: EquipmentByAreaAndTypeComponent;
    @ViewChild(ActivityHoursPieChartComponent)
    activityHours: ActivityHoursPieChartComponent;
    @ViewChild(InspectionsWithoutDueDatesComponent)
    inspectionsWithoutDueDates: InspectionsWithoutDueDatesComponent;

    constructor(
        private readonly _screen: ScreenService,
        public readonly dashboard: DashboardService
    ) {}

    get isLargeScreen(): boolean {
        return !!this._screen.sizes['screen-large'];
    }

    ngOnInit(): void {
        this._observer = createSafeResizeObserver(() => {
            this.assetsDoughnut?.renderChart();
            this.inspectionStatusDoughnut?.renderChart();
            this.dailyInspectionCounts?.renderChart();
            this.weeklyInspectionCounts?.renderChart();
            this.equipment?.renderChart();
            this.activityHours?.renderChart();
            this.inspectionsWithoutDueDates?.renderChart();
        });
        const dashboardLayout = document.getElementById('layout');
        if (dashboardLayout) this._observer.observe(dashboardLayout);
    }

    ngOnDestroy(): void {
        this._observer.disconnect();
        this._observer = null;
    }

    onSelectedProjects(event: FirebaseProject[]) {
        let projects: FirebaseProject[] = null;
        projects = event;
        this.dashboard.selectProjects(projects.map((project) => project.id));
    }

    onRefresh() {
        this.dashboard.refresh();
    }
}
