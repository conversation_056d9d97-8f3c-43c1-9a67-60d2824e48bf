import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
    DxDataGridModule,
    DxPopupModule,
    DxTabPanelModule
} from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import { CreationWorkflowComponent } from '../../components';
import { ApmService } from '../../services';
import { AssetManagementComponent } from './asset-management.component';

describe('AssetManagementComponent', () => {
    let component: AssetManagementComponent;
    let fixture: ComponentFixture<AssetManagementComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                HttpClientTestingModule,
                ToastrModule.forRoot(),
                DxDataGridModule,
                DxTabPanelModule,
                DxPopupModule
            ],
            declarations: [AssetManagementComponent, CreationWorkflowComponent],
            providers: [
                {
                    provide: ApmService,
                    useValue: {
                        buSelected$: of('123'),
                        selectedBU$: of(false),
                        getUsers: () => [],
                        projectDataSource: { store: () => {} }
                    }
                }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(AssetManagementComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
