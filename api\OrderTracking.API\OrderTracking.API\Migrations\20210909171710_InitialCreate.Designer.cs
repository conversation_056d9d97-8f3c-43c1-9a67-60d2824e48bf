﻿// <auto-generated />
using System;
using ClientPortal.Shared.Models.MOS;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace OrderTracking.API.Migrations
{
    [DbContext(typeof(MOSContext))]
    [Migration("20210909171710_InitialCreate")]
    partial class InitialCreate
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("ProductVersion", "5.0.9")
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DataSheet", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("PackageId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("PackageId");

                    b.ToTable("DataSheets");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DataSheetPackage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("ClientName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("JssNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TEAMClientNumber")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DataSheetPackages");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            ClientName = "Valero",
                            Date = new DateTime(2021, 9, 9, 17, 17, 8, 757, DateTimeKind.Utc).AddTicks(3726),
                            JssNumber = "11",
                            TEAMClientNumber = "567"
                        },
                        new
                        {
                            Id = 2,
                            ClientName = "BP",
                            Date = new DateTime(2008, 5, 1, 8, 30, 52, 0, DateTimeKind.Unspecified),
                            JssNumber = "32",
                            TEAMClientNumber = "45"
                        });
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DimensionalSheet", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DataSheet");

                    b.Property<string>("CheckedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CheckedByDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("GivenBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LineSize")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LineSkinTemp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OtherSkinTemp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Plant")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SealType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SeverityOfLeak")
                        .HasColumnType("int");

                    b.Property<string>("ShipTo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SurfaceCondition")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Unit")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("DimensionalSheets");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.HtsEngineeringDataCoverSheet", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DataSheet");

                    b.Property<string>("Additional")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AdditionalNotes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Aflas")
                        .HasColumnType("bit");

                    b.Property<bool>("Angle")
                        .HasColumnType("bit");

                    b.Property<int>("AngleDegree")
                        .HasColumnType("int");

                    b.Property<string>("BackingStrips")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("BallPark")
                        .HasColumnType("bit");

                    b.Property<string>("BallParkNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("BlindFlange")
                        .HasColumnType("bit");

                    b.Property<bool>("BoltOn")
                        .HasColumnType("bit");

                    b.Property<bool>("BoltOn7")
                        .HasColumnType("bit");

                    b.Property<bool>("BoltOn9")
                        .HasColumnType("bit");

                    b.Property<bool>("BranchEndOption")
                        .HasColumnType("bit");

                    b.Property<int>("BranchHeight")
                        .HasColumnType("int");

                    b.Property<bool>("BranchHeightType")
                        .HasColumnType("bit");

                    b.Property<bool>("BranchWithInternalThreads")
                        .HasColumnType("bit");

                    b.Property<bool>("Buna")
                        .HasColumnType("bit");

                    b.Property<bool>("ButtWeld")
                        .HasColumnType("bit");

                    b.Property<int>("CEPercentage")
                        .HasColumnType("int");

                    b.Property<bool>("CalculationPackage")
                        .HasColumnType("bit");

                    b.Property<bool>("ClientSpecified")
                        .HasColumnType("bit");

                    b.Property<bool>("ClientSpecified3")
                        .HasColumnType("bit");

                    b.Property<bool>("ClientSpecified4")
                        .HasColumnType("bit");

                    b.Property<string>("ContactInfo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("CorrosionAllowance")
                        .HasColumnType("int");

                    b.Property<bool>("CutterDiameter")
                        .HasColumnType("bit");

                    b.Property<bool>("DesignCode")
                        .HasColumnType("bit");

                    b.Property<bool>("DesignFactor")
                        .HasColumnType("bit");

                    b.Property<int>("DesignPressure")
                        .HasColumnType("int");

                    b.Property<int>("DesignTemperature")
                        .HasColumnType("int");

                    b.Property<string>("DistrictProjectId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("DrawWorksNotRequired")
                        .HasColumnType("bit");

                    b.Property<bool>("DrawWorksNotRequired9")
                        .HasColumnType("bit");

                    b.Property<bool>("DrawWorksforSettingCompletion")
                        .HasColumnType("bit");

                    b.Property<int>("DrawWorksforSettingPlug9")
                        .HasColumnType("int");

                    b.Property<bool>("EPDM")
                        .HasColumnType("bit");

                    b.Property<bool>("ElbowLet")
                        .HasColumnType("bit");

                    b.Property<bool>("ElbowOnHeel")
                        .HasColumnType("bit");

                    b.Property<bool>("ElbowStraight")
                        .HasColumnType("bit");

                    b.Property<bool>("FieldTest")
                        .HasColumnType("bit");

                    b.Property<string>("FittingMaterial")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("FlangeRF")
                        .HasColumnType("bit");

                    b.Property<bool>("FlangeRTJ")
                        .HasColumnType("bit");

                    b.Property<string>("FlangeRating")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("FlangeRatingList")
                        .HasColumnType("bit");

                    b.Property<bool>("FlangeWithPlug")
                        .HasColumnType("bit");

                    b.Property<int>("FlowThroughPlugWithPigBars")
                        .HasColumnType("int");

                    b.Property<bool>("ForImmediateManufacture")
                        .HasColumnType("bit");

                    b.Property<bool>("FullEncirclementSaddle")
                        .HasColumnType("bit");

                    b.Property<bool>("FullEncorclementSaddleOnly")
                        .HasColumnType("bit");

                    b.Property<bool>("Gasket")
                        .HasColumnType("bit");

                    b.Property<string>("GasketType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Grade")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HSS2Seal")
                        .HasColumnType("bit");

                    b.Property<bool>("HSS2Seal9")
                        .HasColumnType("bit");

                    b.Property<bool>("HTP")
                        .HasColumnType("bit");

                    b.Property<bool>("HiStop")
                        .HasColumnType("bit");

                    b.Property<int>("HoldTime")
                        .HasColumnType("int");

                    b.Property<string>("HotTap")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HtsTechSupportRep")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("LineLocation")
                        .HasColumnType("bit");

                    b.Property<int>("LineSchedule")
                        .HasColumnType("int");

                    b.Property<int>("LineSize")
                        .HasColumnType("int");

                    b.Property<bool>("LineStopFlange")
                        .HasColumnType("bit");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("LongRadius")
                        .HasColumnType("bit");

                    b.Property<int>("MDMT")
                        .HasColumnType("int");

                    b.Property<string>("Material")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MaterialOrigin")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MaterialToughness")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MaximumHardness")
                        .HasColumnType("int");

                    b.Property<bool>("MetalToMetalSeal")
                        .HasColumnType("bit");

                    b.Property<int>("MinValveBore")
                        .HasColumnType("int");

                    b.Property<bool>("NoPressureTest")
                        .HasColumnType("bit");

                    b.Property<bool>("None")
                        .HasColumnType("bit");

                    b.Property<bool>("NotApplicable")
                        .HasColumnType("bit");

                    b.Property<bool>("NotApplicable2")
                        .HasColumnType("bit");

                    b.Property<bool>("NotRequired5")
                        .HasColumnType("bit");

                    b.Property<string>("Notes10")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("NozzleOnly")
                        .HasColumnType("bit");

                    b.Property<bool>("NozzleOnly7")
                        .HasColumnType("bit");

                    b.Property<bool>("NozzleWithFullEncirclementSaddle")
                        .HasColumnType("bit");

                    b.Property<bool>("NozzleWithRepad")
                        .HasColumnType("bit");

                    b.Property<bool>("NptPortSize")
                        .HasColumnType("bit");

                    b.Property<bool>("NuclearNaceRequired")
                        .HasColumnType("bit");

                    b.Property<string>("Other")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Other10")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Other9")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PWHT")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ParallelToRun")
                        .HasColumnType("bit");

                    b.Property<bool>("Part1")
                        .HasColumnType("bit");

                    b.Property<bool>("Part2")
                        .HasColumnType("bit");

                    b.Property<bool>("Part3")
                        .HasColumnType("bit");

                    b.Property<bool>("Part4")
                        .HasColumnType("bit");

                    b.Property<bool>("Part5")
                        .HasColumnType("bit");

                    b.Property<string>("PartNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PeStampRequired")
                        .HasColumnType("bit");

                    b.Property<bool>("PerimeterSeal")
                        .HasColumnType("bit");

                    b.Property<bool>("PermanentDrawWorksHold")
                        .HasColumnType("bit");

                    b.Property<int>("PermanentMachineAndValveRemoved")
                        .HasColumnType("int");

                    b.Property<bool>("PermanentValve")
                        .HasColumnType("bit");

                    b.Property<bool>("PerpendicularToRun")
                        .HasColumnType("bit");

                    b.Property<bool>("PortInFlange")
                        .HasColumnType("bit");

                    b.Property<string>("PortSize")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Pressure")
                        .HasColumnType("int");

                    b.Property<bool>("PressureRatainingSplitTee")
                        .HasColumnType("bit");

                    b.Property<bool>("PriceQuote")
                        .HasColumnType("bit");

                    b.Property<bool>("Primer")
                        .HasColumnType("bit");

                    b.Property<bool>("Priority")
                        .HasColumnType("bit");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<bool>("RepadOnly")
                        .HasColumnType("bit");

                    b.Property<DateTime>("RequestedDeliveryDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Routine")
                        .HasColumnType("bit");

                    b.Property<bool>("RubberSeal")
                        .HasColumnType("bit");

                    b.Property<int>("RunLength")
                        .HasColumnType("int");

                    b.Property<bool>("RunLengthType")
                        .HasColumnType("bit");

                    b.Property<string>("SafetyReviewNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("SealantType")
                        .HasColumnType("bit");

                    b.Property<bool>("SelfSeal")
                        .HasColumnType("bit");

                    b.Property<string>("Service")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ShopTest")
                        .HasColumnType("bit");

                    b.Property<bool>("ShortRadius")
                        .HasColumnType("bit");

                    b.Property<string>("Size")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Size4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Size9")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("SolidCompletionPlugWithPigBars")
                        .HasColumnType("bit");

                    b.Property<bool>("SphericalTee")
                        .HasColumnType("bit");

                    b.Property<bool>("Standard")
                        .HasColumnType("bit");

                    b.Property<bool>("Standard4")
                        .HasColumnType("bit");

                    b.Property<bool>("StandardCompletionPlugWithScarfedNipple")
                        .HasColumnType("bit");

                    b.Property<bool>("StandardCompletionPlugWithoutScarfedNipple")
                        .HasColumnType("bit");

                    b.Property<bool>("StandardFlange")
                        .HasColumnType("bit");

                    b.Property<string>("StockFitting")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("StrongbacksRequired")
                        .HasColumnType("bit");

                    b.Property<bool>("Studs")
                        .HasColumnType("bit");

                    b.Property<bool>("SureStop")
                        .HasColumnType("bit");

                    b.Property<int>("Temperature")
                        .HasColumnType("int");

                    b.Property<int>("TestPressure")
                        .HasColumnType("int");

                    b.Property<string>("ThirdPartyInspection")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Threaded")
                        .HasColumnType("bit");

                    b.Property<bool>("Viton")
                        .HasColumnType("bit");

                    b.Property<bool>("WaitforClientApproval")
                        .HasColumnType("bit");

                    b.Property<int>("WallThickness")
                        .HasColumnType("int");

                    b.Property<int>("WallThickness4")
                        .HasColumnType("int");

                    b.Property<int>("WallThickness9")
                        .HasColumnType("int");

                    b.Property<bool>("WeepHole")
                        .HasColumnType("bit");

                    b.Property<bool>("WeldOLet")
                        .HasColumnType("bit");

                    b.Property<bool>("WeldOn")
                        .HasColumnType("bit");

                    b.Property<bool>("WeldOn7")
                        .HasColumnType("bit");

                    b.Property<bool>("WeldOn9")
                        .HasColumnType("bit");

                    b.Property<int>("YieldStrength")
                        .HasColumnType("int");

                    b.ToTable("HtsEngineeringDataCoverSheets");

                    b.HasData(
                        new
                        {
                            Id = 2,
                            PackageId = 2,
                            Aflas = false,
                            Angle = false,
                            AngleDegree = 0,
                            BallPark = false,
                            BallParkNo = "47",
                            BlindFlange = false,
                            BoltOn = false,
                            BoltOn7 = false,
                            BoltOn9 = false,
                            BranchEndOption = true,
                            BranchHeight = 12,
                            BranchHeightType = true,
                            BranchWithInternalThreads = false,
                            Buna = false,
                            ButtWeld = false,
                            CEPercentage = 0,
                            CalculationPackage = true,
                            ClientSpecified = false,
                            ClientSpecified3 = false,
                            ClientSpecified4 = false,
                            ContactInfo = "6912734532",
                            CorrosionAllowance = 2,
                            CutterDiameter = true,
                            DesignCode = false,
                            DesignFactor = true,
                            DesignPressure = 50,
                            DesignTemperature = 90,
                            DistrictProjectId = "92",
                            DrawWorksNotRequired = false,
                            DrawWorksNotRequired9 = false,
                            DrawWorksforSettingCompletion = false,
                            DrawWorksforSettingPlug9 = 0,
                            EPDM = false,
                            ElbowLet = false,
                            ElbowOnHeel = false,
                            ElbowStraight = false,
                            FieldTest = false,
                            FittingMaterial = "Carbon Steel",
                            FlangeRF = false,
                            FlangeRTJ = false,
                            FlangeRating = "13",
                            FlangeRatingList = true,
                            FlangeWithPlug = false,
                            FlowThroughPlugWithPigBars = 0,
                            ForImmediateManufacture = true,
                            FullEncirclementSaddle = false,
                            FullEncorclementSaddleOnly = true,
                            Gasket = false,
                            HSS2Seal = false,
                            HSS2Seal9 = false,
                            HTP = false,
                            HiStop = false,
                            HoldTime = 0,
                            HotTap = "true",
                            HtsTechSupportRep = "Steph Curry",
                            LineLocation = true,
                            LineSchedule = 4,
                            LineSize = 6,
                            LineStopFlange = false,
                            Location = "Midland",
                            LongRadius = false,
                            MDMT = 20,
                            Material = "Carbon Steel",
                            MaximumHardness = 0,
                            MetalToMetalSeal = false,
                            MinValveBore = 0,
                            NoPressureTest = false,
                            None = false,
                            NotApplicable = false,
                            NotApplicable2 = false,
                            NotRequired5 = false,
                            NozzleOnly = true,
                            NozzleOnly7 = false,
                            NozzleWithFullEncirclementSaddle = false,
                            NozzleWithRepad = false,
                            NptPortSize = true,
                            NuclearNaceRequired = true,
                            Other = "",
                            ParallelToRun = false,
                            Part1 = true,
                            Part2 = false,
                            Part3 = false,
                            Part4 = false,
                            Part5 = false,
                            PartNumber = "134",
                            PeStampRequired = true,
                            PerimeterSeal = true,
                            PermanentDrawWorksHold = false,
                            PermanentMachineAndValveRemoved = 0,
                            PermanentValve = false,
                            PerpendicularToRun = false,
                            PortInFlange = false,
                            Pressure = 29,
                            PressureRatainingSplitTee = true,
                            PriceQuote = true,
                            Primer = false,
                            Priority = true,
                            Quantity = 5,
                            RepadOnly = false,
                            RequestedDeliveryDate = new DateTime(2021, 5, 1, 7, 30, 52, 0, DateTimeKind.Unspecified),
                            Routine = false,
                            RubberSeal = false,
                            RunLength = 33,
                            RunLengthType = true,
                            SafetyReviewNo = "23",
                            SealantType = true,
                            SelfSeal = false,
                            Service = "Hot Tap",
                            ShopTest = false,
                            ShortRadius = false,
                            Size = "4",
                            Size4 = "3",
                            SolidCompletionPlugWithPigBars = false,
                            SphericalTee = true,
                            Standard = true,
                            Standard4 = false,
                            StandardCompletionPlugWithScarfedNipple = false,
                            StandardCompletionPlugWithoutScarfedNipple = false,
                            StandardFlange = false,
                            StockFitting = "false",
                            StrongbacksRequired = false,
                            Studs = false,
                            SureStop = false,
                            Temperature = 87,
                            TestPressure = 0,
                            Threaded = false,
                            Viton = false,
                            WaitforClientApproval = false,
                            WallThickness = 5,
                            WallThickness4 = 0,
                            WallThickness9 = 0,
                            WeepHole = true,
                            WeldOLet = true,
                            WeldOn = false,
                            WeldOn7 = false,
                            WeldOn9 = false,
                            YieldStrength = 0
                        });
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.LeakRepairHardwareSpecificationSheet", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DataSheet");

                    b.Property<string>("ApprovalEmails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ApprovalInformationDeadline")
                        .HasColumnType("datetime2");

                    b.Property<string>("BranchSize")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClientSpecificRequirements")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("DefectCause")
                        .HasColumnType("bit");

                    b.Property<bool>("DefectType")
                        .HasColumnType("bit");

                    b.Property<string>("DistrictWorkOrderNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EnclosureMaterial")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("EnclosureQuantity")
                        .HasColumnType("int");

                    b.Property<bool>("EngineeringPriorityLevel")
                        .HasColumnType("bit");

                    b.Property<DateTime>("ExpectedRemovalDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Frequency")
                        .HasColumnType("int");

                    b.Property<int>("Grade")
                        .HasColumnType("int");

                    b.Property<bool>("IfDosed")
                        .HasColumnType("bit");

                    b.Property<string>("LineContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("LineContentPercentage")
                        .HasColumnType("int");

                    b.Property<bool>("LineMaterial")
                        .HasColumnType("bit");

                    b.Property<string>("LineNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("LineSchedule")
                        .HasColumnType("int");

                    b.Property<int>("MDMT")
                        .HasColumnType("int");

                    b.Property<int>("MainLineSize")
                        .HasColumnType("int");

                    b.Property<int>("MaxSysOpPressure")
                        .HasColumnType("int");

                    b.Property<int>("MaxSystemDesignTemp")
                        .HasColumnType("int");

                    b.Property<int>("MaxSystemOpTemp")
                        .HasColumnType("int");

                    b.Property<bool>("NdtDataProvided")
                        .HasColumnType("bit");

                    b.Property<string>("OrderNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OtherDefect")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrintName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ProcessingRequirements")
                        .HasColumnType("bit");

                    b.Property<bool>("QualityRequirements")
                        .HasColumnType("bit");

                    b.Property<int>("RemainingWallThickness")
                        .HasColumnType("int");

                    b.Property<int>("ReqRepairCorrosionAllowance")
                        .HasColumnType("int");

                    b.Property<DateTime>("RequestedInstallationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SealantSelection")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Site")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("SourService")
                        .HasColumnType("bit");

                    b.Property<bool>("SprayTypeDesuperheater")
                        .HasColumnType("bit");

                    b.Property<bool>("Strongback")
                        .HasColumnType("bit");

                    b.Property<bool>("SuperheatedSteam")
                        .HasColumnType("bit");

                    b.Property<int>("SysDesignPressure")
                        .HasColumnType("int");

                    b.Property<int>("SysOpPressure")
                        .HasColumnType("int");

                    b.Property<int>("SystemDesignTemp")
                        .HasColumnType("int");

                    b.Property<int>("SystemOpTemp")
                        .HasColumnType("int");

                    b.Property<string>("TeamCjNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Technician1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Technician2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Telephone")
                        .HasColumnType("int");

                    b.Property<bool>("Vibration")
                        .HasColumnType("bit");

                    b.ToTable("LeakRepairHardwareSpecificationSheets");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            PackageId = 1,
                            ApprovalEmails = "<EMAIL>",
                            ApprovalInformationDeadline = new DateTime(2021, 9, 9, 11, 17, 8, 758, DateTimeKind.Local).AddTicks(3282),
                            BranchSize = "5",
                            ClientSpecificRequirements = "NA",
                            DefectCause = true,
                            DefectType = true,
                            DistrictWorkOrderNo = "7",
                            Email = "<EMAIL>",
                            EnclosureMaterial = "NA",
                            EnclosureQuantity = 1,
                            EngineeringPriorityLevel = true,
                            ExpectedRemovalDate = new DateTime(2021, 9, 9, 11, 17, 8, 758, DateTimeKind.Local).AddTicks(1068),
                            Frequency = 10,
                            Grade = 2,
                            IfDosed = false,
                            LineContent = "Condensate",
                            LineContentPercentage = 100,
                            LineMaterial = true,
                            LineNo = "56",
                            LineSchedule = 7,
                            MDMT = 25,
                            MainLineSize = 3,
                            MaxSysOpPressure = 20,
                            MaxSystemDesignTemp = 100,
                            MaxSystemOpTemp = 50,
                            NdtDataProvided = true,
                            OrderNo = "2",
                            PrintName = "Jeffery Smith",
                            ProcessingRequirements = true,
                            QualityRequirements = false,
                            RemainingWallThickness = 2,
                            ReqRepairCorrosionAllowance = 2,
                            RequestedInstallationDate = new DateTime(2021, 9, 9, 11, 17, 8, 758, DateTimeKind.Local).AddTicks(3515),
                            SealantSelection = "5X",
                            Site = "Midland",
                            SourService = true,
                            SprayTypeDesuperheater = false,
                            Strongback = true,
                            SuperheatedSteam = false,
                            SysDesignPressure = 15,
                            SysOpPressure = 5,
                            SystemDesignTemp = 75,
                            SystemOpTemp = 20,
                            TeamCjNo = "42",
                            Technician1 = "Alex Ramos",
                            Technician2 = "Brian Turner",
                            Telephone = 2455687,
                            Vibration = true
                        });
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS100", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<bool>("CS035")
                        .HasColumnType("bit");

                    b.Property<string>("Capnuts")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("CapnutsNumbersRequired")
                        .HasColumnType("int");

                    b.Property<bool>("DTP")
                        .HasColumnType("bit");

                    b.Property<int>("DepthToGasket")
                        .HasColumnType("int");

                    b.Property<bool>("Elbows")
                        .HasColumnType("bit");

                    b.Property<int>("FirstDesiredEarLocationPoint")
                        .HasColumnType("int");

                    b.Property<double>("FirstStudDepthForFlange1")
                        .HasColumnType("float");

                    b.Property<double>("FirstStudDepthForFlange2")
                        .HasColumnType("float");

                    b.Property<int>("FirstStudDepthPoint")
                        .HasColumnType("int");

                    b.Property<double>("Flange1OuterDiameter12To6")
                        .HasColumnType("float");

                    b.Property<double>("Flange1OuterDiameter1To7")
                        .HasColumnType("float");

                    b.Property<double>("Flange1OuterDiameter2To8")
                        .HasColumnType("float");

                    b.Property<double>("Flange1OuterDiameter3To9")
                        .HasColumnType("float");

                    b.Property<double>("Flange1OuterDiameter4To10")
                        .HasColumnType("float");

                    b.Property<double>("Flange1OuterDiameter5To11")
                        .HasColumnType("float");

                    b.Property<double>("Flange1Width")
                        .HasColumnType("float");

                    b.Property<double>("Flange2OuterDiameter12To6")
                        .HasColumnType("float");

                    b.Property<double>("Flange2OuterDiameter1To7")
                        .HasColumnType("float");

                    b.Property<double>("Flange2OuterDiameter2To8")
                        .HasColumnType("float");

                    b.Property<double>("Flange2OuterDiameter3To9")
                        .HasColumnType("float");

                    b.Property<double>("Flange2OuterDiameter4To10")
                        .HasColumnType("float");

                    b.Property<double>("Flange2OuterDiameter5To11")
                        .HasColumnType("float");

                    b.Property<double>("Flange2Width")
                        .HasColumnType("float");

                    b.Property<string>("FlgApp")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("FourthStudDepthForFlange1")
                        .HasColumnType("float");

                    b.Property<double>("FourthStudDepthForFlange2")
                        .HasColumnType("float");

                    b.Property<int>("FourthStudDepthPoint")
                        .HasColumnType("int");

                    b.Property<int>("FromFlange")
                        .HasColumnType("int");

                    b.Property<string>("InjectionRings")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("InjectionRingsNumbersRequired")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NumberAndSizeRequired")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NumberOfStuds")
                        .HasColumnType("int");

                    b.Property<string>("OtherWireWrap")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point10Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point10FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point10FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point10Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point10Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point11Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point11FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point11FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point11Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point11Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point12Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point12FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point12FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point12Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point12Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point1Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point1FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point1FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point1Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point1Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point2Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point2FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point2FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point2Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point2Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point3Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point3FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point3FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point3Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point3Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point4Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point4FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point4FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point4Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point4Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point5Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point5FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point5FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point5Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point5Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point6Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point6FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point6FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point6Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point6Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point7Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point7FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point7FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point7Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point7Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point8Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point8FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point8FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point8Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point8Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point9Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point9FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point9FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point9Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point9Overall")
                        .HasColumnType("int");

                    b.Property<bool>("SS016")
                        .HasColumnType("bit");

                    b.Property<bool>("SS035")
                        .HasColumnType("bit");

                    b.Property<int>("SecondDesiredEarLocationPoint")
                        .HasColumnType("int");

                    b.Property<double>("SecondStudDepthForFlange1")
                        .HasColumnType("float");

                    b.Property<double>("SecondStudDepthForFlange2")
                        .HasColumnType("float");

                    b.Property<int>("SecondStudDepthPoint")
                        .HasColumnType("int");

                    b.Property<string>("SlottedStuds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SlottedStudsNumbersRequired")
                        .HasColumnType("int");

                    b.Property<string>("StudMaterial")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StudSize")
                        .HasColumnType("int");

                    b.Property<double>("ThirdStudDepthForFlange1")
                        .HasColumnType("float");

                    b.Property<double>("ThirdStudDepthForFlange2")
                        .HasColumnType("float");

                    b.Property<int>("ThirdStudDepthPoint")
                        .HasColumnType("int");

                    b.ToTable("DS100s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS101", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("DTG")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointA")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointB")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointC")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointD")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointE")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointF")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointG")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointH")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointJ")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointK")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointL")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointM")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointN")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointP")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointQ")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointR")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointS")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointT")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointU")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointW")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointX")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointA")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointB")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointC")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointD")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointE")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointF")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointG")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointH")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointJ")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointK")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointL")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointM")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointN")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointP")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointQ")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointR")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointS")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointT")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointU")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointW")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointX")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NoOfStuds")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("Point10Amount")
                        .HasColumnType("int");

                    b.Property<string>("Point10FlangeOver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point10FlangeUnder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point11Amount")
                        .HasColumnType("int");

                    b.Property<string>("Point11FlangeOver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point11FlangeUnder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point12Amount")
                        .HasColumnType("int");

                    b.Property<string>("Point12FlangeOver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point12FlangeUnder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point1Amount")
                        .HasColumnType("int");

                    b.Property<string>("Point1FlangeOver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point1FlangeUnder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point2Amount")
                        .HasColumnType("int");

                    b.Property<string>("Point2FlangeOver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point2FlangeUnder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point3Amount")
                        .HasColumnType("int");

                    b.Property<string>("Point3FlangeOver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point3FlangeUnder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point4Amount")
                        .HasColumnType("int");

                    b.Property<string>("Point4FlangeOver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point4FlangeUnder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point5Amount")
                        .HasColumnType("int");

                    b.Property<string>("Point5FlangeOver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point5FlangeUnder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point6Amount")
                        .HasColumnType("int");

                    b.Property<string>("Point6FlangeOver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point6FlangeUnder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point7Amount")
                        .HasColumnType("int");

                    b.Property<string>("Point7FlangeOver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point7FlangeUnder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point8Amount")
                        .HasColumnType("int");

                    b.Property<string>("Point8FlangeOver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point8FlangeUnder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point9Amount")
                        .HasColumnType("int");

                    b.Property<string>("Point9FlangeOver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point9FlangeUnder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudMaterial")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StudSize")
                        .HasColumnType("int");

                    b.ToTable("DS101s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS102", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("DTG")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point10X")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point1X")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point1Y")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point2X")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point2Y")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point3X")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point3Y")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point4X")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point4Y")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point5X")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point5Y")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point6X")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point7X")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point8X")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Point9X")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointA")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointB")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointC")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointD")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointE")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointF")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointG")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointH")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointJ")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointW")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointWX")
                        .HasColumnType("int");

                    b.Property<int>("Flange1PointWY")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point10X")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point1X")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point1Y")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point2X")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point2Y")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point3X")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point3Y")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point4X")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point4Y")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point5X")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point5Y")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point6X")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point7X")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point8X")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Point9X")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointA")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointB")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointC")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointD")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointE")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointF")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointG")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointH")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointJ")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointW")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointWX")
                        .HasColumnType("int");

                    b.Property<int>("Flange2PointWY")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IncrOHalf")
                        .HasColumnType("bit");

                    b.Property<bool>("IncrOne")
                        .HasColumnType("bit");

                    b.Property<bool>("IncrOneAndHalf")
                        .HasColumnType("bit");

                    b.Property<int>("NoOfStuds")
                        .HasColumnType("int");

                    b.Property<string>("Point10xAmountSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point10xAmountSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point10xAmountSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point10xAmountSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point10xFlangeOverSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point10xFlangeOverSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point10xFlangeOverSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point10xFlangeOverSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point10xFlangeUnderSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point10xFlangeUnderSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point10xFlangeUnderSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point10xFlangeUnderSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point10xLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point10xLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point10xLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point10xLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point10xLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point10xLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point1xLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point1xLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point1xLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point1xLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point1xLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point1xLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<string>("Point1yAmountSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point1yAmountSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point1yAmountSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point1yAmountSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point1yFlangeOverSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point1yFlangeOverSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point1yFlangeOverSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point1yFlangeOverSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point1yFlangeUnderSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point1yFlangeUnderSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point1yFlangeUnderSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point1yFlangeUnderSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point1yLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point1yLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point1yLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point1yLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point1yLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point1yLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<string>("Point2xAmountSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point2xAmountSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point2xAmountSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point2xAmountSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point2xFlangeOverSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point2xFlangeOverSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point2xFlangeOverSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point2xFlangeOverSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point2xFlangeUnderSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point2xFlangeUnderSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point2xFlangeUnderSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point2xFlangeUnderSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point2xLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point2xLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point2xLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point2xLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point2xLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point2xLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point2yLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point2yLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point2yLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point2yLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point2yLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point2yLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point3xLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point3xLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point3xLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point3xLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point3xLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point3xLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<string>("Point3yAmountSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point3yAmountSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point3yAmountSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point3yAmountSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point3yFlangeOverSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point3yFlangeOverSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point3yFlangeOverSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point3yFlangeOverSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point3yFlangeUnderSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point3yFlangeUnderSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point3yFlangeUnderSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point3yFlangeUnderSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point3yLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point3yLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point3yLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point3yLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point3yLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point3yLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<string>("Point4xAmountSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point4xAmountSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point4xAmountSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point4xAmountSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point4xFlangeOverSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point4xFlangeOverSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point4xFlangeOverSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point4xFlangeOverSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point4xFlangeUnderSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point4xFlangeUnderSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point4xFlangeUnderSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point4xFlangeUnderSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point4xLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point4xLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point4xLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point4xLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point4xLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point4xLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point4yLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point4yLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point4yLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point4yLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point4yLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point4yLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point5xLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point5xLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point5xLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point5xLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point5xLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point5xLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<string>("Point5yAmountSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point5yAmountSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point5yAmountSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point5yAmountSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point5yFlangeOverSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point5yFlangeOverSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point5yFlangeOverSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point5yFlangeOverSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point5yFlangeUnderSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point5yFlangeUnderSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point5yFlangeUnderSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point5yFlangeUnderSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point5yLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point5yLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point5yLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point5yLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point5yLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point5yLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<string>("Point6xAmountSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point6xAmountSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point6xAmountSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point6xAmountSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point6xFlangeOverSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point6xFlangeOverSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point6xFlangeOverSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point6xFlangeOverSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point6xFlangeUnderSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point6xFlangeUnderSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point6xFlangeUnderSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point6xFlangeUnderSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point6xLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point6xLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point6xLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point6xLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point6xLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point6xLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point7xLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point7xLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point7xLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point7xLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point7xLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point7xLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<string>("Point8xAmountSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point8xAmountSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point8xAmountSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point8xAmountSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point8xFlangeOverSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point8xFlangeOverSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point8xFlangeOverSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point8xFlangeOverSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point8xFlangeUnderSection1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point8xFlangeUnderSection2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point8xFlangeUnderSection3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Point8xFlangeUnderSection4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point8xLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point8xLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point8xLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point8xLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point8xLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point8xLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point9xLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point9xLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point9xLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("Point9xLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("Point9xLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("Point9xLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<int>("PointWxLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("PointWxLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("PointWxLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("PointWxLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("PointWxLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("PointWxLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<int>("PointWyLtrFlange1Section2")
                        .HasColumnType("int");

                    b.Property<int>("PointWyLtrFlange1Section3")
                        .HasColumnType("int");

                    b.Property<int>("PointWyLtrFlange1Section4")
                        .HasColumnType("int");

                    b.Property<int>("PointWyLtrFlange2Section2")
                        .HasColumnType("int");

                    b.Property<int>("PointWyLtrFlange2Section3")
                        .HasColumnType("int");

                    b.Property<int>("PointWyLtrFlange2Section4")
                        .HasColumnType("int");

                    b.Property<string>("StudMaterial")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StudSize")
                        .HasColumnType("int");

                    b.ToTable("DS102s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS103", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A")
                        .HasColumnType("int");

                    b.Property<int>("Amount1")
                        .HasColumnType("int");

                    b.Property<int>("Amount2")
                        .HasColumnType("int");

                    b.Property<int>("Amount3")
                        .HasColumnType("int");

                    b.Property<int>("Amount4")
                        .HasColumnType("int");

                    b.Property<int>("Amount5")
                        .HasColumnType("int");

                    b.Property<int>("Amount6")
                        .HasColumnType("int");

                    b.Property<int>("Amount7")
                        .HasColumnType("int");

                    b.Property<int>("Amount8")
                        .HasColumnType("int");

                    b.Property<int>("DTG")
                        .HasColumnType("int");

                    b.Property<bool>("DandT")
                        .HasColumnType("bit");

                    b.Property<int>("Flange1OD")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Width")
                        .HasColumnType("int");

                    b.Property<int>("Flange2OD")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Width")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FlgGapAmount1")
                        .HasColumnType("int");

                    b.Property<int>("FlgGapAmount2")
                        .HasColumnType("int");

                    b.Property<int>("FlgGapAmount3")
                        .HasColumnType("int");

                    b.Property<int>("FlgGapAmount4")
                        .HasColumnType("int");

                    b.Property<string>("FlgOverStud1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgOverStud2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgOverStud3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgOverStud4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgOverStud5")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgOverStud6")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgOverStud7")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgOverStud8")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgUnderStud1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgUnderStud2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgUnderStud3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgUnderStud4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgUnderStud5")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgUnderStud6")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgUnderStud7")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlgUnderStud8")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G")
                        .HasColumnType("int");

                    b.Property<int>("H")
                        .HasColumnType("int");

                    b.Property<bool>("Holland")
                        .HasColumnType("bit");

                    b.Property<int>("MinClearanceBetweenNuts")
                        .HasColumnType("int");

                    b.Property<int>("NoOfStudsBetweenStud2and3")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<bool>("Other")
                        .HasColumnType("bit");

                    b.Property<int>("P")
                        .HasColumnType("int");

                    b.Property<int>("Q")
                        .HasColumnType("int");

                    b.Property<int>("R")
                        .HasColumnType("int");

                    b.Property<int>("Stud1Flg1")
                        .HasColumnType("int");

                    b.Property<int>("Stud1Flg2")
                        .HasColumnType("int");

                    b.Property<int>("Stud2Flg1")
                        .HasColumnType("int");

                    b.Property<int>("Stud2Flg2")
                        .HasColumnType("int");

                    b.Property<int>("Stud3Flg1")
                        .HasColumnType("int");

                    b.Property<int>("Stud3Flg3")
                        .HasColumnType("int");

                    b.Property<string>("StudMaterial")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StudSize")
                        .HasColumnType("int");

                    b.Property<int>("T")
                        .HasColumnType("int");

                    b.Property<int>("U")
                        .HasColumnType("int");

                    b.Property<int>("V")
                        .HasColumnType("int");

                    b.ToTable("DS103s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS104", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("AA")
                        .HasColumnType("int");

                    b.Property<int>("AA2")
                        .HasColumnType("int");

                    b.Property<int>("B")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("BB")
                        .HasColumnType("int");

                    b.Property<int>("BB2")
                        .HasColumnType("int");

                    b.Property<int>("C")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("CC")
                        .HasColumnType("int");

                    b.Property<int>("CC2")
                        .HasColumnType("int");

                    b.Property<int>("D")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("DD")
                        .HasColumnType("int");

                    b.Property<int>("DD2")
                        .HasColumnType("int");

                    b.Property<int>("E")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("EE")
                        .HasColumnType("int");

                    b.Property<int>("EE2")
                        .HasColumnType("int");

                    b.Property<int>("F")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<int>("FF")
                        .HasColumnType("int");

                    b.Property<int>("FF2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Flg1ValvePos")
                        .HasColumnType("int");

                    b.Property<int>("Flg2ValvePos")
                        .HasColumnType("int");

                    b.Property<int>("G")
                        .HasColumnType("int");

                    b.Property<int>("G2")
                        .HasColumnType("int");

                    b.Property<int>("GG")
                        .HasColumnType("int");

                    b.Property<int>("GG2")
                        .HasColumnType("int");

                    b.Property<int>("H")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<int>("HH")
                        .HasColumnType("int");

                    b.Property<int>("HH2")
                        .HasColumnType("int");

                    b.Property<bool>("IdTab")
                        .HasColumnType("bit");

                    b.Property<int>("J")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("JJ")
                        .HasColumnType("int");

                    b.Property<int>("JJ2")
                        .HasColumnType("int");

                    b.Property<int>("K")
                        .HasColumnType("int");

                    b.Property<int>("K2")
                        .HasColumnType("int");

                    b.Property<int>("KK")
                        .HasColumnType("int");

                    b.Property<int>("KK2")
                        .HasColumnType("int");

                    b.Property<int>("L")
                        .HasColumnType("int");

                    b.Property<int>("L2")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlows")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("M")
                        .HasColumnType("int");

                    b.Property<int>("M2")
                        .HasColumnType("int");

                    b.Property<int>("N")
                        .HasColumnType("int");

                    b.Property<int>("N2")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("P")
                        .HasColumnType("int");

                    b.Property<int>("P2")
                        .HasColumnType("int");

                    b.Property<int>("Q")
                        .HasColumnType("int");

                    b.Property<int>("Q2")
                        .HasColumnType("int");

                    b.Property<int>("R")
                        .HasColumnType("int");

                    b.Property<int>("R2")
                        .HasColumnType("int");

                    b.Property<int>("S")
                        .HasColumnType("int");

                    b.Property<int>("S2")
                        .HasColumnType("int");

                    b.Property<int>("T")
                        .HasColumnType("int");

                    b.Property<int>("T2")
                        .HasColumnType("int");

                    b.Property<int>("TabClkPt")
                        .HasColumnType("int");

                    b.Property<bool>("Threaded")
                        .HasColumnType("bit");

                    b.Property<int>("U")
                        .HasColumnType("int");

                    b.Property<int>("U2")
                        .HasColumnType("int");

                    b.Property<int>("V")
                        .HasColumnType("int");

                    b.Property<int>("V2")
                        .HasColumnType("int");

                    b.Property<int>("ValveClkPt")
                        .HasColumnType("int");

                    b.Property<int>("W")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<bool>("Welded")
                        .HasColumnType("bit");

                    b.Property<int>("X")
                        .HasColumnType("int");

                    b.Property<int>("X2")
                        .HasColumnType("int");

                    b.Property<int>("Y")
                        .HasColumnType("int");

                    b.Property<int>("Y2")
                        .HasColumnType("int");

                    b.Property<int>("Z")
                        .HasColumnType("int");

                    b.Property<int>("Z2")
                        .HasColumnType("int");

                    b.ToTable("DS104s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS105", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("DTG")
                        .HasColumnType("int");

                    b.Property<int>("Dim1")
                        .HasColumnType("int");

                    b.Property<int>("Dim2")
                        .HasColumnType("int");

                    b.Property<int>("Dim3")
                        .HasColumnType("int");

                    b.Property<int>("Dim4")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Flg1ODPt12to6F")
                        .HasColumnType("int");

                    b.Property<int>("Flg1ODPt1to7")
                        .HasColumnType("int");

                    b.Property<int>("Flg1ODPt2to8")
                        .HasColumnType("int");

                    b.Property<int>("Flg1ODPt3to9")
                        .HasColumnType("int");

                    b.Property<int>("Flg1ODPt4to10")
                        .HasColumnType("int");

                    b.Property<int>("Flg1ODPt5to11")
                        .HasColumnType("int");

                    b.Property<int>("Flg1StudDepth1")
                        .HasColumnType("int");

                    b.Property<int>("Flg1StudDepth2")
                        .HasColumnType("int");

                    b.Property<int>("Flg1StudDepth3")
                        .HasColumnType("int");

                    b.Property<int>("Flg1StudDepth4")
                        .HasColumnType("int");

                    b.Property<int>("Flg1Width")
                        .HasColumnType("int");

                    b.Property<int>("Flg2ODPt12to6F")
                        .HasColumnType("int");

                    b.Property<int>("Flg2ODPt1to7")
                        .HasColumnType("int");

                    b.Property<int>("Flg2ODPt2to8")
                        .HasColumnType("int");

                    b.Property<int>("Flg2ODPt3to9")
                        .HasColumnType("int");

                    b.Property<int>("Flg2ODPt4to10")
                        .HasColumnType("int");

                    b.Property<int>("Flg2ODPt5to11")
                        .HasColumnType("int");

                    b.Property<int>("Flg2StudDepth1")
                        .HasColumnType("int");

                    b.Property<int>("Flg2StudDepth2")
                        .HasColumnType("int");

                    b.Property<int>("Flg2StudDepth3")
                        .HasColumnType("int");

                    b.Property<int>("Flg2StudDepth4")
                        .HasColumnType("int");

                    b.Property<int>("Flg2Width")
                        .HasColumnType("int");

                    b.Property<int>("Flg3ODPt12to6F")
                        .HasColumnType("int");

                    b.Property<int>("Flg3ODPt1to7")
                        .HasColumnType("int");

                    b.Property<int>("Flg3ODPt2to8")
                        .HasColumnType("int");

                    b.Property<int>("Flg3ODPt3to9")
                        .HasColumnType("int");

                    b.Property<int>("Flg3ODPt4to10")
                        .HasColumnType("int");

                    b.Property<int>("Flg3ODPt5to11")
                        .HasColumnType("int");

                    b.Property<int>("Flg3StudDepth1")
                        .HasColumnType("int");

                    b.Property<int>("Flg3StudDepth2")
                        .HasColumnType("int");

                    b.Property<int>("Flg3StudDepth3")
                        .HasColumnType("int");

                    b.Property<int>("Flg3StudDepth4")
                        .HasColumnType("int");

                    b.Property<int>("Flg3Width")
                        .HasColumnType("int");

                    b.Property<int>("If3Point10Amount")
                        .HasColumnType("int");

                    b.Property<int>("If3Point10FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("If3Point10FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("If3Point11Amount")
                        .HasColumnType("int");

                    b.Property<int>("If3Point11FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("If3Point11FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("If3Point12Amount")
                        .HasColumnType("int");

                    b.Property<int>("If3Point12FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("If3Point12FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("If3Point1Amount")
                        .HasColumnType("int");

                    b.Property<int>("If3Point1FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("If3Point1FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("If3Point2Amount")
                        .HasColumnType("int");

                    b.Property<int>("If3Point2FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("If3Point2FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("If3Point3Amount")
                        .HasColumnType("int");

                    b.Property<int>("If3Point3FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("If3Point3FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("If3Point4Amount")
                        .HasColumnType("int");

                    b.Property<int>("If3Point4FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("If3Point4FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("If3Point5Amount")
                        .HasColumnType("int");

                    b.Property<int>("If3Point5FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("If3Point5FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("If3Point6Amount")
                        .HasColumnType("int");

                    b.Property<int>("If3Point6FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("If3Point6FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("If3Point7Amount")
                        .HasColumnType("int");

                    b.Property<int>("If3Point7FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("If3Point7FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("If3Point8Amount")
                        .HasColumnType("int");

                    b.Property<int>("If3Point8FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("If3Point8FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("If3Point9Amount")
                        .HasColumnType("int");

                    b.Property<int>("If3Point9FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("If3Point9FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("MinNutToNut")
                        .HasColumnType("int");

                    b.Property<int>("NoOfStuds")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NutFlatToFlat")
                        .HasColumnType("int");

                    b.Property<int>("NutPointToPoint")
                        .HasColumnType("int");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("Point10Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point10FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point10FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point10Flg1MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point10Flg1MinusFlg3")
                        .HasColumnType("int");

                    b.Property<int>("Point10Flg3MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point11Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point11FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point11FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point11Flg1MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point11Flg1MinusFlg3")
                        .HasColumnType("int");

                    b.Property<int>("Point11Flg3MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point12Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point12FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point12FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point12Flg1MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point12Flg1MinusFlg3")
                        .HasColumnType("int");

                    b.Property<int>("Point12Flg3MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point1Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point1FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point1FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point1Flg1MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point1Flg1MinusFlg3")
                        .HasColumnType("int");

                    b.Property<int>("Point1Flg3MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point2Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point2FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point2FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point2Flg1MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point2Flg1MinusFlg3")
                        .HasColumnType("int");

                    b.Property<int>("Point2Flg3MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point3Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point3FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point3FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point3Flg1MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point3Flg1MinusFlg3")
                        .HasColumnType("int");

                    b.Property<int>("Point3Flg3MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point4Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point4FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point4FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point4Flg1MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point4Flg1MinusFlg3")
                        .HasColumnType("int");

                    b.Property<int>("Point4Flg3MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point5Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point5FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point5FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point5Flg1MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point5Flg1MinusFlg3")
                        .HasColumnType("int");

                    b.Property<int>("Point5Flg3MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point6Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point6FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point6FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point6Flg1MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point6Flg1MinusFlg3")
                        .HasColumnType("int");

                    b.Property<int>("Point6Flg3MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point7Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point7FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point7FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point7Flg1MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point7Flg1MinusFlg3")
                        .HasColumnType("int");

                    b.Property<int>("Point7Flg3MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point8Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point8FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point8FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point8Flg1MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point8Flg1MinusFlg3")
                        .HasColumnType("int");

                    b.Property<int>("Point8Flg3MinusFlg2")
                        .HasColumnType("int");

                    b.Property<int>("Point9Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point9FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point9FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Pres1")
                        .HasColumnType("int");

                    b.Property<int>("Pres2")
                        .HasColumnType("int");

                    b.Property<int>("Pres3")
                        .HasColumnType("int");

                    b.Property<int>("Pres4")
                        .HasColumnType("int");

                    b.Property<string>("StudDepth1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudDepth2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudDepth3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudDepth4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudMaterial")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StudSize")
                        .HasColumnType("int");

                    b.Property<int>("Temp1")
                        .HasColumnType("int");

                    b.Property<int>("Temp2")
                        .HasColumnType("int");

                    b.Property<int>("Temp3")
                        .HasColumnType("int");

                    b.Property<int>("Temp4")
                        .HasColumnType("int");

                    b.ToTable("DS105s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS110", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<bool>("CanStudsBeCut")
                        .HasColumnType("bit");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("DepthToGasket")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<string>("EarLocations")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("F1a")
                        .HasColumnType("int");

                    b.Property<int>("F1b")
                        .HasColumnType("int");

                    b.Property<int>("F2a")
                        .HasColumnType("int");

                    b.Property<int>("F2b")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Width")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Width")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FromFlange")
                        .HasColumnType("int");

                    b.Property<int>("G1a")
                        .HasColumnType("int");

                    b.Property<int>("G1b")
                        .HasColumnType("int");

                    b.Property<int>("G2a")
                        .HasColumnType("int");

                    b.Property<int>("G2b")
                        .HasColumnType("int");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("K1")
                        .HasColumnType("int");

                    b.Property<int>("K2")
                        .HasColumnType("int");

                    b.Property<int>("L112")
                        .HasColumnType("int");

                    b.Property<int>("L13")
                        .HasColumnType("int");

                    b.Property<int>("L16")
                        .HasColumnType("int");

                    b.Property<int>("L19")
                        .HasColumnType("int");

                    b.Property<int>("L212")
                        .HasColumnType("int");

                    b.Property<int>("L23")
                        .HasColumnType("int");

                    b.Property<int>("L26")
                        .HasColumnType("int");

                    b.Property<int>("L29")
                        .HasColumnType("int");

                    b.Property<int>("MinNutToNut")
                        .HasColumnType("int");

                    b.Property<int>("NoOfStuds")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NutFlatToFlat")
                        .HasColumnType("int");

                    b.Property<int>("NutPointToPoint")
                        .HasColumnType("int");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<string>("OdFlangeOne12to6")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("OdFlangeOne1to7")
                        .HasColumnType("datetime2");

                    b.Property<string>("OdFlangeOne2to8")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeOne3to9")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeOne4to10")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeOne5to11")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeTwo12to6")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("OdFlangeTwo1to7")
                        .HasColumnType("datetime2");

                    b.Property<string>("OdFlangeTwo2to8")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeTwo3to9")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeTwo4to10")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeTwo5to11")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point10Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point10FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point10FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point10Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point10Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point11Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point11FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point11FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point11Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point11Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point12Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point12FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point12FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point12Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point12Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point1Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point1FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point1FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point1Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point1Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point2Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point2FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point2FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point2Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point2Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point3Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point3FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point3FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point3Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point3Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point4Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point4FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point4FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point4Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point4Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point5Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point5FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point5FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point5Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point5Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point6Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point6FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point6FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point6Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point6Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point7Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point7FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point7FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point7Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point7Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point8Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point8FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point8FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point8Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point8Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point9Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point9FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point9FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point9Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point9Overall")
                        .HasColumnType("int");

                    b.Property<string>("StudDepthPoint1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StudDepthPoint1Flange1")
                        .HasColumnType("int");

                    b.Property<int>("StudDepthPoint1Flange2")
                        .HasColumnType("int");

                    b.Property<string>("StudDepthPoint2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StudDepthPoint2Flange1")
                        .HasColumnType("int");

                    b.Property<int>("StudDepthPoint2Flange2")
                        .HasColumnType("int");

                    b.Property<string>("StudDepthPoint3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StudDepthPoint3Flange1")
                        .HasColumnType("int");

                    b.Property<int>("StudDepthPoint3Flange2")
                        .HasColumnType("int");

                    b.Property<string>("StudMaterial")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StudSize")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("WH1")
                        .HasColumnType("int");

                    b.Property<int>("WH2")
                        .HasColumnType("int");

                    b.ToTable("DS110s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS111", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<bool>("CanStudsBeCut")
                        .HasColumnType("bit");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("DepthToGasket")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<string>("EarLocations")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("F1a")
                        .HasColumnType("int");

                    b.Property<int>("F1b")
                        .HasColumnType("int");

                    b.Property<int>("F2a")
                        .HasColumnType("int");

                    b.Property<int>("F2b")
                        .HasColumnType("int");

                    b.Property<int>("Flange1Width")
                        .HasColumnType("int");

                    b.Property<int>("Flange2Width")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FromFlange")
                        .HasColumnType("int");

                    b.Property<int>("G1a")
                        .HasColumnType("int");

                    b.Property<int>("G1b")
                        .HasColumnType("int");

                    b.Property<int>("G2a")
                        .HasColumnType("int");

                    b.Property<int>("G2b")
                        .HasColumnType("int");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("K1")
                        .HasColumnType("int");

                    b.Property<int>("K2")
                        .HasColumnType("int");

                    b.Property<int>("L112")
                        .HasColumnType("int");

                    b.Property<int>("L13")
                        .HasColumnType("int");

                    b.Property<int>("L16")
                        .HasColumnType("int");

                    b.Property<int>("L19")
                        .HasColumnType("int");

                    b.Property<int>("L212")
                        .HasColumnType("int");

                    b.Property<int>("L23")
                        .HasColumnType("int");

                    b.Property<int>("L26")
                        .HasColumnType("int");

                    b.Property<int>("L29")
                        .HasColumnType("int");

                    b.Property<bool>("LapJoint")
                        .HasColumnType("bit");

                    b.Property<int>("MinNutToNut")
                        .HasColumnType("int");

                    b.Property<int>("NoOfStuds")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NutFlatToFlat")
                        .HasColumnType("int");

                    b.Property<int>("NutPointToPoint")
                        .HasColumnType("int");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<string>("OdFlangeOne12to6")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("OdFlangeOne1to7")
                        .HasColumnType("datetime2");

                    b.Property<string>("OdFlangeOne2to8")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeOne3to9")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeOne4to10")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeOne5to11")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeTwo12to6")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("OdFlangeTwo1to7")
                        .HasColumnType("datetime2");

                    b.Property<string>("OdFlangeTwo2to8")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeTwo3to9")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeTwo4to10")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OdFlangeTwo5to11")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point10Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point10FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point10FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point10Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point10Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point11Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point11FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point11FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point11Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point11Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point12Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point12FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point12FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point12Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point12Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point1Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point1FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point1FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point1Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point1Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point2Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point2FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point2FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point2Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point2Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point3Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point3FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point3FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point3Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point3Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point4Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point4FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point4FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point4Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point4Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point5Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point5FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point5FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point5Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point5Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point6Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point6FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point6FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point6Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point6Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point7Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point7FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point7FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point7Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point7Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point8Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point8FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point8FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point8Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point8Overall")
                        .HasColumnType("int");

                    b.Property<int>("Point9Amount")
                        .HasColumnType("int");

                    b.Property<int>("Point9FlangeOver")
                        .HasColumnType("int");

                    b.Property<int>("Point9FlangeUnder")
                        .HasColumnType("int");

                    b.Property<int>("Point9Gap")
                        .HasColumnType("int");

                    b.Property<int>("Point9Overall")
                        .HasColumnType("int");

                    b.Property<bool>("SlipOn")
                        .HasColumnType("bit");

                    b.Property<string>("StudDepthPoint1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StudDepthPoint1Flange1")
                        .HasColumnType("int");

                    b.Property<int>("StudDepthPoint1Flange2")
                        .HasColumnType("int");

                    b.Property<string>("StudDepthPoint2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StudDepthPoint2Flange1")
                        .HasColumnType("int");

                    b.Property<int>("StudDepthPoint2Flange2")
                        .HasColumnType("int");

                    b.Property<string>("StudDepthPoint3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StudDepthPoint3Flange1")
                        .HasColumnType("int");

                    b.Property<int>("StudDepthPoint3Flange2")
                        .HasColumnType("int");

                    b.Property<string>("StudMaterial")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StudSize")
                        .HasColumnType("int");

                    b.Property<bool>("ThreadedAndSocket")
                        .HasColumnType("bit");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("WH1")
                        .HasColumnType("int");

                    b.Property<int>("WH2")
                        .HasColumnType("int");

                    b.ToTable("DS111s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS113", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<bool>("CanStudsBeCut")
                        .HasColumnType("bit");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("L1")
                        .HasColumnType("int");

                    b.Property<int>("L2")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfLeak")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("M")
                        .HasColumnType("int");

                    b.Property<int>("N")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("P")
                        .HasColumnType("int");

                    b.Property<int>("Q1")
                        .HasColumnType("int");

                    b.Property<int>("Q2")
                        .HasColumnType("int");

                    b.Property<int>("R1")
                        .HasColumnType("int");

                    b.Property<int>("R2")
                        .HasColumnType("int");

                    b.Property<int>("S1")
                        .HasColumnType("int");

                    b.Property<int>("S2")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("W3")
                        .HasColumnType("int");

                    b.ToTable("DS113s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS120", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<bool>("Coupling")
                        .HasColumnType("bit");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FtoF")
                        .HasColumnType("int");

                    b.Property<int>("G")
                        .HasColumnType("int");

                    b.Property<int>("H")
                        .HasColumnType("int");

                    b.Property<int>("J")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("PttoPt")
                        .HasColumnType("int");

                    b.Property<bool>("Threaded")
                        .HasColumnType("bit");

                    b.Property<bool>("Union")
                        .HasColumnType("bit");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<bool>("Welded")
                        .HasColumnType("bit");

                    b.ToTable("DS120s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS124", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G1")
                        .HasColumnType("int");

                    b.Property<int>("G2")
                        .HasColumnType("int");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("K2")
                        .HasColumnType("int");

                    b.Property<int>("L")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<bool>("Round")
                        .HasColumnType("bit");

                    b.Property<bool>("Square")
                        .HasColumnType("bit");

                    b.Property<bool>("Threaded")
                        .HasColumnType("bit");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<bool>("Welded")
                        .HasColumnType("bit");

                    b.ToTable("DS124s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS128", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("E")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G1")
                        .HasColumnType("int");

                    b.Property<int>("G2")
                        .HasColumnType("int");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("WH1")
                        .HasColumnType("int");

                    b.Property<int>("WH2")
                        .HasColumnType("int");

                    b.ToTable("DS128s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS129", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G1")
                        .HasColumnType("int");

                    b.Property<int>("G2")
                        .HasColumnType("int");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("K1")
                        .HasColumnType("int");

                    b.Property<int>("K2")
                        .HasColumnType("int");

                    b.Property<int>("L")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<bool>("Screwed")
                        .HasColumnType("bit");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<bool>("Welded")
                        .HasColumnType("bit");

                    b.ToTable("DS129s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS133", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G1")
                        .HasColumnType("int");

                    b.Property<int>("G2")
                        .HasColumnType("int");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("K1")
                        .HasColumnType("int");

                    b.Property<int>("K2")
                        .HasColumnType("int");

                    b.Property<int>("L")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("M")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.ToTable("DS133s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS134", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("A3")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("B3")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<bool>("C1D1")
                        .HasColumnType("bit");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<bool>("C2D2")
                        .HasColumnType("bit");

                    b.Property<int>("C3")
                        .HasColumnType("int");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("D3")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<int>("F3")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G1")
                        .HasColumnType("int");

                    b.Property<int>("G2")
                        .HasColumnType("int");

                    b.Property<int>("G3")
                        .HasColumnType("int");

                    b.Property<int>("H")
                        .HasColumnType("int");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("K1")
                        .HasColumnType("int");

                    b.Property<int>("K2")
                        .HasColumnType("int");

                    b.Property<int>("L1")
                        .HasColumnType("int");

                    b.Property<int>("L2")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("M")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<bool>("Screwed")
                        .HasColumnType("bit");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("W3")
                        .HasColumnType("int");

                    b.Property<bool>("Welded")
                        .HasColumnType("bit");

                    b.ToTable("DS134s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS136", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("A3")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("B3")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("C3")
                        .HasColumnType("int");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("D3")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("E3")
                        .HasColumnType("int");

                    b.Property<int>("F")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G1")
                        .HasColumnType("int");

                    b.Property<int>("G2")
                        .HasColumnType("int");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("K")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<bool>("Screwed")
                        .HasColumnType("bit");

                    b.Property<bool>("ScrewedSocket")
                        .HasColumnType("bit");

                    b.Property<bool>("SmlsButtWeldFitting")
                        .HasColumnType("bit");

                    b.Property<int>("W3")
                        .HasColumnType("int");

                    b.Property<int>("W4")
                        .HasColumnType("int");

                    b.Property<bool>("Welded")
                        .HasColumnType("bit");

                    b.ToTable("DS136s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS137", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("IepLength")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("OepLength")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("WH1")
                        .HasColumnType("int");

                    b.ToTable("DS137s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS138", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("E")
                        .HasColumnType("int");

                    b.Property<int>("F")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G")
                        .HasColumnType("int");

                    b.Property<int>("L12")
                        .HasColumnType("int");

                    b.Property<int>("L3")
                        .HasColumnType("int");

                    b.Property<int>("L6")
                        .HasColumnType("int");

                    b.Property<int>("L9")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("WH1")
                        .HasColumnType("int");

                    b.Property<int>("WH2")
                        .HasColumnType("int");

                    b.ToTable("DS138s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS139", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("E")
                        .HasColumnType("int");

                    b.Property<int>("F")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G")
                        .HasColumnType("int");

                    b.Property<int>("L12")
                        .HasColumnType("int");

                    b.Property<int>("L3")
                        .HasColumnType("int");

                    b.Property<int>("L6")
                        .HasColumnType("int");

                    b.Property<int>("L9")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("WH1")
                        .HasColumnType("int");

                    b.Property<int>("WH2")
                        .HasColumnType("int");

                    b.ToTable("DS139s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS140", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("A3")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("B3")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("C3")
                        .HasColumnType("int");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("D3")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("E3")
                        .HasColumnType("int");

                    b.Property<int>("F")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G")
                        .HasColumnType("int");

                    b.Property<int>("H")
                        .HasColumnType("int");

                    b.Property<int>("J")
                        .HasColumnType("int");

                    b.Property<int>("K")
                        .HasColumnType("int");

                    b.Property<int>("L1")
                        .HasColumnType("int");

                    b.Property<int>("L2")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("M1")
                        .HasColumnType("int");

                    b.Property<int>("M2")
                        .HasColumnType("int");

                    b.Property<int>("N1")
                        .HasColumnType("int");

                    b.Property<int>("N2")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("W3")
                        .HasColumnType("int");

                    b.Property<int>("W4")
                        .HasColumnType("int");

                    b.Property<int>("W5")
                        .HasColumnType("int");

                    b.Property<int>("WH1")
                        .HasColumnType("int");

                    b.Property<int>("WH2")
                        .HasColumnType("int");

                    b.Property<int>("WH3")
                        .HasColumnType("int");

                    b.ToTable("DS140s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS141", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("A3")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("B3")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("C3")
                        .HasColumnType("int");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("D3")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("E3")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<int>("F3")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G2")
                        .HasColumnType("int");

                    b.Property<int>("G3")
                        .HasColumnType("int");

                    b.Property<int>("H")
                        .HasColumnType("int");

                    b.Property<int>("J")
                        .HasColumnType("int");

                    b.Property<int>("K")
                        .HasColumnType("int");

                    b.Property<int>("L")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("M1")
                        .HasColumnType("int");

                    b.Property<int>("M2")
                        .HasColumnType("int");

                    b.Property<int>("N1")
                        .HasColumnType("int");

                    b.Property<int>("N2")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("P1")
                        .HasColumnType("int");

                    b.Property<int>("P2")
                        .HasColumnType("int");

                    b.Property<int>("Q")
                        .HasColumnType("int");

                    b.Property<bool>("Screwed")
                        .HasColumnType("bit");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("W3")
                        .HasColumnType("int");

                    b.Property<bool>("Welded")
                        .HasColumnType("bit");

                    b.ToTable("DS141s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS150", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A")
                        .HasColumnType("int");

                    b.Property<int>("B12")
                        .HasColumnType("int");

                    b.Property<int>("B3")
                        .HasColumnType("int");

                    b.Property<int>("B6")
                        .HasColumnType("int");

                    b.Property<int>("B9")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C10")
                        .HasColumnType("int");

                    b.Property<int>("C11")
                        .HasColumnType("int");

                    b.Property<int>("C12")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("C3")
                        .HasColumnType("int");

                    b.Property<int>("C4")
                        .HasColumnType("int");

                    b.Property<int>("C5")
                        .HasColumnType("int");

                    b.Property<int>("C6")
                        .HasColumnType("int");

                    b.Property<int>("C7")
                        .HasColumnType("int");

                    b.Property<int>("C8")
                        .HasColumnType("int");

                    b.Property<int>("C9")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("VesselCirc")
                        .HasColumnType("int");

                    b.Property<int>("VesselDiameter")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.ToTable("DS150s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS151", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A")
                        .HasColumnType("int");

                    b.Property<int>("B12")
                        .HasColumnType("int");

                    b.Property<int>("B3")
                        .HasColumnType("int");

                    b.Property<int>("B6")
                        .HasColumnType("int");

                    b.Property<int>("B9")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C10")
                        .HasColumnType("int");

                    b.Property<int>("C11")
                        .HasColumnType("int");

                    b.Property<int>("C12")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("C3")
                        .HasColumnType("int");

                    b.Property<int>("C4")
                        .HasColumnType("int");

                    b.Property<int>("C5")
                        .HasColumnType("int");

                    b.Property<int>("C6")
                        .HasColumnType("int");

                    b.Property<int>("C7")
                        .HasColumnType("int");

                    b.Property<int>("C8")
                        .HasColumnType("int");

                    b.Property<int>("C9")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("VesselCirc")
                        .HasColumnType("int");

                    b.Property<int>("VesselDiameter")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.ToTable("DS151s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS194", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A")
                        .HasColumnType("int");

                    b.Property<int>("B")
                        .HasColumnType("int");

                    b.Property<int>("C")
                        .HasColumnType("int");

                    b.Property<int>("D")
                        .HasColumnType("int");

                    b.Property<int>("InjPortSize")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NoOfInjectionPorts")
                        .HasColumnType("int");

                    b.Property<int>("NoOfStuds")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("SizeOfStuds")
                        .HasColumnType("int");

                    b.Property<int>("WallThicknessBtoDby2")
                        .HasColumnType("int");

                    b.ToTable("DS194s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS195", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("BoltDia")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<bool>("CarriageOrStepBolt")
                        .HasColumnType("bit");

                    b.Property<int>("D")
                        .HasColumnType("int");

                    b.Property<int>("E")
                        .HasColumnType("int");

                    b.Property<bool>("EyeBolt")
                        .HasColumnType("bit");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<int>("FaceWidth")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G1")
                        .HasColumnType("int");

                    b.Property<int>("G2")
                        .HasColumnType("int");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<int>("HeadDia")
                        .HasColumnType("int");

                    b.Property<int>("HoleDia")
                        .HasColumnType("int");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("K")
                        .HasColumnType("int");

                    b.Property<int>("L")
                        .HasColumnType("int");

                    b.Property<int>("M")
                        .HasColumnType("int");

                    b.Property<int>("N")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("P")
                        .HasColumnType("int");

                    b.Property<int>("Q")
                        .HasColumnType("int");

                    b.Property<int>("R")
                        .HasColumnType("int");

                    b.Property<int>("S")
                        .HasColumnType("int");

                    b.Property<int>("SquareDepth")
                        .HasColumnType("int");

                    b.Property<int>("SquareWidth")
                        .HasColumnType("int");

                    b.Property<int>("StudDia")
                        .HasColumnType("int");

                    b.Property<bool>("StudFlgVlvBody")
                        .HasColumnType("bit");

                    b.Property<bool>("StudThrdVlvBody")
                        .HasColumnType("bit");

                    b.Property<int>("T")
                        .HasColumnType("int");

                    b.Property<int>("ThrdSize1")
                        .HasColumnType("int");

                    b.Property<int>("ThreadDepth")
                        .HasColumnType("int");

                    b.Property<int>("ThreadSize")
                        .HasColumnType("int");

                    b.ToTable("DS195s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS196", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A")
                        .HasColumnType("int");

                    b.Property<int>("B")
                        .HasColumnType("int");

                    b.Property<int>("C")
                        .HasColumnType("int");

                    b.Property<int>("D")
                        .HasColumnType("int");

                    b.Property<int>("E")
                        .HasColumnType("int");

                    b.Property<int>("F")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G")
                        .HasColumnType("int");

                    b.Property<int>("H")
                        .HasColumnType("int");

                    b.Property<bool>("Handle")
                        .HasColumnType("bit");

                    b.Property<int>("J")
                        .HasColumnType("int");

                    b.Property<int>("K")
                        .HasColumnType("int");

                    b.Property<bool>("OneFlat")
                        .HasColumnType("bit");

                    b.Property<bool>("OvalFlats")
                        .HasColumnType("bit");

                    b.Property<bool>("Rising")
                        .HasColumnType("bit");

                    b.Property<bool>("RisingStem")
                        .HasColumnType("bit");

                    b.Property<bool>("Slotted")
                        .HasColumnType("bit");

                    b.Property<bool>("SqFlats")
                        .HasColumnType("bit");

                    b.Property<bool>("SqTaper")
                        .HasColumnType("bit");

                    b.Property<bool>("Tapered")
                        .HasColumnType("bit");

                    b.ToTable("DS196s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS197", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("D")
                        .HasColumnType("int");

                    b.Property<int>("E")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G1")
                        .HasColumnType("int");

                    b.Property<int>("G2")
                        .HasColumnType("int");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("K")
                        .HasColumnType("int");

                    b.Property<int>("L")
                        .HasColumnType("int");

                    b.Property<int>("Lead")
                        .HasColumnType("int");

                    b.Property<int>("M")
                        .HasColumnType("int");

                    b.Property<int>("N")
                        .HasColumnType("int");

                    b.Property<int>("NoOfStuds")
                        .HasColumnType("int");

                    b.Property<int>("NoOfThreadsPerInch")
                        .HasColumnType("int");

                    b.Property<string>("OtherValveStemData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("P")
                        .HasColumnType("int");

                    b.Property<int>("Q")
                        .HasColumnType("int");

                    b.Property<int>("R")
                        .HasColumnType("int");

                    b.Property<int>("S")
                        .HasColumnType("int");

                    b.Property<int>("SizeOfStuds")
                        .HasColumnType("int");

                    b.Property<bool>("Smooth")
                        .HasColumnType("bit");

                    b.Property<int>("ThreadOD")
                        .HasColumnType("int");

                    b.Property<bool>("Threaded")
                        .HasColumnType("bit");

                    b.ToTable("DS197s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS200", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("Aa")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("Bb")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("Cc")
                        .HasColumnType("int");

                    b.Property<bool>("CircularBonnet")
                        .HasColumnType("bit");

                    b.Property<bool>("CutStem")
                        .HasColumnType("bit");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("Dd")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("Ee")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<int>("Ff")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Gg")
                        .HasColumnType("int");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<int>("He")
                        .HasColumnType("int");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("KbE")
                        .HasColumnType("int");

                    b.Property<int>("KcE")
                        .HasColumnType("int");

                    b.Property<int>("LbF")
                        .HasColumnType("int");

                    b.Property<int>("LcF")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("M")
                        .HasColumnType("int");

                    b.Property<int>("N")
                        .HasColumnType("int");

                    b.Property<int>("Notes")
                        .HasColumnType("int");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<bool>("OvalBonnet")
                        .HasColumnType("bit");

                    b.Property<int>("P")
                        .HasColumnType("int");

                    b.Property<int>("Q")
                        .HasColumnType("int");

                    b.Property<int>("R")
                        .HasColumnType("int");

                    b.Property<bool>("RemoveHandle")
                        .HasColumnType("bit");

                    b.Property<bool>("SquareBonnet")
                        .HasColumnType("bit");

                    b.Property<int>("StemClockPt")
                        .HasColumnType("int");

                    b.Property<int>("T")
                        .HasColumnType("int");

                    b.Property<int>("U")
                        .HasColumnType("int");

                    b.Property<int>("V")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("X1At12")
                        .HasColumnType("int");

                    b.Property<int>("X1At3")
                        .HasColumnType("int");

                    b.Property<int>("X1At6")
                        .HasColumnType("int");

                    b.Property<int>("X1At9")
                        .HasColumnType("int");

                    b.Property<int>("X2At12")
                        .HasColumnType("int");

                    b.Property<int>("X2At3")
                        .HasColumnType("int");

                    b.Property<int>("X2At6")
                        .HasColumnType("int");

                    b.Property<int>("X2At9")
                        .HasColumnType("int");

                    b.Property<bool>("XX")
                        .HasColumnType("bit");

                    b.Property<bool>("XY")
                        .HasColumnType("bit");

                    b.Property<int>("Y")
                        .HasColumnType("int");

                    b.Property<bool>("YX")
                        .HasColumnType("bit");

                    b.Property<bool>("YY")
                        .HasColumnType("bit");

                    b.ToTable("DS200s");

                    b.HasData(
                        new
                        {
                            Id = 3,
                            PackageId = 2,
                            CheckedBy = "Joseph Smith",
                            CheckedByDate = new DateTime(2021, 5, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            GivenBy = "Billy Ben",
                            LineSize = "6",
                            LineSkinTemp = "6",
                            OtherSkinTemp = "41",
                            Plant = "XTO Energy",
                            SealType = "Tubing, Other",
                            SeverityOfLeak = 5,
                            ShipTo = "MSY",
                            SurfaceCondition = "Ok",
                            Unit = "",
                            A1 = 0,
                            A2 = 0,
                            Aa = 0,
                            B1 = 0,
                            B2 = 0,
                            Bb = 0,
                            C1 = 7,
                            C2 = 0,
                            Cc = 0,
                            CircularBonnet = true,
                            CutStem = false,
                            D1 = 7,
                            D2 = 0,
                            Dd = 0,
                            E1 = 2,
                            E2 = 0,
                            Ee = 0,
                            F1 = 1,
                            F2 = 0,
                            Ff = 0,
                            Gg = 0,
                            H1 = 3,
                            H2 = 0,
                            He = 0,
                            J1 = 3,
                            J2 = 0,
                            KbE = 4,
                            KcE = 0,
                            LbF = 1,
                            LcF = 1,
                            LocationOfBlow = "Exterior",
                            M = 1,
                            N = 1,
                            Notes = 0,
                            Obstructions = 0,
                            OvalBonnet = false,
                            P = 2,
                            Q = 4,
                            R = 0,
                            RemoveHandle = true,
                            SquareBonnet = false,
                            StemClockPt = 1,
                            T = 0,
                            U = 0,
                            V = 0,
                            W1 = 1,
                            W2 = 0,
                            X1At12 = 1,
                            X1At3 = 3,
                            X1At6 = 3,
                            X1At9 = 3,
                            X2At12 = 3,
                            X2At3 = 3,
                            X2At6 = 3,
                            X2At9 = 7,
                            XX = true,
                            XY = false,
                            Y = 0,
                            YX = false,
                            YY = true
                        });
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS201", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("Aa")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("Bb")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("Cc")
                        .HasColumnType("int");

                    b.Property<bool>("CircularBonnet")
                        .HasColumnType("bit");

                    b.Property<bool>("CutStem")
                        .HasColumnType("bit");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<int>("He")
                        .HasColumnType("int");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("KbE")
                        .HasColumnType("int");

                    b.Property<int>("KcE")
                        .HasColumnType("int");

                    b.Property<int>("LbF")
                        .HasColumnType("int");

                    b.Property<int>("LcF")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("M")
                        .HasColumnType("int");

                    b.Property<int>("N")
                        .HasColumnType("int");

                    b.Property<int>("Notes")
                        .HasColumnType("int");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<bool>("OvalBonnet")
                        .HasColumnType("bit");

                    b.Property<int>("P")
                        .HasColumnType("int");

                    b.Property<int>("Q")
                        .HasColumnType("int");

                    b.Property<int>("R")
                        .HasColumnType("int");

                    b.Property<bool>("RemoveHandle")
                        .HasColumnType("bit");

                    b.Property<bool>("SquareBonnet")
                        .HasColumnType("bit");

                    b.Property<int>("StemClockPt")
                        .HasColumnType("int");

                    b.Property<int>("T")
                        .HasColumnType("int");

                    b.Property<int>("U")
                        .HasColumnType("int");

                    b.Property<int>("V")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("X")
                        .HasColumnType("int");

                    b.Property<bool>("XX")
                        .HasColumnType("bit");

                    b.Property<bool>("XY")
                        .HasColumnType("bit");

                    b.Property<int>("Xa12")
                        .HasColumnType("int");

                    b.Property<int>("Xa3")
                        .HasColumnType("int");

                    b.Property<int>("Xa6")
                        .HasColumnType("int");

                    b.Property<int>("Xa9")
                        .HasColumnType("int");

                    b.Property<int>("Xd12")
                        .HasColumnType("int");

                    b.Property<int>("Xd3")
                        .HasColumnType("int");

                    b.Property<int>("Xd6")
                        .HasColumnType("int");

                    b.Property<int>("Xd9")
                        .HasColumnType("int");

                    b.Property<int>("Y")
                        .HasColumnType("int");

                    b.Property<bool>("YX")
                        .HasColumnType("bit");

                    b.Property<bool>("YY")
                        .HasColumnType("bit");

                    b.Property<int>("Z")
                        .HasColumnType("int");

                    b.ToTable("DS201s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS205", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("Aa")
                        .HasColumnType("int");

                    b.Property<bool>("AgainstTheLine")
                        .HasColumnType("bit");

                    b.Property<bool>("AlongTheLine")
                        .HasColumnType("bit");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("Bb")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("Cc")
                        .HasColumnType("int");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("L")
                        .HasColumnType("int");

                    b.Property<int>("M")
                        .HasColumnType("int");

                    b.Property<int>("N")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("P")
                        .HasColumnType("int");

                    b.Property<int>("Q")
                        .HasColumnType("int");

                    b.Property<int>("R")
                        .HasColumnType("int");

                    b.Property<int>("S")
                        .HasColumnType("int");

                    b.Property<int>("T")
                        .HasColumnType("int");

                    b.Property<int>("U")
                        .HasColumnType("int");

                    b.Property<int>("V")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("X")
                        .HasColumnType("int");

                    b.Property<int>("X112")
                        .HasColumnType("int");

                    b.Property<int>("X13")
                        .HasColumnType("int");

                    b.Property<int>("X16")
                        .HasColumnType("int");

                    b.Property<int>("X19")
                        .HasColumnType("int");

                    b.Property<int>("X212")
                        .HasColumnType("int");

                    b.Property<int>("X23")
                        .HasColumnType("int");

                    b.Property<int>("X26")
                        .HasColumnType("int");

                    b.Property<int>("X29")
                        .HasColumnType("int");

                    b.Property<int>("Y")
                        .HasColumnType("int");

                    b.Property<int>("Z")
                        .HasColumnType("int");

                    b.ToTable("DS205s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS206", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("Aa")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("Bb")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<int>("Cc")
                        .HasColumnType("int");

                    b.Property<bool>("CircularBonnet")
                        .HasColumnType("bit");

                    b.Property<bool>("CutStem")
                        .HasColumnType("bit");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Ha")
                        .HasColumnType("int");

                    b.Property<bool>("Hex12at6")
                        .HasColumnType("bit");

                    b.Property<bool>("Hex3at9")
                        .HasColumnType("bit");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("K1A")
                        .HasColumnType("int");

                    b.Property<int>("K2A")
                        .HasColumnType("int");

                    b.Property<int>("L1B")
                        .HasColumnType("int");

                    b.Property<int>("L2B")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Ma")
                        .HasColumnType("int");

                    b.Property<int>("Mb")
                        .HasColumnType("int");

                    b.Property<int>("N")
                        .HasColumnType("int");

                    b.Property<int>("Notes")
                        .HasColumnType("int");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<string>("OtherHexOrientation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("OvalBonnet")
                        .HasColumnType("bit");

                    b.Property<int>("P")
                        .HasColumnType("int");

                    b.Property<bool>("RemoveHandle")
                        .HasColumnType("bit");

                    b.Property<bool>("Screwed")
                        .HasColumnType("bit");

                    b.Property<bool>("SquareBonnet")
                        .HasColumnType("bit");

                    b.Property<int>("StemClockPt")
                        .HasColumnType("int");

                    b.Property<int>("T")
                        .HasColumnType("int");

                    b.Property<int>("U")
                        .HasColumnType("int");

                    b.Property<int>("V")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<bool>("Welded")
                        .HasColumnType("bit");

                    b.Property<int>("X")
                        .HasColumnType("int");

                    b.Property<bool>("XX")
                        .HasColumnType("bit");

                    b.Property<bool>("XY")
                        .HasColumnType("bit");

                    b.Property<int>("Y")
                        .HasColumnType("int");

                    b.Property<bool>("YX")
                        .HasColumnType("bit");

                    b.Property<bool>("YY")
                        .HasColumnType("bit");

                    b.ToTable("DS206s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS209", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("Aa")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("Bb")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<bool>("CutStem")
                        .HasColumnType("bit");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("E3")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<int>("F3")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G1")
                        .HasColumnType("int");

                    b.Property<int>("G2")
                        .HasColumnType("int");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<bool>("Hex12at6")
                        .HasColumnType("bit");

                    b.Property<bool>("Hex3at9")
                        .HasColumnType("bit");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("K1")
                        .HasColumnType("int");

                    b.Property<int>("K2")
                        .HasColumnType("int");

                    b.Property<int>("L1")
                        .HasColumnType("int");

                    b.Property<int>("L2")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("M")
                        .HasColumnType("int");

                    b.Property<int>("N")
                        .HasColumnType("int");

                    b.Property<int>("Notes")
                        .HasColumnType("int");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<string>("OtherHexOrientation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("P")
                        .HasColumnType("int");

                    b.Property<int>("Q")
                        .HasColumnType("int");

                    b.Property<int>("R")
                        .HasColumnType("int");

                    b.Property<bool>("RemoveHandle")
                        .HasColumnType("bit");

                    b.Property<int>("S")
                        .HasColumnType("int");

                    b.Property<bool>("Screwed")
                        .HasColumnType("bit");

                    b.Property<int>("StemClockPt")
                        .HasColumnType("int");

                    b.Property<int>("T")
                        .HasColumnType("int");

                    b.Property<int>("U")
                        .HasColumnType("int");

                    b.Property<int>("V")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<bool>("Welded")
                        .HasColumnType("bit");

                    b.Property<int>("X")
                        .HasColumnType("int");

                    b.Property<int>("Y")
                        .HasColumnType("int");

                    b.Property<int>("Z")
                        .HasColumnType("int");

                    b.ToTable("DS209s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS210", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<bool>("CutStem")
                        .HasColumnType("bit");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G1")
                        .HasColumnType("int");

                    b.Property<int>("G2")
                        .HasColumnType("int");

                    b.Property<int>("H1")
                        .HasColumnType("int");

                    b.Property<int>("H2")
                        .HasColumnType("int");

                    b.Property<bool>("Hex12at6")
                        .HasColumnType("bit");

                    b.Property<bool>("Hex3at9")
                        .HasColumnType("bit");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("K1")
                        .HasColumnType("int");

                    b.Property<int>("K2")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("M")
                        .HasColumnType("int");

                    b.Property<int>("N")
                        .HasColumnType("int");

                    b.Property<int>("Notes")
                        .HasColumnType("int");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<string>("OtherHexOrientation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("P")
                        .HasColumnType("int");

                    b.Property<int>("R")
                        .HasColumnType("int");

                    b.Property<bool>("RemoveHandle")
                        .HasColumnType("bit");

                    b.Property<bool>("Screwed")
                        .HasColumnType("bit");

                    b.Property<int>("StemClockPt")
                        .HasColumnType("int");

                    b.Property<int>("T")
                        .HasColumnType("int");

                    b.Property<int>("U")
                        .HasColumnType("int");

                    b.Property<int>("V")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("W3")
                        .HasColumnType("int");

                    b.Property<int>("W4")
                        .HasColumnType("int");

                    b.Property<bool>("Welded")
                        .HasColumnType("bit");

                    b.Property<int>("Y")
                        .HasColumnType("int");

                    b.Property<int>("Y1")
                        .HasColumnType("int");

                    b.Property<int>("Z")
                        .HasColumnType("int");

                    b.Property<int>("Х")
                        .HasColumnType("int");

                    b.ToTable("DS210s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS230", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A1")
                        .HasColumnType("int");

                    b.Property<int>("A2")
                        .HasColumnType("int");

                    b.Property<int>("Aa")
                        .HasColumnType("int");

                    b.Property<int>("B1")
                        .HasColumnType("int");

                    b.Property<int>("B2")
                        .HasColumnType("int");

                    b.Property<int>("C1")
                        .HasColumnType("int");

                    b.Property<int>("C2")
                        .HasColumnType("int");

                    b.Property<bool>("CircularBonnet")
                        .HasColumnType("bit");

                    b.Property<bool>("CutStem")
                        .HasColumnType("bit");

                    b.Property<int>("D1")
                        .HasColumnType("int");

                    b.Property<int>("D2")
                        .HasColumnType("int");

                    b.Property<int>("E1")
                        .HasColumnType("int");

                    b.Property<int>("E2")
                        .HasColumnType("int");

                    b.Property<int>("F1")
                        .HasColumnType("int");

                    b.Property<int>("F2")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Ha")
                        .HasColumnType("int");

                    b.Property<bool>("Hex12at6")
                        .HasColumnType("bit");

                    b.Property<bool>("Hex3at9")
                        .HasColumnType("bit");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("Ja")
                        .HasColumnType("int");

                    b.Property<int>("K1A")
                        .HasColumnType("int");

                    b.Property<int>("K2A")
                        .HasColumnType("int");

                    b.Property<int>("L1A")
                        .HasColumnType("int");

                    b.Property<int>("L2A")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Ma")
                        .HasColumnType("int");

                    b.Property<int>("Mb")
                        .HasColumnType("int");

                    b.Property<int>("N")
                        .HasColumnType("int");

                    b.Property<int>("Notes")
                        .HasColumnType("int");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<string>("OtherHexOrientation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("P")
                        .HasColumnType("int");

                    b.Property<int>("Q")
                        .HasColumnType("int");

                    b.Property<int>("R")
                        .HasColumnType("int");

                    b.Property<bool>("RemoveHandle")
                        .HasColumnType("bit");

                    b.Property<int>("S")
                        .HasColumnType("int");

                    b.Property<bool>("Screwed")
                        .HasColumnType("bit");

                    b.Property<bool>("SquareBonnet")
                        .HasColumnType("bit");

                    b.Property<int>("StemClockPt")
                        .HasColumnType("int");

                    b.Property<int>("T")
                        .HasColumnType("int");

                    b.Property<int>("T1")
                        .HasColumnType("int");

                    b.Property<int>("U")
                        .HasColumnType("int");

                    b.Property<int>("V1")
                        .HasColumnType("int");

                    b.Property<int>("V2")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<bool>("Welded")
                        .HasColumnType("bit");

                    b.Property<int>("X1")
                        .HasColumnType("int");

                    b.Property<int>("X2")
                        .HasColumnType("int");

                    b.Property<bool>("XX")
                        .HasColumnType("bit");

                    b.Property<bool>("XY")
                        .HasColumnType("bit");

                    b.Property<int>("Y2")
                        .HasColumnType("int");

                    b.Property<bool>("YX")
                        .HasColumnType("bit");

                    b.Property<bool>("YY")
                        .HasColumnType("bit");

                    b.Property<int>("Yk")
                        .HasColumnType("int");

                    b.Property<int>("Z")
                        .HasColumnType("int");

                    b.ToTable("DS230s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS300", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A3")
                        .HasColumnType("int");

                    b.Property<int>("B3")
                        .HasColumnType("int");

                    b.Property<int>("C3")
                        .HasColumnType("int");

                    b.Property<int>("D3")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("J1")
                        .HasColumnType("int");

                    b.Property<int>("J2")
                        .HasColumnType("int");

                    b.Property<int>("K1")
                        .HasColumnType("int");

                    b.Property<int>("K2")
                        .HasColumnType("int");

                    b.Property<int>("L1")
                        .HasColumnType("int");

                    b.Property<int>("L2")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfBlow")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("M")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Obstructions")
                        .HasColumnType("int");

                    b.Property<int>("W1")
                        .HasColumnType("int");

                    b.Property<int>("W2")
                        .HasColumnType("int");

                    b.Property<int>("W3")
                        .HasColumnType("int");

                    b.Property<int>("W4")
                        .HasColumnType("int");

                    b.ToTable("DS300s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS901", b =>
                {
                    b.HasBaseType("ClientPortal.Shared.Models.MOS.DimensionalSheet");

                    b.Property<int>("A")
                        .HasColumnType("int");

                    b.Property<int>("B")
                        .HasColumnType("int");

                    b.Property<int>("C")
                        .HasColumnType("int");

                    b.Property<int>("D")
                        .HasColumnType("int");

                    b.Property<int>("E")
                        .HasColumnType("int");

                    b.Property<int>("F")
                        .HasColumnType("int");

                    b.Property<int>("FlatToFlat")
                        .HasColumnType("int");

                    b.Property<string>("Flg")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("G")
                        .HasColumnType("int");

                    b.Property<int>("H")
                        .HasColumnType("int");

                    b.Property<int>("J")
                        .HasColumnType("int");

                    b.Property<int>("K")
                        .HasColumnType("int");

                    b.Property<int>("L")
                        .HasColumnType("int");

                    b.Property<string>("LocationOfLeakingTubeColumnNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LocationOfLeakingTubeRowNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("M")
                        .HasColumnType("int");

                    b.Property<int>("N")
                        .HasColumnType("int");

                    b.Property<int>("P")
                        .HasColumnType("int");

                    b.Property<int>("PointToPoint")
                        .HasColumnType("int");

                    b.Property<int>("Q")
                        .HasColumnType("int");

                    b.Property<int>("R")
                        .HasColumnType("int");

                    b.Property<int>("S")
                        .HasColumnType("int");

                    b.Property<int>("T")
                        .HasColumnType("int");

                    b.Property<int>("U")
                        .HasColumnType("int");

                    b.Property<int>("V")
                        .HasColumnType("int");

                    b.ToTable("DS901s");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DataSheet", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DataSheetPackage", "Package")
                        .WithMany("DataSheets")
                        .HasForeignKey("PackageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Package");
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DimensionalSheet", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DataSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DimensionalSheet", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.HtsEngineeringDataCoverSheet", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DataSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.HtsEngineeringDataCoverSheet", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.LeakRepairHardwareSpecificationSheet", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DataSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.LeakRepairHardwareSpecificationSheet", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS100", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS100", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS101", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS101", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS102", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS102", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS103", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS103", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS104", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS104", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS105", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS105", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS110", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS110", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS111", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS111", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS113", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS113", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS120", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS120", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS124", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS124", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS128", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS128", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS129", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS129", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS133", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS133", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS134", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS134", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS136", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS136", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS137", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS137", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS138", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS138", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS139", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS139", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS140", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS140", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS141", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS141", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS150", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS150", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS151", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS151", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS194", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS194", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS195", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS195", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS196", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS196", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS197", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS197", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS200", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS200", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS201", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS201", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS205", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS205", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS206", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS206", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS209", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS209", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS210", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS210", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS230", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS230", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS300", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS300", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DS901", b =>
                {
                    b.HasOne("ClientPortal.Shared.Models.MOS.DimensionalSheet", null)
                        .WithOne()
                        .HasForeignKey("ClientPortal.Shared.Models.MOS.DS901", "Id")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ClientPortal.Shared.Models.MOS.DataSheetPackage", b =>
                {
                    b.Navigation("DataSheets");
                });
#pragma warning restore 612, 618
        }
    }
}
