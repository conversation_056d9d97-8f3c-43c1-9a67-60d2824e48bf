import { Pipe, PipeTransform } from '@angular/core';
import {
    APMStatus,
    APMTaskType,
    AssetCategory,
    inProgressStatus,
    WorkOrder
} from '../models';

@Pipe({
    name: 'taskTypesForAssetCategory'
})
export class TaskTypesForAssetCategoryPipe implements PipeTransform {
    /**
     * Returns the task types that are available for a particular asset category (usually when attempting to add a new task to a work order).
     * If the work order is included in the params, the available task types will be limited to what is still available while avoiding duplicate
     * task types in a single work order.
     * @param category Asset Category
     * @param workOrder Optional.  If provided, will limit task types to what is available considering other existing tasks in the work order
     * @returns available task types
     */
    transform(category: AssetCategory, workOrder?: WorkOrder): string[] {
        let taskTypes: APMTaskType[] = [];
        if (category) {
            switch (category) {
                case 'Piping':
                    taskTypes = ['Asset Walkdown', 'External Visual'];
                    break;
                case 'Tank':
                    taskTypes = [
                        'Asset Walkdown',
                        'Internal Visual',
                        'External Visual'
                    ];
                    break;
                case 'Vessel':
                    taskTypes = [
                        'Asset Walkdown',
                        'Internal Visual',
                        'External Visual',
                        'Full'
                    ];
                    break;
            }

            if (workOrder) {
                const taskTypesAlreadyInUse = Array.from(
                    new Set(
                        workOrder.tasks
                            .filter((t) =>
                                inProgressStatus(
                                    t.status.currentValue as APMStatus
                                )
                            )
                            .map((t) => t.taskType)
                    )
                );

                // Don't allow External Visual (or Internal) if a full is in progress
                if (taskTypesAlreadyInUse.includes('Full')) {
                    taskTypes = taskTypes.filter(
                        (t) =>
                            t !== 'External Visual' && t !== 'Internal Visual'
                    );
                }

                // Don't allow Full if External Visual (or Internal) is in progress
                if (
                    taskTypesAlreadyInUse.includes('External Visual') ||
                    taskTypesAlreadyInUse.includes('Internal Visual')
                ) {
                    taskTypes = taskTypes.filter((t) => t !== 'Full');
                }

                taskTypes = taskTypes.filter(
                    (t) => !taskTypesAlreadyInUse.includes(t)
                );
            }
        } else {
            return [];
        }

        return taskTypes;
    }
}
