﻿using System;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderTracking.API.Interfaces;
using OrderTracking.API.Models;

namespace OrderTracking.API.Services
{
    ///// <summary>
    /////     A blob storage service specific to EDR
    ///// </summary>
    //public class EDRBlobStorageService : BlobStorageService, IEDRBlobStorageService
    //{
    //    /// <summary>
    //    ///     Constructs the EDR specific blob storage service
    //    /// </summary>
    //    /// <param name="logger"></param>
    //    /// <param name="options"></param>
    //    // ReSharper disable once SuggestBaseTypeForParameter
    //    public EDRBlobStorageService(ILogger<EDRBlobStorageService> logger, IOptions<BlobStorage> options) : base(options, logger)
    //    {
    //        if (options == null) throw new ArgumentNullException(nameof(options));

    //        ContainerClient = ServiceClient.GetBlobContainerClient(options.Value.EDRContainer) ??
    //                          ServiceClient.CreateBlobContainer(options.Value.EDRContainer);

    //        logger.LogInformation("Created container client for EDR");
    //    }
    //}
}