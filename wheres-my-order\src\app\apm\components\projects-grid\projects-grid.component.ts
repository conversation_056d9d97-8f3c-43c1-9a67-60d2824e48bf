import {
    Component,
    EventEmitter,
    Input,
    Output,
    ViewChild
} from '@angular/core';
import { Router } from '@angular/router';
import { DxDataGridComponent, DxFormComponent } from 'devextreme-angular';
import CustomStore from 'devextreme/data/custom_store';
import { ClickEvent } from 'devextreme/ui/button';
import { ToastrService } from 'ngx-toastr';
import { combineLatest, lastValueFrom } from 'rxjs';
import { map } from 'rxjs/operators';
import { Project } from '../../../pipeline-inspection/models';
import { isNotEmpty } from '../../../shared/helpers';
import { DataGridService } from '../../../shared/services';
import { ProjectLocationTabItem } from '../../models/view-models/project-location-tab-item';
import { ApmService } from '../../services';

type NewProjectFields = {
    teamProjectNumber: string;
    client: string;
    locationId: string;
    plannedStart: Date;
    plannedEnd: Date;
    name: string;
};

@Component({
    selector: 'app-projects-grid',
    templateUrl: './projects-grid.component.html',
    styleUrls: ['./projects-grid.component.scss']
})
export class ProjectsGridComponent {
    projectsDataSource: CustomStore;
    locations$ = this._apm.locations$;

    @ViewChild(DxDataGridComponent) dataGrid: DxDataGridComponent;
    @Input() allowEditing: boolean;
    @ViewChild('createLocationForm') createLocationForm: DxFormComponent;
    @Output() selectionChanged = new EventEmitter<any>();
    newLocation = new ProjectLocationTabItem();
    createLocationPopupVisible = false;
    locationSelectOptions$ = combineLatest([
        this.locations$,
        this._apm.selectedBU$
    ]).pipe(
        map(([locations, bu]) => ({
            dataSource: locations,
            displayExpr: 'name.currentValue',
            valueExpr: 'id',
            buttons: [
                {
                    name: 'newLocation',
                    location: 'after',
                    options: {
                        icon: 'add',
                        stylingMode: 'text',
                        disabled: bu === null || bu === undefined,
                        onClick: (e) => {
                            this.newLocation = new ProjectLocationTabItem();
                            this.createLocationPopupVisible = true;
                        }
                    }
                }
            ],
            searchEnabled: 'true',
            searchMode: 'contains',
            searchExpr: 'name.currentValue'
        }))
    );
    addRowButtonOptions$ = this._apm.selectedBU$.pipe(
        map((bu) => bu === null || bu === undefined),
        map((disabled) => ({
            icon: null,
            text: 'Create',
            type: 'success',
            stylingMode: 'contained',
            disabled
        }))
    );
    selectedBU: string = "";

    constructor(
        private readonly _apm: ApmService,
        private readonly _grid: DataGridService,
        private readonly _router: Router,
        private readonly _toasts: ToastrService
    ) {
        this.projectsDataSource = new CustomStore({
            key: 'id',
            byKey: (key: string) => lastValueFrom(this._apm.getProject(key)),
            load: (loadOptions) =>
                lastValueFrom(this._apm.loadProjects(loadOptions)),
            insert: async (row: NewProjectFields) => {
                var response = await lastValueFrom(
                    this._apm.postProject({
                        locationId: row.locationId,
                        plannedEnd: row.plannedEnd,
                        plannedStart: row.plannedStart,
                        projectName: row.name,
                        teamProjectNumber: row.teamProjectNumber
                    })
                );
                return new Promise((resolve) =>
                    setTimeout(() => {
                        // this is here because without it a project created with a newly
                        // created location will not have its location in the grid, I blame caching.
                        this.dataGrid.instance.refresh();

                        return resolve(response);
                    }, 300)
                );
            }
        });
        this._apm.selectedBU$.subscribe((selected: string) => {
            if (!this.selectedBU) {
                this.selectedBU = selected;
                return;
            }
            if (selected != this.selectedBU) {
                this.selectedBU = selected
                this.refreshGrid()
            }
        });
    }

    async onRestoreGridDefaults(e: ClickEvent) {
        const result = await this._grid.resetGridState(this.dataGrid);
        if (result) localStorage.removeItem('apmProjectGridState');
    }

    onSelectionChanged(e) {
        this.selectionChanged.next(e);
    }

    refreshGrid() {
        // we need this timeout because of the caching that the nuget package is doing.
        // without the timeout we will see the cached value (previous value) in the grid
        setTimeout(() => this.dataGrid?.instance?.refresh(), 300);
    }

    onAPMNumberClicked(project: Project) {
        this._router.navigateByUrl('apm/work-orders', {
            state: { projectId: project.id }
        });
    }

    restoreDefaultsClicked = async (e: ClickEvent) => {
        const result = await this._grid.resetGridState(this.dataGrid);
        if (result) {
            localStorage.removeItem('apmProjectGridState');
        }
    };

    onRowInserted(e) {
        this._toasts.success('Project successfully created', 'Success!');
        this._apm.projectDataSource.reload();
    }

    cancelEditClicked() {
        this.createLocationPopupVisible = false;
    }

    async saveEditLocationClicked() {
        const result = this.createLocationForm.instance.validate();
        if (!result.isValid) return;
        const nonEmptyProperties = Object.getOwnPropertyNames(
            this.newLocation
        ).filter((prop) => isNotEmpty(this.newLocation[prop]));

        const cityPropertyIndex = nonEmptyProperties.indexOf('city');

        const namePropertyIndex = nonEmptyProperties.indexOf('name');

        if (cityPropertyIndex >= 0 && namePropertyIndex >= 0) {
            this.createLocationPopupVisible = false;
            this._apm.createLocation(this.newLocation).subscribe();
        }
    }
}
