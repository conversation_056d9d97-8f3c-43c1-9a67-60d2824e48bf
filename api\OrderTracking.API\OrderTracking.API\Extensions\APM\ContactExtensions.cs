﻿using APMWebDataInterface.ExampleDataModel;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Extensions.APM
{
    /// <summary>
    ///     Extension methods for updating contacts on APM projects
    /// </summary>
    public static class ContactExtensions
    {
        /// <summary>
        ///     Updates a APM project contact from a <see cref="ClientContact" />
        /// </summary>
        /// <param name="contact"></param>
        /// <param name="clientContact"></param>
        public static void Update(this Contact contact, ClientContact clientContact)
        {
            contact.email.SetValue(clientContact.ContactEmail);
            contact.name.SetValue(clientContact.ContactName);
            contact.phoneNumber.SetValue(clientContact.ContactPhoneNumber);
            contact.title.SetValue(clientContact.ContactTitle);
        }
    }
}