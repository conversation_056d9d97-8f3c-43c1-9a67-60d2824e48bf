﻿using CommonDataInterface.Attributes;
using OrderTracking.API.Exceptions.APM;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Extensions.APM
{
    public static class LocationAttributeExtensions
    {
        public static bool IsValid(this LocationAttribute location)
        {
            var (latitude, longitude) = location.GetLatAndLong();
            // If latitude or longitude is null but not both, fail validation
            if (latitude == null ^ longitude == null) return false;
            if (latitude is < -90 or > 90) return false;
            if (longitude is < -180 or > 180) return false;
            return true;
        }

        public static void SetLocation(this LocationAttribute location, double? latitude, double? longitude)
        {
            location.SetValue(latitude, longitude);
            if (location.IsValid() == false)
                throw new InvalidLocationException(location);
        }

        public static void SetLocation(this LocationAttribute location, ValueChangedTransport<double?> latitude, ValueChangedTransport<double?> longitude)
        {
            var (existingLatitude, existingLongitude) = GetLatAndLong(location);
            if (latitude != null && longitude == null)
                location.SetValue(latitude.Value, existingLongitude);
            if (latitude == null && longitude != null)
                location.SetValue(existingLatitude, longitude.Value);
            if (latitude != null && longitude != null)
                location.SetValue(latitude.Value, longitude.Value);

            // TODO:
            // The implementation of AreCurrentCoordinatesValid in the nuget package
            // allows for latitude or longitude to be null while the other one isn't.
            // We don't want this.  Can fix in nuget package later, but fixing here
            // for now because easier for me.
            //if (location.AreCurrentCoordinatesValid() == false)
            if (location.IsValid() == false)
                throw new InvalidLocationException(location);
        }
        
        private static (double?, double?) GetLatAndLong(this LocationAttribute location) =>
            (location.GetLatValue(), location.GetLongValue());
    }
}
