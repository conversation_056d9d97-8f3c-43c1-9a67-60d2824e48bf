export interface AssetPPEViewModel {
    id: string;
    hardHatRequired: boolean;
    eyeProtection: string[];
    earProtection: string[];
    fireRetardantClothing: string[];
    safetyGloves: string[];
    snakeChapsRequired: boolean;
    footProtection: string[];
    chemicalSuit: string[];
    fallProtection: string[];
    breathingProtection: string[];
    atmosphere: string[];
    generalWork: boolean;
    generalHotWork: boolean;
    openFlameHotWork: boolean;
    permitRequired: boolean;
    holeWatchNeeded: boolean;
    controlAreaPermit: boolean;
    hazardousAreaPermit: boolean;
    personnelAccessConditions: string[];
    personnelAccessConditionNotes: string;
    vehicleAccessibility: string;
    standingWater: boolean;
    drainageNeeded: boolean;
    overgrownVegetation: boolean;
    abatementRequired: boolean;
    powerAvailable: boolean;
    waterAvailable: boolean;
    vehicleAccessibilityComments: string;
    onSiteLeaks: boolean;
    onSiteLeaksComments: string;

    eyeProtections: string[];
    hearingProtections: string[];
    fireRetardantClothings: string[];
    safetyGloveOptions: string[];
    footProtections: string[];
    chemicalSuits: string[];
    fallProtections: string[];
    breathingProtections: string[];
    atmospheres: string[];
    vehicleAccessibilities: string[];
    onSiteConditions: string[];
}
