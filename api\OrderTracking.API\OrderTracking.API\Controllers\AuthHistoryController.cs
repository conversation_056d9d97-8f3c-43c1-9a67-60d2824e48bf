﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderTracking.API.Services;

namespace OrderTracking.API.Controllers
{
    /// <summary>
    ///     Controller providing authorized access to the history of changes occurring in the auth section of the app
    /// </summary>
    [Authorize(Policy = "App:Admin")]
    [Route("api/[controller]")]
    [ApiController]
    public class AuthHistoryController : ControllerBase
    {
        private readonly IAuthHistoryService _authHistory;

        /// <summary>
        ///     Constructs the controller, injecting an auth history service
        /// </summary>
        /// <param name="authHistory"></param>
        public AuthHistoryController(IAuthHistoryService authHistory)
        {
            _authHistory = authHistory;
        }

        /// <summary>
        ///     Get auth history objects
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IEnumerable<ChangeEvent>> Get() => await _authHistory.GetItemsAsync();

        /// <summary>
        ///     Get a specific auth history object
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<ChangeEvent>> Get(string id)
        {
            var authHistory = await _authHistory.GetItemAsync(id);
            return authHistory ?? NotFound();
        }

        /// <summary>
        ///     Delete change events (only available to QA members of the application)
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [Authorize("App:QA")]
        [HttpDelete]
        public async Task<IActionResult> DeleteChangeEvents(string[] ids)
        {
            if (ids == null) throw new ArgumentNullException(nameof(ids));

            foreach (var role in ids)
            {
                await _authHistory.RemoveAsync(role);
            }

            return NoContent();
        }
    }
}