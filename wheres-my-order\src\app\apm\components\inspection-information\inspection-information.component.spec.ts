import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxLoadIndicatorModule, DxTemplateHost } from 'devextreme-angular';
import { DxButtonModule } from 'devextreme-angular/ui/button';
import { DxTextAreaModule } from 'devextreme-angular/ui/text-area';
import { InspectionInformationComponent } from './inspection-information.component';

describe('InspectionInformationComponent', () => {
    let component: InspectionInformationComponent;
    let fixture: ComponentFixture<InspectionInformationComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DxTextAreaModule, DxButtonModule, DxLoadIndicatorModule],
            declarations: [InspectionInformationComponent],
            providers: [
                {
                    provide: DxTemplateHost,
                    useValue: { setTemplate: () => {} }
                }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(InspectionInformationComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
