<div class="dx-card content-block responsive-paddings"
     style="width: 25%;"
     *ngIf="showSelector">
    <dx-select-box [dataSource]="businessUnits$ | async"
                   displayExpr="name.currentValue"
                   keyExpr="id"
                   valueExpr="id"
                   label="Business Unit"
                   labelMode="floating"
                   [searchEnabled]="true"
                   searchMode="contains"
                   searchExpr="name.currentValue"
                   [value]="(currentUser$ | async).selectedBusinessUnit"
                   (onSelectionChanged)="selectBusinessUnit($event)">
    </dx-select-box>
</div>
