import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
    DxFormModule,
    DxGalleryModule,
    DxLoadIndicatorModule,
    DxPopupModule
} from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { PhotoSasPipe } from '../../pipes';
import { ApmService } from '../../services';
import { ApmAssetDetailsFiveTenComponent } from './apm-asset-details-five-ten.component';

describe('ApmAssetDetailsFiveTenComponent', () => {
    let component: ApmAssetDetailsFiveTenComponent;
    let fixture: ComponentFixture<ApmAssetDetailsFiveTenComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxFormModule,
                DxPopupModule,
                DxGalleryModule,
                DxLoadIndicatorModule,
                HttpClientTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [ApmAssetDetailsFiveTenComponent],
            providers: [{ provide: ApmService, useValue: {} }, PhotoSasPipe]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ApmAssetDetailsFiveTenComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
