﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;
//using ClientPortal.Shared.Models;
//using DevExtreme.AspNet.Data;
//using DevExtreme.AspNet.Data.ResponseModel;
//using Newtonsoft.Json;
//using Newtonsoft.Json.Linq;
//using Newtonsoft.Json.Serialization;
//using OrderTracking.API.Interfaces;
//using OrderTracking.API.Models;

//namespace OrderTracking.API.Services
//{
//    /// <summary>
//    ///     Transforms the devextreme query parameters (filters/searches/etc.)
//    ///     that come from using their data layer in conjunction with our snake-case
//    ///     typescript models to their C# versions.
//    /// </summary>
//    public class ClientPortalResultsLoader : IClientPortalResultsLoader
//    {
//        private readonly JsonObjectContract _contract =
//            JsonSerializer.CreateDefault().ContractResolver.ResolveContract(typeof(Order)) as JsonObjectContract;

//        /// <summary>
//        ///     The field names being sorted and filtered on are of the friendly names we have in
//        ///     the client portal front-end model.  This is to try to update the fields to their
//        ///     C# counterpart so that the SQL query can succeed.
//        /// </summary>
//        /// <param name="loadOptions"></param>
//        /// <param name="ordersQueryable"></param>
//        /// <returns></returns>
//        public async Task<LoadResult> LoadResult<T>(DataSourceLoadOptions loadOptions, IQueryable<T> ordersQueryable)
//        {
//            if (loadOptions == null) throw new ArgumentNullException(nameof(loadOptions));
//            if (ordersQueryable == null) throw new ArgumentNullException(nameof(ordersQueryable));

//            UpdateFieldNamesForSort(loadOptions);
//            UpdateFieldNamesForFilter(loadOptions);

//            return await DataSourceLoader.LoadAsync(ordersQueryable, loadOptions);
//        }

//        private void UpdateFieldNamesForFilter(DataSourceLoadOptionsBase loadOptions)
//        {
//            if (!(loadOptions.Filter is List<object> expressions)) return;

//            // Sometimes, if the filter is simple, it is just a list of strings.  Handle that first
//            // as the rest of this is only worried about looking inside of JArrays.  If the first two
//            // are strings (field name and operator), that is enough to tell we are in this situation.
//            // The third item may be null, or a string.
//            if (expressions.Any() && expressions.Take(2).All(expression => expression is string))
//                expressions[0] = GetUnderlyingName(expressions[0].ToString());
//            else
//                UpdateFilterExpressions(expressions.OfType<JArray>());
//        }

//        private void UpdateFilterExpressions(IEnumerable<JArray> expressions)
//        {
//            foreach (var expression in expressions)
//                if (expression.Any(element => element is JArray))
//                    UpdateFilterExpressions(expression.OfType<JArray>());
//                else
//                    expression[0].Replace(JToken.FromObject(GetUnderlyingName(expression[0].ToString())));
//        }

//        private void UpdateFieldNamesForSort(DataSourceLoadOptionsBase loadOptions)
//        {
//            if (loadOptions.Sort == null) return;

//            foreach (var sortInfo in loadOptions.Sort)
//            {
//                var property =
//                    _contract.Properties.GetProperty(sortInfo.Selector, StringComparison.Ordinal);

//                if (property == null)
//                    throw new InvalidOperationException(
//                        $"{sortInfo.Selector} is an unknown field, but is being sorted on.");

//                sortInfo.Selector = property.UnderlyingName;
//            }
//        }

//        private string GetUnderlyingName(string jsonPropertyName)
//        {
//            var property = _contract.Properties.GetProperty(jsonPropertyName, StringComparison.Ordinal);

//            if (property == null)
//                throw new InvalidOperationException(
//                    $"{jsonPropertyName} is an unknown field, but is being filtered on.");

//            return property.UnderlyingName ??
//                   throw new InvalidOperationException($"UnderlyingName for field {jsonPropertyName} is unknown.");
//        }
//    }
//}