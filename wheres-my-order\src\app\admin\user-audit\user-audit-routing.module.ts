import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';

import { AppAdminGuard } from '../../core/guards';
import { UserAuditComponent } from './user-audit.component';

const routes: Routes = [
    {
        path: '',
        component: UserAuditComponent,
        data: { pageTitle: 'User Audit' },
        canActivate: [MsalGuard, AppAdminGuard],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class UserAuditRoutingModule {}
