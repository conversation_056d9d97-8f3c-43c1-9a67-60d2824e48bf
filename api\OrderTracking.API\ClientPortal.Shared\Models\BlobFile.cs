﻿#region Copyright Quest Integrity Group, LLC 2020

// www.QuestIntegrity.com
// +1-303-415-1475
// 
// All rights are reserved. Reproduction or transmission in whole or in part, in
// any form or by any means, electronic, mechanical or otherwise, is prohibited
// without the prior written consent of the copyright owner.
// 
// File Created: 2020-04-06 9:57 AM
// Updated:      2020-04-06 9:57 AM
// Created by:   <PERSON><PERSON><PERSON><PERSON>, Sean

#endregion

using System;
using Newtonsoft.Json;

namespace ClientPortal.Shared.Models
{
    public class BlobFile
    {
        [JsonProperty(PropertyName = "uploadedAt")]
        public DateTime UploadedAt { get; set; }

        [JsonProperty(PropertyName = "uploadedBy")]
        public string UploadedBy { get; set; }

        [JsonProperty(PropertyName = "name")] public string Name { get; set; }

        [JsonProperty(PropertyName = "size")] public long Size { get; set; }

        [JsonProperty(PropertyName = "type")] public string Type { get; set; }
    }
}