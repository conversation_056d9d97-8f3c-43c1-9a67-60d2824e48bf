using System.Collections.Generic;
using System.Threading.Tasks;
using OrderTracking.API.Models;

namespace OrderTracking.API.Interfaces
{
    /// <summary>
    ///     Interface for NotificationsService
    /// </summary>
    public interface INotificationsService
    {
        #region Public Methods

        /// <summary>
        ///     get notification by user id
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<IEnumerable<Notification>> GetItemByUserAsync( string userId);

        /// <summary>
        ///     Update a Notification.  Main purpose is to mark notifications as read.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="equipmentRequest"></param>
        /// <returns></returns>
        Task UpdateItemAsync(string id, Notification equipmentRequest);

        #endregion
    }
}
