import { AssetCategory } from '../apm-asset-category';

export type ListChangeLog = {
    [key: string]: {
        A: 'Added' | 'Removed';
        T: number;
        U: string;
        V: string;
    };
};

export type CosmosUser = {
    id: string;
    BusinessUnitIds: CosmosListAttribute<string>;
    EffectiveBusinessUnitIds: string[];
    Name: CosmosAttribute<string>;
};

export type ProjectActivityItem = {
    Count: CosmosAttribute<number>;
    Duration: CosmosAttribute<number>;
};

export type ProjectActivityTrackerValue = {
    Permitting?: ProjectActivityItem;
    'Job Setup'?: ProjectActivityItem;
    Lunch?: ProjectActivityItem;
    'Post CleanUp'?: ProjectActivityItem;
    'FW-RT'?: ProjectActivityItem;
    'FW-MT'?: ProjectActivityItem;
    'FW-PT'?: ProjectActivityItem;
    'FW-UT'?: ProjectActivityItem;
    'FW-VT'?: ProjectActivityItem;
    'FW-ML'?: ProjectActivityItem;
    'FW-GW'?: ProjectActivityItem;
    'FW-ET'?: ProjectActivityItem;
    'FW-LS'?: ProjectActivityItem;
    'FW-GPR'?: ProjectActivityItem;
    'FW-LT'?: ProjectActivityItem;
    'FW-IR'?: ProjectActivityItem;
    'FW-PMI'?: ProjectActivityItem;
    'FW-AE'?: ProjectActivityItem;
    'FW-VA'?: ProjectActivityItem;
    'FW-API 510'?: ProjectActivityItem;
    'FW-API 570'?: ProjectActivityItem;
    'FW-API 653'?: ProjectActivityItem;
    'FW-AWS CWI'?: ProjectActivityItem;
    'FW-NACE'?: ProjectActivityItem;
    'FW-Other'?: ProjectActivityItem;
    'Client WO Number': CosmosAttribute<string>;
    Date: CosmosAttribute<string>;
    User: CosmosAttribute<string>;
    id: string;
};

export interface CosmosProject {
    id: string;
    AccountingDetails: {
        'APM Project Number': CosmosAttribute<string>;
        'Project Number': CosmosAttribute<string>;
        'Team District Number': CosmosAttribute<string>;
        'TEAM Project Number': CosmosAttribute<string>;
    };
    // TODO: ClientDetails
    AssetIds: CosmosListAttribute<string>;
    BusinessUnitId: CosmosAttribute<string>;
    LocationId: string;
    Name: CosmosAttribute<string>;
    'Planned End': CosmosAttribute<string>;
    'Planned Start': CosmosAttribute<string>;
    ProjectActivities: {
        Value: string;
        ValueChangeLog: ListChangeLog;
        Values: {
            [key: string]: ProjectActivityTrackerValue;
        };
    };
}

export interface CosmosAsset {
    id: string;
    BusinessUnitId: CosmosAttribute<string>;
    ProjectIds: string[];
    AssetCategory: AssetCategory;
    Section510_Asset_Walkdown_Details_F?: unknown; // TODO:
    Section570_Asset_Walkdown_Details_F?: unknown; // TODO:
    Section653_Asset_Walkdown_Details_F?: unknown; // TODO:
    Area: CosmosAttribute<string>;
    Unit: CosmosAttribute<string>;
}

export type TaskActivityTrackerValue = {
    Date: CosmosAttribute<string>;
    Permitting: CosmosAttribute<number>;
    'Job Setup': CosmosAttribute<number>;
    Lunch: CosmosAttribute<number>;
    'Post CleanUp': CosmosAttribute<number>;
    'FW-VT': CosmosAttribute<number>;
    'FW-API 510': CosmosAttribute<number>;
    'FW-API 570': CosmosAttribute<number>;
    'FW-API 653': CosmosAttribute<number>;
    'FW-Other': CosmosAttribute<number>;
    id: string;
};

export interface CosmosTask {
    id: string;
    AssetId: string;
    AssignedUsers: string[];
    BusinessUnitId: CosmosAttribute<string>;
    'Client Work Order Number': CosmosAttribute<string>;
    ProjectId: string;
    Status: CosmosAttribute<string>;
    'Task APM Number': CosmosAttribute<string>;
    TaskType: string;
    WorkOrderId: string;
    'Due Date': CosmosAttribute<string>;
    Supervisor: CosmosAttribute<string>;
    'Planned Start': CosmosAttribute<string>;
    'Planned End': CosmosAttribute<string>;
    'Activity Tracker': {
        Value: string;
        ValueChangeLog: ListChangeLog;
        Values: {
            [key: string]: TaskActivityTrackerValue;
        };
    };
}

export interface CosmosWorkOrder {
    id: string;
    'APM Work Order Number': CosmosAttribute<string>;
    AssetId: string;
    BusinessUnitId: CosmosAttribute<string>;
    'Due Date': CosmosAttribute<string>;
    ProjectId: string;
    'Planned Start': CosmosAttribute<string>;
    'Planned End': CosmosAttribute<string>;
    'Facility Name': CosmosAttribute<string>;
    Status: CosmosAttribute<string>;
    AssignedUsers: string[];
    'GIS Location': CosmosLocationAttribute;
    'Primary Contact Name': CosmosAttribute<string>;
    'Primary Contact Phone': CosmosAttribute<string>;
    'Job Scope': CosmosAttribute<string>;
    'Inspection Summary': CosmosAttribute<string>;
    'Applicable Damage Mechanisms': CosmosAttribute<string>;

    'Field Work Completed': CosmosAttribute<string>;
    Recommendations: CosmosAttribute<string>;
    'Relevent Indications Findings': CosmosAttribute<string>;
}

export class CosmosAttribute<T> {
    Value?: T;
    ValueChangeLog: {
        [key: string]: { T: number; U: string; V: T };
    };
}

export class CosmosLocationAttribute extends CosmosAttribute<string> {}

export class CosmosListAttribute<T> {
    Value?: T[];
    ValueChangeLog: ListChangeLog;
}

// Legacy Firebase types - DEPRECATED (kept for backward compatibility)
export type FirebaseAttribute<T> = CosmosAttribute<T>;
export type FirebaseLocationAttribute = CosmosLocationAttribute;
export type FirebaseListAttribute<T> = CosmosListAttribute<T>;
export type FirebaseUser = CosmosUser;
export type FirebaseProject = CosmosProject;
export type FirebaseAsset = CosmosAsset;
export type FirebaseTask = CosmosTask;
export type FirebaseWorkOrder = CosmosWorkOrder;
