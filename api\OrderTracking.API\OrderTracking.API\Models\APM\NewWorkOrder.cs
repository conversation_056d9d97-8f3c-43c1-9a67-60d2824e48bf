﻿using System;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class NewWorkOrder
    {
        [JsonProperty("assetCategory")] public string AssetCategory { get; set; }
        
        [JsonProperty("assetDatabaseId")] public string AssetDatabaseID { get; set; }
        
        [JsonProperty("dueDate")] public DateTime? DueDate { get; set; }
        
        [JsonProperty("facilityName")] public string FacilityName { get; set; }
        
        [JsonProperty("plannedEnd")] public DateTime? PlannedEnd { get; set; }
        
        [JsonProperty("plannedStart")] public DateTime? PlannedStart { get; set; }
        
        [JsonProperty("status")] public string Status { get; set; }

        [JsonProperty("projectId")] public string ProjectId { get; set; }
    }
}