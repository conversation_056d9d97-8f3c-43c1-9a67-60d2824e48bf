﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/CodeStyle/CodeCleanup/RecentlyUsedProfile/@EntryValue">Built-in: Full Cleanup</s:String>
	<s:String x:Key="/Default/CodeStyle/CodeFormatting/CSharpCodeStyle/METHOD_OR_OPERATOR_BODY/@EntryValue">ExpressionBody</s:String>
	<s:Int64 x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/INDENT_SIZE/@EntryValue">4</s:Int64>
	<s:Int64 x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/TAB_WIDTH/@EntryValue">4</s:Int64>
	<s:String x:Key="/Default/CodeStyle/CSharpFileLayoutPatterns/Pattern/@EntryValue">&lt;?xml version="1.0" encoding="utf-16"?&gt;&#xD;
&lt;Patterns xmlns="urn:schemas-jetbrains-com:member-reordering-patterns"&gt;&#xD;
  &lt;TypePattern DisplayName="Non-reorderable types"&gt;&#xD;
    &lt;TypePattern.Match&gt;&#xD;
      &lt;Or&gt;&#xD;
        &lt;And&gt;&#xD;
          &lt;Kind Is="Interface" /&gt;&#xD;
          &lt;Or&gt;&#xD;
            &lt;HasAttribute Name="System.Runtime.InteropServices.InterfaceTypeAttribute" /&gt;&#xD;
            &lt;HasAttribute Name="System.Runtime.InteropServices.ComImport" /&gt;&#xD;
          &lt;/Or&gt;&#xD;
        &lt;/And&gt;&#xD;
        &lt;Kind Is="Struct" /&gt;&#xD;
        &lt;HasAttribute Name="JetBrains.Annotations.NoReorderAttribute" /&gt;&#xD;
        &lt;HasAttribute Name="JetBrains.Annotations.NoReorder" /&gt;&#xD;
      &lt;/Or&gt;&#xD;
    &lt;/TypePattern.Match&gt;&#xD;
  &lt;/TypePattern&gt;&#xD;
  &lt;TypePattern DisplayName="xUnit.net Test Classes" RemoveRegions="All"&gt;&#xD;
    &lt;TypePattern.Match&gt;&#xD;
      &lt;And&gt;&#xD;
        &lt;Kind Is="Class" /&gt;&#xD;
        &lt;HasMember&gt;&#xD;
          &lt;And&gt;&#xD;
            &lt;Kind Is="Method" /&gt;&#xD;
            &lt;HasAttribute Name="Xunit.FactAttribute" Inherited="True" /&gt;&#xD;
            &lt;HasAttribute Name="Xunit.TheoryAttribute" Inherited="True" /&gt;&#xD;
          &lt;/And&gt;&#xD;
        &lt;/HasMember&gt;&#xD;
      &lt;/And&gt;&#xD;
    &lt;/TypePattern.Match&gt;&#xD;
    &lt;Entry DisplayName="Setup/Teardown Methods"&gt;&#xD;
      &lt;Entry.Match&gt;&#xD;
        &lt;Or&gt;&#xD;
          &lt;Kind Is="Constructor" /&gt;&#xD;
          &lt;And&gt;&#xD;
            &lt;Kind Is="Method" /&gt;&#xD;
            &lt;ImplementsInterface Name="System.IDisposable" /&gt;&#xD;
          &lt;/And&gt;&#xD;
        &lt;/Or&gt;&#xD;
      &lt;/Entry.Match&gt;&#xD;
      &lt;Entry.SortBy&gt;&#xD;
        &lt;Kind Order="Constructor" /&gt;&#xD;
      &lt;/Entry.SortBy&gt;&#xD;
    &lt;/Entry&gt;&#xD;
    &lt;Entry DisplayName="All other members" /&gt;&#xD;
    &lt;Entry DisplayName="Test Methods" Priority="100"&gt;&#xD;
      &lt;Entry.Match&gt;&#xD;
        &lt;And&gt;&#xD;
          &lt;Kind Is="Method" /&gt;&#xD;
          &lt;HasAttribute Name="Xunit.FactAttribute" /&gt;&#xD;
          &lt;HasAttribute Name="Xunit.TheoryAttribute" /&gt;&#xD;
        &lt;/And&gt;&#xD;
      &lt;/Entry.Match&gt;&#xD;
      &lt;Entry.SortBy&gt;&#xD;
        &lt;Name /&gt;&#xD;
      &lt;/Entry.SortBy&gt;&#xD;
    &lt;/Entry&gt;&#xD;
  &lt;/TypePattern&gt;&#xD;
  &lt;TypePattern DisplayName="NUnit Test Fixtures" RemoveRegions="All"&gt;&#xD;
    &lt;TypePattern.Match&gt;&#xD;
      &lt;And&gt;&#xD;
        &lt;Kind Is="Class" /&gt;&#xD;
        &lt;Or&gt;&#xD;
          &lt;HasAttribute Name="NUnit.Framework.TestFixtureAttribute" Inherited="True" /&gt;&#xD;
          &lt;HasAttribute Name="NUnit.Framework.TestFixtureSourceAttribute" Inherited="True" /&gt;&#xD;
          &lt;HasMember&gt;&#xD;
            &lt;And&gt;&#xD;
              &lt;Kind Is="Method" /&gt;&#xD;
              &lt;HasAttribute Name="NUnit.Framework.TestAttribute" /&gt;&#xD;
              &lt;HasAttribute Name="NUnit.Framework.TestCaseAttribute" /&gt;&#xD;
              &lt;HasAttribute Name="NUnit.Framework.TestCaseSourceAttribute" /&gt;&#xD;
            &lt;/And&gt;&#xD;
          &lt;/HasMember&gt;&#xD;
        &lt;/Or&gt;&#xD;
      &lt;/And&gt;&#xD;
    &lt;/TypePattern.Match&gt;&#xD;
    &lt;Entry DisplayName="Setup/Teardown Methods"&gt;&#xD;
      &lt;Entry.Match&gt;&#xD;
        &lt;And&gt;&#xD;
          &lt;Kind Is="Method" /&gt;&#xD;
          &lt;Or&gt;&#xD;
            &lt;HasAttribute Name="NUnit.Framework.SetUpAttribute" Inherited="True" /&gt;&#xD;
            &lt;HasAttribute Name="NUnit.Framework.TearDownAttribute" Inherited="True" /&gt;&#xD;
            &lt;HasAttribute Name="NUnit.Framework.TestFixtureSetUpAttribute" Inherited="True" /&gt;&#xD;
            &lt;HasAttribute Name="NUnit.Framework.TestFixtureTearDownAttribute" Inherited="True" /&gt;&#xD;
            &lt;HasAttribute Name="NUnit.Framework.OneTimeSetUpAttribute" Inherited="True" /&gt;&#xD;
            &lt;HasAttribute Name="NUnit.Framework.OneTimeTearDownAttribute" Inherited="True" /&gt;&#xD;
          &lt;/Or&gt;&#xD;
        &lt;/And&gt;&#xD;
      &lt;/Entry.Match&gt;&#xD;
    &lt;/Entry&gt;&#xD;
    &lt;Entry DisplayName="All other members" /&gt;&#xD;
    &lt;Entry DisplayName="Test Methods" Priority="100"&gt;&#xD;
      &lt;Entry.Match&gt;&#xD;
        &lt;And&gt;&#xD;
          &lt;Kind Is="Method" /&gt;&#xD;
          &lt;HasAttribute Name="NUnit.Framework.TestAttribute" /&gt;&#xD;
          &lt;HasAttribute Name="NUnit.Framework.TestCaseAttribute" /&gt;&#xD;
          &lt;HasAttribute Name="NUnit.Framework.TestCaseSourceAttribute" /&gt;&#xD;
        &lt;/And&gt;&#xD;
      &lt;/Entry.Match&gt;&#xD;
      &lt;Entry.SortBy&gt;&#xD;
        &lt;Name /&gt;&#xD;
      &lt;/Entry.SortBy&gt;&#xD;
    &lt;/Entry&gt;&#xD;
  &lt;/TypePattern&gt;&#xD;
  &lt;TypePattern DisplayName="Default Pattern"&gt;&#xD;
    &lt;Entry DisplayName="Public Delegates" Priority="100"&gt;&#xD;
      &lt;Entry.Match&gt;&#xD;
        &lt;And&gt;&#xD;
          &lt;Access Is="Public" /&gt;&#xD;
          &lt;Kind Is="Delegate" /&gt;&#xD;
        &lt;/And&gt;&#xD;
      &lt;/Entry.Match&gt;&#xD;
      &lt;Entry.SortBy&gt;&#xD;
        &lt;Name /&gt;&#xD;
      &lt;/Entry.SortBy&gt;&#xD;
    &lt;/Entry&gt;&#xD;
    &lt;Entry DisplayName="Public Enums" Priority="100"&gt;&#xD;
      &lt;Entry.Match&gt;&#xD;
        &lt;And&gt;&#xD;
          &lt;Access Is="Public" /&gt;&#xD;
          &lt;Kind Is="Enum" /&gt;&#xD;
        &lt;/And&gt;&#xD;
      &lt;/Entry.Match&gt;&#xD;
      &lt;Entry.SortBy&gt;&#xD;
        &lt;Name /&gt;&#xD;
      &lt;/Entry.SortBy&gt;&#xD;
    &lt;/Entry&gt;&#xD;
    &lt;Entry DisplayName="Static Fields and Constants"&gt;&#xD;
      &lt;Entry.Match&gt;&#xD;
        &lt;Or&gt;&#xD;
          &lt;Kind Is="Constant" /&gt;&#xD;
          &lt;And&gt;&#xD;
            &lt;Kind Is="Field" /&gt;&#xD;
            &lt;Static /&gt;&#xD;
          &lt;/And&gt;&#xD;
        &lt;/Or&gt;&#xD;
      &lt;/Entry.Match&gt;&#xD;
      &lt;Entry.SortBy&gt;&#xD;
        &lt;Kind Order="Constant Field" /&gt;&#xD;
      &lt;/Entry.SortBy&gt;&#xD;
    &lt;/Entry&gt;&#xD;
    &lt;Entry DisplayName="Fields"&gt;&#xD;
      &lt;Entry.Match&gt;&#xD;
        &lt;And&gt;&#xD;
          &lt;Kind Is="Field" /&gt;&#xD;
          &lt;Not&gt;&#xD;
            &lt;Static /&gt;&#xD;
          &lt;/Not&gt;&#xD;
        &lt;/And&gt;&#xD;
      &lt;/Entry.Match&gt;&#xD;
      &lt;Entry.SortBy&gt;&#xD;
        &lt;Readonly /&gt;&#xD;
        &lt;Name /&gt;&#xD;
      &lt;/Entry.SortBy&gt;&#xD;
    &lt;/Entry&gt;&#xD;
    &lt;Entry DisplayName="Constructors"&gt;&#xD;
      &lt;Entry.Match&gt;&#xD;
        &lt;Kind Is="Constructor" /&gt;&#xD;
      &lt;/Entry.Match&gt;&#xD;
      &lt;Entry.SortBy&gt;&#xD;
        &lt;Static /&gt;&#xD;
      &lt;/Entry.SortBy&gt;&#xD;
    &lt;/Entry&gt;&#xD;
    &lt;Entry DisplayName="Properties, Indexers"&gt;&#xD;
      &lt;Entry.Match&gt;&#xD;
        &lt;Or&gt;&#xD;
          &lt;Kind Is="Property" /&gt;&#xD;
          &lt;Kind Is="Indexer" /&gt;&#xD;
        &lt;/Or&gt;&#xD;
      &lt;/Entry.Match&gt;&#xD;
    &lt;/Entry&gt;&#xD;
    &lt;Entry DisplayName="Interface Implementations" Priority="100"&gt;&#xD;
      &lt;Entry.Match&gt;&#xD;
        &lt;And&gt;&#xD;
          &lt;Kind Is="Member" /&gt;&#xD;
          &lt;ImplementsInterface /&gt;&#xD;
        &lt;/And&gt;&#xD;
      &lt;/Entry.Match&gt;&#xD;
      &lt;Entry.SortBy&gt;&#xD;
        &lt;ImplementsInterface Immediate="True" /&gt;&#xD;
      &lt;/Entry.SortBy&gt;&#xD;
    &lt;/Entry&gt;&#xD;
    &lt;Entry DisplayName="All other members" /&gt;&#xD;
    &lt;Entry DisplayName="Private Methods"&gt;&#xD;
      &lt;Entry.Match&gt;&#xD;
        &lt;And&gt;&#xD;
          &lt;Access Is="Private" /&gt;&#xD;
          &lt;Kind Is="Method" /&gt;&#xD;
        &lt;/And&gt;&#xD;
      &lt;/Entry.Match&gt;&#xD;
    &lt;/Entry&gt;&#xD;
    &lt;Entry DisplayName="Nested Types"&gt;&#xD;
      &lt;Entry.Match&gt;&#xD;
        &lt;Kind Is="Type" /&gt;&#xD;
      &lt;/Entry.Match&gt;&#xD;
    &lt;/Entry&gt;&#xD;
  &lt;/TypePattern&gt;&#xD;
&lt;/Patterns&gt;</s:String>
	<s:Boolean x:Key="/Default/CodeStyle/Generate/=Implementations/@KeyIndexDefined">True</s:Boolean>
	<s:String x:Key="/Default/CodeStyle/Generate/=Implementations/Options/=Async/@EntryIndexedValue">False</s:String>
	<s:Boolean x:Key="/Default/CodeStyle/Generate/=Overrides/@KeyIndexDefined">True</s:Boolean>
	<s:String x:Key="/Default/CodeStyle/Generate/=Overrides/Options/=Async/@EntryIndexedValue">False</s:String>
	<s:Boolean x:Key="/Default/CodeStyle/Naming/CSharpAutoNaming/IsNotificationDisabled/@EntryValue">True</s:Boolean>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=APM/@EntryIndexedValue">APM</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=AX/@EntryIndexedValue">AX</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=BI/@EntryIndexedValue">BI</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=CRD/@EntryIndexedValue">CRD</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=DB/@EntryIndexedValue">DB</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=EDR/@EntryIndexedValue">EDR</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=ETL/@EntryIndexedValue">ETL</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=ID/@EntryIndexedValue">ID</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=JSON/@EntryIndexedValue">JSON</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=JSS/@EntryIndexedValue">JSS</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=PDF/@EntryIndexedValue">PDF</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=PPE/@EntryIndexedValue">PPE</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=SAS/@EntryIndexedValue">SAS</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=SQL/@EntryIndexedValue">SQL</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=TEAM/@EntryIndexedValue">TEAM</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=TS/@EntryIndexedValue">TS</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=URL/@EntryIndexedValue">URL</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=UT/@EntryIndexedValue">UT</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=VM/@EntryIndexedValue">VM</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=WMO/@EntryIndexedValue">WMO</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=JS_005FBLOCK_005FSCOPE_005FCONSTANT/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=JS_005FBLOCK_005FSCOPE_005FFUNCTION/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=JS_005FBLOCK_005FSCOPE_005FVARIABLE/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=JS_005FCLASS/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=JS_005FCONSTRUCTOR/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=JS_005FFUNCTION/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=JS_005FGLOBAL_005FVARIABLE/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=JS_005FLABEL/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=JS_005FLOCAL_005FCONSTRUCTOR/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=JS_005FLOCAL_005FVARIABLE/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=JS_005FOBJECT_005FPROPERTY_005FOF_005FFUNCTION/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=JS_005FPARAMETER/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FCLASS/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FENUM/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FENUM_005FMEMBER/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FINTERFACE/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="I" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FMIXED_005FENUM/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FMODULE/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FMODULE_005FEXPORTED/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FMODULE_005FLOCAL/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FPRIVATE_005FMEMBER_005FACCESSOR/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FPRIVATE_005FSTATIC_005FTYPE_005FFIELD/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FPRIVATE_005FTYPE_005FFIELD/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FPRIVATE_005FTYPE_005FMETHOD/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FPROTECTED_005FMEMBER_005FACCESSOR/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FPROTECTED_005FSTATIC_005FTYPE_005FFIELD/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FPROTECTED_005FTYPE_005FFIELD/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FPROTECTED_005FTYPE_005FMETHOD/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FPUBLIC_005FMEMBER_005FACCESSOR/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FPUBLIC_005FSTATIC_005FTYPE_005FFIELD/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FPUBLIC_005FTYPE_005FFIELD/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FPUBLIC_005FTYPE_005FMETHOD/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FTYPE_005FALIAS/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/JavaScriptNaming/UserRules/=TS_005FTYPE_005FPARAMETER/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="T" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/WebNaming/UserRules/=ASP_005FFIELD/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/WebNaming/UserRules/=ASP_005FHTML_005FCONTROL/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/WebNaming/UserRules/=ASP_005FTAG_005FNAME/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/WebNaming/UserRules/=ASP_005FTAG_005FPREFIX/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/XamlNaming/UserRules/=NAMESPACE_005FALIAS/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/XamlNaming/UserRules/=XAML_005FFIELD/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/XamlNaming/UserRules/=XAML_005FRESOURCE/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="AaBb" /&gt;</s:String>
	<s:String x:Key="/Default/Environment/PerformanceGuide/SwitchBehaviour/=Antivirus/@EntryIndexedValue">LIVE_MONITOR</s:String>
	<s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ESettingsUpgrade_002EAlwaysTreatStructAsNotReorderableMigration/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ESettingsUpgrade_002EMigrateBlankLinesAroundFieldToBlankLinesAroundProperty/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ECSharpUseContinuousIndentInsideBracesMigration/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ECSharpPlaceEmbeddedOnSameLineMigration/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ECSharpKeepExistingMigration/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=appsettings/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=dataset/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=datasets/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=includable/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=manufacturinguser/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=Migrator/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=Workspaces/@EntryIndexedValue">True</s:Boolean></wpf:ResourceDictionary>