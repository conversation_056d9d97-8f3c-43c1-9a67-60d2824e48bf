<!--Overall Container-->
<div class="responsive-paddings">
    <dx-popup [(visible)]="submissionPopupVisible"
              [width]="1200"
              [height]="700"
              [showTitle]="true"
              [dragEnabled]="false"
              [showCloseButton]="true">
        <dxi-toolbar-item toolbar="top"
                          [text]="submissionPopupTitle"
                          location="center"
                          locateInMenu="always"></dxi-toolbar-item>

        <ng-container *ngIf="submissionPopupVisible">

            <app-client-data-submission [initialAnomaly]="initialAnomaly"
                                        (clientSubmissionTitleValueChange)="clientSubmissionTitleValueChange($event)"
                                        (formSubmittedValueChange)="clientDataFormSubmitted($event)">
            </app-client-data-submission>
        </ng-container>
    </dx-popup>
    <!--Selection Box Container-->
    <div class="content-block"
         style="display: flex; justify-content:left;">
        <dx-select-box id="selectBox"
                       *ngIf="availableSites?.length>0"
                       #siteSelectionBox
                       style="width:370px; margin:2px"
                       [items]="availableSites"
                       [displayExpr]="customDisplayExpr"
                       [hint]="selectedSite | siteLabel"
                       [searchEnabled]="true"
                       [(value)]="selectedSite"
                       [showClearButton]="false"
                       itemTemplate="item"
                       [stylingMode]="'filled'"
                       (onSelectionChanged)="changeSite($event)">
            <dxo-drop-down-options
                                   container="#selectBox"></dxo-drop-down-options>
            <div *dxTemplate="let data of 'item'">
                <div style="display:inline-block">{{data | siteLabel}}
                </div>
            </div>
        </dx-select-box>
        <dx-select-box id="selectBox2"
                       #selectionBox
                       style="width:210px; margin:2px"
                       [items]="selectionOption"
                       [value]="selectedDashboard"
                       [stylingMode]="'filled'"
                       [showClearButton]="false"
                       (onValueChanged)="dashboardChanged($event)">
            <dxo-drop-down-options
                                   container="#selectBox2"></dxo-drop-down-options>
        </dx-select-box>

        <div style="margin-left: auto;"
             *ngIf="availableSites?.length>0">

            <dx-button [routerLink]="['../drilldown']"
                       class="listpagebuttons">
                Equipment List
            </dx-button>
            <dx-button [routerLink]="['../inspection-drilldown']"
                       class="listpagebuttons">
                Inspections List
            </dx-button>
            <dx-button [routerLink]="['../anomaly-drilldown']"
                       class="listpagebuttons">
                Recommendations List
            </dx-button>

            <dx-button *ngIf="availableSites?.length>0 && (( 
                currentUser | hasRole: 'AIMaaS:Edit') ||( currentUser | hasRole: 'App:Admin') || 
                (currentUser | hasRole: 'AIMaaS:Admin') || ( currentUser | hasRole: 'AIMaaS:Demo'))"
                       (onClick)="clientSubmitDataOnclick('frombuttonclick')"
                       class="listpagebuttons">
                Action Center
            </dx-button>
        </div>
    </div>
    <!-- Equipment By Area and Type Dashboard -->
    <div *ngIf="selectionBox.value === selectionOption[0] && !isLoading">
        <app-equipment-dashboard [assets]="assetsForSite$ | async"
                                 [inspections]="inspectionsForSite$ | async">
        </app-equipment-dashboard>
    </div>
    <!-- Inspections Dashboard -->
    <div *ngIf="selectionBox.value === selectionOption[1] && !isLoading">
        <app-inspections-dashboard [assets]="assetsForSite$ | async"
                                   [inspectionsData]="inspectionsForSite$ | async">
        </app-inspections-dashboard>


    </div>
    <!-- Recommendations Dashboard -->
    <div *ngIf="selectionBox.value === selectionOption[2] && !isLoading">

        <app-recommendations-dashboard [anamolies]="anomaliesForSite$ | async">
        </app-recommendations-dashboard>

    </div>

    <div *ngIf="isLoading"
         style="width: 100%; height: 100%;">
        <dx-load-indicator class="centered"
                           id="large-indicator"
                           height="300"
                           width="300"></dx-load-indicator>
    </div>
</div>