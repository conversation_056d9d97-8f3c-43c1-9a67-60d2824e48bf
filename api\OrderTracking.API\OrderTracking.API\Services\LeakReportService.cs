﻿using System.Collections.Generic;
using System.Threading.Tasks;
using APMWebDataInterface.DataModel.LeakReport;
using APMWebDataInterface.ExampleDataModel;
using OrderTracking.API.Interfaces;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     Service class for leak reporting
    /// </summary>
    public class LeakReportService : ILeakReportService
    {
        private readonly APM_WebDataInterface _apm;

        /// <summary>
        ///     constructs the leak report service, injecting our apm nuget package
        /// </summary>
        /// <param name="apm"></param>
        public LeakReportService(APM_WebDataInterface apm)
        {
            _apm = apm;
        }

        /// <summary>
        ///     Gets a single leak report based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<LeakReport> GetLeakReportAsync(string id, string email)
        {
            var leakReports = await _apm.GetLeakReports(email);
            return leakReports.Find(r => r.id == id);
        }

        /// <summary>
        ///     gets all leak reports
        /// </summary>
        /// <returns></returns>
        public async Task<List<LeakReport>> GetLeakReportsAsync(string email) => await _apm.GetLeakReports(email);

        /// <summary>
        ///     Saves a leak report
        /// </summary>
        /// <param name="report"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task SaveLeakReportAsync(LeakReport report, string user)
        {
            await report.SavePendingChanges(user);
        }

        /// <summary>
        ///     gets a leak report photogroup based on photogroup id
        /// </summary>
        /// <param name="report"></param>
        /// <param name="photoGroupId"></param>
        /// <returns></returns>
        public LeakReportPhoto GetLeakReportPhotoGroup(LeakReport report, string photoGroupId)
        {
            return report.leakReportPhotos.CurrentEntries.Find(g => g.DatabaseId == photoGroupId);
        }
    }
}