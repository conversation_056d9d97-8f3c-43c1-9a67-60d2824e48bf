<div class="responsive-paddings content-block details-tab">
    <dx-form #form
             [colCount]="2"
             [(formData)]="workDetails"
             [readOnly]="!isEditing"
             (onFieldDataChanged)="onFieldDataChanged($event)">

        <!-- Client -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Client"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="client.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="client.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.client?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('client',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- State -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="State"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="state.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="state.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.state?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('state',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- City -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="City"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="city.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="city.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.city?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('city',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Area -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Area"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="area.value"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: workDetails?.area?.options}">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="area.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.area?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('area',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Facility -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Facility"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="facility.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="facility.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.facility?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('facility',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Lease -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Lease"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="lease.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="lease.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.lease?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('lease',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Client Contact -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Client Contact"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="clientContact.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="clientContact.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.clientContact?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('clientContact',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Client Work Order -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Client Work Order"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="clientWorkOrder.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="clientWorkOrder.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.clientWorkOrder?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('clientWorkOrder',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Purchase Order -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Purchase Order"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="purchaseOrder.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="purchaseOrder.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.purchaseOrder?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('purchaseOrder',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Team Project Number -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Team Project Number"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="teamProjectNumber.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="teamProjectNumber.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.teamProjectNumber?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('teamProjectNumber',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Reference Edition -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Reference Edition"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="referenceEdition.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="referenceEdition.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.referenceEdition?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('referenceEdition',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Inspection Types -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Inspection Type(s)"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="inspectionTypes.value"
                          editorType="dxTagBox"
                          [editorOptions]="{items: workDetails?.inspectionTypes?.options, acceptCustomValue : true}">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="inspectionTypes.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.inspectionTypes?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('inspectionTypes',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Inspected By -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Inspected By"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="inspectedBy.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="inspectedBy.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.inspectedBy?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('inspectedBy',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>


        <!-- Postal Code -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Postal Code"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="postalCode.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="postalCode.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.postalCode?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('postalCode',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Job Description -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Job Description"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="jobDescription.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="jobDescription.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.jobDescription?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('jobDescription',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Client Contact Number -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Client Contact Number"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="clientContactNumber.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="clientContactNumber.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.clientContactNumber?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('clientContactNumber',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Team District -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Team District"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="teamDistrict.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="teamDistrict.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.teamDistrict?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('teamDistrict',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Client Cost Code -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Client Cost Code"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="clientCostCode.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="clientCostCode.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.clientCostCode?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('clientCostCode',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Inspection Reference -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Inspection Reference"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="inspectionReference.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="inspectionReference.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.inspectionReference?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('inspectionReference',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Inspection Date -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Inspection Date"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="inspectionDate.value"
                          editorType="dxDateBox">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="inspectionDate.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.inspectionDate?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('inspectionDate',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Inspector Certificate Number -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Inspector Certificate Number"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="inspectorCertificateNumber.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="inspectorCertificateNumber.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.inspectorCertificateNumber?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('inspectorCertificateNumber',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Reviewed By -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Reviewed By"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="reviewedBy.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="reviewedBy.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.reviewedBy?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('reviewedBy',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Reviewer Email -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Reviewer Email"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="reviewerEmail.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="reviewerEmail.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.reviewerEmail?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('reviewerEmail',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Reviewer Certificate Number -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Reviewer Certificate Number"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="reviewerCertificateNumber.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="reviewerCertificateNumber.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="workDetails?.reviewerCertificateNumber?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('reviewerCertificateNumber',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="empty"></dxi-item>

        <dxi-item itemType="group"
                  [colSpan]="4">
            <dxi-item [template]="'buttons'"></dxi-item>
        </dxi-item>

        <div *dxTemplate="let data of 'buttons'"
             class="buttons">
            <dx-button *ngIf="!isEditing; else saveAndCancel"
                       text="Edit"
                       type="default"
                       [disabled]="!allowEditing"
                       (onClick)="onEditClicked($event)"></dx-button>
            <ng-template #saveAndCancel>
                <dx-button text="Cancel"
                           type="danger"
                           [disabled]="!allowEditing"
                           (onClick)="onCancelClicked($event, form)">
                </dx-button>
                <dx-button text="Save"
                           type="success"
                           [disabled]="!allowEditing"
                           (onClick)="onSaveClicked($event)"></dx-button>
            </ng-template>
        </div>
    </dx-form>


</div>

<dx-popup [(visible)]="showPhotoPopup"
          [showTitle]="true"
          [title]="'Photo'"
          [showCloseButton]="true"
          [hideOnOutsideClick]="true"
          #popup>
    <dx-gallery #gallery
                id="gallery"
                [dataSource]="allPhotos"
                [loop]="true"
                width="100%"
                height="100%"
                [showNavButtons]="true"
                [showIndicator]="true"
                [(selectedIndex)]="popupGallerySelectedIndex">

        <div *dxTemplate="let photoInfo of 'item'"
             class="popup-template">

            <div *ngIf="photoInfo?.blobName"
                 class="image-container">
                <img *ngIf="assetPathLoadingCompleted"
                     [src]="getAssetImage(null, photoInfo.blobName)"
                     alt="">

            </div>
            <div class="info">
                <div class="text-content">
                    <h6>Description</h6>
                    <dx-text-area #descriptionEditor
                                  [readOnly]="!isEditingPhotoDescription"
                                  [value]="photoInfo?.description?.currentValue"
                                  [autoResizeEnabled]="true">
                    </dx-text-area>
                    <div class="description-buttons">
                        <dx-button *ngIf="!isEditingPhotoDescription; else saveCancelPhotoDescription"
                                   icon="edit"
                                   text="Edit"
                                   [disabled]="!allowEditing"
                                   (onClick)="onEditDescriptionClicked($event, photoInfo?.description?.currentValue)">
                        </dx-button>
                        <ng-template #saveCancelPhotoDescription>
                            <dx-button icon="save"
                                       text="Save"
                                       type="success"
                                       [disabled]="!allowEditing"
                                       (onClick)="onDescriptionSave($event, photoInfo, descriptionEditor.value)">
                            </dx-button>
                            <dx-button text="Cancel"
                                       type="danger"
                                       [disabled]="!allowEditing"
                                       (onClick)="onDescriptionCancel($event, descriptionEditor)">
                            </dx-button>
                        </ng-template>
                    </div>
                </div>
                <div class="buttons">

                    <dx-button icon="remove"
                               text="Delete"
                               type="danger"
                               [disabled]="!allowEditing"
                               (onClick)="onDeletePhotoClicked($event, photoInfo)">
                    </dx-button>

                    <dx-button *ngIf="assetPathLoadingCompleted"
                               icon="download"
                               text="Download"
                               type="default"
                               (onClick)="onDownloadClicked(photoInfo?.blobName)">
                    </dx-button>
                </div>
            </div>
        </div>
    </dx-gallery>
</dx-popup>