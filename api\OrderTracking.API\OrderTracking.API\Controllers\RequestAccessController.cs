﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderTracking.API.Models;
using OrderTracking.API.Services;

namespace OrderTracking.API.Controllers
{
    /// <summary>
    ///     Controller to request additional access to features/functionality/modules
    ///     provided by the Client Portal.
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class RequestAccessController : ControllerBase
    {
        private readonly IEmailService _emails;
        private readonly IUserProfilesService _userProfiles;

        /// <summary>
        ///     Constructor for controller
        /// </summary>
        /// <param name="emails"></param>
        /// <param name="userProfiles"></param>
        public RequestAccessController(IEmailService emails, IUserProfilesService userProfiles)
        {
            _emails = emails;
            _userProfiles = userProfiles;
        }

        /// <summary>
        ///     Endpoint for requesting access
        /// </summary>
        /// <param name="accessRequest"></param>
        [HttpPost]
        public async Task<IActionResult> RequestAccess([FromBody] AccessRequest accessRequest)
        {
            if (accessRequest == null) throw new ArgumentNullException(nameof(accessRequest));

            var user = await _userProfiles.GetAsync(User.Identity.Name.ToLower());

            var recipients = new List<IEmailRecipient>()
            {
                new UserProfile {Id = "<EMAIL>", Email = "<EMAIL>"},
                new UserProfile {Id = "<EMAIL>", Email = "<EMAIL>"}
            };

            var subject = $"OneInsight Access Request for {user.Id}";
            var htmlContent = accessRequest.HtmlContent(user);
            var requestAccessEmail = new EmailData()
            {
                Recipients = recipients, Subject = subject, HtmlContent = htmlContent
            };
            await _emails.SendEmail(requestAccessEmail);

            return NoContent();
        }
    }
}