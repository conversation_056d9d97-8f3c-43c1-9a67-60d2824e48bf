//using System;
//using ClientPortal.Shared.Models;
//using Microsoft.EntityFrameworkCore;

//namespace OrderTracking.API.Models
//{
//    /// <summary>
//    ///     Entity Framework context Orders and related records
//    /// </summary>
//    public class OrderContext : DbContext
//    {
//        /// <summary>
//        ///     Constructor for OrderContext
//        /// </summary>
//        /// <param name="options"></param>
//        /// <returns></returns>
//        public OrderContext(DbContextOptions<OrderContext> options) : base(options)
//        {
//        }

//        /// <summary>
//        ///     Orders DbSet
//        /// </summary>
//        /// <value></value>
//        public DbSet<Order> Orders { get; set; }

//        /// <summary>
//        ///     OrderFiles DbSet
//        /// </summary>
//        /// <value></value>
//        public DbSet<OrderBlobFile> OrderFiles { get; set; }

//        /// <summary>
//        ///     OrdersJobs DbSet
//        /// </summary>
//        /// <value></value>
//        public DbSet<OrdersJob> OrdersJobs { get; set; }

//        /// <summary>
//        ///     Model configuration during creation
//        /// </summary>
//        /// <param name="modelBuilder"></param>
//        protected override void OnModelCreating(ModelBuilder modelBuilder)
//        {
//            if (modelBuilder == null) throw new ArgumentNullException(nameof(modelBuilder));
            
//            // This is a custom relationship set up between orderBlobFile and Order. This allows us to use
//            // a field other than Id from the Order to link with the salesLineRecId on OrderBlobFile

//            modelBuilder.Entity<Order>()
//                .HasMany(order => order.Files)
//                .WithOne(file => file.Order)
//                .HasForeignKey(file => file.SalesLineRecId)
//                .HasPrincipalKey(order => order.SALESLINE_RECID);

//            // When using Entity Framework Core and DateTime? columns, there is an issue where
//            // EF Core's default column for DateTime? is datetime2, but our database is using datetime.
//            // This issue presented itself when trying to use the DevExtreme DxDataGrid remote
//            // operations feature and filtering on date columns.  By helping EF Core know what type
//            // of column we are using for nullable dates, we are helping it write the correct 
//            // SQL when using DevExpress' DataSourceLoader.  More information can be found 
//            // https://supportcenter.devexpress.com/ticket/details/t737816/datagrid-the-conversion-failed-when-converting-date-and-or-time-from-character-string
//            // and
//            // https://github.com/dotnet/efcore/issues/14095#issuecomment-464365442
//            modelBuilder.Entity<Order>().Property(x => x.JSSDATERECEIVED).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.JSSDATEDESIGNED).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.JSSDATECONVERTED).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.JSSDATEVERIFIED).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.JSSDATETOMFG).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.CREATEDDATETIME).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.SCHEDSTART).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.SCHEDEND).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.FINISHEDDATE).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.SHIPPINGDATEREQUESTED).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.SHIPPINGDATECONFIRMED).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.DELIVERYDATE).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.SHIPDATE).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.CONFIRMEDRECEIPTDATE).HasColumnType("datetime");
//            modelBuilder.Entity<Order>().Property(x => x.PRICEUNIT).HasColumnType("numeric(32,16)");
//            modelBuilder.Entity<Order>().Property(x => x.DELIVERED).HasColumnType("numeric(32,16)");
//            modelBuilder.Entity<Order>().Property(x => x.LINEAMOUNT).HasColumnType("numeric(32,16)");
//            modelBuilder.Entity<Order>().Property(x => x.ORDERED).HasColumnType("numeric(32,16)");
//            modelBuilder.Entity<Order>().Property(x => x.REMAININGQUANTITY).HasColumnType("numeric(32,16)");
//            modelBuilder.Entity<Order>().Property(x => x.SALESLINE_SALESPRICE).HasColumnType("numeric(32,16)");
//            modelBuilder.Entity<Order>().Property(x => x.SALESQTY).HasColumnType("numeric(32,16)");

//            modelBuilder.Entity<OrdersJob>().Property(x => x.StartedUTC).HasColumnType("datetime");
//            modelBuilder.Entity<OrdersJob>().Property(x => x.EndedUTC).HasColumnType("datetime");

//            base.OnModelCreating(modelBuilder);
//        }
//    }
//}