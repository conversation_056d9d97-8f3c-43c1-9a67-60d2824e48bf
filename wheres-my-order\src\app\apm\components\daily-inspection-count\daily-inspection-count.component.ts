import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    Input,
    ViewChild
} from '@angular/core';
import { DxChartComponent } from 'devextreme-angular/ui/chart';
import { AssetCategoriesSelectorComponent } from '../../components';
import { AssetCategoryAPICode, statusColor, StatusesByBin } from '../../models';

@Component({
    selector: 'app-daily-inspection-count',
    templateUrl: './daily-inspection-count.component.html',
    styleUrls: ['./daily-inspection-count.component.scss']
})
export class DailyInspectionCountComponent implements AfterViewInit {
    _inspectionStatusesByMonth: StatusesByBin[];
    get inspectionStatusesByMonth(): StatusesByBin[] {
        return this._inspectionStatusesByMonth;
    }
    @Input() set inspectionStatusesByMonth(value: StatusesByBin[]) {
        this._inspectionStatusesByMonth = value;
    }

    @Input() loading: boolean;
    @ViewChild(DxChartComponent) chart: DxChartComponent;

    @ViewChild(AssetCategoriesSelectorComponent)
    categoriesSelector: AssetCategoriesSelectorComponent;

    axisLabel: string[];

    constructor(private readonly _cd: ChangeDetectorRef) {}

    ngAfterViewInit(): void {
        this.populateSeries();
    }

    renderChart() {
        this.chart?.instance?.render({ force: true });
    }

    onCategoryChanged(_: AssetCategoryAPICode[]) {
        this.populateSeries();
    }

    private populateSeries() {
        this.axisLabel = [];
        this.chart.series = [];

        if (this.categoriesSelector.selectedCategories.includes('510')) {
            this.axisLabel.push('510');

            this.chart.series.push({
                valueField: 'vesselScheduledCount',
                name: '510 Scheduled',
                color: statusColor('Scheduled'),
                stack: '510'
            });
            this.chart.series.push({
                valueField: 'vesselInProgressCount',
                name: '510 In Progress',
                color: statusColor('In Progress'),
                stack: '510'
            });
            this.chart.series.push({
                valueField: 'vesselCompleteCount',
                name: '510 Complete',
                color: statusColor('Completed'),
                stack: '510'
            });
            this.chart.series.push({
                valueField: 'vesselNotStartedCount',
                name: '510 Not Started',
                color: statusColor('Not Started'),
                stack: '510'
            });
            this.chart.series.push({
                valueField: 'vesselPublishedCount',
                name: '510 Published',
                color: statusColor('Published'),
                stack: '510'
            });
            this.chart.series.push({
                valueField: 'vesselOnHoldCount',
                name: '510 On Hold',
                color: statusColor('On Hold'),
                stack: '510'
            });
            this.chart.series.push({
                valueField: 'vesselOverdueCount',
                name: '510 Overdue',
                color: statusColor('Overdue'),
                stack: '510'
            });
            this.chart.series.push({
                valueField: `vesselCanceledCount`,
                name: `510 Canceled`,
                color: statusColor('Canceled'),
                stack: '510'
            });
            this.chart.series.push({
                valueField: 'vesselUnknownCount',
                name: '510 Unknown',
                color: statusColor('Unknown'),
                stack: '510'
            });
        }

        if (this.categoriesSelector.selectedCategories.includes('570')) {
            this.axisLabel.push('570');

            this.chart.series.push({
                valueField: 'pipingScheduledCount',
                name: '570 Scheduled',
                color: statusColor('Scheduled'),
                stack: '570'
            });
            this.chart.series.push({
                valueField: 'pipingInProgressCount',
                name: '570 In Progress',
                color: statusColor('In Progress'),
                stack: '570'
            });
            this.chart.series.push({
                valueField: 'pipingCompleteCount',
                name: '570 Complete',
                color: statusColor('Completed'),
                stack: '570'
            });
            this.chart.series.push({
                valueField: 'pipingNotStartedCount',
                name: '570 Not Started',
                color: statusColor('Not Started'),
                stack: '570'
            });
            this.chart.series.push({
                valueField: 'pipingPublishedCount',
                name: '570 Published',
                color: statusColor('Published'),
                stack: '570'
            });
            this.chart.series.push({
                valueField: 'pipingOnHoldCount',
                name: '570 On Hold',
                color: statusColor('On Hold'),
                stack: '570'
            });
            this.chart.series.push({
                valueField: 'pipingOverdueCount',
                name: '570 Overdue',
                color: statusColor('Overdue'),
                stack: '570'
            });
            this.chart.series.push({
                valueField: `pipingCanceledCount`,
                name: `570 Canceled`,
                color: statusColor('Canceled'),
                stack: '570'
            });
            this.chart.series.push({
                valueField: 'pipingUnknownCount',
                name: '570 Unknown',
                color: statusColor('Unknown'),
                stack: '570'
            });
        }

        if (this.categoriesSelector.selectedCategories.includes('653')) {
            this.axisLabel.push('653');

            this.chart.series.push({
                valueField: 'tankScheduledCount',
                name: '653 Scheduled',
                color: statusColor('Scheduled'),
                stack: '653'
            });
            this.chart.series.push({
                valueField: 'tankInProgressCount',
                name: '653 In Progress',
                color: statusColor('In Progress'),
                stack: '653'
            });
            this.chart.series.push({
                valueField: 'tankCompleteCount',
                name: '653 Complete',
                color: statusColor('Completed'),
                stack: '653'
            });
            this.chart.series.push({
                valueField: 'tankNotStartedCount',
                name: '653 Not Started',
                color: statusColor('Not Started'),
                stack: '653'
            });
            this.chart.series.push({
                valueField: 'tankPublishedCount',
                name: '653 Published',
                color: statusColor('Published'),
                stack: '653'
            });
            this.chart.series.push({
                valueField: 'tankOnHoldCount',
                name: '653 On Hold',
                color: statusColor('On Hold'),
                stack: '653'
            });
            this.chart.series.push({
                valueField: 'tankOverdueCount',
                name: '653 Overdue',
                color: statusColor('Overdue'),
                stack: '653'
            });
            this.chart.series.push({
                valueField: `tankCanceledCount`,
                name: `653 Canceled`,
                color: statusColor('Canceled'),
                stack: '653'
            });
            this.chart.series.push({
                valueField: 'tankUnknownCount',
                name: '653 Unknown',
                color: statusColor('Unknown'),
                stack: '653'
            });
        }

        this.renderChart();

        this._cd.detectChanges();
    }
}
