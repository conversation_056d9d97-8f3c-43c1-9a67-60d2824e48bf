﻿using Newtonsoft.Json;

namespace ClientPortal.Shared.Models
{
    /// <summary>
    ///     TODO: Find out if we can make this a base class for Order and when asking for source, we get the Ids and HASHes
    ///     still.
    /// </summary>
    public class Entity
    {
        [JsonProperty(PropertyName = "id")] public string Id { get; set; }

        [JsonProperty(PropertyName = "hash")] public string HASH { get; set; }
    }
}