import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxPopupModule } from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { ApmService } from '../../services';
import { CreationWorkflowComponent } from './creation-workflow.component';

describe('CreationWorkflowComponent', () => {
    let component: CreationWorkflowComponent;
    let fixture: ComponentFixture<CreationWorkflowComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ToastrModule.forRoot(), DxPopupModule],
            declarations: [CreationWorkflowComponent],
            providers: [
                { provide: ApmService, useValue: { getUsers: () => [] } }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(CreationWorkflowComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
