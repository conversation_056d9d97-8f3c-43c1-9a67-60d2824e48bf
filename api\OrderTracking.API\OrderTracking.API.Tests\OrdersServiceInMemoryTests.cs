//using System;
//using System.Data.Common;
//using System.Linq;
//using System.Threading.Tasks;
//using ClientPortal.Shared.Models;
//using Microsoft.EntityFrameworkCore;
//using Microsoft.EntityFrameworkCore.Infrastructure;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.Logging;
//using Moq;
//using NUnit.Framework;
//using OrderTracking.API.Interfaces;
//using OrderTracking.API.Models;
//using OrderTracking.API.Services;

//namespace OrderTracking.API.Tests
//{
//    [TestFixture]
//    public class OrdersServiceInMemoryTests : IDisposable
//    {
//        [OneTimeSetUp]
//        public void OneTimeSetUp()
//        {
//            _config = InMemoryEFHelpers.MockIConfigurationForOrdersConnection();
//        }

//        private readonly DbConnection _connection;
//        private Mock<IConfiguration> _config;

//        public OrdersServiceInMemoryTests()
//        {
//            _contextOptions = new DbContextOptionsBuilder<OrderContext>()
//                .UseSqlite(InMemoryEFHelpers.CreateInMemoryDatabase())
//                .Options;

//            _connection = RelationalOptionsExtension.Extract(_contextOptions).Connection;

//            Seed();
//        }

//        private DbContextOptions<OrderContext> _contextOptions { get; }

//        [Test]
//        public async Task GetItemAsync_SufficientPermissions_OrderIsNotNull()
//        {
//            await using var context = new OrderContext(_contextOptions);
//            var service = MakeOrdersService(context);
//            var user = MakeManufacturingUser();

//            // Act
//            var order = await service.GetItemAsync("0xFakeId", user);

//            // Assert
//            Assert.That(order, Is.Not.Null);
//        }

//        [Test]
//        public async Task GetItemAsync_DistrictMatches_OrderIsNotNull()
//        {
//            await using var context = new OrderContext(_contextOptions);
//            // Arrange
//            var service = MakeOrdersService(context);
//            var user = new UserProfile();
//            user.DistrictIds.Add("123");

//            // Act
//            var order = await service.GetItemAsync("0xFakeId", user);

//            // Assert
//            Assert.That(order, Is.Not.Null);
//        }

//        [Test]
//        public async Task GetItemAsync_CustomerAccountMatches_OrderIsNotNull()
//        {
//            await using var context = new OrderContext(_contextOptions);
//            // Arrange
//            var service = MakeOrdersService(context);
//            var user = new UserProfile();
//            user.CustomerAccounts.Add("abc");

//            // Act
//            var order = await service.GetItemAsync("0xAnotherFakeId", user);

//            // Assert
//            Assert.That(order, Is.Not.Null);
//        }

//        [Test]
//        public async Task GetItemAsync_InsufficientPermissions_OrderIsNull()
//        {
//            await using var context = new OrderContext(_contextOptions);
//            // Arrange
//            var service = MakeOrdersService(context);
//            var user = new UserProfile();

//            // Act
//            var order = await service.GetItemAsync("0xFakeId", user);

//            // Assert
//            Assert.That(order, Is.Null);
//        }

//        [Test]
//        public async Task GetOrdersForUser_ManufacturingUser_GetsAllOrders()
//        {
//            await using var context = new OrderContext(_contextOptions);
//            // Arrange
//            var service = MakeOrdersService(context);
//            var user = MakeManufacturingUser();

//            // Act
//            var orders = service.GetOrdersForUser(user);

//            // Assert
//            Assert.That(await orders.CountAsync(), Is.EqualTo(2));
//        }

//        [Test]
//        public async Task GetOrdersForUser_EngineeringUser_GetsAllOrders()
//        {
//            await using var context = new OrderContext(_contextOptions);
//            // Arrange
//            var service = MakeOrdersService(context);
//            var user = MakeEngineeringUser();

//            // Act
//            var orders = service.GetOrdersForUser(user);

//            // Assert
//            Assert.That(await orders.CountAsync(), Is.EqualTo(2));
//        }

//        [Test]
//        public async Task GetOrdersForUser_ManufacturingAndEngineeringUser_GetsAllOrders()
//        {
//            await using var context = new OrderContext(_contextOptions);
//            // Arrange
//            var service = MakeOrdersService(context);
//            var user = MakeManufacturingUser();
//            user.Roles.Add("WMO:EngineeringUser");

//            // Act
//            var orders = service.GetOrdersForUser(user);

//            // Assert
//            Assert.That(await orders.CountAsync(), Is.EqualTo(2));
//        }

//        [Test]
//        public async Task GetOrdersForUser_UserWithDistricts_GetsOnlyOrdersWithThoseDistricts()
//        {
//            await using var context = new OrderContext(_contextOptions);
//            // Arrange
//            var service = MakeOrdersService(context);
//            var user = new UserProfile(){Id = "<EMAIL>"};
//            user.DistrictIds.Add("123");

//            // Act
//            var orders = service.GetOrdersForUser(user);

//            // Assert
//            Assert.That(await orders.AnyAsync(), Is.True);
//            Assert.That(orders.ToList().All(o => o.INTERNALDISTRICTNUMBER == "123"), Is.True);
//        }

//        [Test]
//        public async Task GetOrdersForUser_UserWithCustomerAccount_GetsOnlyOrdersWithThatCustomerAccount()
//        {
//            await using var context = new OrderContext(_contextOptions);
//            // Arrange
//            var service = MakeOrdersService(context);
//            var user = new UserProfile(){Id = "<EMAIL>"};
//            user.CustomerAccounts.Add("abc");

//            // Act
//            var orders = service.GetOrdersForUser(user);

//            // Assert
//            Assert.That(await orders.AnyAsync(), Is.True);
//            Assert.That(orders.ToList().All((o => o.EXTERNALCUSTOMER == "abc")), Is.True);
//        }

//        [Test]
//        public async Task GetOrdersForUser_InsufficientPermissions_GetsNothing()
//        {
//            await using var context = new OrderContext(_contextOptions);
//            // Arrange
//            var service = MakeOrdersService(context);
//            var user = new UserProfile();

//            // Act
//            var orders = service.GetOrdersForUser(user);

//            // Assert
//            Assert.That(await orders.AnyAsync(), Is.False);
//        }

//        public void Dispose()
//        {
//            _connection.Dispose();
//        }

//        private void Seed()
//        {
//            using var context = new OrderContext(_contextOptions);
//            context.Database.EnsureDeleted();
//            context.Database.EnsureCreated();

//            var order123 = new Order
//            {
//                Id = "0xFakeId",
//                INTERNALDISTRICTNUMBER = "123",
//                SALESLINE_RECID = 123465798
//            };

//            var orderAbc = new Order
//            {
//                Id = "0xAnotherFakeId",
//                EXTERNALCUSTOMER = "abc",
//                SALESLINE_RECID = 987654321
//            };

//            context.AddRange(order123, orderAbc);
//            context.SaveChanges();
//        }

//        private static UserProfile MakeManufacturingUser()
//        {
//            var user = new UserProfile();
//            user.Roles.Add("WMO:ManufacturingUser");
//            return user;
//        }

//        private static UserProfile MakeEngineeringUser()
//        {
//            var user = new UserProfile();
//            user.Roles.Add("WMO:EngineeringUser");
//            return user;
//        }

//        private OrdersService MakeOrdersService(OrderContext context) =>
//            // Arrange
//            new OrdersService(Mock.Of<ILogger<IOrdersService>>(), _config.Object, Mock.Of<IWMOBlobStorageService>(),
//                context);
//    }
//}