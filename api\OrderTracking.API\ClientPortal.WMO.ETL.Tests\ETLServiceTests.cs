using ClientPortal.WMO.ETL.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;

namespace ClientPortal.WMO.ETL.Tests
{
    public class ETLServiceTests
    {
        [SetUp]
        public void Setup()
        {
        }

        [Test]
        public void CanConstructETLService()
        {
            var logger = Mock.Of<ILogger<IETLService>>();
            var config = Mock.Of<IConfiguration>();
            var etlSettings = Mock.Of<IETLSettingsService>();
            Assert.DoesNotThrow(() => new ETLService(logger, config, etlSettings));
        }
    }
}