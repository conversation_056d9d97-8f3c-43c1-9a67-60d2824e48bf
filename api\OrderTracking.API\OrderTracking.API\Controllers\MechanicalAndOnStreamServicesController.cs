﻿//using System.Threading.Tasks;
//using ClientPortal.Shared.Models.MOS;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.EntityFrameworkCore;

//namespace OrderTracking.API.Controllers
//{
//    /// <summary>
//    ///     Controller setup
//    /// </summary>
//    [Route("api/[controller]")]
//    [ApiController]
//    public class MechanicalAndOnStreamServicesController : ControllerBase
//    {
//        private readonly MOSContext _context;

//        /// <summary>
//        ///     Master Database for MOS?
//        /// </summary>
//        /// <param name="context"></param>
//        public MechanicalAndOnStreamServicesController(MOSContext context)
//        {
//            _context = context;
//        }

//        /// <summary>
//        ///     Get Dimensional sheets (DS100, ..., DS901)
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("DimensionalSheets")]
//        public async Task<IActionResult> GetDimensionalSheets()
//        {
//            var sheets = await _context
//                .DimensionalSheets
//                .Include(s => s.Package)
//                .ToListAsync();
//            return Ok(sheets);
//        }

//        /// <summary>
//        ///     Get a data sheet package with a list of data sheets within it.
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("DataSheetPackages")]
//        public async Task<IActionResult> GetDataSheetPackages()
//        {
//            var packages = await _context
//                .DataSheetPackages
//                .Include(p => p.DataSheets)
//                .ToListAsync();
//            return Ok(packages);
//        }

//        /// <summary>
//        ///     Get a single data sheet package with all associated data sheets
//        /// </summary>
//        /// <param name="id"></param>
//        /// <returns></returns>
//        [HttpGet("DataSheetPackages/{id:int}")]
//        public async Task<IActionResult> GetDataSheetPackageById(int id)
//        {
//            var package = await _context
//                .DataSheetPackages
//                .FindAsync(id);
//            await _context
//                .Entry(package)
//                .Collection(p => p.DataSheets)
//                .LoadAsync();
//            return Ok(package);
//        }

//        /// <summary>
//        ///     Get all data sheets
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("DataSheets")]
//        public async Task<IActionResult> GetDataSheets()
//        {
//            var sheets = await _context
//                .DataSheets
//                .Include(s => s.Package)
//                .ToListAsync();
//            return Ok(sheets);
//        }
//    }
//}