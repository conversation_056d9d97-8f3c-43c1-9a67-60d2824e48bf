import { Component, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Observable } from 'rxjs';
import { delay, map, shareReplay, switchMap, take } from 'rxjs/operators';
import { LeakReportingGridComponent } from '../../components';
import {
    LeakReport,
    LeakReportGridRow,
    LeakReportInfo,
    LeakReportWorkDetails,
    PhotoDelete,
    PhotoDescriptionUpdate
} from '../../models';
import { ApmService } from '../../services';

@Component({
    selector: 'app-leak-reporting',
    templateUrl: './leak-reporting.component.html',
    styleUrls: ['./leak-reporting.component.scss']
})
export class LeakReportingComponent {
    private _reports = new BehaviorSubject<LeakReport[]>([]);
    get reports$(): Observable<LeakReport[]> {
        return this._reports.asObservable().pipe(shareReplay());
    }
    selectedReport$: Observable<LeakReport>;
    @ViewChild(LeakReportingGridComponent) grid: LeakReportingGridComponent;

    constructor(
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService
    ) {
        this._apm
            .getLeakReports()
            .subscribe((reports) => this._reports.next(reports));
        this._apm.buSelected$
            .pipe(switchMap((_) => this._apm.getLeakReports()))
            .subscribe((reports) => this._reports.next(reports));
    }

    onAddingLeakReport(report: LeakReportGridRow) {
        this._apm
            .createLeakReport(report)
            .pipe(
                delay(300),
                switchMap(() => this._apm.getLeakReports())
            )
            .subscribe((reports) => {
                this._reports.next(reports);
                this._toasts.success(
                    'Leak Report successfully created',
                    'Created'
                );
            });
    }

    onReportSelected(reportID: string) {
        this.selectedReport$ = this.reports$.pipe(
            map((reports) => reports.find((report) => report.id === reportID))
        );
    }

    onStatusChanged(status: string) {
        this.selectedReport$
            .pipe(
                take(1),
                switchMap((report) =>
                    this._apm.updateLeakReportStatus(report.id, status)
                ),
                // TODO: This all of the sudden became necessary as of 12/7/2021.  The SetStatus
                // method in the NuGet package was changed to take an enum instead of a string,
                // and now, if we ask for leak reports immediately after updating one of them,
                // it includes the old version of the one we just updated.  Delaying for
                // half a second gives us the right version of the one we updated. *shrug*
                // - JDS
                delay(500),
                switchMap(() => this._apm.getLeakReports())
            )
            .subscribe((reports) => {
                this._reports.next(reports);
                this._toasts.success(
                    'Successfully updated Leak Report status',
                    'Success'
                );
            });
    }

    onLeakReportInfoSaving(info: Partial<LeakReportInfo>) {
        this.selectedReport$
            .pipe(
                take(1),
                switchMap((report) =>
                    this._apm.updateLeakReportInfo(report.id, info)
                ),
                delay(500),
                switchMap(() => this._apm.getLeakReports())
            )
            .subscribe((reports) => {
                this._reports.next(reports);
                this._toasts.success(
                    'Successfully updated leak report information',
                    'Success'
                );
            });
    }

    onLeakReportWorkDetailsSaving(info: Partial<LeakReportWorkDetails>) {
        this.selectedReport$
            .pipe(
                take(1),
                switchMap((report) =>
                    this._apm.updateLeakReportWorkDetails(report.id, info)
                ),
                switchMap(() => this._apm.getLeakReports())
            )
            .subscribe((reports) => {
                this._reports.next(reports);
                this._toasts.success(
                    'Successfully updated leak report work details',
                    'Success'
                );
            });
    }

    onPhotoDescriptionUpdate(update: PhotoDescriptionUpdate) {
        this._apm
            .updatePhotoDescription(update)
            .pipe(
                delay(500),
                switchMap(() => this._apm.getLeakReports())
            )
            .subscribe((reports) => {
                this._reports.next(reports);
                this._toasts.success(
                    'Photo Description successfully updated',
                    'Description saved'
                );
            });
    }

    onPhotoDelete(photoDelete: PhotoDelete) {
        this._apm
            .deletePhoto(photoDelete)
            .pipe(
                delay(500),
                switchMap(() => this._apm.getLeakReports())
            )
            .subscribe((reports) => {
                this._reports.next(reports);
                this._toasts.success(
                    'Photo was successfully deleted',
                    'Photo deleted'
                );
            });
    }
}
