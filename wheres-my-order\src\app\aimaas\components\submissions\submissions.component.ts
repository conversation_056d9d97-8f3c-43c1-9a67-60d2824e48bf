import { Component, Input, OnInit } from '@angular/core';
import { UserProfile } from '../../../profile/models';
import { UsersService } from '../../../shared/services';
import {
    ToastNotificationService,
    ToastType
} from '../../../shared/services/toast-notification.service';
import { CredoSoftService } from '../../services/credo-soft.service';
@Component({
    selector: 'app-submissions',
    templateUrl: './submissions.component.html',
    styleUrls: ['./submissions.component.scss']
})
export class SubmissionsComponent implements OnInit {
    submissions: any[];
    currentUser: UserProfile;
    @Input() selectedAssetId: any;
    constructor(
        private readonly credoService: CredoSoftService,
        private readonly _toasts: ToastNotificationService,
        private readonly _users: UsersService
    ) {}

    ngOnInit(): void {
        this._users.currentProfile$.subscribe((data) => {
            this.currentUser = data;
        });
        this.getSubmissions();
    }
    getSubmissions() {
        if (this.selectedAssetId)
            this.credoService
                .getSubmissions(this.selectedAssetId)
                .subscribe((data) => {
                    this.submissions = data;
                });
    }
    commentCellTemplate(data) {
        if (data.comment == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(data.comment, 'text/html');
        return doc.documentElement.textContent ?? ' ';
    }
    submissionDeleted(event) {
        let submissionId = event.key?.id;
        this.credoService.deleteSubmission(submissionId).subscribe(
            (result) => {
                this._toasts.show('', result?.message, ToastType.success, 3000);
                this.getSubmissions();
            },
            (err) => {
                this._toasts.show(
                    'Error',
                    err.error.message,
                    ToastType.error,
                    3000
                );
                this.getSubmissions();
            }
        );
    }
}
