import { Component, EventEmitter, Output, ViewChild } from '@angular/core';
import { DxPopupComponent } from 'devextreme-angular';
import { ClickEvent } from 'devextreme/ui/button';
import { confirm } from 'devextreme/ui/dialog';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, filter, firstValueFrom, map, switchMap } from 'rxjs';
import {
    AssetCreationComponent,
    WorkOrderCreationComponent
} from '../../components';
import { NewTask, TaskUpdate } from '../../models';
import { ApmService } from '../../services';
import { NewAssetTemp } from '../asset-creation/asset-creation.component';
import { TasksTabComponent } from '../tasks-tab/tasks-tab.component';
import { NewWorkOrderTemp } from '../work-order-creation/work-order-creation.component';

type CreationStep = 'asset' | 'workOrder' | 'tasks' | undefined;

@Component({
    selector: 'app-creation-workflow',
    templateUrl: './creation-workflow.component.html',
    styleUrls: ['./creation-workflow.component.scss']
})
export class CreationWorkflowComponent {
    private _creationStep = new BehaviorSubject<CreationStep>(undefined);
    private readonly _creationWorkOrderId = new BehaviorSubject<string>(
        undefined
    );

    createdAssetId: string;
    assetProjectId: string;
    createdWorkOrderId: string;

    @ViewChild(DxPopupComponent) popup: DxPopupComponent;
    @ViewChild(AssetCreationComponent) assetCreation: AssetCreationComponent;
    @ViewChild(WorkOrderCreationComponent)
    workOrderCreation: WorkOrderCreationComponent;
    @ViewChild(TasksTabComponent) taskCreation: TasksTabComponent;

    @Output() assetCreated = new EventEmitter<void>();
    @Output() workOrderCreated = new EventEmitter<void>();

    saving = false;

    readonly creationWorkOrder$ = this._creationWorkOrderId.pipe(
        filter(Boolean),
        switchMap((id) => this._apm.getWorkOrder(id, this.assetProjectId))
    );
    readonly users$ = this._apm.getUsers();

    readonly title$ = this._creationStep
        .asObservable()
        .pipe(
            map((step) =>
                step === 'asset'
                    ? 'Create Asset'
                    : step === 'workOrder'
                        ? 'Create Work Order'
                        : step === 'tasks'
                            ? 'Create Tasks'
                            : undefined
            )
        );

    readonly creationStep$ = this._creationStep.asObservable();

    constructor(
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService
    ) { }

    start() {
        this._creationStep.next('asset');
        this.popup.instance.show();
    }

    startWorkOrderCreation(assetId?: string) {
        this.createdAssetId = assetId;
        this._creationStep.next('workOrder');
        this.popup.instance.show();
    }

    onSaveAndCreateWorkOrderClicked = async (e: ClickEvent) => {
        if (this.assetCreation) {
            const isValid = this.assetCreation.validate();
            if (isValid) {
                try {
                    this.saving = true;
                    const newAsset = this.assetCreation.formData;
                    const resultAsset = await this.createAsset(newAsset);
                    this.createdAssetId = resultAsset.id;
                    this.assetProjectId = newAsset.projectId;
                    this._creationStep.next('workOrder');
                    this.assetCreated.next();
                } finally {
                    this.saving = false;
                }
            }
        }
    };

    onSaveAndCreateTasksClicked = async (e: ClickEvent) => {
        if (this.workOrderCreation) {
            const isValid = this.workOrderCreation.validate();
            if (isValid) {
                try {
                    this.saving = true;
                    const newWorkOrder = this.workOrderCreation.formData;
                    const result = await this.createWorkOrder(newWorkOrder);
                    this.createdWorkOrderId = result.id;
                    this._creationWorkOrderId.next(result.id);
                    this._creationStep.next('tasks');
                    this.workOrderCreated.next();
                } finally {
                    this.saving = false;
                }
            }
        }
    };

    onSaveCreationClicked = async (e: ClickEvent) => {
        if (this._creationStep.value === 'asset') {
            if (this.assetCreation) {
                const isValid = this.assetCreation.validate();
                if (isValid) {
                    try {
                        this.saving = true;
                        const newAsset = this.assetCreation.formData;
                        await this.createAsset(newAsset);
                        this.close();
                        this.assetCreated.next();
                    } finally {
                        this.saving = false;
                    }
                }
            }
        } else if (this._creationStep.value === 'workOrder') {
            if (this.workOrderCreation) {
                const isValid = this.workOrderCreation.validate();
                if (isValid) {
                    try {
                        this.saving = true;
                        const newWorkOrder = this.workOrderCreation.formData;
                        await this.createWorkOrder(newWorkOrder);
                        this.close();
                        this.workOrderCreated.next();
                    } finally {
                        this.saving = false;
                    }
                }
            }
        }
    };

    onCancelCreationClicked = async (e: ClickEvent) => {
        if (
            this._creationStep.value === 'tasks' &&
            this.taskCreation.dataGrid.instance.getVisibleRows().length <= 0
        ) {
            const response = await confirm(
                'Are you sure you want to close this workflow without creating any tasks for the newly created work order?',
                'Are you sure?'
            );
            if (response) {
                this.close();
            }
        } else {
            this.close();
        }
    };

    newTask(newTask: NewTask) {
        this._apm.createTask(newTask).subscribe((task) => {
            this._creationWorkOrderId.next(this._creationWorkOrderId.value);
            this._toasts.success(
                'Task was added successfully',
                'Saved successfully'
            );
        });
    }

    taskUpdated(taskUpdate: TaskUpdate) {
        this._apm.updateTask(taskUpdate).subscribe((wos) => {
            this._creationWorkOrderId.next(this._creationWorkOrderId.value);
            this._toasts.success(
                'Task was updated successfully',
                'Saved successfully'
            );
        });
    }

    close() {
        this._creationStep.next(undefined);
        this.createdAssetId = undefined;
        this.assetProjectId = undefined;
        this.createdWorkOrderId = undefined;
        this.popup.instance.hide();
    }

    private async createAsset(newAsset: NewAssetTemp) {
        return await firstValueFrom(
            this._apm.postAsset({
                assetId: newAsset.assetId,
                assetName: newAsset.assetName,
                category: newAsset.category,
                endLat: newAsset.endLat,
                endLong: newAsset.endLong,
                projectId: newAsset.projectId,
                startLat: newAsset.startLat,
                startLong: newAsset.startLong,
                id: undefined,
                locationId: undefined
            })
        );
    }

    private async createWorkOrder(newWorkOrder: NewWorkOrderTemp) {
        return await firstValueFrom(
            this._apm.createWorkOrder({
                assetDatabaseId: newWorkOrder.assetId,
                projectId: newWorkOrder.projectId,
                dueDate: newWorkOrder.dueDate,
                facilityName: newWorkOrder.facilityName,
                plannedEnd: newWorkOrder.plannedEnd,
                plannedStart: newWorkOrder.plannedStart,
                status: newWorkOrder.status
            })
        );
    }
}
