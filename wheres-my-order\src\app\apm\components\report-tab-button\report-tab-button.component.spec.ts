import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrModule } from 'ngx-toastr';
import { BehaviorSubject, of } from 'rxjs';
import { UserProfile } from '../../../profile/models';
import { UsersService } from '../../../shared/services';
import { ApmService } from '../../services';
import { ReportTabButtonComponent } from './report-tab-button.component';

describe('ReportTabButtonComponent', () => {
    let component: ReportTabButtonComponent;
    let fixture: ComponentFixture<ReportTabButtonComponent>;
    let userSubject = new BehaviorSubject<UserProfile>(new UserProfile());
    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [HttpClientTestingModule, ToastrModule.forRoot()],
            providers: [
                { provide: ApmService, useValue: {} },
                {
                    provide: UsersService,
                    useValue: { currentProfile$: of(new UserProfile()) }
                }
            ],
            declarations: [ReportTabButtonComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ReportTabButtonComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
