import { Photo } from './photo';

export interface SixFiftyThreeWalkDownTask {
    displayName: string;
    sectionIdentification: SectionIdentification;
    sectionGeneralInformation: SectionGeneralInformation;
    sectionRepairsandAlterations: SectionRepairsandAlterations;
    sectionOperatingDesignConditions: SectionOperatingDesignConditions;
    sectionComponents: SectionComponents;
}

export interface SectionComponents {
    displayName: string;
    sectionShellCourses: SectionShellCourses;
    sectionTankBottomFloor: SectionTankBottomFloor;
    sectionTankRoof: SectionTankRoof;
    sectionNozzles: SectionNozzles;
}

export interface SectionNozzles {
    name: string;
    changeLog: ChangeLog;
    currentEntries: SectionNozzlesCurrentEntry[];
    pendingEntries: SectionNozzlesCurrentEntry[];
}

export interface ChangeLog {
    entries: ChangeLogEntry[];
}

export interface ChangeLogEntry {
    action?: Action;
    key: string;
    value: string;
    timeChanged: Date;
    userName: string;
}

export enum Action {
    Added = 'Added',
}

export interface SectionNozzlesCurrentEntry {
    attributeNumber: Attribute;
    attributeType: AttributeRepairAlterationPlatesLegible;
    attributeMaterial_Spec_and_Grade: AttributeRepairAlterationPlatesLegible;
    attributePipe_Size: AttributeConstructionMethodClass;
    attributePipe_Schedule: AttributeConstructionMethodClass;
    attributeFlange_Rating: AttributeConstructionMethodClass;
    attributeReinforcement_pad_type: AttributeRepairAlterationPlatesLegible;
    attributeReinforcement_pad_dimensions: AttributeRepairAlterationPlatesLegible;
    attributeReinforcement_pad_thickness: Attribute;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface AttributeConstructionMethodClass {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ChangeLog;
    currentValue: string[];
    pendingValue: string[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: ChangeLog;
    commentChangeLog: CommentChangeLogClass;
    comment: null | string;
}

export interface CommentChangeLogClass {
    entries: CommentChangeLogEntry[];
    pendingChange: null;
    pendingChangeCopy: null;
}

export interface CommentChangeLogEntry {
    key: string;
    value: number | string;
    timeChanged: Date;
    userName: string;
}

export interface Option {
    isCommentRequired: boolean;
    value: string;
    description: null;
}

export interface AttributeRepairAlterationPlatesLegible {
    areCommentsRequired: boolean;
    attributeType: AttributeType;
    currentValue: null | string;
    pendingValue: null;
    currentPendingOrValue: null | string;
    valueChangeLog: CommentChangeLogClass;
    displayName: string;
    databaseName: null | string;
    photos: Photo[];
    photoChangeLog: ChangeLog;
    commentChangeLog: CommentChangeLogClass;
    comment: null | string;
    hasOtherOption?: boolean;
    options?: Option[];
}

export enum AttributeType {
    Coordinate = 'Coordinate',
    Date = 'Date',
    Double = 'Double',
    Integer = 'Integer',
    PredefinedValue = 'PredefinedValue',
    String = 'String',
}

export interface Description {
    areCommentsRequired: boolean;
    attributeType: AttributeType;
    currentValue: null | string;
    pendingValue: null;
    currentPendingOrValue: null | string;
    valueChangeLog: CommentChangeLogClass;
    displayName: string;
    databaseName: null | string;
    photos: any[];
    photoChangeLog: ChangeLog;
    commentChangeLog: CommentChangeLogClass;
    comment: null;
}

export interface Attribute {
    areCommentsRequired: boolean;
    attributeType: AttributeType;
    allowNegatives?: boolean;
    unit?: Unit | null;
    currentValue: number | null | string;
    pendingValue: null;
    currentPendingOrValue: number | null | string;
    valueChangeLog: CommentChangeLogClass;
    displayName: string;
    databaseName: null | string;
    photos: Photo[];
    photoChangeLog: ChangeLog;
    commentChangeLog: CommentChangeLogClass;
    comment: null | string;
    hasOtherOption?: boolean;
    options?: Option[];
}

export enum Unit {
    Bbl = 'BBL',
    Ft = 'ft',
    In = 'in',
    Psi = 'psi',
}

export interface SectionShellCourses {
    name: string;
    changeLog: ChangeLog;
    currentEntries: SectionShellCoursesCurrentEntry[];
    pendingEntries: SectionShellCoursesCurrentEntry[];
}

export interface SectionShellCoursesCurrentEntry {
    attributeNumber: Attribute;
    attributeMaterial_Spec_and_Grade: AttributeRepairAlterationPlatesLegible;
    attributeAllowable_Stress_at_Temperature: Attribute;
    attributeNominal_Thickness: Attribute;
    attributeCorrosion_Allowance: Attribute;
    attributeLength_or_Height: Attribute;
    attributeJoint_Efficiency: Attribute;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface SectionTankBottomFloor {
    attributeType: AttributeRepairAlterationPlatesLegible;
    attributeMaterial_Spec_and_Grade: AttributeRepairAlterationPlatesLegible;
    attributeNominal_thickness_annular_ring: Attribute;
    attributeNominal_thickness_sketch_plates: Attribute;
    attributeNominal_thickness_inner_plates: Attribute;
    attributeCorrosion_Allowance: Attribute;
    displayName: string;
}

export interface SectionTankRoof {
    name: string;
    changeLog: ChangeLog;
    currentEntries: SectionTankRoofCurrentEntry[];
    pendingEntries: SectionTankRoofCurrentEntry[];
}

export interface SectionTankRoofCurrentEntry {
    attributeType: AttributeConstructionMethodClass;
    attributeMaterial_Spec_and_Grade: Attribute;
    attributeNominal_thickness_roof: Attribute;
    attributeCorrosion_Allowance: Attribute;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface SectionGeneralInformation {
    displayName: string;
    sectionPhotos: SectionPhotos;
    sectionDesign: SectionDesign;
    sectionService: SectionService;
}

export interface SectionDesign {
    attributeDesign_Code: AttributeRepairAlterationPlatesLegible;
    attributeCode_Year: AttributeRepairAlterationPlatesLegible;
    attributeAddendum: AttributeRepairAlterationPlatesLegible;
    attributeMaximum_Fill_Height: Attribute;
    attributeDiameter: Attribute;
    attributeHeight: Attribute;
    attributeTank_Volume_in_BBL: Attribute;
    attributeConstruction_Method: AttributeConstructionMethodClass;
    attributeOrientation: AttributeRepairAlterationPlatesLegible;
    attributeRT: AttributeRepairAlterationPlatesLegible;
    attributeInstallation_Date: AttributeRepairAlterationPlatesLegible;
    attributeIn_service_Date: AttributeRepairAlterationPlatesLegible;
    attributePID_Number: AttributeRepairAlterationPlatesLegible;
    attributeConstructionDesign_Drawing_Number: AttributeRepairAlterationPlatesLegible;
    attributeLowest_Flange_Rating: AttributeRepairAlterationPlatesLegible;
    displayName: string;
    sectionInspectionOpenings: SectionInspectionOpenings;
    sectionInspection: SectionInspection;
    sectionDataPlate: SectionDataPlate;
    sectionManufacturerFabricator: SectionManufacturerFabricator;
}

export interface SectionDataPlate {
    attributeAttached: AttributeRepairAlterationPlatesLegible;
    attributeLegible: Attribute;
    displayName: string;
}

export interface SectionInspection {
    attributeInspection_Code: AttributeRepairAlterationPlatesLegible;
    attributeYear: Attribute;
    attributeAddendum: AttributeRepairAlterationPlatesLegible;
    displayName: string;
}

export interface SectionInspectionOpenings {
    name: string;
    changeLog: ChangeLog;
    currentEntries: SectionInspectionOpeningsCurrentEntry[];
    pendingEntries: SectionInspectionOpeningsCurrentEntry[];
}

export interface SectionInspectionOpeningsCurrentEntry {
    attributeOpening_Type: Attribute;
    attributeOpening_Number: Attribute;
    attributeOpening_Size: Attribute;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface SectionManufacturerFabricator {
    attributeName: Attribute;
    attributeDate: Attribute;
    attributeSerial_Number: AttributeRepairAlterationPlatesLegible;
    displayName: string;
}

export interface SectionPhotos {
    attributeFront: AttributeBackClass;
    attributeBack: AttributeBackClass;
    attributeLeft: AttributeLeftClass;
    attributeRight: AttributeLeftClass;
    displayName: string;
}

export interface AttributeBackClass {
    areCommentsRequired: boolean;
    attributeType: string;
    displayName: string;
    databaseName: string;
    photos: Photo[];
    photoChangeLog: ChangeLog;
    commentChangeLog: CommentChangeLogClass;
    comment: null;
}

export interface AttributeLeftClass {
    areCommentsRequired: boolean;
    attributeType: string;
    displayName: string;
    databaseName: string;
    photos: Photo[];
    photoChangeLog: ChangeLog;
    commentChangeLog: CommentChangeLogClass;
    comment: null;
}

export interface SectionService {
    attributeServiceProductContents: Attribute;
    attributeSpecific_Gravity: Attribute;
    attributeIntended_Service: Attribute;
    displayName: string;
}

export interface SectionIdentification {
    attributeName: Attribute;
    attributeNumber_or_ID: Attribute;
    attributeAsset_Type: Attribute;
    attributeEquipment_Description: Attribute;
    attributeLast_known_inspection_date: Attribute;
    attributeLocation: Attribute;
    attributeGIS_Location: Attribute;
    displayName: string;
}

export interface SectionOperatingDesignConditions {
    attributeTank_equipped_with_Leak_Detection: Attribute;
    attributeIs_the_tank_equipped_with_VRU: Attribute;
    attributeOperation_Status: Attribute;
    attributeDesign_Temp: Attribute;
    attributeCurrent_Operating_Temperature: Attribute;
    attributeCurrent_Fill_level_if_available: Attribute;
    attributeCurrent_service: Attribute;
    displayName: string;
    sectionTankOutOfServiceRequirements: SectionTankOutOfServiceRequirements;
    sectionRegulatoryRequirements: SectionRegulatoryRequirements;
}

export interface SectionRegulatoryRequirements {
    name: string;
    changeLog: ChangeLog;
    currentEntries: SectionRegulatoryRequirementsCurrentEntry[];
    pendingEntries: SectionRegulatoryRequirementsCurrentEntry[];
}

export interface SectionRegulatoryRequirementsCurrentEntry {
    attributeJurisdiction_Regulatory_agency: Description;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface SectionTankOutOfServiceRequirements {
    attribute653AWQ331: Attribute;
    displayName: string;
}

export interface SectionRepairsandAlterations {
    attributeDoes_the_asset_have_a_repair_or_alteration_plate: Attribute;
    attributeRepairAlteration_Plates_Legible: AttributeRepairAlterationPlatesLegible;
    displayName: string;
    sectionRepairs: SectionRepairs;
}

export interface SectionRepairs {
    name: string;
    changeLog: ChangeLog;
    currentEntries: SectionRepairsCurrentEntry[];
    pendingEntries: SectionRepairsCurrentEntry[];
}

export interface SectionRepairsCurrentEntry {
    attributeDate_Repaired_or_Altered: Description;
    attributeRepairAlteration_organization: Attribute;
    attributePurpose_of_repairalteration: Description;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}
