﻿using System;
using System.Configuration;
using NUnit.Framework;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Edge;
using OpenQA.Selenium.Firefox;
using OpenQA.Selenium.IE;

namespace ClientPortal.Utils
{
    public class SetupTeardown
    {
        string browserName = ConfigurationManager.AppSettings["BrowserName"];
        string environment = ConfigurationManager.AppSettings["Environment"];
        string devurl = ConfigurationManager.AppSettings["DevURL"];
        string qaurl = ConfigurationManager.AppSettings["QAURL"];
        string stageurl = ConfigurationManager.AppSettings["StageURL"];
        string driverpath = System.AppDomain.CurrentDomain.BaseDirectory + "driver";
        public static IWebDriver _driver;

        [OneTimeSetUp]
        public void Setup()
        {
            switch (browserName)
            {
                case "Chrome":
                    ChromeOptions options = new ChromeOptions();
                    options.AddArguments("start-maximized");
                    options.AddArguments("--incognito");
                    _driver = new ChromeDriver(driverpath, options);
                    break;
                case "Firefox":
                    System.Environment.SetEnvironmentVariable("webdriver.gecko.driver", driverpath);
                    _driver = new FirefoxDriver();
                    _driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(30);
                    _driver.Manage().Window.Maximize();
                    break;
                case "IE":
                    System.Environment.SetEnvironmentVariable("webdriver.ie.driver", driverpath);
                    _driver = new InternetExplorerDriver();
                    _driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(30);
                    _driver.Manage().Window.Maximize();
                    break;
                case "Edge":
                    _driver = new EdgeDriver();
                    _driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(30);
                    _driver.Manage().Window.Maximize();
                    break;
            }
            if (_driver != null)
            {
                _driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(30);
                _driver.Url = devurl;
            }

            switch (environment)
            {
                case "DEV":
                    _driver.Url = devurl;
                    break;

                case "QA":
                    _driver.Url = qaurl;
                    break;

                case "STAGE":
                    _driver.Url = stageurl;
                    break;
            }
        }

        [SetUp]
        public void TestSetup()
        {

        }

        [TearDown]
        public void TestTeardown()
        {

        }

        [OneTimeTearDown]
        public void Teardown()
        {
            _driver.Close();
        }
    }
}
