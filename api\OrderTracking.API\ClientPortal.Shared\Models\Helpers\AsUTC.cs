using System;
using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.TypeConversion;
using Newtonsoft.Json;

namespace ClientPortal.Shared.Models.Helpers
{
    public class AsUTCDateTimeConverter : JsonConverter, ITypeConverter
    {
        #region ITypeConverter Implementation

        public object ConvertFromString(string text, IR<PERSON>erRow row, MemberMapData memberMapData) =>
            DateTime.SpecifyKind(DateTime.Parse(text), DateTimeKind.Utc);

        public string ConvertToString(object value, IWriterRow row, MemberMapData memberMapData) =>
            throw new NotImplementedException();

        #endregion

        #region JsonConverter Implementation

        public override bool CanConvert(Type objectType) => objectType == typeof(DateTime);

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue,
            JsonSerializer serializer) => throw new NotImplementedException();

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            var date = value as DateTime?;
            if (date.HasValue) date = DateTime.SpecifyKind(date.Value, DateTimeKind.Utc);
            writer.WriteValue(date);
        }

        #endregion
    }
}