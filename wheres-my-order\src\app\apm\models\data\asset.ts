import { GenericAttribute } from '../../../report/models/report-source.model';
import { IAttribute } from '../../../shared/models/attributes';
import { AssetCategory } from '../apm-asset-category';
import {
    AttributeConstructionMethod,
    CommentChangeLogOrValueChangeLog,
    PhotoChangeLogOrValueChangeLog,
    SectionManufacturerFabricator
} from './five-seventy-walkdown';
import { Photo } from './photo';
import {
    SectionRepairsandAlterations,
    SectionTankBottomFloor,
    SectionTankOutOfServiceRequirements
} from './six-fifty-three-walk-down';

export interface Asset {
    id: string;
    assetCategory: AssetCategory;
    locationId: string;
    area: IAttribute<string>;
    unit: IAttribute<string>;
    mediaItems: { changeLog: { entries: any[] } }; // TODO: entries should have a type at some point.  Let's figure that out as we go and replace `any` with a real type
    walkDown: WalkDown; // TODO: We will have to get clever with how we type this.  For now, I'm leaving blank because I'm focused on the assets grid.  But this will come in when we do the question sections in inspection results tabs
    assetPPE: AssetPPE;
    assetAccess: AssetAccessObj;
}

export class Asset {
    static findAssetNumber(walkdown: WalkDown, assetCategory: string) {
        return assetCategory === 'Piping'
            ? walkdown.sectionIdentification?.attributeNumber_or_Circuit_ID
                  ?.currentValue
            : walkdown.sectionIdentification?.attributeNumber_or_ID
                  ?.currentValue;
    }
}

export interface Option {
    isCommentRequired: boolean;
    value: string;
    description?: any;
}

export interface Entry {
    key: string;
    value: string;
    timeChanged: Date;
    userName: string;
}

export interface ValueChangeLog {
    entries: Entry[];
    pendingChange?: any;
    pendingChangeCopy?: any;
}

export interface PhotoChangeLog {
    entries: any[];
}

export interface CommentChangeLog {
    entries: any[];
    pendingChange?: any;
    pendingChangeCopy?: any;
}

export interface AttributeHardHat {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeEyeProtection {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: string[];
    pendingValue: string[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeHearingProtection {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: string[];
    pendingValue: string[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeFireRetardantClothing {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: string[];
    pendingValue: string[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeSafetyGloves {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: string[];
    pendingValue: string[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeSnakeChaps {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeFootProtection {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: string[];
    pendingValue: string[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeChemicalSuit {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: string[];
    pendingValue: string[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeFallProtection {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: string[];
    pendingValue: string[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeBreathingProtection {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: string[];
    pendingValue: string[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeAtmosphere {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: string[];
    pendingValue: string[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionPPERequirements {
    attributeHard_Hat: AttributeHardHat;
    attributeEye_Protection: AttributeEyeProtection;
    attributeHearing_Protection: AttributeHearingProtection;
    attributeFire_Retardant_Clothing: AttributeFireRetardantClothing;
    attributeSafety_Gloves: AttributeSafetyGloves;
    attributeSnake_Chaps: AttributeSnakeChaps;
    attributeFoot_Protection: AttributeFootProtection;
    attributeChemical_Suit: AttributeChemicalSuit;
    attributeFall_Protection: AttributeFallProtection;
    attributeBreathing_Protection: AttributeBreathingProtection;
    attributeAtmosphere: AttributeAtmosphere;
    displayName: string;
}

export interface AttributeAreThereAnyOnSiteLeaks {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeVehicleAccessibility {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeGeneralWork {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeGeneralHotWork {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeOpenFlameHotWork {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeControlAreaOfPermit {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: string;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeHazardousAreaPermit {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: string;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeHazardousMaterialPermit {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributePermitRequired {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeHoleWatchNeeded {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: string;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionConfinedSpaceRequirements {
    attributePermit_required: AttributePermitRequired;
    attributeHole_watch_needed: AttributeHoleWatchNeeded;
    displayName: string;
}

export interface SectionPermittingRequired {
    attributeGeneral_Work: AttributeGeneralWork;
    attributeGeneral_Hot_Work: AttributeGeneralHotWork;
    attributeOpen_Flame_Hot_Work: AttributeOpenFlameHotWork;
    attributeControl_Area_of_Permit: AttributeControlAreaOfPermit;
    attributeHazardous_Area_Permit: AttributeHazardousAreaPermit;
    attributeHazardous_Material_Permit: AttributeHazardousMaterialPermit;
    displayName: string;
    sectionConfinedSpaceRequirements: SectionConfinedSpaceRequirements;
}

export interface AttributeConditionsObservedOnSite {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: string[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributePowerAvailable {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: string;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeWaterAvailable {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeIsThereStandingWater {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: string;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeDrainageNeeded {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: string;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionStandingWater {
    attributeIs_there_standing_water: AttributeIsThereStandingWater;
    attributeDrainage_needed: AttributeDrainageNeeded;
    displayName: string;
}

export interface AttributeIsThereOvergrownVegation {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: string;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeAbatementRequired {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: string;
    pendingValue?: string;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionOvergrownvegetation {
    attributeIs_there_overgrown_vegation: AttributeIsThereOvergrownVegation;
    attributeAbatement_required: AttributeAbatementRequired;
    displayName: string;
}

export interface SectionPersonnelAccessConditions {
    attributeConditions_observed_on_site: AttributeConditionsObservedOnSite;
    attributePower_available: AttributePowerAvailable;
    attributeWater_available: AttributeWaterAvailable;
    displayName: string;
    sectionStandingWater: SectionStandingWater;
    sectionOvergrownvegetation: SectionOvergrownvegetation;
}

export interface SectionGeneralSiteConditions {
    attributeAre_there_any_on_site_leaks: AttributeAreThereAnyOnSiteLeaks;
    attributeVehicle_Accessibility: AttributeVehicleAccessibility;
    displayName: string;
    sectionPermittingRequired: SectionPermittingRequired;
    sectionPersonnelAccessConditions: SectionPersonnelAccessConditions;
}

export interface AssetPPE {
    displayName: string;
    sectionPPERequirements: SectionPPERequirements;
    sectionGeneralSiteConditions: SectionGeneralSiteConditions;
}

export interface AttributeDoesTheAssetHaveInsulation {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributePossibleAsbestos {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeJacketingType {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeInsulationType {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeInsulationRemovalRequired {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeHeatTracing {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionInsulation {
    attributeDoes_the_asset_have_insulation: AttributeDoesTheAssetHaveInsulation;
    attributePossible_asbestos: AttributePossibleAsbestos;
    attributeJacketing_type: AttributeJacketingType;
    attributeInsulation_type: AttributeInsulationType;
    attributeInsulation_removal_required: AttributeInsulationRemovalRequired;
    attributeHeat_tracing: AttributeHeatTracing;
    displayName: string;
}

export interface AttributeExistingInspectionPorts {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeInsulationPlugsMissing {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeAdditionalPortsNeeded {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionInspectionPorts {
    attributeExisting_inspection_ports: AttributeExistingInspectionPorts;
    attributeInsulation_plugs_missing: AttributeInsulationPlugsMissing;
    attributeAdditional_ports_needed: AttributeAdditionalPortsNeeded;
    displayName: string;
}

export interface SectionInspectionAccessConditions {
    displayName: string;
    sectionInsulation: SectionInsulation;
    sectionInspectionPorts: SectionInspectionPorts;
}
export interface AttributeCoatingType {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeCoatingCondition {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeCoatingConditionsObserved {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeCoatingRemovalRequired {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionCoating {
    attributeCoating_Type: AttributeCoatingType;
    attributeCoating_Condition: AttributeCoatingCondition;
    attributeCoating_Conditions_Observed: AttributeCoatingConditionsObserved;
    attributeCoating_Removal_Required: AttributeCoatingRemovalRequired;
    displayName: string;
}

export interface AttributeCorrosionIdentified {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeCorrosionRemovalRecommendation {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionCorrosion {
    attributeCorrosion_identified: AttributeCorrosionIdentified;
    attributeCorrosion_removal_recommendation: AttributeCorrosionRemovalRecommendation;
    displayName: string;
}

export interface SectionExternalSurfaceConditions {
    displayName: string;
    sectionCoating: SectionCoating;
    sectionCorrosion: SectionCorrosion;
}

export interface AttributeFixedEquipmentLaddersStairwaysPlatformsInstalled {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeAllComponentsUnder4FtInHeight {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeLadderRequirements {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeScaffoldingRequired {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeRopeAccessRequired {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeAerialLiftNeeded {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeAWAQ222 {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeGasPoweredPermitted {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeBatteryPoweredPermitted {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeClientRequiredProofOfTraining {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeClientProvidedOperator {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeEstimatedDistanceToAnyLiveElectricalOverheadLines {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit: string;
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionAerialLiftRequirements {
    attributeAerial_Lift_Needed: AttributeAerialLiftNeeded;
    attributeAWAQ222: AttributeAWAQ222;
    attributeGas_Powered_Permitted: AttributeGasPoweredPermitted;
    attributeBattery_Powered_Permitted: AttributeBatteryPoweredPermitted;
    attributeClient_required_proof_of_training: AttributeClientRequiredProofOfTraining;
    attributeClient_provided_operator: AttributeClientProvidedOperator;
    attributeEstimated_distance_to_any_live_electrical_overhead_lines: AttributeEstimatedDistanceToAnyLiveElectricalOverheadLines;
    displayName: string;
}

export interface SectionAccessibility {
    attributeFixed_Equipment_LaddersStairwaysPlatforms_Installed: AttributeFixedEquipmentLaddersStairwaysPlatformsInstalled;
    attributeAll_components_under_4_ft_in_height: AttributeAllComponentsUnder4FtInHeight;
    attributeLadder_Requirements: AttributeLadderRequirements;
    attributeScaffolding_required: AttributeScaffoldingRequired;
    attributeRope_access_required: AttributeRopeAccessRequired;
    displayName: string;
    sectionAerialLiftRequirements: SectionAerialLiftRequirements;
}

export interface AttributeIsTheAssetOutOfService {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeAreThereInspectionOpenings {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeInspectionOpeningTypes {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeSizeOfAllAccessibleOpenings {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeVentilationRequirements {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionInternalAccessRequirements {
    attributeAre_there_inspection_openings: AttributeAreThereInspectionOpenings;
    attributeInspection_opening_Types: AttributeInspectionOpeningTypes;
    attributeSize_of_all_accessible_openings: AttributeSizeOfAllAccessibleOpenings;
    attributeVentilation_requirements: AttributeVentilationRequirements;
    displayName: string;
}

export interface AttributeCleaningRecommendations {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeCleaningServiceReview {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionCleaningRequirements {
    attributeCleaning_recommendations: AttributeCleaningRecommendations;
    attributeCleaning_service_review: AttributeCleaningServiceReview;
    displayName: string;
}

export interface AttributeCoatingLinerType {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeCoatingLinerCondition {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeCoatingLinerConditionsObserved {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: any[];
    pendingValue: any[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionInternalCoatingLiner {
    attributeCoatingLiner_Type: AttributeCoatingLinerType;
    attributeCoatingLiner_Condition: AttributeCoatingLinerCondition;
    attributeCoatingLiner_Conditions_Observed: AttributeCoatingLinerConditionsObserved;
    displayName: string;
}

export interface SectionInternalAccessConditions {
    attributeIs_the_asset_out_of_service: AttributeIsTheAssetOutOfService;
    displayName: string;
    sectionInternalAccessRequirements: SectionInternalAccessRequirements;
    sectionCleaningRequirements: SectionCleaningRequirements;
    sectionInternalCoatingLiner: SectionInternalCoatingLiner;
    sectionAccessibility?: SectionAccessibility;
}

export interface AssetAccessObj {
    displayName: string;
    sectionInspectionAccessConditions: SectionInspectionAccessConditions;
    sectionExternalSurfaceConditions: SectionExternalSurfaceConditions;
    sectionAccessibility: SectionAccessibility;
    sectionInternalAccessConditions: SectionInternalAccessConditions;
}

export interface Entry {
    key: string;
    value: string;
    timeChanged: Date;
    userName: string;
}

export interface ValueChangeLog {
    entries: Entry[];
    pendingChange?: any;
    pendingChangeCopy?: any;
}

export interface PhotoChangeLog {
    entries: any[];
}

export interface CommentChangeLog {
    entries: any[];
    pendingChange?: any;
    pendingChangeCopy?: any;
}

export interface AttributeName {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeNumberOrID {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeAssetType {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeEquipmentDescription {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeLastKnownInspectionDate {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeLocation {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeGISLocation {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionIdentification {
    attributeName: AttributeName;
    attributeNumber_or_ID?: AttributeNumberOrID;
    attributeProduct_Handled: GenericAttribute;
    attributeLine_from_what_equipment_ID: GenericAttribute;
    attributeLine_to_what_eqiupment_ID: GenericAttribute;
    attributeStart_Latitude: GenericAttribute;
    attributeStart_Longitude: GenericAttribute;
    attributeEnd_Latitude: GenericAttribute;
    attributeEnd_Longitude: GenericAttribute;
    attributeNumber_or_Circuit_ID?: AttributeNumberOrID;
    attributeAsset_Type: AttributeAssetType;
    attributeEquipment_Description: AttributeEquipmentDescription;
    attributeLast_known_inspection_date: AttributeLastKnownInspectionDate;
    attributeLocation: AttributeLocation;
    attributeGIS_Location?: AttributeGISLocation;
    attributeStart_GIS_Location?: AttributeGISLocation;
    attributeEnd_GIS_Location?: AttributeGISLocation;
    displayName: string;
}

export interface AttributeOrientation {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeRT {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeInstallationDate {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeInServiceDate {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributePIDNumber {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeConstructionDesignDrawingNumber {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeLowestFlangeRating {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeHydroTestPressure {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit?: any;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeTypeOfConstruction {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributePostWeldHeatTreatment {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeHasTheEquipmentBeenDeRatedOrReRated {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeIsThisAFiredPressureVessel {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeFront {
    areCommentsRequired: boolean;
    attributeType: string;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeBack {
    areCommentsRequired: boolean;
    attributeType: string;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeLeft {
    areCommentsRequired: boolean;
    attributeType: string;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeRight {
    areCommentsRequired: boolean;
    attributeType: string;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionPhotos {
    attributeFront: AttributeFront;
    attributeBack: AttributeBack;
    attributeLeft: AttributeLeft;
    attributeRight: AttributeRight;
    displayName: string;
}

export interface AttributeDesignCode {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeCodeYear {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeAddendum {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionDesign {
    attributeDesign_Code: AttributeDesignCode;
    attributeCode_Year: AttributeCodeYear;
    attributeAddendum: AttributeAddendum;
    displayName: string;
    sectionManufacturerFabricator?: SectionManufacturerFabricator;
}
export interface Description {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName?: any;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeInspectionCode {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: Photo[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeYear {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface SectionInspection {
    attributeInspection_Code: AttributeInspectionCode;
    attributeYear: AttributeYear;
    attributeAddendum: AttributeAddendum;
    attributeNDE_Examination_Methods: GenericAttribute;
    displayName: string;
}
export interface AttributeAttached {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeLegible {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionDataPlate {
    attributeAttached: AttributeAttached;
    attributeLegible: AttributeLegible;
    displayName: string;
}
export interface AttributeDate {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeSerialNumber {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeNationalBoardNumber {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeRTNumber {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionManufacturer {
    attributeName: AttributeName;
    attributeDate: AttributeDate;
    attributeSerial_Number: AttributeSerialNumber;
    attributeNational_Board_Number: AttributeNationalBoardNumber;
    attributeRT_Number: AttributeRTNumber;
    displayName: string;
}
export interface AttributeServiceProductContents {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeSpecificGravity {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit?: any;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionService {
    attributeServiceProductContents: AttributeServiceProductContents;
    attributeSpecific_Gravity: AttributeSpecificGravity;
    displayName: string;
}
export interface ChangeLog {
    entries: Entry[];
}
export interface AttributeOpeningType {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeOpeningNumber {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit?: any;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeOpeningSize {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface InspectionOpeningsCurrentEntry {
    attributeOpening_Type: AttributeOpeningType;
    attributeOpening_Number: AttributeOpeningNumber;
    attributeOpening_Size: AttributeOpeningSize;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface InspectionOpeningsPendingEntry {
    attributeOpening_Type: AttributeOpeningType;
    attributeOpening_Number: AttributeOpeningNumber;
    attributeOpening_Size: AttributeOpeningSize;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface SectionRepairsCurrentEntry {
    attributeDate_Repaired_or_Altered: AttributeDateRepairedOrAltered;
    attributeRepairAlteration_organization: AttributeRepairAlterationOrganization;
    attributePurpose_of_repairalteration: AttributePurposeOfRepairalteration;
    attributeIs_NB_Form_R_1_Available: AttributeIsNBFormR1Available;
    attributeIs_NB_Form_R_2_Available: AttributeIsNBFormR2Available;
    attributeNB_R_Certificate_Number: AttributeNBRCertificateNumber;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface SectionRepairsPendingEntry {
    attributeDate_Repaired_or_Altered: AttributeDateRepairedOrAltered;
    attributeRepairAlteration_organization: AttributeRepairAlterationOrganization;
    attributePurpose_of_repairalteration: AttributePurposeOfRepairalteration;
    attributeIs_NB_Form_R_1_Available: AttributeIsNBFormR1Available;
    attributeIs_NB_Form_R_2_Available: AttributeIsNBFormR2Available;
    attributeNB_R_Certificate_Number: AttributeNBRCertificateNumber;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface ShellCourseCurrentEntry {
    attributeNumber: AttributeNumber;
    attributeMaterial_Spec_and_Grade: AttributeMaterialSpecAndGrade;
    attributeAllowable_Stress_at_Temperature: AttributeAllowableStressAtTemperature;
    attributeNominal_Thickness: AttributeNominalThickness;
    attributeCorrosion_Allowance: AttributeCorrosionAllowance;
    attributeLength_or_Height: AttributeLengthOrHeight;
    attributeJoint_Efficiency: AttributeJointEfficiency;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface ShellCoursePendingEntry {
    attributeNumber: AttributeNumber;
    attributeMaterial_Spec_and_Grade: AttributeMaterialSpecAndGrade;
    attributeAllowable_Stress_at_Temperature: AttributeAllowableStressAtTemperature;
    attributeNominal_Thickness: AttributeNominalThickness;
    attributeCorrosion_Allowance: AttributeCorrosionAllowance;
    attributeLength_or_Height: AttributeLengthOrHeight;
    attributeJoint_Efficiency: AttributeJointEfficiency;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface ChannelsCurrentEntry {
    attributeNumber: AttributeNumber;
    attributeLocation: AttributeLocation;
    attributeMaterial_Spec_and_Grade: AttributeMaterialSpecAndGrade;
    attributeAllowable_Stress_at_Temperature: AttributeAllowableStressAtTemperature;
    attributeNominal_Thickness: AttributeNominalThickness;
    attributeCorrosion_Allowance: AttributeCorrosionAllowance;
    attributeLength_or_Height: AttributeLengthOrHeight;
    attributeJoint_Efficiency: AttributeJointEfficiency;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface ChannelsPendingEntry {
    attributeNumber: AttributeNumber;
    attributeLocation: AttributeLocation;
    attributeMaterial_Spec_and_Grade: AttributeMaterialSpecAndGrade;
    attributeAllowable_Stress_at_Temperature: AttributeAllowableStressAtTemperature;
    attributeNominal_Thickness: AttributeNominalThickness;
    attributeCorrosion_Allowance: AttributeCorrosionAllowance;
    attributeLength_or_Height: AttributeLengthOrHeight;
    attributeJoint_Efficiency: AttributeJointEfficiency;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface AttributeGeometry {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface HeadsCurrentEntry {
    attributeNumber: AttributeNumber;
    attributeLocation: AttributeLocation;
    attributeGeometry: AttributeGeometry;
    attributeMaterial_Spec_and_Grade: AttributeMaterialSpecAndGrade;
    attributeAllowable_Stress_at_Temperature: AttributeAllowableStressAtTemperature;
    attributeNominal_Thickness: AttributeNominalThickness;
    attributeCorrosion_Allowance: AttributeCorrosionAllowance;
    attributeJoint_Efficiency: AttributeJointEfficiency;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface HeadsPendingEntry {
    attributeNumber: AttributeNumber;
    attributeLocation: AttributeLocation;
    attributeGeometry: AttributeGeometry;
    attributeMaterial_Spec_and_Grade: AttributeMaterialSpecAndGrade;
    attributeAllowable_Stress_at_Temperature: AttributeAllowableStressAtTemperature;
    attributeNominal_Thickness: AttributeNominalThickness;
    attributeCorrosion_Allowance: AttributeCorrosionAllowance;
    attributeJoint_Efficiency: AttributeJointEfficiency;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface NozzleCurrentEntry {
    attributeNumber: AttributeNumber;
    attributeType: AttributeType;
    attributeMaterial_Spec_and_Grade: AttributeMaterialSpecAndGrade;
    attributePipe_Size: AttributePipeSize;
    attributePipe_Schedule: AttributePipeSchedule;
    attributeFlange_Rating: AttributeFlangeRating;
    attributeReinforcement_pad_type: AttributeReinforcementPadType;
    attributeReinforcement_pad_dimensions: AttributeReinforcementPadDimensions;
    attributeReinforcement_pad_thickness: AttributeReinforcementPadThickness;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface NozzlePendingEntry {
    attributeNumber: AttributeNumber;
    attributeType: AttributeType;
    attributeMaterial_Spec_and_Grade: AttributeMaterialSpecAndGrade;
    attributePipe_Size: AttributePipeSize;
    attributePipe_Schedule: AttributePipeSchedule;
    attributeFlange_Rating: AttributeFlangeRating;
    attributeReinforcement_pad_type: AttributeReinforcementPadType;
    attributeReinforcement_pad_dimensions: AttributeReinforcementPadDimensions;
    attributeReinforcement_pad_thickness: AttributeReinforcementPadThickness;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface PipeCurrentEntry {
    attributeLine_No_: GenericAttribute;
    attributeMaterial_Spec_and_Grade: GenericAttribute;
    attributeAllowable_Stress_at_Temperature: GenericAttribute;
    attributeNominal_Thickness_schedule: GenericAttribute;
    attributeCorrosion_Allowance: GenericAttribute;
    attributeJoint_Efficiency: GenericAttribute;
    attributePipe_Spec_Number: GenericAttribute;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface PipePendingEntry {
    attributeLine_No_: GenericAttribute;
    attributeMaterial_Spec_and_Grade: GenericAttribute;
    attributeAllowable_Stress_at_Temperature: GenericAttribute;
    attributeNominal_Thickness_schedule: GenericAttribute;
    attributeCorrosion_Allowance: GenericAttribute;
    attributeJoint_Efficiency: GenericAttribute;
    attributePipe_Spec_Number: GenericAttribute;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface SectionInspectionOpenings {
    name: string;
    changeLog: ChangeLog;
    currentEntries: InspectionOpeningsCurrentEntry[];
    pendingEntries: InspectionOpeningsPendingEntry[];
}

export interface AttributeDoesTheAssetHaveARepairOrAlterationPlate {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeRepairAlterationPlatesLegible {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: boolean;
    pendingValue?: any;
    currentPendingOrValue: boolean;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeDateRepairedOrAltered {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeRepairAlterationOrganization {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributePurposeOfRepairalteration {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeIsNBFormR1Available {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeIsNBFormR2Available {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeNBRCertificateNumber {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface SectionRepairs {
    name: string;
    changeLog: ChangeLog;
    currentEntries: SectionRepairsCurrentEntry[];
    pendingEntries: SectionRepairsPendingEntry[];
}

export interface SectionRepairRecord {
    attributeDoes_the_asset_have_a_repair_or_alteration_plate: AttributeDoesTheAssetHaveARepairOrAlterationPlate;
    attributeRepairAlteration_Plates_Legible: AttributeRepairAlterationPlatesLegible;
    displayName: string;
    sectionRepairs: SectionRepairs;
}

export interface SectionGeneralInformation {
    attributeOrientation: AttributeOrientation;
    attributeRT: AttributeRT;
    attributeInstallation_Date: AttributeInstallationDate;
    attributeIn_service_Date: AttributeInServiceDate;
    attributePID_Number: AttributePIDNumber;
    attributeConstructionDesign_Drawing_Number: AttributeConstructionDesignDrawingNumber;
    attributeLowest_Flange_Rating: AttributeLowestFlangeRating;
    attributeHydro_Test_Pressure: AttributeHydroTestPressure;
    attributeType_of_construction: AttributeTypeOfConstruction;
    attributePost_Weld_Heat_Treatment: AttributePostWeldHeatTreatment;
    attributeHas_the_equipment_been_de_rated_or_re_rated: AttributeHasTheEquipmentBeenDeRatedOrReRated;
    attributeIs_this_a_fired_pressure_vessel: AttributeIsThisAFiredPressureVessel;
    displayName: string;
    sectionPhotos: SectionPhotos;
    sectionDesign: SectionDesign;
    sectionInspection: SectionInspection;
    sectionDataPlate: SectionDataPlate;
    sectionManufacturer: SectionManufacturer;
    sectionService: SectionService;
    sectionInspectionOpenings: SectionInspectionOpenings;
    sectionRepairRecord: SectionRepairRecord;
    attributePipe_Class: GenericAttribute;
    attributePipe_Size: GenericAttribute;
    attributePipe_Schedule: GenericAttribute;
    attributePID_No_: GenericAttribute;
    sectionManufacturerFabricator: SectionManufacturerFabricator;
    attributeConstruction_Method: AttributeConstructionMethod;
    attribute570AWQ149: Attribute570AW149;
}

export interface Attribute570AW149 {
    queryableValue: unknown;
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue?: null[] | null;
    pendingValue?: null[] | null;
    currentPendingOrValue: unknown;
    valueChangeLog: PhotoChangeLogOrValueChangeLog;
    displayName: string;
    databaseName: string;
    isQueryable: boolean;
    previewText: null[] | null | string;
    photos?: null[] | null;
    photoChangeLog: PhotoChangeLogOrValueChangeLog;
    commentChangeLog: CommentChangeLogOrValueChangeLog;
    comment?: null;
}

export interface AttributeOperatingTemperature {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit?: any;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeDesignMAWP {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit: string;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeDesignTemperature {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit: string;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeOperatingPressure {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit: string;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributePRVSetPressure {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit: string;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface SectionShellSide {
    attributeDesign_MAWP: AttributeDesignMAWP;
    attributeDesign_Temperature: AttributeDesignTemperature;
    attributeOperating_Temperature: AttributeOperatingTemperature;
    attributeOperating_Pressure: AttributeOperatingPressure;
    attributePRV_Set_Pressure: AttributePRVSetPressure;
    displayName: string;
}
export interface SectionTubeSide {
    attributeDesign_MAWP: AttributeDesignMAWP;
    attributeDesign_Temperature: AttributeDesignTemperature;
    attributeOperating_Temperature: AttributeOperatingTemperature;
    attributeOperating_Pressure: AttributeOperatingPressure;
    attributePRV_Set_Pressure: AttributePRVSetPressure;
    displayName: string;
}
export interface AttributeDiameterMeasurement {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeDiameter {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit: string;
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeDoesTheShellHaveMultipleDiameters {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeOverallLengthOrHeight {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit: string;
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}
export interface AttributeAreThereToriconicalTransitionSectionsInTheShell {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface AttributeOperationStatus {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface SectionDimensions {
    attributeDiameter_Measurement: AttributeDiameterMeasurement;
    attributeDiameter: AttributeDiameter;
    attributeDoes_the_shell_have_multiple_diameters: AttributeDoesTheShellHaveMultipleDiameters;
    attributeOverall_Length_or_Height: AttributeOverallLengthOrHeight;
    attributeAre_there_toriconical_transition_sections_in_the_shell: AttributeAreThereToriconicalTransitionSectionsInTheShell;
    displayName: string;
}

export interface SectionOperatingDesignConditions {
    attributeOperating_Temperature: AttributeOperatingTemperature;
    displayName: string;
    sectionShellSide: SectionShellSide;
    sectionTubeSide: SectionTubeSide;
    sectionDimensions: SectionDimensions;
    attributeOperation_Status: AttributeOperationStatus;
    attributeDesign_MAWP: GenericAttribute;
    attributeOperating_Pressure: GenericAttribute;
    attributeDesign_Temperature: GenericAttribute;
    attributePRV_Set_Pressure: GenericAttribute;
    sectionTankOutOfServiceRequirements?: SectionTankOutOfServiceRequirements;
}
export interface AttributeNumber {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit?: any;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeMaterialSpecAndGrade {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeAllowableStressAtTemperature {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit: string;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeNominalThickness {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit: string;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeCorrosionAllowance {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit: string;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeLengthOrHeight {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit: string;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributeJointEfficiency {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit?: any;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface SectionShellCourses {
    name: string;
    changeLog: ChangeLog;
    currentEntries: ShellCourseCurrentEntry[];
    pendingEntries: ShellCoursePendingEntry[];
}
export interface SectionChannels {
    name: string;
    changeLog: ChangeLog;
    currentEntries: ChannelsCurrentEntry[];
    pendingEntries: ChannelsPendingEntry[];
}

export interface AttributeMaterialSpecAndGrade5 {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface SectionHeads {
    name: string;
    changeLog: ChangeLog;
    currentEntries: HeadsCurrentEntry[];
    pendingEntries: HeadsPendingEntry[];
}

export interface AttributeType {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributePipeSize {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: string[];
    pendingValue: string[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}
export interface AttributePipeSchedule {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: string[];
    pendingValue: string[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeFlangeRating {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options: Option[];
    valueChangeLog: ValueChangeLog;
    currentValue: string[];
    pendingValue: string[];
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeReinforcementPadType {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeReinforcementPadDimensions {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface AttributeReinforcementPadThickness {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit: string;
    currentValue: number;
    pendingValue?: any;
    currentPendingOrValue: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface SectionNozzles {
    name: string;
    changeLog: ChangeLog;
    currentEntries: NozzleCurrentEntry[];
    pendingEntries: NozzlePendingEntry[];
}

export interface SectionPipe {
    name: string;
    changeLog: ChangeLog;
    currentEntries: PipeCurrentEntry[];
    pendingEntries: PipePendingEntry[];
}

export interface SectionComponents {
    displayName: string;
    sectionShellCourses: SectionShellCourses;
    sectionChannels: SectionChannels;
    sectionHeads: SectionHeads;
    sectionNozzles: SectionNozzles;
    sectionPipe: SectionPipe;
    sectionTankBottomFloor?: SectionTankBottomFloor;
}

export interface WalkDown {
    displayName: string;
    sectionIdentification: SectionIdentification;
    sectionGeneralInformation: SectionGeneralInformation;
    sectionOperatingDesignConditions: SectionOperatingDesignConditions;
    sectionComponents: SectionComponents;
    sectionRepairsandAlterations?: SectionRepairsandAlterations;
}
