<div class="dx-card responsive-paddings content-block"
     style="padding: 30px;">

    <h5>Inspection Information</h5>
    <dx-form [colCount]="6">

        <dxi-item itemType="group"
                  [colSpan]="1"
                  [colCount]="1">
            <b>Asset ID</b>
            <p>{{asset?.assetName ? asset.assetName : '--'}}</p>
        </dxi-item>
        <dxi-item itemType="group"
                  [colSpan]="1"
                  [colCount]="1">
            <b>Type</b>
            <p>{{asset?.category ? asset.category : '--'}}</p>
        </dxi-item>
    </dx-form>
    <dx-form [colCount]="6">

        <dxi-item itemType="group"
                  [colSpan]="1"
                  [colCount]="1">
            <b>APM Number</b>
            <p>{{task?.apmNumber ? task.apmNumber : '--'}}</p>
        </dxi-item>
        <dxi-item itemType="group"
                  [colSpan]="1"
                  [colCount]="1">
            <b>Type</b>
            <p>{{task?.taskType ? task.taskType : '--'}}</p>
        </dxi-item>
        <dxi-item itemType="group"
                  [colSpan]="1"
                  [colCount]="1">
            <b>Status</b>
            <p>{{task?.status ? task.status : '--'}}</p>
        </dxi-item>
        <dxi-item itemType="group"
                  [colSpan]="1"
                  [colCount]="1">
            <b>Planned start</b>
            <p>{{task?.plannedStart ? task.plannedStart : '--'}}</p>
        </dxi-item>
        <dxi-item itemType="group"
                  [colSpan]="1"
                  [colCount]="1">
            <b>Planned end</b>
            <p>{{task?.plannedEnd ? task.plannedEnd : '--'}}</p>
        </dxi-item>
        <dxi-item itemType="group"
                  [colSpan]="1"
                  [colCount]="1">
            <b>Due date</b>
            <p>{{task?.dueDate ? task.dueDate : '--'}}</p>
        </dxi-item>

        <dxi-item itemType="group"
                  [colSpan]="2"
                  [colCount]="2">
            <b>Supervisor</b>
            <p>{{task?.supervisor ? task.supervisor : '--'}}</p>
        </dxi-item>
        <dxi-item itemType="group"
                  [colSpan]="4"
                  [colCount]="4">
            <b>Assigned User(s)</b>
            <p>{{task?.assignedUsers.length > 0 ?
                task.assignedUsers.join(", ")
                : '--'}}
            </p>
        </dxi-item>
    </dx-form>
    <hr>
    <h5>Inspection Information</h5>
    <div class="responsive-paddings content-block"
         *ngIf="workOrder">
        <div class="textarea-wrapper">
            <dx-text-area height="80"
                          #jobScope
                          label="Job Scope"
                          maxLength="maxLength"
                          [(value)]="workOrder.jobScope"
                          [readOnly]="!(currentUser$ | async | hasRoles : 'app:admin' : 'apm:qaqc' : 'apm:admin' : 'apm:edit')"
                          [inputAttr]="{ 'aria-label': 'Notes' }">
            </dx-text-area>
        </div>
        <br>
        <div class="textarea-wrapper">
            <dx-text-area #inspectionSummary
                          height="80"
                          label="Inspection Summary"
                          maxLength="maxLength"
                          [(value)]="workOrder.inspectionSummary"
                          [readOnly]="!(currentUser$ | async | hasRoles : 'app:admin' : 'apm:qaqc' : 'apm:admin' : 'apm:edit')"
                          [inputAttr]="{ 'aria-label': 'Notes' }">
            </dx-text-area>
        </div>
        <br>
        <div class="textarea-wrapper">
            <dx-text-area height="80"
                          label="Applicable Damage Mechanism(s)"
                          maxLength="maxLength"
                          [(value)]="workOrder.applicableDamageMechanisms"
                          [readOnly]="!(currentUser$ | async | hasRoles : 'app:admin' : 'apm:qaqc' : 'apm:admin' : 'apm:edit')"
                          [inputAttr]="{ 'aria-label': 'Notes' }">
            </dx-text-area>
        </div>
        <br>
        <div class="textarea-wrapper">
            <dx-text-area height="80"
                          label="Relevant Indications/Finding(s)"
                          maxLength="maxLength"
                          [(value)]="workOrder.relevantIndications"
                          [readOnly]="!(currentUser$ | async | hasRoles : 'app:admin' : 'apm:qaqc' : 'apm:admin' : 'apm:edit')"
                          [inputAttr]="{ 'aria-label': 'Notes' }">
            </dx-text-area>
        </div>
        <br>
        <div class="textarea-wrapper">
            <dx-text-area height="80"
                          label="Recommendations"
                          maxLength="maxLength"
                          [(value)]="workOrder.recommendations"
                          [readOnly]="!(currentUser$ | async | hasRoles : 'app:admin' : 'apm:qaqc' : 'apm:admin' : 'apm:edit')"
                          [inputAttr]="{ 'aria-label': 'Notes' }">
            </dx-text-area>
        </div>
    </div>
    <dx-button text="Save"
               (onClick)="onSaveClicked($event)"
               [visible]="(currentUser$ | async | hasRoles : 'app:admin' : 'apm:qaqc' : 'apm:admin' : 'apm:edit')"
               type="success"></dx-button>
</div>