#region Copyright Quest Integrity Group, LLC 2020

// www.QuestIntegrity.com
// +1-303-415-1475
// 
// All rights are reserved. Reproduction or transmission in whole or in part, in
// any form or by any means, electronic, mechanical or otherwise, is prohibited
// without the prior written consent of the copyright owner.
// 
// File Created: 2020-03-30 10:38 AM
// Updated:      2020-03-30 10:38 AM
// Created by:   <PERSON><PERSON><PERSON><PERSON>, Sean

#endregion

using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderTracking.API.Helpers;
using OrderTracking.API.Interfaces.Services;
using OrderTracking.API.Models;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     Service class for interacting with blob storage
    /// </summary>
    public class BlobStorageService : CloudStorageService, IBlobStorageService
    {
        #region Constructors

        /// <summary>
        ///     Constructs a BlobStorageService, injecting BlobStorage configuration and a logger
        /// </summary>
        /// <param name="options"></param>
        /// <param name="logger"></param>
        protected BlobStorageService(IOptions<BlobStorage> options, ILogger logger) : base(options, logger)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));
            _logger = logger;
            //ServiceClient = new BlobServiceClient(options.Value.ConnectionString);
        }

        #endregion

        #region Fields and Constants

        private readonly ILogger _logger;

        /// <summary>
        ///     Object that provides access to the Azure Blob Storage service
        /// </summary>
        //protected readonly BlobServiceClient ServiceClient;

        ///// <summary>
        /////     Object that provides access to the Azure Blob Storage container
        ///// </summary>
        //protected BlobContainerClient ContainerClient;

        //#endregion

        //#region Public Methods

        ///// <summary>
        /////     Delete a blob file associated with a resource
        ///// </summary>
        ///// <param name="resourceId"></param>
        ///// <param name="fileName"></param>
        ///// <returns></returns>
        //public async Task DeleteBlobAsync(string resourceId, string fileName)
        //{
        //    var blobName = BlobServiceHelpers.FullBlobStringEncoded(resourceId, fileName);
        //    var blobClient = ContainerClient.GetBlobClient(blobName);
        //    try
        //    {
        //        await blobClient.DeleteAsync();
        //        _logger.LogInformation($"{blobName} removed from {resourceId}");
        //    }
        //    catch (RequestFailedException e)
        //    {
        //        if (e.Status != 404) throw;
        //        _logger.LogError(e.Message);
        //    }
        //}

        //public async Task DeleteBlobAsync(string fileName)
        //{
        //    var blobName = BlobServiceHelpers.EncodeUri(fileName);
        //    var blobClient = ContainerClient.GetBlobClient(blobName);
        //    try
        //    {
        //        await blobClient.DeleteAsync();
        //    }
        //    catch (RequestFailedException e)
        //    {
        //        if (e.Status != 404) throw;
        //        _logger.LogError(e.Message);
        //    }
        //}

        ///// <summary>
        /////     Download a blob file associated with a resource
        ///// </summary>
        ///// <param name="resourceId"></param>
        ///// <param name="fileName"></param>
        ///// <returns></returns>
        //public async Task<BlobDownloadInfo> DownloadBlobAsync(string resourceId, string fileName)
        //{
        //    var blobName = BlobServiceHelpers.FullBlobStringEncoded(resourceId, fileName);
        //    var blobClient = ContainerClient.GetBlobClient(blobName);
        //    var response = await blobClient.DownloadAsync();
        //    return response.Value;
        //}

        ///// <summary>
        /////     List the blob files associated with a resource
        ///// </summary>
        ///// <param name="resourceId"></param>
        ///// <returns></returns>
        //public async Task<IEnumerable<BlobItem>> ListBlobsAsync(string resourceId)
        //{
        //    var blobs = new List<BlobItem>();
        //    await foreach (var blobItem in ContainerClient.GetBlobsAsync(prefix: $"{resourceId}",
        //        traits: BlobTraits.All)) blobs.Add(blobItem);
        //    return blobs;
        //}

        //public async Task<(string AbsoluteUri, BlobContentInfo Value, string blobName)> UploadStreamAsync(
        //    MemoryStream stream,
        //    string blobName,
        //    string uploadedByEmail = null)
        //{
        //    if (stream == null) throw new ArgumentNullException(nameof(stream));

        //    var blobClient = ContainerClient.GetBlobClient(blobName);
        //    var response = await blobClient.UploadAsync(stream, true);
        //    if (uploadedByEmail != null)
        //        await blobClient.SetMetadataAsync(new Dictionary<string, string> { { "user", uploadedByEmail } });
        //    _logger.LogInformation($"{blobName} uploaded");
            
        //    return (blobClient.Uri.AbsoluteUri, response.Value, blobName);
        //}

        ///// <summary>
        /////     Upload a blob file attachment
        ///// </summary>
        ///// <param name="id"></param>
        ///// <param name="file"></param>
        ///// <param name="uploadedByEmail"></param>
        ///// <returns></returns>
        //public async Task<BlobContentInfo> UploadAttachmentAsync(string id, IFormFile file,
        //    string uploadedByEmail = null)
        //{
        //    if (file == null) throw new ArgumentNullException(nameof(file));

        //    var blobName = BlobServiceHelpers.FullBlobStringEncoded(id, file.FileName);
        //    await using var stream = file.OpenReadStream();
        //    var blobClient = ContainerClient.GetBlobClient(blobName);
        //    var response = await blobClient.UploadAsync(stream, new BlobHttpHeaders { ContentType = file.ContentType });
        //    if (uploadedByEmail != null)
        //        await blobClient.SetMetadataAsync(new Dictionary<string, string> { { "user", uploadedByEmail } });
        //    _logger.LogInformation($"{blobName} uploaded");
        //    return response.Value;
        //}

        #endregion
    }
}