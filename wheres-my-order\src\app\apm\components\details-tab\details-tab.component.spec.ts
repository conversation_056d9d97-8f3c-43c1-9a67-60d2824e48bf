import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxButtonModule } from 'devextreme-angular';
import { DxFormModule } from 'devextreme-angular/ui/form';
import { ToastrModule } from 'ngx-toastr';
import { ApmService } from '../../services';
import { DetailsTabComponent } from './details-tab.component';

describe('DetailsTabComponent', () => {
    let component: DetailsTabComponent;
    let fixture: ComponentFixture<DetailsTabComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxFormModule,
                DxButtonModule,
                HttpClientTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [DetailsTabComponent],
            providers: [{ provide: ApmService, useValue: {} }]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(DetailsTabComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
