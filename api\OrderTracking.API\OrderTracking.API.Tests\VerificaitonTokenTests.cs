﻿using System;
using System.Threading;
using NUnit.Framework;
using OrderTracking.API.Models;

namespace OrderTracking.API.Tests
{
    [TestFixture]
    public class VerificationTokenTests
    {
        [Test]
        public void Token_Serialization_Deserialization()
        {
            var time = (int) DateTime.UtcNow.Subtract(new DateTime(1970, 1, 1)).TotalSeconds +
                       new TimeSpan(24, 0, 0).TotalSeconds;
            var originalToken = new VerificationToken(time, "<EMAIL>");
            var serializedToken = VerificationToken.SerializeToken(originalToken);
            var deserializedToken = VerificationToken.DeserializeToken(serializedToken);
            Assert.That(originalToken.VerificationExpirationInSeconds ==
                        deserializedToken.VerificationExpirationInSeconds);
            Assert.That(originalToken.Email == deserializedToken.Email);
        }

        [Test]
        public void Token_Is_Expired()
        {
            //going to ensure that token is expired
            var time = (int) DateTime.UtcNow.Subtract(new DateTime(1970, 1, 1)).TotalSeconds;
            Thread.Sleep(1);

            var originalToken = new VerificationToken(time, "<EMAIL>");
            Assert.That(originalToken.IsExpired);
        }

        [Test]
        public void Token_Is_Not_Expired()
        {
            var time = (int) DateTime.UtcNow.Subtract(new DateTime(1970, 1, 1)).TotalSeconds +
                       new TimeSpan(24, 0, 0).TotalSeconds;
            var originalToken = new VerificationToken(time, "<EMAIL>");

            Assert.That(!originalToken.IsExpired);
        }
    }
}