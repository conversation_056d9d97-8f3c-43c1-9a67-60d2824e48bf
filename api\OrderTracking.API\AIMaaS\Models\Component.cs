﻿using System;

// (properties are mapped to database columns, and we are not in control of naming convention)
// <PERSON><PERSON><PERSON><PERSON> disable InconsistentNaming
// <PERSON><PERSON><PERSON><PERSON> disable UnusedMember.Global
// ReSharper disable IdentifierTypo
#pragma warning disable 1591

namespace AIMaaS.Models
{
    /// <summary>
    ///     Component (children of assets) in CredoSoft
    /// </summary>
    // Re<PERSON><PERSON><PERSON> disable once ClassNeverInstantiated.Global
    public class Component
    {
#pragma warning disable CA1707 // Identifiers should not contain underscores
        public long OBJPARENT { get; set; }
        public string RSITE_NAME { get; set; }
        public string OBJGROUPID { get; set; }
        public string OBJNAME_PARENT { get; set; }
        public string OBJNAME { get; set; }
        public string OBJDESC { get; set; }
        public string OBJTYPECODE { get; set; }
        public string OBJSERVICE { get; set; }
        public string RSTREAM_NAME { get; set; }
        public string TM_PV_SPEC_GRAVITY { get; set; }
        public string OBJCOMMENT { get; set; }
        public string EQDESIGNCODE { get; set; }
        public string EQINSPCODE { get; set; }
        public string EQMANUFACTURER { get; set; }
        public DateTime? EQMANUFDATE { get; set; }
        public DateTime? OBJCOMMISSION { get; set; }
        public string TM_PV_NAT_BOARD_NO { get; set; }
        public string EQSERIALNUM { get; set; }
        public DateTime? TM_PV_DATE_REPAIRED { get; set; }
        public DateTime? TM_PV_DATE_ALTERED { get; set; }
        public string TM_PV_R_CERT_NO { get; set; }
        public string TM_PV_CERT_HOLDER { get; set; }
        public string TM_PV_ORIENTATION { get; set; }
        public string TM_PV_CONFIG_GEOM { get; set; }
        public decimal DIM_DIAMOUTSIDE { get; set; }
        public string DIM_DIAMUNITS { get; set; }
        public decimal DIM_LENGTH { get; set; }
        public string DIM_LENGTHUNIT { get; set; }
        public string XPICK_OBJREF_MATID { get; set; }
        public string MATSPEC { get; set; }
        public string MATGRADE { get; set; }
        public string RMAT_DAMAGE_GROUP { get; set; }
        public string PRESSUNITS { get; set; }
        public decimal PRESSDESMAX { get; set; }
        public decimal PRSSRELIEF { get; set; }
        public decimal PRESSOPERNORM { get; set; }
        public string TEMPUNITS { get; set; }
        public decimal TEMPDESMAX { get; set; }
        public decimal TEMPOPERNORM { get; set; }
        public int EQ_IS_PWHT { get; set; }
        public string OBJCORRCIRC { get; set; }
        public string EQLININGEXT { get; set; }
        public string EQLININGINT { get; set; }
        public string OBJRISKCODE { get; set; }
        public string OBJSTATUS { get; set; }
        public string OBJUNIQUEID { get; set; }
        public long OBJID { get; set; }
        public long RSITE_RID { get; set; }
        public string OBJSYS_SORT_1 { get; set; }
        public string OBJSYS_SORT_2 { get; set; }
#pragma warning restore CA1707 // Identifiers should not contain underscores
    }
}