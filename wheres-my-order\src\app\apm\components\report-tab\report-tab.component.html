<div class="outer-div">
    <div class="inner-div">
        <div style="display: flex; justify-content: left;
        flex-direction: column;">
            <div class="dx-field">
                <div class="dx-field-label"
                     style="width: 100%; margin-bottom: 5px">Field Work
                    Completed Date</div>
            </div>
            <dx-date-box [(value)]="fieldWorkCompletedDate"
                         (onValueChanged)="onFieldWorkCompletedChanged($event)"
                         [readOnly]="!(currentUser$ | async | hasRoles : 'app:admin' : 'apm:qaqc' : 'apm:admin')">
            </dx-date-box>

            <dx-button class="report-button"
                       (onClick)="onGenerateNewReportClicked($event)"
                       [disabled]="isMarkedAsPublished || publishing || !allowEditing"
                       text="Generate New Report"
                       type="success"></dx-button>

            <dx-button *ngIf="currentUser$ | async | hasRoles : 'app:admin' : 'apm:qaqc' : 'apm:admin'"
                       id="publish-button"
                       class="report-button"
                       type="success"
                       [disabled]="publishing || !allowEditing"
                       [text]="isMarkedAsPublished ? 'Mark as Unpublished' :'Mark as Published'"
                       (onClick)="onPublishUnpublishClicked($event)">
            </dx-button>
        </div>
        <span
              [hidden]="!isMarkedAsPublished || workOrderDetail.publishTime === null">Work
            Order was marked as
            published: {{workOrderDetail.publishTime | date : 'short'}}</span>
        <span class="filler-text"
              [hidden]="isMarkedAsPublished">filler text</span>
    </div>
</div>
<dx-popup [(visible)]="isReportPopupDisplayed"
          [showCloseButton]="true"
          [hideOnOutsideClick]="true"
          (onHiding)="onHidingPopup($event)">
    <dx-scroll-view id="scroll-view"
                    style="width: 100%;">
        <dx-data-grid id="reports-grid"
                      #reportsGrid
                      [dataSource]="workOrderDetail.inspectionTypes"
                      (onSelectionChanged)="onSelectionChanged($event)">
            <dxo-selection mode="single"></dxo-selection>
            <dxi-column dataField="taskId"></dxi-column>
            <dxi-column dataField="taskType"></dxi-column>
            <dxi-column dataField="taskAssignees"></dxi-column>
            <dxi-column dataField="supervisor"></dxi-column>
            <dxi-column dataField="status"></dxi-column>

        </dx-data-grid>
        <div class="inner-div">
            <dx-button style="justify-self: center;"
                       class="report-button"
                       type="success"
                       (onClick)="onReportsForSelectionsClicked($event)"
                       text="Generate Report(s)"
                       [disabled]="!hasSelections || generatingReports || !allowEditing">
            </dx-button>
        </div>
    </dx-scroll-view>
</dx-popup>
<!-- Test comment -->
<dx-load-panel #loadPanel
               shadingColor="rgba(0,0,0,0.4)"
               [position]="{ of: '#scroll-view' }"
               [(visible)]="generatingReports"
               [showIndicator]="true"
               [showPane]="true"
               [shading]="true"
               message="Generating report(s), please wait..."
               [hideOnOutsideClick]="false">
</dx-load-panel>