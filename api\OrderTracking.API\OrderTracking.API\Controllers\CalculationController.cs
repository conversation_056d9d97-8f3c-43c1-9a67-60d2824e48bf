﻿using System;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Converters;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json.Linq;
using OrderTracking.API.Interfaces;
using OrderTracking.API.Services;

namespace OrderTracking.API.Controllers
{
    /// <summary> 
    ///     API Controller for managing calculations 
    /// </summary> 
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Policy = "UserIsActive")]
    public class CalculationController : ControllerBase
    {
        private readonly ICalculationService _calculations;
        private readonly IConfiguration _config;
        private readonly DeploymentEnvironment _env;
        private readonly IUserProfilesService _userProfiles;

        /// <summary> 
        ///     Constructor that injects an IUserProfilesService and an IEquipmentRequestsService. 
        /// </summary> 
        /// <param name="userProfiles"></param> 
        /// <param name="calculations"></param> 
        /// <param name="config"></param> 
        /// <param name="env"></param> 
        public CalculationController(
            IUserProfilesService userProfiles,
            ICalculationService calculations,
            IConfiguration config,
            DeploymentEnvironment env)
        {
            _userProfiles = userProfiles;
            _calculations = calculations;
            _config = config;
            _env = env;
        }

        /// <summary> 
        ///     Get a specific Calculation 
        /// </summary> 
        /// <param name="id"></param> 
        /// <returns></returns> 
        [HttpGet("{id}")]
        [Authorize(Policy = "JOINTS:Edit")]
        public async Task<IActionResult> Get(string id)
        {
            var email = User.Identity.Name.ToLower();
            var user = await _userProfiles.GetAsync(email);
            if (user == null) return Unauthorized();

            var calculation = await _calculations.GetItemAsync(id);

            return Ok(calculation);
        }

        /// <summary> 
        ///     Get all Calculations 
        /// </summary> 
        /// <returns></returns> 
        [HttpGet]
        [Authorize(Policy = "JOINTS:Edit")]
        public async Task<IActionResult> Get()
        {
            var email = User.Identity.Name.ToLower();
            var user = await _userProfiles.GetAsync(email, email);
            if (user == null) return Unauthorized();

            var calculations = await _calculations.GetItemsAsync();

            return Ok(calculations);
        }

        /// <summary> 
        ///     Create a new Calculation 
        /// </summary> 
        /// <param name="calc"></param> 
        /// <returns></returns> 
        [HttpPost]
        [Authorize(Policy = "JOINTS:Edit")]
        public async Task<IActionResult> Post(JObject calc)
        {
            if (calc == null) throw new ArgumentNullException(nameof(calc));

            // Get current user profile 
            var email = User?.Identity?.Name?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            if (user == null) return Unauthorized();

            // Add new document to database and returns id of added document
            var id = await _calculations.AddItemAsync(calc); 

            return CreatedAtAction(nameof(Get), new { id },
                await _calculations.GetItemAsync(id));
        }

        /// <summary> 
        ///     Update an existing calculation 
        /// </summary> 
        /// <param name="id"></param> 
        /// <param name="calc"></param> 
        /// <returns></returns> 
        [HttpPut("{id}")]
        [Authorize(Policy = "JOINTS:Edit")]
        public async Task<IActionResult> Put(string id, JObject calc)
        {
            var email = User?.Identity?.Name?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            if (user == null) return Unauthorized();
            if (calc == null) throw new ArgumentNullException(nameof(calc));
            if (id == null) throw new ArgumentNullException(nameof(id));

            var valid = TryValidateModel(calc);

            if (valid == false)
                return UnprocessableEntity(ModelState.Values
                    .Where(v => v.ValidationState == ModelValidationState.Invalid).Select(v => v.Errors));
            //updates existing document adds if dosent exist
            var calculation = _calculations.UpdateItemAsync(id, calc);

            return Ok(calc);
        }


        /// <summary> 
        ///     Delete 1 or many calculations 
        /// </summary> 
        /// <param name="ids"></param> 
        /// <returns></returns> 
        [HttpDelete]
        [Authorize(Policy = "JOINTS:Edit")]
        public async Task<IActionResult> DeleteCalculations(StringList ids)
        {
            if (ids == null) return BadRequest("Must provide at least one id");
            await _calculations.DeleteItemsAsync(ids.ToArray());
            return NoContent();
        }
    }
}   