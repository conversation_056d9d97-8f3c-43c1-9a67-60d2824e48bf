﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using OrderTracking.API.Models.PowerBI;
using OrderTracking.API.Services;
using OrderTracking.API.Services.PowerBI;

namespace OrderTracking.API.Controllers
{
    /// <summary>
    ///     Controller for interacting with PowerBI
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class PowerBIController : ControllerBase
    {
        private readonly PbiEmbedService _powerBIService;
        private readonly IOptions<AzureAd> _azureAd;
        private readonly IOptions<PowerBI> _powerBI;

        /// <summary>
        ///     Constructor
        /// </summary>
        /// <param name="pbiEmbedService"></param>
        /// <param name="azureAd"></param>
        /// <param name="powerBI"></param>
        public PowerBIController(PbiEmbedService pbiEmbedService, IOptions<AzureAd> azureAd, IOptions<PowerBI> powerBI)
        {
            _powerBIService = pbiEmbedService;
            _azureAd = azureAd;
            _powerBI = powerBI;
        }


        /// <summary>
        /// Returns Embed token, Embed URL, and Embed token expiry to the client
        /// </summary>
        /// <returns>JSON containing parameters for embedding</returns>
        [HttpGet]
        public async Task<string> GetEmbedInfo()
        {
            try
            {
                // Validate whether all the required configurations are provided in appsettings.json
                var configValidationResult = ConfigValidatorService.ValidateConfig(_azureAd, _powerBI);
                if (configValidationResult != null)
                {
                    HttpContext.Response.StatusCode = 400;
                    return configValidationResult;
                }
                
                var embedParams = await _powerBIService.GetEmbedParams(new Guid(_powerBI.Value.WorkspaceId), new Guid(_powerBI.Value.ReportId));
                return JsonConvert.SerializeObject(embedParams);
            }
            catch (Exception ex)
            {
                HttpContext.Response.StatusCode = 500;
                return ex.Message + "\n\n" + ex.StackTrace;
            }
        }

        [HttpGet("GetConnectedWorker/{client}/{dashboard}")]
        public async Task<string> GetConnectedWorker(String client, String dashboard)
        {
            try
            {
                var configValidationResult = ConfigValidatorService.ValidateConfig(_azureAd, _powerBI);
                if (configValidationResult != null)
                {
                    HttpContext.Response.StatusCode = 400;
                    return configValidationResult;
                }

                var workspaceId = "";
                var dashboardId = "";

                if (client == ConnectedWorkerClient.Chevron.name)
                {
           
                    switch(dashboard)
                    {
                        case ConnectedWorkerClient.Chevron.ActivityTracker.name:
                            workspaceId = ConnectedWorkerClient.Chevron.ActivityTracker.workspaceId;
                            dashboardId = ConnectedWorkerClient.Chevron.ActivityTracker.dashboardId;
                            break;
                        case ConnectedWorkerClient.Chevron.CESOSI.name:
                            workspaceId = ConnectedWorkerClient.Chevron.CESOSI.workspaceId;
                            dashboardId = ConnectedWorkerClient.Chevron.CESOSI.dashboardId;
                            break;
                        default:
                            break;
                    }
                }
                else if (client == ConnectedWorkerClient.GPC.name)
                {
                    switch(dashboard)
                    {
                        case ConnectedWorkerClient.GPC.SupplementSections.name:
                            workspaceId = ConnectedWorkerClient.GPC.SupplementSections.workspaceId;
                            dashboardId = ConnectedWorkerClient.GPC.SupplementSections.dashboardId;
                            break;
                        default:
                            break;
                    }
                }

                var embedParams = await _powerBIService.GetEmbedParams(
                    new Guid(workspaceId),
                    new Guid(dashboardId));
                return JsonConvert.SerializeObject(embedParams);
            }
            catch (Exception ex)
            {
                HttpContext.Response.StatusCode = 500;
                return ex.Message + "\n\n" + ex.StackTrace;
            }
        }
    }
}
