﻿using System;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Extensions;
using Microsoft.AspNetCore.Authorization;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Authorization.Handlers
{
    /// <summary>
    ///     Handles role requirements assigned to various controller actions
    /// </summary>
    public class RoleRequirementHandler : AuthorizationHandler<ClientPortalRoleRequirement>
    {
        #region Fields and Constants

        private readonly IUserProfilesService _userProfiles;

        #endregion Fields and Constants

        #region Constructors

        /// <summary>
        ///     Constructor (injects user profile service)
        /// </summary>
        /// <param name="userProfiles"></param>
        public RoleRequirementHandler(IUserProfilesService userProfiles)
        {
            _userProfiles = userProfiles;
        }

        #endregion Constructors

        /// <summary>
        ///     Handle the authorization based on the user making the
        ///     request and the role requirement assigned to the endpoint being called.
        /// </summary>
        /// <param name="context"></param>
        /// <param name="requirement"></param>
        /// <returns></returns>
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context,
            ClientPortalRoleRequirement requirement)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (requirement == null) throw new ArgumentNullException(nameof(requirement));

            if (context.User.Identity.Name != null)
                await AuthorizeUserRoles(context, requirement, context.User.Identity.Name.ToLower());
            else
                context.Fail();
        }

        private async Task AuthorizeUserRoles(AuthorizationHandlerContext context,
            ClientPortalRoleRequirement requirement, string email)
        {
            var user = await _userProfiles.GetAsync(email);
            if (user == null)
            {
                context.Fail();
                return;
            }

            var userHasAnyOfRequiredRoles = requirement.Roles.Any(role => user.HasRole(role));

            if (userHasAnyOfRequiredRoles)
                context.Succeed(requirement);
            else context.Fail();
        }
    }
}