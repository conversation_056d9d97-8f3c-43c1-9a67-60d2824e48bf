using System.Collections.Generic;
using System.Diagnostics.Tracing;
using System.IO.Compression;
using AIMaaS.Models;
using AIMaaS.Services;
using DevExpress.Utils;
// Migrated from Google Cloud Diagnostics to Azure Application Insights
// using Google.Cloud.Diagnostics.AspNetCore3;
// using Google.Cloud.Diagnostics.Common;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using OrderTracking.API.AuthHandlers;
using OrderTracking.API.Authorization.Handlers;
using OrderTracking.API.Controllers;
using OrderTracking.API.Extensions;
using OrderTracking.API.Hubs;

namespace OrderTracking.API
{
    /// <summary>
    ///     This is how the ASP.NET Core Web API is instructed to startup the process
    /// </summary>
    public class Startup
    {
        #region Constructors

        /// <summary>
        ///     Constructor for web api startup.  Necessary to inject IConfiguration object.
        /// </summary>
        /// <param name="configuration"></param>
        public Startup(
            IConfiguration configuration)
        {
            Configuration = configuration;
        }

        #endregion

        #region Properties

        /// <summary>
        ///     IConfiguration object used during startup phase, injected in constructor.
        /// </summary>
        /// <value></value>
        // ReSharper disable once MemberCanBePrivate.Global
        public IConfiguration Configuration { get; }

        #endregion

        #region Public Methods

        /// <summary>
        ///     This method gets called by the runtime. Use this method to add services to the container
        /// </summary>
        /// <param name="services"></param>
        public void ConfigureServices(
            IServiceCollection services)
        {
            // Connection String for Antea
            services.Configure<AnteaData>(options => Configuration.Bind("AnteaDb", options));
            services.AddMvc()
                .AddNewtonsoftJson(options => options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore);
            // Migrated from Google Cloud Diagnostics to Azure Application Insights
            services.AddApplicationInsightsTelemetry(Configuration);

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddMicrosoftIdentityWebApi(options => options.TokenValidationParameters.NameClaimType = "emails",
                    options => Configuration.Bind("AzureAdB2C", options));

            services.AddResponseCompression(options =>
            {
                options.EnableForHttps = true;
                options.Providers.Add<GzipCompressionProvider>();
            });

            services.Configure<GzipCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.SmallestSize;
            });

            services.AddControllers()
                .AddApplicationPart(typeof(UsersController).Assembly)
                .AddApplicationPart(typeof(AnteaController).Assembly);

            services.AddAuthorization(options =>
            {
                options
                    .AddCorePolicies()
                    .AddWMOPolicies()
                    .AddCredoSoftPolicies()
                    //.AddPipelineInspectionPolicies()
                    .AddCalculationPolicies()
                    .AddApmPolicies()
                    .AddCRDPolicies();
            });

            services
                .AddSingleton<IAuthorizationHandler, RoleRequirementHandler>()
                .AddSingleton<IAuthorizationHandler, UserIsActiveHandler>()
                .AddSingleton<IAuthorizationHandler, DistrictAssignmentAuthorizationHandler>()
                .AddSingleton<IAuthorizationHandler, ClientAccountAssignmentAuthorizationHandler>()
                .AddSingleton<IAuthorizationHandler, IsTeamEmployeeHandler>()
                .AddSingleton<IAuthorizationHandler, UserIsModuleAdminHandler>()
                .AddSingleton<IAuthorizationHandler, AssetManagementSiteAssignmentAuthorizationHandler>()
                .AddSingleton<ThreadPoolStarvationDetector>()
                .AddSingleton<IAnteaService, AnteaService>();

            //NOTE: as Portal modules require other additions, add them
            //      to the implementation of the method below (vs. adding
            //      additional lines here.
            services.AddPortalApplicationServices(Configuration);

            services.AddSwaggerDocumentation();

            AzureCompatibility.Enable = true;

            services.AddCors(options =>
            {
                options.AddPolicy("LocalCORSPolicy", builder => builder.WithOrigins("http://localhost:4200")
                    .AllowCredentials()
                    .SetIsOriginAllowed((host) => true));
            });

            //services.AddSignalR().AddAzureSignalR();
        }

        /// <summary>
        ///     This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        public void Configure(
            IApplicationBuilder app,
            IWebHostEnvironment env)
        {
            app.UseResponseCompression();

            if (env.IsDevelopment()) app.UseDeveloperExceptionPage();

            // Enable middleware to serve generated Swagger as a JSON endpoint.
            app.UseSwagger();

            // Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.),
            // specifying the Swagger JSON endpoint.
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Client Portal V1");
                c.OAuthClientId(Configuration["Swagger:ClientId"]);
                c.OAuthClientSecret(Configuration["Swagger:ClientSecret"]);
                c.OAuthRealm(Configuration["AzureAdB2C:ClientId"]);
                c.OAuthScopeSeparator(" ");
                c.OAuthAdditionalQueryStringParams(new Dictionary<string, string>
                {
                    {"resource", Configuration["AzureAdB2C:ClientId"]}
                });
            });

            app.UseCors(builder =>
                builder.WithOrigins(
                        "http://localhost:4200",
                        // TODO: When we are ready to stop deploying to kubernetes, these can go away
                        "https://ordertrackingportal.azurewebsites.net",
                        "https://teamdigitaldev.teaminc.com",
                        // TODO: These below are the urls for our Angular app in various environments.
                        //       Should we make these only for the environment we are building for?
                        "https://teamdigitalclientportal.azurewebsites.net",
                        "https://teamdigitalclientportal-stg.azurewebsites.net",
                        "https://teamdigitalclientportal-test.azurewebsites.net",
                        "https://teamdigitalclientportal-dev.azurewebsites.net",
                        "https://digital.teaminc.com",
                        "https://digitaldev.teaminc.com",
                        "https://digitaltest.teaminc.com",
                        "https://digitalstaging.teaminc.com",
                        "https://run-clientportal-frontend-dev-usc1-2conmidenq-uc.a.run.app",
                        "https://run-clientportal-frontend-stg-usc1-2atiipqgyq-uc.a.run.app",
                        "https://run-clientportal-frontend-prod-usc1-yonwy2nc7q-uc.a.run.app"

                    )
                    .WithExposedHeaders("Location")
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials());

            // UseHsts isn't recommended in development because the HSTS settings are highly cacheable by browsers. By default, UseHsts excludes the local loopback address.
            // HSTS:
            // https://docs.microsoft.com/en-us/aspnet/core/security/enforcing-ssl?view=aspnetcore-2.1&tabs=visual-studio#http-strict-transport-security-protocol-hsts
            // SignalR:
            // https://docs.microsoft.com/en-us/aspnet/core/tutorials/signalr?view=aspnetcore-5.0&tabs=visual-studio#configure-signalr
            if (!env.IsDevelopment()) app.UseHsts();

            app.UseRouting();
            // TODO: Is this required?
            // app.UseFileServer();

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                //endpoints.MapHub<EDRHub>("/edr");
            });
        }

        #endregion
    }


    public class ThreadPoolStarvationDetector : EventListener
    {
        private const int EventIdForThreadPoolWorkerThreadAdjustmentAdjustment = 55;
        private const uint ReasonForStarvation = 6;

        private readonly ILogger<ThreadPoolStarvationDetector> _logger;

        public ThreadPoolStarvationDetector(ILogger<ThreadPoolStarvationDetector> logger)
        {
            _logger = logger;
        }

        protected override void OnEventSourceCreated(EventSource eventSource)
        {
            if (eventSource.Name == "Microsoft-Windows-DotNETRuntime")
            {
                EnableEvents(eventSource, EventLevel.Informational, EventKeywords.All);
            }
        }

        protected override void OnEventWritten(EventWrittenEventArgs eventData)
        {
            // See: https://docs.microsoft.com/en-us/dotnet/framework/performance/thread-pool-etw-events#threadpoolworkerthreadadjustmentadjustment
            if (eventData.EventId == EventIdForThreadPoolWorkerThreadAdjustmentAdjustment &&
                eventData.Payload[3] as uint? == ReasonForStarvation)
            {
                _logger.LogWarning("Thread pool starvation detected!!");
            }
        }
    }
}