<div class="responsive-paddings content-block">
    <div *ngIf="!inspectionInfo">
        <dx-load-indicator style="position: relative; left: 50%;"
                           id="large-indicator"
                           height="60"
                           width="60"></dx-load-indicator>
    </div>
    <div *ngIf="inspectionInfo">
        <div class="button-container">
            <dx-button #expandCollapseButton
                       [stylingMode]="'text'"
                       [text]="'Collapse All'"
                       (onClick)="toggleExpandCollapseAll($event, accordion)">
            </dx-button>
        </div>

        <dx-accordion #accordion
                      [items]="items"
                      [multiple]="true"
                      [collapsible]="true"
                      (onSelectionChanged)="onSelectionChanged(accordion, expandCollapseButton)"
                      (onContentReady)="onContentReady($event)">
            <div *dxTemplate="let item of 'item'">
                <dx-form [formData]="inspectionInfo"
                         [readOnly]="!allowEditing">
                    <dxi-item [dataField]="item.dataField"
                              editorType="dxTextArea"
                              [editorOptions]="{readOnly: !isEditing}">
                        <dxo-label [visible]="false"></dxo-label>
                    </dxi-item>
                </dx-form>
            </div>
        </dx-accordion>


        <div class="buttons">
            <dx-button *ngIf="!isEditing; else saveAndCancel"
                       text="Edit"
                       type="default"
                       [disabled]="!allowEditing"
                       (onClick)="onEditClicked($event)"></dx-button>
            <ng-template #saveAndCancel>
                <dx-button text="Cancel"
                           type="danger"
                           [disabled]="isSaving"
                           (onClick)="onCancelClicked($event)">
                </dx-button>
                <dx-button text="Save"
                           type="success"
                           [disabled]="!allowEditing || isSaving"
                           (onClick)="onSaveClicked($event)"></dx-button>
            </ng-template>
        </div>
    </div>
</div>
