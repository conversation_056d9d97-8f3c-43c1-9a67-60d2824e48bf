﻿using System.Collections.Generic;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class AssetPPEUpdate
    {
        [JsonProperty("abatementRequired")] public string AbatementRequired;

        [JsonProperty("atmosphere")] public string[] Atmosphere;

        [JsonProperty("breathingProtection")] public string[] BreathingProtection;

        [JsonProperty("chemicalSuit")] public string[] ChemicalSuit;

        [JsonProperty("controlAreaPermit")] public string ControlAreaPermit;

        [JsonProperty("drainageNeeded")] public string DrainageNeeded;

        [JsonProperty("earProtection")] public string[] EarProtection;

        [JsonProperty("eyeProtection")] public string[] EyeProtection;

        [JsonProperty("fallProtection")] public string[] FallProtection;

        [Json<PERSON>roperty("fireRetardantClothing")]
        public string[] FireRetardantClothing;

        [JsonProperty("footProtection")] public string[] FootProtection;

        [JsonProperty("generalHotWork")] public string GeneralHotWork;

        [<PERSON><PERSON><PERSON><PERSON><PERSON>("generalWork")] public string GeneralWork;

        [<PERSON><PERSON><PERSON><PERSON><PERSON>("hardHatRequired")] public string HardHatRequired;

        [JsonProperty("hazardousAreaPermit")] public string HazardousAreaPermit;

        [JsonProperty("holeWatchNeeded")] public string HoleWatchNeeded;

        [JsonProperty("onSiteLeaks")] public string OnSiteLeaks;

        [JsonProperty("onSiteLeaksComments")] public string OnSiteLeaksComments;

        [JsonProperty("openFlameHotWork")] public string OpenFlameHotWork;

        [JsonProperty("overgrownVegetation")] public string OvergrownVegetation;

        [JsonProperty("permitRequired")] public string PermitRequired;

        [JsonProperty("personnelAccessConditionNotes")]
        public string PersonnelAccessConditionNotes;

        [JsonProperty("personnelAccessConditions")]
        public string[] PersonnelAccessConditions;

        [JsonProperty("powerAvailable")] public string PowerAvailable;

        [JsonProperty("safetyGloves")] public string[] SafetyGloves;

        [JsonProperty("snakeChapsRequired")] public string SnakeChapsRequired;

        [JsonProperty("standingWater")] public string StandingWater;

        [JsonProperty("vehicleAccessibility")] public string VehicleAccessibility;

        [JsonProperty("vehicleAccessibilityComments")]
        public string VehicleAccessibilityComments;

        [JsonProperty("waterAvailable")] public string WaterAvailable;
    }
}