.details-tab {
    display: flex;
    flex-direction: column;
}

.buttons {
    display: flex;
    justify-content: flex-end;
    dx-button {
        margin-left: 1rem;
    }
}

::ng-deep .item-bottom-spacing {
    margin-bottom: 30px;
}

::ng-deep .photo-spacing {
    margin-bottom: 30.5px;
}

::ng-deep .dx-gallery .dx-gallery-nav-button-prev,
::ng-deep .dx-gallery .dx-gallery-nav-button-next {
    width: 0% !important;
}

::ng-deep .popup-template {
    width: 100%;
    height: 100%;
    max-height: 100%;
    display: flex;
    flex-direction: row;
    white-space: pre-wrap;
    .image-container {
        flex-grow: 1;
        display: flex;
        height: 100%;
        dx-button {
            justify-self: flex-end;
        }
        img {
            height: 100%;
            width: 100%;
            object-fit: contain;
            justify-self: center;
            margin: auto;
        }
    }
    .info {
        margin-left: 1em;
        justify-content: space-between;
        height: 100%;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        .text-content {
            text-align: left;
            .description-buttons {
                display: flex;
                margin-bottom: 1em;
                margin-right: 1em;
                dx-button {
                    margin-top: 1em;
                    margin-left: 1em;
                    &:first-child {
                        margin-left: auto;
                    }
                }
            }
        }
        .buttons {
            margin-bottom: 1em;
            margin-right: 1em;
            align-self: flex-end;
            dx-button {
                margin-left: 1em;
            }
        }
    }
}
