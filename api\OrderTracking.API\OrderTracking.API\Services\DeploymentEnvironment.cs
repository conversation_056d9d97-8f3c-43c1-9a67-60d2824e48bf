using System;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     Service for querying the deployment environment of the current process
    /// </summary>
    public class DeploymentEnvironment
    {
        private readonly string[] _allowedEnvironments = {"Local", "Development", "Test", "Staging", "Production"};
        private readonly IConfiguration _config;
        private readonly ILogger<DeploymentEnvironment> _logger;

        /// <summary>
        ///     Constructor for DeploymentEnvironment service
        /// </summary>
        /// <param name="config"></param>
        /// <param name="logger"></param>
        public DeploymentEnvironment(IConfiguration config, ILogger<DeploymentEnvironment> logger)
        {
            _config = config;
            _logger = logger;
        }

        /// <summary>
        ///     Get the name of the deployment environment this process is currently running in.
        /// </summary>
        /// <returns></returns>
        public string GetName()
        {
            string name;
            try
            {
                name = _config.GetValue<string>("DeploymentEnvironment");
                if (_allowedEnvironments.Contains(name) == false)
                    throw new InvalidDeploymentEnvironmentNameException(name);
                if (name != null)
                {
                    return name;
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e.Message);
                throw;
            }
            finally
            {
                name = "Unknown";
            }

            return name;
        }

        public bool IsLocal() => GetName() == "Local";

        public bool IsDevelopment() => GetName() == "Development";

        public bool IsTest() => GetName() == "Test";

        public bool IsStaging() => GetName() == "Staging";

        public bool IsProduction() => GetName() == "Production";
    }

    /// <summary>
    ///     Specific exception for detecting an invalid deployment environment name
    /// </summary>
    public class InvalidDeploymentEnvironmentNameException : Exception
    {
        /// <summary>
        ///     Creates a new <see cref="InvalidDeploymentEnvironmentNameException" /> and provides the invalid name
        /// </summary>
        /// <param name="name"></param>
        public InvalidDeploymentEnvironmentNameException(string name) : base(
            $"Invalid deployment environment name: {name}.  Expected to be one of the following: Local, Development, Test, Staging, Production")
        {
        }

        /// <summary>
        ///     Creates a new <see cref="InvalidDeploymentEnvironmentNameException" />.  Please specify the invalid name if
        ///     possible.
        /// </summary>
        public InvalidDeploymentEnvironmentNameException() : base(
            "Invalid deployment environment name.  Expected to be one of the following: Local, Development, Test, Staging, Production")
        {
        }

        /// <summary>
        ///     Creates a new <see cref="InvalidDeploymentEnvironmentNameException" />.  Please specify the invalid name if
        ///     possible.
        /// </summary>
        /// <param name="message"></param>
        /// <param name="innerException"></param>
        public InvalidDeploymentEnvironmentNameException(string message, Exception innerException) : base(message,
            innerException)
        {
        }
    }
}