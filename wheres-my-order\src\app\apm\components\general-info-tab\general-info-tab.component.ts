import { Component, EventEmitter, Input, Output } from '@angular/core';
import { LeakReport } from '../../models/data/leak-report';

@Component({
    selector: 'app-general-info-tab',
    templateUrl: './general-info-tab.component.html',
    styleUrls: ['./general-info-tab.component.scss']
})
export class GeneralInfoTabComponent {
    @Input() allowEditing: boolean;
    @Input() report: LeakReport;
    @Output() statusChanged = new EventEmitter<string>();

    statusEditorOptions = {
        dataSource: ['active', 'closed'],
        layout: 'horizontal',
        onValueChanged: this.onStatusChanged.bind(this)
    };

    constructor() {}

    private onStatusChanged(e) {
        // Only forward this event if the change came from user interaction.
        // Don't want to broadcast when the form is just getting bound to values
        // upon initialization
        if (e.event) this.statusChanged.next(e.value);
    }
}
