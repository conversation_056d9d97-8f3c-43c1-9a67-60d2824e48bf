using System.ComponentModel.DataAnnotations;

namespace OrderTracking.API.Models.APM
{
    /// <summary>
    ///     Creation payload for POST BusinessUnits
    /// </summary>
    public class NewBusinessUnit
    {
        /// <summary>
        ///     ID of the client to add the business unit to
        /// </summary>
        [Required]
        public string ClientId { get; set; }

        /// <summary>
        ///     Name of new business unit.
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        ///     The users to add to the new business unit.
        /// </summary>
        public string[] UserIds { get; set; }
    }
}