import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import { LeakReportGridRowsPipe } from '../../pipes';
import { ApmService } from '../../services';
import { LeakReportingComponent } from './leak-reporting.component';

describe('LeakReportingComponent', () => {
    let component: LeakReportingComponent;
    let fixture: ComponentFixture<LeakReportingComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [HttpClientTestingModule, ToastrModule.forRoot()],
            providers: [
                {
                    provide: ApmService,
                    useValue: {
                        getLeakReports: () => of([]),
                        buSelected$: of('123')
                    }
                }
            ],
            declarations: [LeakReportingComponent, LeakReportGridRowsPipe]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(LeakReportingComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
