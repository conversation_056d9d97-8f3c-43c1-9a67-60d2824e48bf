import { WorkOrderVm } from './work-order-vm';

export class WorkOrderDetailTabInfo {
    projectId: string;
    id: string;
    apmWorkOrderNumber: string;
    plannedStartDate: Date;
    apmProjectNumber: string;
    latitude: number;
    longitude: number;
    status: string;
    plannedEndDate: Date;
    assetCategory: string;
    primaryContactName: string;
    facility: string;
    dueDate: Date;
    teamProjectNumber: string;
    primaryContactPhone: string;
    availableStatuses: string[];

    static build(vm: Partial<WorkOrderVm>): WorkOrderDetailTabInfo {
        const workOrder = new WorkOrderDetailTabInfo();
        workOrder.id = vm.id;
        workOrder.projectId = vm.projectId;
        workOrder.apmWorkOrderNumber = vm.apmWorkOrderNumber;
        workOrder.status = vm.status;
        workOrder.facility = vm.facilityName;
        workOrder.assetCategory = vm.assetCategory;
        if (vm.plannedStart)
            workOrder.plannedStartDate = new Date(vm.plannedStart);
        if (vm.plannedEnd) workOrder.plannedEndDate = new Date(vm.plannedEnd);
        if (vm.dueDate) workOrder.dueDate = new Date(vm.dueDate);
        return workOrder;
    }
}
