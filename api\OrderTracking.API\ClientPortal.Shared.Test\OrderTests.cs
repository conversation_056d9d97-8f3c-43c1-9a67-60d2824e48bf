﻿using ClientPortal.Shared.Models;
using NUnit.Framework;

namespace ClientPortal.Shared.Test
{

  [TestFixture]
  public class OrderTests
  {
    [Test]
    public void SalesLineItemIdIsNull()
    {
      var order = new Order();
      Assert.That(order.ISCUSTOMORDER, Is.EqualTo(false));
    }

    [Test]
    public void SalesLineItemIdStartsWith4()
    {
      var order = new Order() { SALESLINEITEMID = "4andSomeOtherStuff" };
      Assert.That(order.ISCUSTOMORDER, Is.EqualTo(true));
    }

    [Test]
    public void SalesLineItemIdDoesNotStartWith4()
    {
      var order = new Order() { SALESLINEITEMID = "JustSomeOtherStuff" };
      Assert.That(order.ISCUSTOMORDER, Is.EqualTo(false));
    }

    [Test]
    public void SalesLineItemIdIsNullButJSSJobIdIsNot(){
      var order = new Order() {SALESLINEITEMID = null, JSSJOBID = "not empty"};
      Assert.That(order.ISCUSTOMORDER, Is.True);
    }

    [Test]
    public void SalesLineItemIdAndJSSJobIdAreBothNull() {
      var order = new Order() {SALESLINEITEMID = null, JSSJOBID = null};
      Assert.That(order.ISCUSTOMORDER, Is.False);
    }
  }
}
