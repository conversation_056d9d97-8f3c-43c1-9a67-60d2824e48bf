<dx-data-grid [dataSource]="inspectionattachments"
              [wordWrapEnabled]="true"
              [showRowLines]="true"
              [showBorders]="true"
              [rowAlternationEnabled]="true">
    <dxo-header-filter [visible]="true"></dxo-header-filter>
    <dxo-filter-row [visible]="true"></dxo-filter-row>
    <dxo-filter-panel [visible]="true"></dxo-filter-panel>
    <dxi-column dataField="documenttype"
                caption="Document Type"></dxi-column>
    <dxi-column dataField="filename"
                caption="File Name"
                [cellTemplate]="'link'"></dxi-column>
    <dxi-column dataField="description"
                caption="Description"></dxi-column>
    <dxi-column dataField="filetype"
                caption="File Type"
                [calculateCellValue]="formatFileType"></dxi-column>
    <dxi-column dataField="creationdate"
                caption="Creation date"
                dataType="date"
                format="MMM dd, yyyy"></dxi-column>
    <!--  -->
    <div *dxTemplate="let data of 'link'">
        <a *ngIf="data && data.value"
           href="javascript://void"
           (click)="download(data)">{{data.value}}</a>
    </div>

</dx-data-grid>