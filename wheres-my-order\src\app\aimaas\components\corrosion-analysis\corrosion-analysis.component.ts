import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CorrosionAnalysis } from '../../models';
import { CredoSoftService } from '../../services';

@Component({
    selector: 'app-corrosion-analysis',
    templateUrl: './corrosion-analysis.component.html',
    styleUrls: ['./corrosion-analysis.component.scss']
})
export class CorrosionAnalysisComponent implements OnInit, OnDestroy {
    corrosionanalysis: CorrosionAnalysis[];
    @Input() operationId: any;
    constructor(private readonly credoService: CredoSoftService) {}
    ngOnDestroy(): void {
        console.log('on destroy for corrsoion analysis');
    }
    ngOnInit(): void {
        console.log(this.operationId, 'operation id');
        if (this.operationId) {
            this.credoService
                .getAllCorrosionAnalysis(this.operationId)
                .subscribe((data) => {
                    this.corrosionanalysis = data.map((item) => {
                        return {
                            ...item,
                            reading:
                                item.reading !== null &&
                                item.reading !== undefined
                                    ? parseFloat(
                                          item.reading.replace('\\in', '')
                                      ).toFixed(3) + ' in'
                                    : null,
                            nominalthickness:
                                item.nominalthickness !== null &&
                                item.nominalthickness !== undefined
                                    ? parseFloat(
                                          item.nominalthickness.replace(
                                              '\\in',
                                              ''
                                          )
                                      ).toFixed(3) + ' in'
                                    : null,
                            retirementthickness:
                                item.retirementthickness !== null &&
                                item.retirementthickness !== undefined
                                    ? parseFloat(
                                          item.retirementthickness.replace(
                                              '\\in',
                                              ''
                                          )
                                      ).toFixed(3) + ' in'
                                    : null
                        };
                    });
                });
        }
    }
}
