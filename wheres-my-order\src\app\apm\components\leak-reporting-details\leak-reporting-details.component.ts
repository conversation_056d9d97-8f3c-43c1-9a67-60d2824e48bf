import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ApmService } from '../../../apm/services';
import {
    LeakReport,
    LeakReportInfo,
    LeakReportWorkDetails,
    PhotoDelete,
    PhotoDescriptionUpdate
} from '../../models';

@Component({
    selector: 'app-leak-reporting-details',
    templateUrl: './leak-reporting-details.component.html',
    styleUrls: ['./leak-reporting-details.component.scss']
})
export class LeakReportingDetailsComponent {
    @Input() report: LeakReport;
    @Output() statusChanged = new EventEmitter<string>();
    @Output() leakReportInfoSaving = new EventEmitter<
        Partial<LeakReportInfo>
    >();
    @Output() leakReportWorkDetailsSaving = new EventEmitter<
        Partial<LeakReportWorkDetails>
    >();
    @Output() photoDescriptionUpdate =
        new EventEmitter<PhotoDescriptionUpdate>();
    @Output() photoDelete = new EventEmitter<PhotoDelete>();

    allowEditing$ = this._apm.allowEditing$;

    constructor(private readonly _apm: ApmService) {}

    tabs = [
        { title: 'General', template: 'general' },
        { title: 'Work Details', template: 'workDetails' },
        { title: 'Leak Report', template: 'leakReport' },
        { title: 'Photos', template: 'photos' }
        // { title: 'Reports', template: 'reports' }
    ];

    onStatusChanged(status: string) {
        this.statusChanged.next(status);
    }

    onLeakReportInfoSaving(info: Partial<LeakReportInfo>) {
        this.leakReportInfoSaving.next(info);
    }

    onLeakReportWorkDetailsSaving(details: Partial<LeakReportWorkDetails>) {
        this.leakReportWorkDetailsSaving.next(details);
    }

    onPhotoDescriptionUpdate(update: PhotoDescriptionUpdate) {
        this.photoDescriptionUpdate.next(update);
    }

    onPhotoDelete(photoDelete: PhotoDelete) {
        this.photoDelete.next(photoDelete);
    }
}
