import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxDataGridModule } from 'devextreme-angular/ui/data-grid';
import { Observable, of, Subject } from 'rxjs';
import { TaskTypesForAssetCategoryPipe, WoTaskDetailsPipe } from '../../pipes';
import { ApmService } from '../../services';
import { TasksTabComponent } from './tasks-tab.component';

describe('TasksTabComponent', () => {
    let component: TasksTabComponent;
    let fixture: ComponentFixture<TasksTabComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DxDataGridModule],
            declarations: [TasksTabComponent],
            providers: [
                {
                    provide: ApmService,
                    useValue: {
                        creatingTask$: new Subject(),
                        selectedBU$: of('123'),
                        gettingWorkOrder$: new Observable()
                    }
                },
                WoTaskDetailsPipe,
                TaskTypesForAssetCategoryPipe
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(TasksTabComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
