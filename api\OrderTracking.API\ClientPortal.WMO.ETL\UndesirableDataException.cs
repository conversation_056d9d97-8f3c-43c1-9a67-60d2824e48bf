using System;

namespace ClientPortal.WMO.ETL
{
    public class UndesirableDataException : Exception
    {
        public UndesirableDataException(int oldDataCount, int newDataCount) : base(
            $"Data is undesirable.  Proceeding will lead to data loss.  Record count would go from {oldDataCount} to {newDataCount}.")
        {
            OldDataCount = oldDataCount;
            NewDataCount = newDataCount;
        }

        public int OldDataCount { get; }
        public int NewDataCount { get; }
    }
}