import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';

import { AppAdminGuard } from '../../core/guards';
import { AuthHistoryComponent } from './auth-history.component';

const routes: Routes = [
    {
        path: '',
        component: AuthHistoryComponent,
        data: { pageTitle: 'Auth History' },
        canActivate: [MsalGuard, AppAdminGuard],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class AuthHistoryRoutingModule {}
