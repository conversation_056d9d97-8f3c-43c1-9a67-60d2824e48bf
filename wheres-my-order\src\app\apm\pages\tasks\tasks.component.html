<div class="dx-card content-block responsive-paddings">
    <h4>Tasks</h4>
    <dx-data-grid #tasks
                  [dataSource]="tasksDataSource"
                  [remoteOperations]="true"
                  [allowColumnReordering]="true"
                  [allowColumnResizing]="true"
                  [columnResizingMode]="'widget'"
                  (onSelectionChanged)="onSelectionChanged($event)"
                  (onToolbarPreparing)="onToolbarPreparing($event)">

        <!-- CONFIG -->
        <dxo-filter-row [visible]="true"></dxo-filter-row>
        <dxo-header-filter [visible]="true"></dxo-header-filter>
        <dxo-group-panel [visible]="true"></dxo-group-panel>
        <dxo-column-chooser [enabled]="true"
                            mode="dragAndDrop">
        </dxo-column-chooser>
        <dxo-search-panel [visible]="true"
                          [highlightCaseSensitive]="true">
        </dxo-search-panel>
        <dxo-load-panel [enabled]="true"></dxo-load-panel>
        <dxo-paging [pageSize]="5"></dxo-paging>
        <dxo-pager [showPageSizeSelector]="true"
                   [visible]="true"
                   [allowedPageSizes]="[5, 10, 25, 50]"></dxo-pager>
        <dxo-selection mode="single"></dxo-selection>
        <dxo-filter-panel [visible]="true"></dxo-filter-panel>
        <dxo-state-storing [enabled]="true"
                           type="localStorage"
                           storageKey="apmTasksPageGridState">
        </dxo-state-storing>
        <dxo-export [enabled]="true"
                    [allowExportSelectedData]="true"></dxo-export>

        <!-- COLUMNS -->
        <dxi-column dataField="equipmentId"
                    caption="Equipment ID"
                    dataType="string"></dxi-column>
        <dxi-column dataField="assetDatabaseId"
                    caption="Asset Database ID"
                    [visible]="false"></dxi-column>
        <dxi-column dataField="area"
                    caption="Area"
                    dataType="string"></dxi-column>
        <dxi-column dataField="equipmentType"
                    caption="Equipment Type"
                    dataType="string"></dxi-column>
        <dxi-column dataField="equipmentDescription"
                    caption="Equipment Description"
                    dataType="string"></dxi-column>
        <dxi-column dataField="dueDate"
                    caption="Due Date"
                    dataType="date"></dxi-column>
        <dxi-column dataField="id"
                    caption="Task Database ID"
                    dataType="string"
                    [visible]="false"></dxi-column>
        <dxi-column dataField="taskType"
                    caption="Task Type"
                    dataType="string"></dxi-column>
        <dxi-column dataField="assignedUsers"
                    caption="Task Assignees"
                    [allowSorting]="false"
                    [allowFiltering]="false"
                    [allowGrouping]="false"
                    dataType="object"></dxi-column>
        <dxi-column dataField="taskUpdatedDate"
                    caption="Task Updated Date"
                    dataType="date"></dxi-column>
        <dxi-column dataField="clientWorkOrderNumber"
                    caption="Work Order"
                    dataType="string"></dxi-column>
        <dxi-column dataField="afe"
                    caption="AFE"
                    dataType="string"></dxi-column>
        <dxi-column dataField="supervisor"
                    caption="Supervisor"
                    dataType="string"></dxi-column>
        <dxi-column dataField="plannedStart"
                    caption="Planned Start"
                    dataType="date"></dxi-column>
        <dxi-column dataField="plannedEnd"
                    caption="Planned End"
                    dataType="date"></dxi-column>
        <dxi-column dataField="status"
                    caption="Status"
                    dataType="string"></dxi-column>
        <dxi-column dataField="apmProjectNumber"
                    caption="APM Project Number"
                    dataType="string"
                    [visible]="false">
        </dxi-column>
        <dxi-column dataField="apmWorkOrderNumber"
                    caption="APM Work Order Number"
                    dataType="string"
                    [visible]="false"></dxi-column>
        <dxi-column dataField="projectName"
                    caption="Project Name"
                    dataType="string"
                    [visible]="false">
        </dxi-column>
        <dxi-column dataField="projectId"
                    caption="Project Database ID"
                    [visible]="false"></dxi-column>
        <dxi-column dataField="teamProjectNumber"
                    caption="TEAM Project Number"
                    dataType="string"
                    [visible]="false"></dxi-column>
        <dxi-column dataField="apmTaskNumber"
                    caption="APM Task Number"
                    dataType="string"
                    [visible]="false"></dxi-column>
        <!-- TODO: What is this supposed to be? -->
        <dxi-column dataField="client"
                    caption="Client"
                    dataType="string"
                    [visible]="false"></dxi-column>

    </dx-data-grid>

</div>

<div class="dx-card content-block responsive-paddings">
    <div *ngIf="loading$ | async">
        <dx-load-indicator style="position: relative; left: 50%;"
                           id="large-indicator"
                           height="60"
                           width="60"></dx-load-indicator>
    </div>
    <ng-container *ngIf="workOrder$ | async as workOrder">
        <dx-form [colCount]="5"
                 [(formData)]="workOrderDetail">
            <dxi-item itemType="group"
                      [colSpan]="2"
                      [colCount]="2">
                <dxi-item itemType="group">
                    <dxi-item dataField="assetID"
                              [editorOptions]="{readOnly:true}">
                        <dxo-label text="Asset Number or Identification">
                        </dxo-label>
                    </dxi-item>
                    <dxi-item dataField="assetDescription"
                              [editorOptions]="{hint: workOrderDetail?.assetDescription, readOnly:true}">
                        <dxo-label text="Asset Description"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="client"
                              [editorOptions]="{readOnly:true}"></dxi-item>
                    <dxi-item dataField="location"
                              [editorOptions]="{readOnly:true}">
                        <dxo-label text="Facility Name"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item itemType="group"
                          [colSpan]="2"
                          [colCount]="2">
                    <dxi-item dataField="projectName"
                              [editorOptions]="{readOnly:true}"></dxi-item>
                    <dxi-item dataField="afeNumber"
                              [editorOptions]="{hint: workOrderDetail?.afeNumber, readOnly:true}">

                        <dxo-label text="AFE Number"></dxo-label>
                    </dxi-item>
                </dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      [colSpan]="3">
                <dx-data-grid #headerTasks
                              style="max-height: 11rem;"
                              (onRowUpdated)="onRowUpdated($event)"
                              [dataSource]="workOrderDetail?.inspectionTypes"
                              (onEditorPreparing)="onHeaderTaskGridEditorPreparing($event)">
                    <dxo-editing mode="cell"
                                 [allowUpdating]="true">

                    </dxo-editing>

                    <dxo-column-fixing [enabled]="false"></dxo-column-fixing>
                    <dxo-paging [enabled]="false"></dxo-paging>

                    <dxi-column [allowEditing]="false"
                                dataField="taskType"
                                dataType="string"></dxi-column>
                    <dxi-column dataField="taskId"
                                [allowEditing]="false"
                                caption="Task ID"
                                dataType="string"></dxi-column>
                    <dxi-column dataField="taskAssignees"
                                [allowEditing]="allowEditing$ | async"
                                dataType="object"
                                editCellTemplate="tagBoxEditor"></dxi-column>
                    <dxi-column dataField="supervisor"
                                dataType="string"
                                [allowEditing]="allowEditing$ | async"
                                editCellTemplate="selectBoxEditor"></dxi-column>
                    <dxi-column dataField="status"
                                dataType="string"
                                [allowEditing]="allowEditing$ | async"
                                [editorOptions]="{itemTemplate: 'statusTemplate'}">
                        <dxo-lookup></dxo-lookup>
                    </dxi-column>
                    <div *dxTemplate="let status of 'statusTemplate'">
                        <span class="middle">{{status}}</span>
                    </div>

                    <div *dxTemplate="let cellInfo of 'tagBoxEditor'">
                        <dx-tag-box [dataSource]="users$ | async | userEmails"
                                    [value]="cellInfo.value"
                                    [showSelectionControls]="true"
                                    [showMultiTagOnly]="false"
                                    applyValueMode="useButtons"
                                    [width]="200"
                                    [searchEnabled]="true"
                                    [disabled]="(allowEditing$ | async) === false"
                                    (onValueChanged)="cellInfo.setValue($event.value)"
                                    (onSelectionChanged)="cellInfo.component.updateDimensions()">
                        </dx-tag-box>
                    </div>

                    <div *dxTemplate="let cellInfo of 'selectBoxEditor'">
                        <dx-select-box [dataSource]="users$ | async | userEmails"
                                       [value]="cellInfo.value"
                                       [showSelectionControls]="true"
                                       applyValueMode="useButtons"
                                       [width]="200"
                                       [searchEnabled]="true"
                                       [readOnly]="(allowEditing$ | async) === false"
                                       (onValueChanged)="cellInfo.setValue($event.value)"
                                       (onSelectionChanged)="cellInfo.component.updateDimensions()">
                        </dx-select-box>
                    </div>

                </dx-data-grid>
            </dxi-item>
        </dx-form>
    </ng-container>
    <dx-tab-panel *ngIf="workOrder$ | async as workOrder; else emptyTabPanel"
                  [showNavButtons]="true"
                  (onSelectionChanged)="onTabSelectionChanged($event)">

        <dxi-item title="Asset Details">
            <app-apm-asset-details [workOrder]="workOrder"
                                   (photoDelete)="onAssetDetailsPhotoDelete($event)"
                                   (photoDescriptionUpdate)="onAssetDetailsPhotoDescriptionUpdate($event)"
                                   [allowEditing]="allowEditing$ | async">
            </app-apm-asset-details>
        </dxi-item>
        <dxi-item title="Asset PPE">
            <app-asset-ppe [assetPPE]="workOrder | assetPpe"
                           (save)="onAssetPPESave($event)"
                           [allowEditing]="allowEditing$ | async">
            </app-asset-ppe>
        </dxi-item>
        <dxi-item title="Asset Access">
            <app-asset-access [assetAccess]="workOrder | assetAccess"
                              (assetAccessUpdated)="assetAccessUpdated($event)"
                              [allowEditing]="allowEditing$ | async">
            </app-asset-access>
        </dxi-item>
        <dxi-item title="Inspection Information">
            <app-inspection-information [inspectionInfo]="workOrder | inspectionInfo"
                                        (save)="onSaveInspectionInfo($event)"
                                        [allowEditing]="allowEditing$ | async">
            </app-inspection-information>
        </dxi-item>
        <dxi-item title="Internal Inspection Results"
                  [visible]="internalInspectionResultsVisible$ | async">
            <app-inspection-results [inspectionResult]="workOrder | inspectionResults: 'Internal Visual' : (taskId$ | async)"
                                    (photoDelete)="onPhotoDelete($event)"
                                    (photoDescriptionUpdate)="onPhotoDescriptionUpdate($event)"
                                    [allowEditing]="allowEditing$ | async">
            </app-inspection-results>
        </dxi-item>
        <dxi-item title="External Inspection Results"
                  [visible]="externalInspectionResultsVisible$ | async">
            <app-inspection-results [inspectionResult]="workOrder | inspectionResults: 'External Visual' : (taskId$ | async)"
                                    (photoDelete)="onPhotoDelete($event)"
                                    (photoDescriptionUpdate)="onPhotoDescriptionUpdate($event)"
                                    [allowEditing]="allowEditing$ | async">
            </app-inspection-results>
        </dxi-item>
        <dxi-item title="Attachments">
            <app-attachments-tab [workOrder]="workOrder"
                                 [allowEditing]="allowEditing$ | async">
            </app-attachments-tab>
        </dxi-item>
        <dxi-item title="Reports">
            <app-report-tab
                [workOrder]="workOrder$ | async"
                [workOrderDetail]="workOrderDetail"
                (publishedUnpublished)="onPublishedUnpublished($event)"
                [allowEditing]="allowEditing$ | async">
            </app-report-tab>
        </dxi-item>

    </dx-tab-panel>
    <ng-template #emptyTabPanel>
        <dx-tab-panel></dx-tab-panel>
    </ng-template>
</div>