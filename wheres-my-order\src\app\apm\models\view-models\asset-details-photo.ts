import { Photo } from '..';

export interface AssetDetailsPhoto {
    section: string;
    photo: Photo;
    projectId: string;
    workOrderId: string;
    taskId?: string;
}

export interface AssetPath {
    allPhotos?: object;
    frontPhotos?: Object;
    backPhotos?: Object;
    leftPhotos?: Object;
    rightPhotos?: Object;
    questionPhotos?: Object;
    subSectionQuestionPhotos?: Object;
    leakRepairPhotos?: Object;
    leakReportPhotos?: {
        equipmentID: Object;
        equipmentIDAtLineStart: Object;
        pipeSize: Object;
        processService: Object;
        distanceBetweenTieInPoints: Object;
        estimatedLossRate: Object;
        featureFittingCount: Object;
        equipmentDescription: Object;
        equipmentIDAtLineEnd: Object;
        pipeSchedule: Object;
        pipeCover: Object;
        corrosionType: Object;
        existingClampCount: Object;
        observationSummary: Object;
        affectedLength: Object;
    };
    workDetailPhotos?: {
        client: Object;
        state: Object;
        city: Object;
        area: Object;
        facility: Object;
        lease: Object;
        clientContact: Object;
        clientWorkOrder: Object;
        purchaseOrder: Object;
        teamProjectNumber: Object;
        referenceEdition: Object;
        inspectionTypes: Object;
        inspectedBy: Object;
        postalCode: Object;
        jobDescription: Object;
        clientContactNumber: Object;
        teamDistrict: Object;
        clientCostCode: Object;
        inspectionReference: Object;
        inspectionDate: Object;
        inspectorCertificateNumber: Object;
        reviewedBy: Object;
        reviewerEmail: Object;
        reviewerCertificateNumber: Object;
    };
}
