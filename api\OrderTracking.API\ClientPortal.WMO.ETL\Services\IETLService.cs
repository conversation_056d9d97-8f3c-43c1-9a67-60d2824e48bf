using System.Threading.Tasks;
using ClientPortal.Shared.Models;

namespace ClientPortal.WMO.ETL.Services
{
    public interface IETLService
    {
        Task FinishJobAsync(OrdersJob ordersJob, string status);
        Task BulkCopySourceToTargetAsync();
        OrdersJob StartJob();
        Task InsertNewOrdersAsync(OrdersJob ordersJob);
        Task UpdateChangedOrdersAsync(OrdersJob ordersJob);
        Task DeleteRemovedOrdersAsync(OrdersJob ordersJob);
        Task SkipJobAsync();
        Task EnsureTargetDatabaseReadyAsync();
        Task CheckSourceData();
    }
}