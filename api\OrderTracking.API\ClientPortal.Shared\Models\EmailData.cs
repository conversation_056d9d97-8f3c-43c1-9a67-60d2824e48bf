﻿using System.Collections.Generic;
using ClientPortal.Shared.Services;

namespace ClientPortal.Shared.Models
{
    /// <summary>
    /// A data type used to store info needed to send an email
    /// </summary>
    public class EmailData
    {
        /// <summary>
        /// List of recipients for the email being sent
        /// </summary>
        public List<IEmailRecipient> Recipients;
        /// <summary>
        /// The subject line of the email
        /// </summary>
        public string Subject;
        /// <summary>
        /// The body/html content of the email
        /// </summary>
        public string HtmlContent;
        /// <summary>
        /// List of any included attachments
        /// </summary>
        public List<EmailAttachment> Attachments;
    }
}
