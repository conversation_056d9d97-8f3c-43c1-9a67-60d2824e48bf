﻿//using System.Threading.Tasks;
//using ClientPortal.Shared.Models;

//namespace OrderTracking.API.Repositories
//{
//    /// <summary>
//    ///     Standard CRUD methods for Cosmos Entities where a Partition Id is needed.
//    ///     This interface is for methods that require PartitionId and the implementor
//    ///     knows how to assign this appropriately.
//    /// </summary>
//    public interface IAsyncCosmosRepository<TEntity, in TKey>
//#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
//        : IAsyncRepository<TEntity, TKey>, ICosmosRepository where TEntity : ICosmosEntity<TKey>?
//#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
//    {
//        #region Public Methods

//        /// <summary>
//        ///     Get document with id and partition key
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="partitionKey"></param>
//        /// <returns></returns>
//        Task<TEntity> GetAsync(TKey id, string partitionKey);

//        /// <summary>
//        ///     Update document
//        /// </summary>
//        /// <param name="entity"></param>
//        /// <param name="originalId"></param>
//        /// <param name="originalPartitionKey"></param>
//        /// <returns></returns>
//        Task<TEntity> UpdateAsync(TEntity entity, TKey originalId, string originalPartitionKey);
        
//        /// <summary>
//        ///     Remove document
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="partitionKey"></param>
//        /// <returns></returns>
//        Task RemoveAsync(TKey id, string partitionKey);
        
//        #endregion
//    }
//}