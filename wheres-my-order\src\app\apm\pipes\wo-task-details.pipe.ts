import { Pipe, PipeTransform } from '@angular/core';
import { TaskDetail, WorkOrder } from '../models';

@Pipe({
    name: 'woTaskDetails'
})
export class WoTaskDetailsPipe implements PipeTransform {
    transform(workOrder: WorkOrder): TaskDetail[] {
        if (workOrder) {
            return workOrder.tasks.map((entry) => {
                return {
                    id: entry.id,
                    clientWorkOrderDescription:
                        entry.clientWorkOrderDescription.currentValue,
                    leadTechnician: entry.leadTech.currentValue,
                    clientWorkOrderNumber:
                        entry.clientWorkOrderNumber.currentValue,
                    plannedEnd: entry.plannedEnd.currentValue,
                    plannedStart: entry.plannedStart.currentValue,
                    status: entry.status.currentValue,
                    supervisor: entry.taskDetails.supervisor.currentValue,
                    taskAssignees: entry.assignedUsers,
                    taskCreatedBy: entry.createdBy,
                    taskCreatedDate: entry.createdTime,
                    taskType: entry.taskType,
                    taskUpdatedDate: entry.lastChangedTime,
                    statuses: workOrder.status.options,
                    clientCostCode: entry.clientCostCode.currentValue,
                    purchaseOrderAFE: entry.purchaseOrderAFE.currentValue,
                    dueDate: entry.dueDate.currentValue
                };
            });
        } else {
            return [];
        }
    }
}
