﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class NewLeakReport
    {
        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonProperty("area")]
        public string Area { get; set; }

        [JsonProperty("lease")]
        public string Lease { get; set; }

        [JsonProperty("jobDescription")]
        public string JobDescription { get; set; }

        [JsonProperty("equipmentId")]
        public string EquipmentID;
    }
}
