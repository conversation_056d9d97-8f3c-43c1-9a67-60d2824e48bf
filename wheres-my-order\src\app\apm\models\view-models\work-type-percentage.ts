import { ActivitySummaryItem } from '../../models';

export interface WorkTypePercentage {
    workType: string;
    hours: number;
}

export class WorkTypePercentages {
    static createFrom(activities: ActivitySummaryItem[]): WorkTypePercentage[] {
        const map = new Map();
        activities?.forEach((activity) => {
            if (activity.name.toLowerCase().includes('fw'))
                activity.name = 'Field Work';
            if (map.has(activity.name))
                map.set(
                    activity.name,
                    map.get(activity.name) + activity.duration
                );
            else map.set(activity.name, activity.duration);
        });
        return Array.from(map, ([name, value]) => ({
            workType: name,
            hours: Number(value.toFixed(2))
        }));
    }
}
