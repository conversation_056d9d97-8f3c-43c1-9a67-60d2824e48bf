import { HttpClient, HttpParams } from '@angular/common/http';
import {
    Component,
    EventEmitter,
    Input,
    Output,
    ViewChild
} from '@angular/core';
import { Router } from '@angular/router';
import { DxDataGridComponent } from 'devextreme-angular';
import CustomStore from 'devextreme/data/custom_store';
import DataSource from 'devextreme/data/data_source';
import { ClickEvent } from 'devextreme/ui/button';
import {
    ColumnButtonClickEvent,
    ContentReadyEvent,
    EditorPreparingEvent,
    RowInsertedEvent,
    SelectionChangedEvent
} from 'devextreme/ui/data_grid';
import { ToastrService } from 'ngx-toastr';
import { lastValueFrom } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { debug } from '../../../core/operators';
import { isNotEmpty } from '../../../shared/helpers';
import { DataGridService } from '../../../shared/services';
import { AssetVm, ProjectVm, WorkOrder, WorkOrderGridRow } from '../../models';
import { ApmService } from '../../services';

let that: WorkOrdersGridComponent;

@Component({
    selector: 'app-work-orders-grid',
    templateUrl: './work-orders-grid.component.html',
    styleUrls: ['./work-orders-grid.component.scss']
})
export class WorkOrdersGridComponent {
    private _firstContentReady = true;

    assetEditorOptions = {
        showClearButton: true,
        searchEnabled: true,
        searchExpr: ['assetCategory', 'equipmentId'],
        hint: 'Search by Asset Category or Asset ID',
        dataSource: this._apm.assetsDataSource,
        disabled: true,
        valueExpr: 'id',
        paginate: true,
        displayExpr: (e: AssetVm) =>
            e ? `${e.equipmentId} - ${e?.assetCategory}` : null
    };

    projectEditorOptions = {
        showClearButton: true,
        searchEnabled: true,
        searchExpr: ['name'],
        hint: 'Search by Project Name',
        dataSource: this._apm.projectDataSource,
        valueExpr: 'id',
        paginate: true,
        displayExpr: (e: ProjectVm) => (e ? e.name : null)
    };
    projectId: string;
    selectedBU: string = "";
    projectsDataSource = new CustomStore({
        key: 'id',
        byKey: (key: any) =>
            lastValueFrom(
                this._http.get(`${environment.api.url}/APM/Project/${key}`)
            ),
        load: (loadOptions) => {
            let params: HttpParams = new HttpParams();
            [
                'filter',
                'group',
                'groupSummary',
                'parentIds',
                'requireGroupCount',
                'requireTotalCount',
                'searchExpr',
                'searchOperation',
                'searchValue',
                'select',
                'sort',
                'skip',
                'take',
                'totalSummary',
                'userData' // TODO:
            ].forEach((i) => {
                if (i in loadOptions && isNotEmpty(loadOptions[i])) {
                    params = params.set(i, JSON.stringify(loadOptions[i]));
                }
            });

            return lastValueFrom(
                this._http.get<{ data: ProjectVm[] }>(
                    `${environment.api.url}/APM/Projects/names`,
                    { params }
                )
            );
        }
    });
    workOrdersDataSource = new DataSource({
        store: new CustomStore({
            key: 'id',
            byKey: (key: any) =>
                lastValueFrom(
                    this._http.get(
                        `${environment.api.url}/APM/WorkOrder/${key}`
                    )
                ),
            load: async (loadOptions) => {
                let res = lastValueFrom(
                    this._apm
                        .loadWorkOrders(loadOptions)
                        .pipe(debug('Load Work Orders'))
                )
                return res.then((value) => {
                    return value ?? Promise.resolve({ data: [], totalCount: 0 })
                })
            },
            insert: (value: any) =>
                lastValueFrom(
                    this._apm
                        .createWorkOrder({
                            projectId: value.projectId,
                            assetDatabaseId: value.asset.id,
                            dueDate: value?.dueDate,
                            facilityName: value?.facilityName,
                            plannedEnd: value?.plannedEnd,
                            plannedStart: value?.plannedStart,
                            status: value?.status
                        })
                        .pipe(debug('Create Work Order'))
                )
        })
    });

    @Input() allowEditing: boolean;

    @Output() selectionChanged = new EventEmitter<WorkOrderGridRow>();
    @ViewChild(DxDataGridComponent) dataGrid: DxDataGridComponent;

    disableAddRowButton$ = this._apm.selectedBU$.pipe(
        map((bu) => bu === undefined || bu === null)
    );

    constructor(
        private readonly _router: Router,
        private readonly _apm: ApmService,
        private readonly _grid: DataGridService,
        private readonly _http: HttpClient,
        private readonly _toasts: ToastrService
    ) {
        that = this;
        this.projectId =
            this._router.getCurrentNavigation()?.extras?.state?.projectId;

        this._apm.selectedBU$.subscribe((selected: string) => {
            if (!this.selectedBU) {
                this.selectedBU = selected;
                return;
            }
            if (selected != this.selectedBU) {
                this.selectedBU = selected
                this.refreshGrid()
            }
        });
    }

    setProjectCellValue(newData: any, value: any, currentRowData: any): void {
        that.assetEditorOptions.dataSource.load();

        newData.asset = { id: null };
        (<any>this).defaultSetCellValue(newData, value);
    }

    restoreDefaultsClicked = async (e: ClickEvent) => {
        const result = await this._grid.resetGridState(this.dataGrid);
        if (result) localStorage.removeItem('apmWorkOrdersGridState');
    };

    onSelectionChanged(e: SelectionChangedEvent) {
        this.selectionChanged.next(e.selectedRowsData[0]);
        if (e.selectedRowsData.length > 0) {
            this.dataGrid.focusedRowKey = e.selectedRowsData[0].id;
        } else {
            this.dataGrid.focusedRowKey = null;
        }
    }

    workOrderDetailsClicked = (e: ColumnButtonClickEvent) => {
        this._router.navigate(['apm/work-order-details', e.row.data.id], {
            queryParams: { projectId: e.row.data.projectId }
        });
    };

    onRowInserted(e: RowInsertedEvent) {
        this.dataGrid.instance.selectRows([e.key], false);
        this.dataGrid.focusedRowKey = e.key;
        this._toasts.success('Work Order successfully created', 'Success!');
    }

    onEditorPreparing(e: EditorPreparingEvent) {
        if (e.parentType === 'dataRow') {
            if (e.dataField === 'asset.id') {
                e.editorOptions.disabled =
                    typeof e.row.data.projectId !== 'string';
            }
        }
    }

    onContentReady(e: ContentReadyEvent) {
        if (this.projectId && this._firstContentReady) {
            this._firstContentReady = false;
            e.component.columnOption(
                'projectId',
                'filterValue',
                this.projectId
            );
        }
    }

    calculateAssetId(rowData: WorkOrder) {
        return rowData?.asset?.assetCategory === 'Piping'
            ? rowData.asset?.walkDown.sectionIdentification
                ?.attributeNumber_or_Circuit_ID?.currentValue
            : rowData?.asset?.walkDown.sectionIdentification
                ?.attributeNumber_or_ID?.currentValue;
    }

    refreshGrid() {
        this.dataGrid?.instance?.refresh();
    }
}
