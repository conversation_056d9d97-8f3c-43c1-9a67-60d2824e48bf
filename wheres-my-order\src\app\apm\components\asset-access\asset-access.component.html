<div class="responsive-paddings content-block">
    <div *ngIf="!assetAccess">
        <dx-load-indicator style="position: relative; left: 50%;"
                           id="large-indicator"
                           height="60"
                           width="60"></dx-load-indicator>
    </div>
    <dx-form *ngIf="assetAccess"
             [(formData)]="assetAccess"
             [readOnly]="!isEditing || !allowEditing">
        <dxi-item itemType="group"
                  caption="External Surface Conditions">
            <dxi-item itemType="group"
                      caption="Insulation"
                      cssClass="smaller-caption"
                      [colCount]="4">
                <dxi-item dataField="hasInsulation"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: hasInsulationItems}">
                </dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="possibleAsbestos"
                              editorType="dxSwitch"
                              [editorOptions]="{items: possibleAsbestosItems}">
                    </dxi-item>
                    <dxi-item dataField="possibleAsbestosComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item dataField="jacketingType"
                          editorType="dxTagBox"
                          [editorOptions]="{items: jacketingTypeItems, acceptCustomValue: true}">
                </dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="insulationType"
                              editorType="dxTagBox"
                              [editorOptions]="{items: insulationTypeItems, acceptCustomValue: true}">
                    </dxi-item>
                    <dxi-item dataField="insulationTypeComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="insulationRemovalRequired"
                              editorType="dxSwitch"></dxi-item>
                    <dxi-item dataField="insulationRemovalRequiredComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="heatTracing"
                              editorType="dxTagBox"
                              [editorOptions]="{items: heatTracingItems, acceptCustomValue: true}">
                    </dxi-item>
                    <dxi-item dataField="heatTracingComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>

            </dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="Inspection Ports"
                  [colCount]="4">
            <dxi-item dataField="existingInspectionPorts"
                      editorType="dxSwitch"></dxi-item>
            <dxi-item itemType="group">
                <dxi-item dataField="insulationPlugsMissing"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item dataField="insulationPlugsMissingComment"
                          editorType="dxTextArea">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item itemType="group">
                <dxi-item dataField="additionalPortsNeeded"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item dataField="additionalPortsNeededComment"
                          editorType="dxTextArea">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="External Surface Conditions">
            <dxi-item itemType="group"
                      caption="Coating"
                      cssClass="smaller-caption"
                      [colCount]="4">
                <dxi-item itemType="group">
                    <dxi-item dataField="coatingType"
                              editorType="dxTagBox"
                              [editorOptions]="{items: coatingTypeItems, acceptCustomValue: true}">
                    </dxi-item>
                    <dxi-item dataField="coatingTypeComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item dataField="coatingCondition"
                          editorType="dxTagBox"
                          [editorOptions]="{items: coatingConditionItems, acceptCustomValue: true}">
                </dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="coatingConditionsObserved"
                              editorType="dxTagBox"
                              [editorOptions]="{items: coatingConditionsObservedItems, acceptCustomValue: true}">
                    </dxi-item>
                    <dxi-item dataField="coatingConditionsObservedComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="coatingRemovalRequired"
                              editorType="dxSwitch"></dxi-item>
                    <dxi-item dataField="coatingRemovalRequiredComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      caption="Corrosion"
                      cssClass="smaller-caption"
                      [colCount]="4">
                <dxi-item itemType="group">
                    <dxi-item dataField="corrosionIdentified"
                              editorType="dxTagBox"
                              [editorOptions]="{items: corrosionIdentifiedItems, acceptCustomValue: true}">
                    </dxi-item>
                    <dxi-item dataField="corrosionIdentifiedComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="corrosionRemovalRecommendation"
                              editorType="dxTagBox"
                              [editorOptions]="{items: corrosionRemovalRecommendationItems, acceptCustomValue: true}">
                    </dxi-item>
                    <dxi-item dataField="corrosionRemovalRecommendationComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
            </dxi-item>
        </dxi-item>
        <dxi-item itemType="group"
                  caption="Accessibility">
            <dxi-item itemType="group"
                      [colCount]="4">
                <dxi-item itemType="group">
                    <dxi-item dataField="fixedEquipmentLaddersStairwaysPlatformsInstalled"
                              editorType="dxSwitch">
                        <dxo-label
                                   text="Fixed Equipment Ladders/Stairways/Platforms Installed">
                        </dxo-label>
                    </dxi-item>
                    <dxi-item dataField="fixedEquipmentLaddersStairwaysPlatformsInstalledComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="allComponentsUnder4FeetInHeight"
                              editorType="dxSwitch">
                        <dxo-label text="All Components Under 4 ft. in Height">
                        </dxo-label>
                    </dxi-item>
                    <dxi-item dataField="allComponentsUnder4FeetInHeightComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item itemType="group"
                          [disabled]="allUnderFourFeet">
                    <dxi-item dataField="ladderRequirements"
                              editorType="dxTagBox"
                              [editorOptions]="{items: ladderRequirementsItems, acceptCustomValue: true}">
                    </dxi-item>
                    <dxi-item dataField="ladderRequirementsComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      caption="Aerial Lift Requirements"
                      cssClass="smaller-caption"
                      [colCount]="4"
                      [disabled]="allUnderFourFeet">
                <dxi-item itemType="group">
                    <dxi-item dataField="aerialLiftNeeded"
                              editorType="dxSwitch"></dxi-item>
                    <dxi-item dataField="aerialLiftNeededComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item dataField="accessForAerialLiftForAllLocationsAtHeight"
                          editorType="dxSwitch">
                    <dxo-label
                               text="Site Has Access for Aerial Lift for ALL Locations at Height">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="gasPoweredPermitted"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item dataField="batteryPoweredPermitted"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item dataField="clientRequiredProofOfTraining"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item dataField="clientProvidedOperator"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item dataField="estimatedDistanceToAnyLiveElectricalOverheadLines"
                          editorType="dxNumberBox"></dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="scaffoldingRequired"
                              editorType="dxSwitch"></dxi-item>
                    <dxi-item dataField="scaffoldingRequiredComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="ropeAccessRequired"
                              editorType="dxSwitch"></dxi-item>
                    <dxi-item dataField="ropeAccessRequiredComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item dataField="assetOutOfService"
                          editorType="dxSwitch"></dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      caption="Internal Access Requirements"
                      cssClass="smaller-caption"
                      [colCount]="4">
                <dxi-item dataField="inspectionOpeningsPresent"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="inspectionOpeningTypes"
                              editorType="dxTagBox"
                              [editorOptions]="{items: inspectionOpeningTypes, acceptCustomValue: true}">
                    </dxi-item>
                    <dxi-item dataField="inspectionOpeningTypesComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item dataField="sizeOfAllAccessOpenings"
                          editorType="dxTextBox"></dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="ventilationRequirements"
                              editorType="dxTagBox"
                              [editorOptions]="{items: ventilationRequirementsItems, acceptCustomValue: true}">
                    </dxi-item>
                    <dxi-item dataField="ventilationRequirementsComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      caption="Cleaning Requirements"
                      cssClass="smaller-caption"
                      [colCount]="4">
                <dxi-item itemType="group">
                    <dxi-item dataField="cleaningRecommendations"
                              editorType="dxTagBox"
                              [editorOptions]="{items: cleaningRecommendations, acceptCustomValue: true}">
                    </dxi-item>
                    <dxi-item dataField="cleaningRecommendationsComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="cleaningServiceReview"
                              editorType="dxSelectBox"
                              [editorOptions]="{items: cleaningServiceReviewItems}">
                    </dxi-item>
                    <dxi-item dataField="cleaningServiceReviewComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      caption="Internal Coating Layer"
                      cssClass="smaller-caption"
                      [colCount]="4">
                <dxi-item itemType="group">
                    <dxi-item dataField="coatingLinerType"
                              editorType="dxTagBox"
                              [editorOptions]="{items:coatingLinerTypes, acceptCustomValue: true}">
                        <dxo-label text="Coating/Liner Type"></dxo-label>
                    </dxi-item>
                    <dxi-item dataField="coatingLinerTypeComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
                <dxi-item dataField="coatingLinerConditions"
                          editorType="dxTagBox"
                          [editorOptions]="{items: coatingLinerConditions, acceptCustomValue: true}">
                    <dxo-label text="Coating/Liner Conditions"></dxo-label>
                </dxi-item>
                <dxi-item itemType="group">
                    <dxi-item dataField="coatingLinerConditionsObserved"
                              editorType="dxTagBox"
                              [editorOptions]="{items: coatingLinerConditionsObservedItems, acceptCustomValue: true}">
                        <dxo-label text="Coating/Liner Conditions Observed">
                        </dxo-label>
                    </dxi-item>
                    <dxi-item dataField="coatingLinerConditionsObservedComment"
                              editorType="dxTextArea">
                        <dxo-label text="Comment"></dxo-label>
                    </dxi-item>
                </dxi-item>
            </dxi-item>

        </dxi-item>

        <dxi-item [template]="'buttons'"></dxi-item>

        <div *dxTemplate="let data of 'buttons'"
             class="buttons">
            <dx-button *ngIf="!isEditing; else saveAndCancel"
                       text="Edit"
                       type="default"
                       (onClick)="onEditClicked($event)"
                       [disabled]="!allowEditing"></dx-button>
            <ng-template #saveAndCancel>
                <dx-button text="Cancel"
                           type="danger"
                           [disabled]="isSaving"
                           (onClick)="onCancelClicked($event)">
                </dx-button>
                <dx-button text="Save"
                           type="success"
                           (onClick)="onSaveClicked($event)"
                           [disabled]="!allowEditing || isSaving"></dx-button>
            </ng-template>
        </div>

    </dx-form>
</div>
