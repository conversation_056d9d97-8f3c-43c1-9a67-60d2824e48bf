<dx-data-grid [dataSource]="tasks"
              [columnAutoWidth]="true"
              [allowColumnReordering]="true"
              [allowColumnResizing]="true"
              [columnResizingMode]="'widget'"
              [filterSyncEnabled]="true"
              [filterValue]="showCancelledTasks ? [] : ['status', '<>', 'Canceled']"
              (onRowInserting)="onRowInserting($event)"
              (onRowUpdating)="onRowUpdating($event)"
              (onEditorPreparing)="onEditorPreparing($event)"
              (onEditingStart)="onEditingStart($event)"
              (onEditCanceled)="onEditCanceled($event)">

    <dxo-toolbar>
        <dxi-item name="groupPanel"></dxi-item>
        <dxi-item name="addRowButton"
                  [showText]="'always'"
                  [options]="addRowButtonOptions$ | async"
                  [visible]="allowAdding$ | async">
        </dxi-item>
        <dxi-item widget="dxButton"
                  location="after"
                  [options]="{icon: showCancelledTasks ? 'fa fa-toggle-on' : 'fa fa-toggle-off', hint: 'Toggle cancel filter', onClick: toggleCancelledTasks}">
        </dxi-item>
        <dxi-item widget="dxButton"
                  location="after"
                  [options]="{icon: 'fa fa-undo', hint: 'Restore Grid Defaults', onClick: restoreDefaultsClicked}">
        </dxi-item>
        <dxi-item name="columnChooserButton"></dxi-item>
        <dxi-item name="exportButton"></dxi-item>
        <dxi-item name="searchPanel"></dxi-item>
    </dxo-toolbar>

    <dxo-group-panel [visible]="true"></dxo-group-panel>
    <dxo-scrolling [useNative]="true"></dxo-scrolling>
    <dxo-paging [pageSize]="5"></dxo-paging>
    <dxo-pager [showPageSizeSelector]="true"
               [visible]="true"
               [allowedPageSizes]="[5, 10, 25, 50]"></dxo-pager>
    <dxo-header-filter [visible]="true"></dxo-header-filter>
    <dxo-filter-row [visible]="true"></dxo-filter-row>
    <dxo-search-panel [visible]="true"
                      [highlightCaseSensitive]="true">
    </dxo-search-panel>
    <dxo-filter-panel [visible]="true"></dxo-filter-panel>
    <dxo-column-chooser [enabled]="true"
                        mode="dragAndDrop">
    </dxo-column-chooser>
    <dxo-state-storing [enabled]="true"
                       type="localStorage"
                       storageKey="apmTaskTabGridState">
    </dxo-state-storing>
    <dxo-export [enabled]="true"
                [allowExportSelectedData]="true"></dxo-export>

    <!-- EDITING -->
    <dxo-editing mode="popup"
                 [allowAdding]="allowAdding$ | async"
                 [allowUpdating]="allowEditing$ | async"
                 [allowDeleting]="false">
        <dxo-popup title="Task Info"
                   [showTitle]="true"
                   [width]="700"
                   [height]="525"></dxo-popup>
        <dxo-form>
            <dxi-item dataField="taskType"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: availableTaskTypes}">
                <dxi-validation-rule type="required"></dxi-validation-rule>
            </dxi-item>
            <dxi-item dataField="taskAssignees"
                      editorType="dxTagBox"
                      [editorOptions]="{items: availableUsers, searchEnabled: true}">
            </dxi-item>
            <dxi-item dataField="clientWorkOrderNumber">
                <dxi-validation-rule type="required"></dxi-validation-rule>
            </dxi-item>
            <dxi-item dataField="supervisor"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: availableUsers}">
            </dxi-item>
            <dxi-item dataField="plannedStart"
                      editorType="dxDateBox">
            </dxi-item>
            <dxi-item dataField="plannedEnd"
                      editorType="dxDateBox">
            </dxi-item>
            <dxi-item dataField="leadTechnician"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: availableUsers}">
            </dxi-item>

            <dxi-item dataField="clientCostCode"></dxi-item>
            <dxi-item dataField="purchaseOrderAFE"></dxi-item>

            <dxi-item dataField="clientWorkOrderDescription">
            </dxi-item>
            <!-- TODO: This really doesn't seem correct (to allow custom values EVER).  Please ask Brian when he's back. -->
            <dxi-item dataField="status"
                      [editorOptions]='{readOnly: editingStatus === "Published", acceptCustomValue: editingStatus === "Published"}'
                      editorType="dxSelectBox">
            </dxi-item>
            <dxi-item dataField="dueDate"
                      editorType="dxDateBox">
            </dxi-item>

        </dxo-form>
    </dxo-editing>

    <!-- COLUMNS -->
    <dxi-column dataField="id"
                caption="Task Database ID"
                [visible]="false"></dxi-column>
    <dxi-column dataField="taskType"
                dataType="string"></dxi-column>
    <dxi-column dataField="taskAssignees"
                dataType="string"></dxi-column>
    <dxi-column dataField="taskCreatedBy"
                dataType="string"></dxi-column>
    <dxi-column dataField="taskCreatedDate"
                dataType="date"></dxi-column>
    <dxi-column dataField="taskUpdatedDate"
                dataType="date"></dxi-column>
    <dxi-column dataField="clientWorkOrderNumber"
                dataType="string"></dxi-column>
    <dxi-column dataField="supervisor"
                dataType="string"></dxi-column>
    <dxi-column dataField="plannedStart"
                dataType="date"></dxi-column>
    <dxi-column dataField="plannedEnd"
                dataType="date"></dxi-column>
    <dxi-column dataField="dueDate"
                dataType="date"></dxi-column>
    <dxi-column dataField="leadTechnician"
                dataType="string"></dxi-column>
    <dxi-column dataField="clientCostCode"
                dataType="string"></dxi-column>
    <dxi-column dataField="purchaseOrderAFE"
                dataType="string"></dxi-column>
    <dxi-column dataField="clientWorkOrderDescription"
                dataType="string"></dxi-column>
    <dxi-column dataField="status"
                dataType="string"></dxi-column>
</dx-data-grid>
