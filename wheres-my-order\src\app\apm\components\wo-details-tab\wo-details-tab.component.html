<dx-form #form
         id="form"
         [(formData)]="woDetail"
         [readOnly]="!isEditing"
         (onFieldDataChanged)="onFieldDataChanged($event)">
    <dxi-item itemType="group"
              [colCount]="5">
        <dxi-item itemType="group"
                  [colCount]="4"
                  [colSpan]="4">
            <dxi-item dataField="apmWorkOrderNumber">
                <dxo-label text="APM Work Order Number"></dxo-label>
            </dxi-item>
            <dxi-item dataField="apmProjectNumber">
                <dxo-label text="APM Project Number"></dxo-label>
            </dxi-item>
            <dxi-item dataField="facility">
                <dxo-label text="Facility"></dxo-label>
            </dxi-item>
            <dxi-item dataField="assetCategory"
                      [editorOptions]="{readOnly: true}">
                <dxo-label text="Asset Category"></dxo-label>
            </dxi-item>
            <dxi-item dataField="status"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: woDetail?.availableStatuses === null ? [] : woDetail?.availableStatuses, readOnly: woDetail?.status === 'Published' || !isEditing, acceptCustomValue: woDetail?.status === 'Published'}">
                <dxo-label text="Status"></dxo-label>
            </dxi-item>
            <dxi-item dataField="plannedStartDate"
                      editorType="dxDateBox">
                <dxo-label text="Planned Start"></dxo-label>
            </dxi-item>
            <dxi-item dataField="plannedEndDate"
                      editorType="dxDateBox">
                <dxo-label text="Planned End"></dxo-label>
            </dxi-item>
            <dxi-item dataField="dueDate"
                      editorType="dxDateBox">
                <dxo-label text="Due Date"></dxo-label>
            </dxi-item>
            <dxi-item dataField="primaryContactName">
                <dxo-label text="Primary Contact"></dxo-label>
            </dxi-item>
            <dxi-item dataField="primaryContactPhone">
                <dxo-label text="Primary Contact Phone"></dxo-label>
            </dxi-item>
            <dxi-item dataField="teamProjectNumber">
                <dxo-label text="TEAM Project Number"></dxo-label>
            </dxi-item>
        </dxi-item>
        <dxi-item itemType="group"
                  caption="GIS Location">
            <dxi-item dataField="latitude">
                <dxo-label text="Latitude"></dxo-label>
            </dxi-item>
            <dxi-item dataField="longitude">
                <dxo-label text="Longitude"></dxo-label>
            </dxi-item>
        </dxi-item>

    </dxi-item>

    <dxi-item [template]="'buttons'"></dxi-item>

    <div *dxTemplate="let data of 'buttons'"
         class="buttons">
        <dx-button *ngIf="!isEditing; else saveAndCancel"
                   text="Edit"
                   type="default"
                   (onClick)="onEditClicked($event)"
                   [disabled]="!allowEditing"></dx-button>
        <ng-template #saveAndCancel>
            <dx-button text="Cancel"
                       type="danger"
                       [disabled]="isSaving"
                       (onClick)="onCancelClicked($event, form)">
            </dx-button>
            <dx-button text="Save"
                       type="success"
                       [disabled]="isSaving"
                       (onClick)="onSaveClicked($event)"></dx-button>
        </ng-template>
    </div>

</dx-form>
