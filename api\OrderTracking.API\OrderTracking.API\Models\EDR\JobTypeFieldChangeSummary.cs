using System.Collections.Generic;
using System.Linq;
using ClientPortal.Shared.Extensions;

namespace OrderTracking.API.Models.EDR
{
    /// <summary>
    ///     An object to represent the difference between two sets of Job Types on an Equipment Request
    /// </summary>
    public class JobTypeFieldChangeSummary : Dictionary<string, List<Variance>>
    {
        /// <summary>
        ///     The list of job types that were on the request originally
        /// </summary>
        public List<JobType> OriginalJobTypes { get; set; }

        /// <summary>
        ///     The list of job types that are on the request after a proposed update
        /// </summary>
        public List<JobType> NewJobTypes { get; set; }

        /// <summary>
        ///     The JSSNumbers of the equipment after the change
        /// </summary>
        public KeyCollection JSSNumbers => Keys;

        /// <summary>
        ///     Whether or not there are any field changes to the equipment or equipment added/removed
        /// </summary>
        public bool HasChanges => JSSNumbers.Any() || OriginalJobTypes.Count != NewJobTypes.Count;
    }
}