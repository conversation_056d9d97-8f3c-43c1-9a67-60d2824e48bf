import { HttpParams } from '@angular/common/http';
import CustomStore from 'devextreme/data/custom_store';
import DataSource from 'devextreme/data/data_source';

import { isNotEmpty } from '../../../shared/helpers';
import { Order } from '../../../wmo/models/order';

export type DataSourceLoadFn = (
    httpParams: HttpParams
) => Promise<{
    data: Pick<Order, 'externalCustomer' | 'externalCustomerName'>[];
}>;

export function customerAccountsDataSource(loadWithParams: DataSourceLoadFn) {
    const operations = [
        'filter',
        'searchExpr',
        'searchOperation',
        'searchValue',
        'skip',
        'take',
    ];
    const store = new CustomStore({
        useDefaultSearch: true,
        load: (loadOptions) => {
            let params: HttpParams = new HttpParams();
            operations
                .filter(
                    (param) =>
                        param in loadOptions && isNotEmpty(loadOptions[param])
                )
                .forEach(
                    (i) =>
                        (params = params.set(i, JSON.stringify(loadOptions[i])))
                );
            return loadWithParams(params);
        },
    });
    return new DataSource({
        store,
        paginate: true,
        pageSize: 10,
    });
}
