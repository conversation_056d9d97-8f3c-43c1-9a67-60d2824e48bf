import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';
import {
    AcceptedAgreementGuard,
    AdminGuard,
    AppAdminGuard,
    IsActiveGuard
} from '../core/guards';
import { RolesComponent, UsersComponent } from './pages';

const routes: Routes = [
    {
        path: 'users',
        component: UsersComponent,
        data: { pageTitle: 'User Administration' },
        canActivate: [
            MsalGuard,
            AdminGuard,
            IsActiveGuard,
            AcceptedAgreementGuard
        ],
        runGuardsAndResolvers: 'always'
    },
    {
        path: 'roles',
        component: RolesComponent,
        data: { pageTitle: 'Role Administration' },
        canActivate: [
            MsalGuard,
            AdminGuard,
            IsActiveGuard,
            AcceptedAgreementGuard
        ],
        runGuardsAndResolvers: 'always'
    },
    {
        path: 'auth-history',
        loadChildren: () =>
            import('./auth-history/auth-history.module').then(
                (m) => m.AuthHistoryModule
            ),
        canLoad: [AppAdminGuard, IsActiveGuard, AcceptedAgreementGuard]
    },
    {
        path: 'user-audit',
        loadChildren: () =>
            import('./user-audit/user-audit.module').then(
                (m) => m.UserAuditModule
            ),
        canLoad: [AppAdminGuard, IsActiveGuard, AcceptedAgreementGuard]
    },
    {
        path: '',
        redirectTo: 'users',
        pathMatch: 'full'
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AdminRoutingModule {}
