import {
    Component,
    EventEmitter,
    Input,
    Output,
    ViewChild
} from '@angular/core';
import { DxDataGridComponent } from 'devextreme-angular';
import { ClickEvent } from 'devextreme/ui/button';
import {
    EditCanceledEvent,
    EditingStartEvent,
    EditorPreparingEvent,
    RowInsertingEvent,
    RowUpdatingEvent
} from 'devextreme/ui/data_grid';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { DataGridService } from '../../../shared/services';
import {
    APMStatus,
    APMTaskType,
    availableStatusesToChangeTo,
    NewTask,
    TaskDetail,
    TaskUpdate,
    WorkOrder
} from '../../models';
import { TaskTypesForAssetCategoryPipe, WoTaskDetailsPipe } from '../../pipes';
import { ApmService } from '../../services';

@Component({
    selector: 'app-tasks-tab',
    templateUrl: './tasks-tab.component.html',
    styleUrls: ['./tasks-tab.component.scss']
})
export class TasksTabComponent {
    private _workOrder: WorkOrder;
    private _tasks: TaskDetail[];
    private readonly _availableTaskTypes = new BehaviorSubject<string[]>([]);
    @ViewChild(DxDataGridComponent) dataGrid: DxDataGridComponent;
    get tasks(): TaskDetail[] {
        return this._tasks;
    }
    @Input() set workOrder(value: WorkOrder) {
        this._workOrder = value;
        if (!this._workOrder) return;
        this._tasks = this._woTaskDetailsPipe.transform(this._workOrder);
        this._availableTaskTypes.next(
            this._taskTypesForAssetCategoryPipe.transform(
                this._workOrder.asset.assetCategory,
                this._workOrder
            )
        );
    }
    get workOrder(): WorkOrder {
        return this._workOrder;
    }
    @Input() availableUsers: string[];
    get availableTaskTypes(): string[] {
        return this._availableTaskTypes.value;
    }
    @Output() newTask = new EventEmitter<NewTask>();
    @Output() taskUpdate = new EventEmitter<TaskUpdate>();
    showCancelledTasks = false;
    editingStatus: string = null;

    allowEditing$ = this._apm.allowEditing$;
    gettingWorkOrder$ = this._apm.gettingWorkOrder$.pipe(startWith(false));
    creatingTask$ = this._apm.creatingTask$.pipe(startWith(false));
    addRowButtonOptions$ = combineLatest([
        this._apm.selectedBU$,
        this.allowEditing$
    ]).pipe(
        map(
            ([bu, allowEditing]) =>
                bu === undefined || bu === null || !allowEditing
        ),
        map((disabled) => ({
            icon: null,
            text: 'Create',
            type: 'success',
            disabled: disabled,
            stylingMode: 'contained'
        }))
    );
    allowAdding$ = combineLatest([
        this.allowEditing$,
        this.gettingWorkOrder$,
        this.creatingTask$,
        this._availableTaskTypes.asObservable()
    ]).pipe(
        map(
            ([
                allowEditing,
                gettingWorkOrder,
                creatingTask,
                availableTaskTypes
            ]) =>
                allowEditing &&
                availableTaskTypes?.length > 0 &&
                gettingWorkOrder === false &&
                creatingTask === false
        )
    );

    constructor(
        private readonly _apm: ApmService,
        private readonly _grid: DataGridService,
        private readonly _woTaskDetailsPipe: WoTaskDetailsPipe,
        private readonly _taskTypesForAssetCategoryPipe: TaskTypesForAssetCategoryPipe
    ) {}

    restoreDefaultsClicked = async (e: ClickEvent) => {
        const result = await this._grid.resetGridState(this.dataGrid);
        if (result) localStorage.removeItem('apmTaskTabGridState');
    };

    toggleCancelledTasks = (e: ClickEvent) => {
        this.showCancelledTasks = !this.showCancelledTasks;
        this.dataGrid.instance.repaint();
    };

    onRowInserting(e: RowInsertingEvent) {
        this.newTask.next({
            workOrderId: this.workOrder.id,
            projectId: this.workOrder.projectId,
            ...e.data
        });
    }

    onRowUpdating(e: RowUpdatingEvent) {
        this.editingStatus = null;
        this.taskUpdate.next({
            workOrderId: this.workOrder.id,
            projectId: this.workOrder.projectId,
            databaseId: e.oldData.id,
            ...e.newData
        });
    }

    onEditorPreparing(e: EditorPreparingEvent<TaskDetail, string>) {
        if (e.parentType === 'dataRow') {
            if (e.dataField === 'status') {
                if (e.row.isNewRow) {
                    e.editorOptions.disabled = true;
                    e.editorOptions.value = 'Not Started';
                    e.editorOptions.items = ['Not Started'];
                } else {
                    e.editorOptions.items = availableStatusesToChangeTo(
                        this.workOrder,
                        e.row.data.id,
                        e.row.data.taskType as APMTaskType,
                        e.row.data.status as APMStatus
                    );
                }
            }

            // If editor is for `taskType` field and we aren't creating a new
            // task, we need to disable the editor and allow the dropdown to
            // include the task type of the current task.  Otherwise, the editor
            // is intended to show available task types to choose from (if creating
            // a new task)
            if (e.dataField === 'taskType' && !e.row.isNewRow) {
                e.editorOptions.disabled = true;
                e.editorOptions.items.push(e.row.data.taskType);
            }
        }
    }

    onEditingStart(e: EditingStartEvent) {
        this.editingStatus = e.data.status;
    }

    onEditCanceled(e: EditCanceledEvent) {
        this.editingStatus = null;
    }
}
