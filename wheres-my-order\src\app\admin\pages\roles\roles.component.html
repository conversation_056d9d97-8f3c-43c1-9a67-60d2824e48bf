<app-breadcrumbs [crumbs]="crumbs"> </app-breadcrumbs>
<div class="content-block-dashboard">
    <div class="side-by-side">
        <div
             class="dx-card dx-card-w-title content-block responsive-paddings left">
            <h1>Roles</h1>
            <dx-button id="add-role-button"
                       icon="fa fa-plus"
                       hint="Add role"
                       style="margin-right: .5rem;"
                       [disabled]="!this.isUserAppAdmin"
                       validationGroup="validation"
                       (onClick)="newRoleClicked($event)">
            </dx-button>
            <dx-button id="edit-role-button"
                       icon="fa fa-pencil"
                       hint="Edit selected role"
                       style="margin-right: .5rem;"
                       validationGroup="validation"
                       [disabled]="(this.mode !== 'roleSelected' || !this.isUserAppAdmin) || this.roleOriginal?.id.toLowerCase() === 'app:admin'"
                       (onClick)="roleEditClicked($event)">
            </dx-button>
            <dx-button id="delete-role-button"
                       icon="fa fa-trash"
                       hint="Delete selected role"
                       style="margin-right: .5rem;"
                       validationGroup="validation"
                       [disabled]="(this.mode !== 'roleSelected' || !this.isUserAppAdmin) || this.roleOriginal?.id.toLowerCase() === 'app:admin'"
                       (onClick)="roleDeleteClicked($event)">
            </dx-button>
            <dx-button icon="fa fa-refresh"
                       hint="Refresh role list"
                       style="margin-right: .5rem;"
                       validationGroup="validation"
                       [disabled]="this.mode !== 'roleSelected'"
                       (onClick)="refreshRolesClicked($event)"></dx-button>
            <dx-data-grid id="searchList"
                          #searchList
                          [dataSource]="rolesDataSource"
                          [showBorders]="false"
                          [showColumnHeaders]="false"
                          [focusedRowEnabled]="true"
                          [hoverStateEnabled]="true"
                          [errorRowEnabled]="false"
                          [(focusedRowKey)]="focusedRowKey"
                          (onFocusedRowChanged)="roleSelected($event)"
                          (onContextMenuPreparing)="addMenuItems($event)"
                          height="550px">
                <dxo-search-panel [visible]="true"
                                  [width]="250"
                                  [highlightSearchText]="false"
                                  [searchVisibleColumnsOnly]="false"
                                  placeholder="Search..."></dxo-search-panel>
                <dxo-load-panel [enabled]="true"></dxo-load-panel>
                <dxo-scrolling mode="virtual"></dxo-scrolling>
                <dxo-sorting mode="none"></dxo-sorting>
                <dxo-filter-panel [visible]="true"></dxo-filter-panel>

                <dxi-column dataField="group"
                            [groupIndex]="0"></dxi-column>
                <dxi-column dataField="id"
                            [allowSorting]="true"
                            [sortIndex]="0"
                            sortOrder="asc"></dxi-column>
                <dxi-column dataField="description"
                            [visible]="false">
                </dxi-column>

                <dxo-summary>
                    <dxi-group-item column="group"
                                    summaryType="count"
                                    displayFormat="{0} roles">
                    </dxi-group-item>
                </dxo-summary>
            </dx-data-grid>
        </div>
        <div
             class="dx-card dx-card-w-title content-block responsive-paddings right">
            <h1>{{title}}</h1>
            <div class="header">
                <img class="clip"
                     src="/assets/images/roles-256x256.png">
                <div>
                    <span *ngIf="roleEdit"
                          class="name">{{roleEdit.id}}</span>
                    <span class="description">{{roleEdit.description}}</span>
                </div>
            </div>

            <ng-container *ngIf="mode === 'roleSelected'">
                <div style="display:flex">
                    <dx-select-box id="userSelectBox"
                                   #userSelectBox
                                   [dataSource]="availableUsers | async"
                                   [(selectedItem)]="selectedUser"
                                   [minSearchLength]="2"
                                   [searchEnabled]="true"
                                   [searchExpr]="['name','id']"
                                   [searchTimeout]="200"
                                   [showDataBeforeSearch]="true"
                                   [showDropDownButton]="false"
                                   [disabled]="this.roleOriginal.id.toLowerCase() === 'app:admin' && !this.isUserAppAdmin"
                                   displayExpr="name"
                                   hint="Search for user to add"
                                   placeholder="Select user to add..."
                                   searchMode="contains"
                                   style="flex-grow: 1;"
                                   stylingMode="outlined"></dx-select-box>
                    <dx-button id="add-user-button"
                               icon="fa fa-plus"
                               hint="Add selected user to role"
                               style="align-self: center; margin-left: .5rem;"
                               (onClick)="addUserClicked($event)"
                               [disabled]="this.roleOriginal.id.toLowerCase() === 'app:admin' && !this.isUserAppAdmin">
                    </dx-button>
                </div>
                <dx-list id="userList"
                         #userList
                         [items]="usersForSelectedRole$ | async"
                         height="100%"
                         keyExpr="id"
                         [allowItemDeleting]="(this.roleOriginal.id.toLowerCase() === 'app:admin' && this.isUserAppAdmin)
                                                || this.roleOriginal.id.toLowerCase() !== 'app:admin'"
                         itemDeleteMode="static"
                         selectionMode="single"
                         [selectedItemKeys]="selectedUserInList | async"
                         [activeStateEnabled]="true"
                         [focusStateEnabled]="true"
                         noDataText="No users assigned yet to this role"
                         (onItemDeleting)="removeUserClicked($event)">
                    <div *dxTemplate="let item of 'item'">
                        <div>{{item.name}} ({{item.id}})
                        </div>
                    </div>
                </dx-list>
            </ng-container>

            <ng-container *ngIf="mode === 'editRole' || mode === 'newRole'">
                <dx-form id="roleEditForm"
                         #roleEditForm
                         [readOnly]="this.mode === 'none'"
                         [(formData)]="roleEdit"
                         [colCount]="1"
                         validationGroup="validation"
                         [showValidationSummary]="true">
                    <dxi-item dataField="id"
                              [label]="{text: 'Role Key'}">
                        <dxi-validation-rule type="required"
                                             message="The role key is required">
                        </dxi-validation-rule>
                        <dxi-validation-rule type="pattern"
                                             pattern="^[a-zA-Z\d]{2,}:[a-zA-Z\d]{2,}$"
                                             message="Group and role must be at least 2 characters long separated by a ':' and consist of only numbers and letters">
                        </dxi-validation-rule>
                    </dxi-item>
                    <dxi-item dataField="description">
                        <dxi-validation-rule type="required"
                                             message="The description of the role is required">
                        </dxi-validation-rule>
                    </dxi-item>
                </dx-form>
                <div style="margin-top: 1rem;">
                    <dx-button *ngIf="mode === 'editRole'"
                               (onClick)="updateClicked($event)"
                               text="Update"
                               type="success"
                               validationGroup="validation"
                               style="margin-right: .5rem;">
                    </dx-button>
                    <dx-button *ngIf="mode === 'newRole'"
                               (onClick)="createClicked($event)"
                               text="Create"
                               type="success"
                               validationGroup="validation"
                               style="margin-right: .5rem;">
                    </dx-button>
                    <dx-button text="Cancel"
                               (onClick)="cancelClicked($event)"
                               type="normal"
                               validationGroup="validation">
                    </dx-button>
                </div>
            </ng-container>
        </div>
    </div>
</div>
