﻿using System.Collections.Generic;
using System.Linq;
using ClientPortal.Shared.Extensions;

namespace OrderTracking.API.Models.EDR
{
    /// <summary>
    /// Class representing any changes that happen to an Equipment Request
    /// </summary>
    public class EquipmentRequestFieldChangeSummary 
    {
        /// <summary>
        ///     The original request 
        /// </summary>
        public EquipmentRequest OriginalRequest { get; set; }

        /// <summary>
        ///     The updated request
        /// </summary>
        public EquipmentRequest UpdatedRequest { get; set; }
       
        /// <summary> 
        ///     The list of job types that changed on the request
        /// </summary>
        public JobTypeFieldChangeSummary JobTypeChanges { get; set; }

        /// <summary>
        ///    the ids of the 
        /// </summary>
        public List<Variance> Changes;

        /// <summary>
        ///     Whether or not there are any field changes to the equipment or equipment added/removed
        /// </summary>
        public bool HasChanges => Changes.Any() || JobTypeChanges.JSSNumbers.Any() ;
    }
}
