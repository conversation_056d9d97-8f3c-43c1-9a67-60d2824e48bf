<div class="dx-card responsive-paddings content-block"
     style="padding: 30px;">
    <h4>Manage Tasks</h4>
    <p>Asset Number or Identification</p>
    <dx-select-box [dataSource]="assetsDataSource"
                   (onValueChanged)="onValueChanged($event)"
                   displayExpr="equipmentId"
                   valueExpr="Id"
                   placeholder="Start typing the Asset ID"
                   [searchEnabled]="true"
                   [showDropDownButton]="false"
                   [showClearButton]="true"
                   valueExpr="equipmentId">
    </dx-select-box>
    <div style="height: 20px;"></div>
    <h5>Tasks</h5>
    <dx-data-grid [dataSource]="assetTasks"
                  noDataText="Loading tasks..."
                  [allowColumnResizing]="true"
                  [wordWrapEnabled]="true"
                  (onRowUpdated)="onRowUpdated($event)">
        <dxo-editing mode="cell"
                     [allowUpdating]="true">
        </dxo-editing>

        <!-- Column definitions -->

        <dxi-column dataField="id"
                    [visible]="false"
                    [allowEditing]="false"
                    caption="Id">
        </dxi-column>
        <dxi-column dataField="workOrderId"
                    [visible]="false"
                    [allowEditing]="false"
                    caption="Work Order">
        </dxi-column>
        <dxi-column dataField="apmTaskNumber"
                    [sortIndex]="0"
                    sortOrder="asc"
                    [allowEditing]="false"
                    caption="Task ID"
                    [width]="'15%'">
        </dxi-column>
        <dxi-column dataField="taskType"
                    caption="Task Type"
                    [allowEditing]="false"
                    [width]="'15%'">
        </dxi-column>
        <dxi-column dataField="assignedUsers"
                    [allowEditing]="allowEditing$ | async"
                    editCellTemplate="assigneesTemplate"
                    caption="Task Assignees"
                    [customizeText]="customizeText"
                    [width]="'25%'">
        </dxi-column>
        <dxi-column dataField="supervisor"
                    caption="Supervisor"
                    [allowEditing]="allowEditing$ | async"
                    editCellTemplate="supervisorTemplate"
                    [width]="'25%'">
        </dxi-column>
        <dxi-column dataField="status"
                    [allowEditing]="allowEditing$ | async"
                    caption="Status"
                    editCellTemplate="statusTemplate"
                    [width]="'10%'">
        </dxi-column>

        <dxi-column type="buttons"
                    caption="Results"
                    [width]="'10%'">
            <dxi-button icon="fa fa-tasks"
                        hint="View results"
                        [onClick]="tasksResultsClicked">
            </dxi-button>
        </dxi-column>

        <!--Templates definition -->

        <div *dxTemplate="let cellInfo of 'assigneesTemplate'">
            <dx-tag-box [dataSource]="users$ | async | userEmails"
                        [showMultiTagOnly]="false"
                        width="300"
                        [value]="cellInfo.value"
                        searchEnabled="true"
                        (onValueChanged)="cellInfo.setValue($event.value)"
                        (onSelectionChanged)="cellInfo.component.updateDimensions()"
                        applyValueMode="useButtons">
            </dx-tag-box>
        </div>
        <div *dxTemplate="let cellInfo of 'supervisorTemplate'">
            <dx-select-box [dataSource]="users$ | async | userEmails"
                           width="300"
                           [value]="cellInfo.value"
                           (onValueChanged)="cellInfo.setValue($event.value)"
                           [showSelectionControls]="true"
                           [searchEnabled]="true"
                           applyValueMode="useButtons">
            </dx-select-box>
        </div>
        <div *dxTemplate="let cellInfo of 'statusTemplate'">
            <dx-select-box [dataSource]="availableStatus"
                           (onOpened)="updateValues(cellInfo)"
                           (onValueChanged)="cellInfo.setValue($event.value)"
                           noDataText="Loading...">
            </dx-select-box>
        </div>
    </dx-data-grid>
    <div style="height: 20px;"></div>
</div>