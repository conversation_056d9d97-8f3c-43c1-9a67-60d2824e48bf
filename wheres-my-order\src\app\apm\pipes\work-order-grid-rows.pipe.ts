import { Pipe, PipeTransform } from '@angular/core';
import { Asset, WorkOrder, WorkOrderGridRow } from '../models';

@Pipe({
    name: 'workOrderGridRows'
})
export class WorkOrderGridRowsPipe implements PipeTransform {
    transform(workOrders?: WorkOrder[]): WorkOrderGridRow[] {
        return workOrders?.map((w) => ({
            id: w.id,
            projectId: w.projectId,
            assetDatabaseId: w.asset.id,
            assetId: Asset.findAssetNumber(
                w.asset?.walkDown,
                w.asset?.assetCategory
            ),
            apmWorkOrderNumber: w.apmWorkOrderNumber.currentValue,
            assetCategory: w.asset.assetCategory,
            dueDate: w.dueDate.currentValue,
            facilityName: w.facilityName.currentValue,
            plannedEnd: w.plannedEnd.currentValue,
            plannedStart: w.plannedStart.currentValue,
            status: w.status.currentValue
        }));
    }
}
