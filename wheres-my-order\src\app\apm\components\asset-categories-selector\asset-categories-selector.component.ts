import { Component, EventEmitter, Output, ViewChild } from '@angular/core';
import { DxTagBoxComponent } from 'devextreme-angular';
import { ValueChangedEvent } from 'devextreme/ui/tag_box';
import { AssetCategoryAPICode } from '../../models';

@Component({
    selector: 'app-asset-categories-selector',
    templateUrl: './asset-categories-selector.component.html',
    styleUrls: ['./asset-categories-selector.component.scss']
})
export class AssetCategoriesSelectorComponent {
    readonly items = ['510', '570', '653'];

    @ViewChild(DxTagBoxComponent) tagBox: DxTagBoxComponent;

    @Output() categories = new EventEmitter<AssetCategoryAPICode[]>();

    get selectedCategories(): AssetCategoryAPICode[] {
        return this.tagBox.selectedItems;
    }

    constructor() {}

    onCategoryChanged(e: ValueChangedEvent) {
        this.categories.next(e.value);
    }
}
