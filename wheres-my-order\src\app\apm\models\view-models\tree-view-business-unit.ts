import { District } from '../../../shared/services';
import { TreeViewClient } from './tree-view-client';

export class TreeViewUser {
    constructor(public id: string, public name: string) {}
}

export class TreeViewBusinessUnit {
    private _parentId: string;
    private _parentText: string;
    get parentText(): string {
        return this._parentText;
    }
    get parentId(): string {
        return this._parentId;
    }
    constructor(
        public id: string,
        public text: string,
        public users: TreeViewUser[] = [],
        public districts: District[] = [],
        public groups: string[] = []
    ) {}

    setParent(client: TreeViewClient): void {
        this._parentId = client.id;
        this._parentText = client.text;
    }

    /** TODO: Update me if this class changes in any way! */
    static clone(bu: TreeViewBusinessUnit): TreeViewBusinessUnit {
        return new TreeViewBusinessUnit(
            bu.id,
            bu.text,
            bu.users?.map((u) => ({ ...u, name: u.name })),
            bu.districts?.map((d) => ({ ...d })),
            bu.groups.map((g) => g)
        );
    }
}
