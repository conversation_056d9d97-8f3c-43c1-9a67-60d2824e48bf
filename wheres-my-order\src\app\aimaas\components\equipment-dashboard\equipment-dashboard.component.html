<div class="dx-card content-block">
    <h2
        style="text-align: center; margin:0,10px,0,0; font-weight:500; font-size:20px">
        Equipment by Schedule</h2>
    <div class="flex-container">

        <div class="dx-card content-block checkbox-container">
            <div style="padding-left: 10px;">
                <dx-check-box text="Include 'Out Of Service'"
                              [(value)]="isChecked"
                              (onValueChanged)="onValueChanged($event)"></dx-check-box>
            </div>
            <div class="equipment-doughnut-container ">
                <div class="left-overview-flex-container">
                    <app-overview-box [(value)]="assetInspectionCount"
                                      icon="fa-folder-open-o"
                                      label="Total Assets"
                                      [routerLink]="['../drilldown']"
                                      [state]="{data: {assetObjIds: assetInspectionObjIds}, breadCrumbLabel: 'Total Assets'}">
                    </app-overview-box>
                </div>
                <div class="right-overview-flex-container">
                    <app-overview-box icon="fa-check-square-o"
                                      [(value)]="currentAssetInspectionCount"
                                      label="Assets with Current Inspections"
                                      [routerLink]="['../drilldown']"
                                      [state]="{data: {assetObjIds: currentAssetInspectionObjIds}, breadCrumbLabel: 'Assets with Current Inspections'}">
                    </app-overview-box>
                    <app-overview-box icon="fa-exclamation-triangle"
                                      [(value)]="overdueAssetInspectionCount"
                                      label="Assets with Overdue Inspections"
                                      [routerLink]="['../drilldown']"
                                      [state]="{data: {assetObjIds: overdueAssetInspectionObjIds},breadCrumbLabel: 'Assets with Overdue Inspections'}">
                    </app-overview-box>
                    <app-overview-box icon="fa-minus-square-o"
                                      [(value)]="noInspectionAssetCount"
                                      label="ASSETS WITH NO INSPECTIONS SCHEDULED "
                                      [routerLink]="['../drilldown']"
                                      [state]="{data: {assetObjIds: noInspectionAssetObjIds},breadCrumbLabel: 'Assets with No Inspections'}">
                    </app-overview-box>
                </div>
            </div>
        </div>
        <div class="equipment-doughnut-container">
            <dx-pie-chart #assetAndInspectionPie
                          [dataSource]="aggregateAssetAndInspectionWithCategory"
                          class="doughnut-chart"
                          type="doughnut"
                          centerTemplate="centerTemplate"
                          [resolveLabelOverlapping]="resolveOverlappingTypes[0]"
                          palette="Soft Pastel"
                          sizeGroup="doughnutCharts"
                          [innerRadius]="0.65"
                          (onPointClick)="equipmentDonutChartPointClicked($event)">
                <dxo-tooltip [enabled]="true">
                    <dxo-connector [visible]="true"
                                   [width]="1"></dxo-connector>
                </dxo-tooltip>
                <dxi-series argumentField="category"
                            valueField="count">
                    <dxo-label [visible]="true"
                               [customizeText]="customizeLabel">

                        <dxo-connector [visible]="true"
                                       [width]="1"></dxo-connector>
                    </dxo-label>
                </dxi-series>
                <dxo-legend [margin]="{top:15}"
                            horizontalAlignment="center"
                            verticalAlignment="bottom"></dxo-legend>
                <svg *dxTemplate="let pieChart of 'centerTemplate'">
                    <text text-anchor="middle"
                          style="font-size: 18px"
                          x="100"
                          y="120"
                          fill="#494949">
                        <tspan style="font-size:32;"
                               x="100"
                               dy="20px">Assets</tspan>
                    </text>
                </svg>
            </dx-pie-chart>
        </div>

    </div>
</div>
<div class="flex-container dx-card content-block"
     style="display: flex; justify-content: center; align-items: center; flex-direction: column;">
    <dx-chart #assetTypesByAreaIdChart
              style="width: 100%; padding:15px"
              [dataSource]="assetTypesByAreaId"
              (onPointClick)="equipmentPointClicked($event)">

        <dxi-series *ngFor="let objcat of objcats"
                    type="stackedbar"
                    [valueField]="objcat"
                    [name]="objcat"
                    argumentField="areaname">
        </dxi-series>

        <dxo-argument-axis>
            <dxo-label [overlappingBehavior]="'none'"
                       [textOverflow]="'ellipsis'"
                       [customizeHint]="customizeArgumentAxisLabel">
            </dxo-label>
        </dxo-argument-axis>

        <dxo-legend verticalAlignment="bottom"
                    horizontalAlignment="center"
                    orientation="horizontal">
        </dxo-legend>
        <dxo-tooltip [enabled]="true">
        </dxo-tooltip>
        <dxo-title verticalAlignment="top"
                   horizontalAlignment="center"
                   text="Equipment by Area and Type">
        </dxo-title>
    </dx-chart>
</div>