import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxLoadIndicatorModule } from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { PhotoSasPipe } from '../../pipes';
import { ApmService } from '../../services';
import { LeakRepairPhotosComponent } from './leak-repair-photos.component';

describe('LeakRepairPhotosComponent', () => {
    let component: LeakRepairPhotosComponent;
    let fixture: ComponentFixture<LeakRepairPhotosComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                HttpClientTestingModule,
                ToastrModule.forRoot(),
                DxLoadIndicatorModule
            ],
            providers: [{ provide: ApmService, useValue: {} }, PhotoSasPipe],
            declarations: [LeakRepairPhotosComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(LeakRepairPhotosComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
