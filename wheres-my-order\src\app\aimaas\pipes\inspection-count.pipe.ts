import { Pipe, PipeTransform } from '@angular/core';

import { InspectionType } from '../models';

@Pipe({
    name: 'inspectionCount',
})
export class InspectionCountPipe implements PipeTransform {
    totalCount = 0;
    transform(inspections: InspectionType[]): number {
        for (const inspection of inspections) {
            this.totalCount += inspection.count;
        }
        return this.totalCount;
    }
}
