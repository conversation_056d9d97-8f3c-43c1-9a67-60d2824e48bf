import { WalkDown } from '.';
import {
    IAttribute,
    PredefinedValueAttribute
} from '../../../shared/models/attributes';
import { APMTaskType } from '../apm-task-type';

export interface Task {
    id: string;
    activities: any; // TODO:
    assignedUsers: any[]; // TODO:
    clientWorkOrderDescription: IAttribute<string>;
    clientWorkOrderNumber: IAttribute<string>;
    clientCostCode: IAttribute<string>;
    purchaseOrderAFE: IAttribute<string>;
    createdBy: string;
    createdTime: Date;
    dueDate: IAttribute<Date>;
    lastChangedTime: Date;
    leadTech: IAttribute<string>;
    plannedEnd: IAttribute<Date>;
    plannedStart: IAttribute<Date>;
    projectId: string;
    status: PredefinedValueAttribute;
    taskData: WalkDown | any; // TODO:
    taskDetails: any; // TODO:
    taskAPMNumber: IAttribute<string>;
    taskType: APMTaskType;
    completedTime?: string;
}
