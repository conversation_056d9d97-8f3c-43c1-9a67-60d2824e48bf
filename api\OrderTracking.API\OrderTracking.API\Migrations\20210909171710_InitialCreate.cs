﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace OrderTracking.API.Migrations
{
    public partial class InitialCreate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DataSheetPackages",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    JssNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ClientName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    TEAMClientNumber = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataSheetPackages", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DataSheets",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PackageId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataSheets", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DataSheets_DataSheetPackages_PackageId",
                        column: x => x.PackageId,
                        principalTable: "DataSheetPackages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DimensionalSheets",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    GivenBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Plant = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Unit = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CheckedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CheckedByDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    SurfaceCondition = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LineSize = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SeverityOfLeak = table.Column<int>(type: "int", nullable: false),
                    ShipTo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SealType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    LineSkinTemp = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OtherSkinTemp = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DimensionalSheets", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DimensionalSheets_DataSheets_Id",
                        column: x => x.Id,
                        principalTable: "DataSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "HtsEngineeringDataCoverSheets",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    DistrictProjectId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SafetyReviewNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Location = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    HtsTechSupportRep = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BallParkNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ContactInfo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RequestedDeliveryDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ForImmediateManufacture = table.Column<bool>(type: "bit", nullable: false),
                    PriceQuote = table.Column<bool>(type: "bit", nullable: false),
                    CalculationPackage = table.Column<bool>(type: "bit", nullable: false),
                    Routine = table.Column<bool>(type: "bit", nullable: false),
                    WaitforClientApproval = table.Column<bool>(type: "bit", nullable: false),
                    BallPark = table.Column<bool>(type: "bit", nullable: false),
                    PeStampRequired = table.Column<bool>(type: "bit", nullable: false),
                    Priority = table.Column<bool>(type: "bit", nullable: false),
                    LineSize = table.Column<int>(type: "int", nullable: false),
                    Pressure = table.Column<int>(type: "int", nullable: false),
                    Material = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LineSchedule = table.Column<int>(type: "int", nullable: false),
                    Temperature = table.Column<int>(type: "int", nullable: false),
                    Service = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlangeRating = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NuclearNaceRequired = table.Column<bool>(type: "bit", nullable: false),
                    LineLocation = table.Column<bool>(type: "bit", nullable: false),
                    DesignPressure = table.Column<int>(type: "int", nullable: false),
                    FittingMaterial = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Quantity = table.Column<int>(type: "int", nullable: false),
                    DesignTemperature = table.Column<int>(type: "int", nullable: false),
                    FlangeRatingList = table.Column<bool>(type: "bit", nullable: false),
                    MDMT = table.Column<int>(type: "int", nullable: false),
                    Other = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CorrosionAllowance = table.Column<int>(type: "int", nullable: false),
                    CutterDiameter = table.Column<bool>(type: "bit", nullable: false),
                    DesignCode = table.Column<bool>(type: "bit", nullable: false),
                    DesignFactor = table.Column<bool>(type: "bit", nullable: false),
                    RunLengthType = table.Column<bool>(type: "bit", nullable: false),
                    BranchHeightType = table.Column<bool>(type: "bit", nullable: false),
                    RunLength = table.Column<int>(type: "int", nullable: false),
                    BranchHeight = table.Column<int>(type: "int", nullable: false),
                    HotTap = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockFitting = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PartNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NotApplicable = table.Column<bool>(type: "bit", nullable: false),
                    PressureRatainingSplitTee = table.Column<bool>(type: "bit", nullable: false),
                    NozzleWithRepad = table.Column<bool>(type: "bit", nullable: false),
                    SphericalTee = table.Column<bool>(type: "bit", nullable: false),
                    NozzleWithFullEncirclementSaddle = table.Column<bool>(type: "bit", nullable: false),
                    NozzleOnly = table.Column<bool>(type: "bit", nullable: false),
                    RepadOnly = table.Column<bool>(type: "bit", nullable: false),
                    WeldOLet = table.Column<bool>(type: "bit", nullable: false),
                    FullEncorclementSaddleOnly = table.Column<bool>(type: "bit", nullable: false),
                    FullEncirclementSaddle = table.Column<bool>(type: "bit", nullable: false),
                    Part1 = table.Column<bool>(type: "bit", nullable: false),
                    Part2 = table.Column<bool>(type: "bit", nullable: false),
                    Part3 = table.Column<bool>(type: "bit", nullable: false),
                    Part4 = table.Column<bool>(type: "bit", nullable: false),
                    Part5 = table.Column<bool>(type: "bit", nullable: false),
                    WeepHole = table.Column<bool>(type: "bit", nullable: false),
                    NptPortSize = table.Column<bool>(type: "bit", nullable: false),
                    NotApplicable2 = table.Column<bool>(type: "bit", nullable: false),
                    PerimeterSeal = table.Column<bool>(type: "bit", nullable: false),
                    SelfSeal = table.Column<bool>(type: "bit", nullable: false),
                    StrongbacksRequired = table.Column<bool>(type: "bit", nullable: false),
                    SealantType = table.Column<bool>(type: "bit", nullable: false),
                    ClientSpecified = table.Column<bool>(type: "bit", nullable: false),
                    Size = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BranchEndOption = table.Column<bool>(type: "bit", nullable: false),
                    WallThickness = table.Column<int>(type: "int", nullable: false),
                    Standard = table.Column<bool>(type: "bit", nullable: false),
                    ClientSpecified3 = table.Column<bool>(type: "bit", nullable: false),
                    Size4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlangeRF = table.Column<bool>(type: "bit", nullable: false),
                    FlangeRTJ = table.Column<bool>(type: "bit", nullable: false),
                    ButtWeld = table.Column<bool>(type: "bit", nullable: false),
                    Threaded = table.Column<bool>(type: "bit", nullable: false),
                    WallThickness4 = table.Column<int>(type: "int", nullable: false),
                    Standard4 = table.Column<bool>(type: "bit", nullable: false),
                    ClientSpecified4 = table.Column<bool>(type: "bit", nullable: false),
                    ParallelToRun = table.Column<bool>(type: "bit", nullable: false),
                    PerpendicularToRun = table.Column<bool>(type: "bit", nullable: false),
                    NotRequired5 = table.Column<bool>(type: "bit", nullable: false),
                    StandardCompletionPlugWithoutScarfedNipple = table.Column<bool>(type: "bit", nullable: false),
                    StandardCompletionPlugWithScarfedNipple = table.Column<bool>(type: "bit", nullable: false),
                    FlowThroughPlugWithPigBars = table.Column<int>(type: "int", nullable: false),
                    SolidCompletionPlugWithPigBars = table.Column<bool>(type: "bit", nullable: false),
                    MinValveBore = table.Column<int>(type: "int", nullable: false),
                    Buna = table.Column<bool>(type: "bit", nullable: false),
                    Viton = table.Column<bool>(type: "bit", nullable: false),
                    Aflas = table.Column<bool>(type: "bit", nullable: false),
                    EPDM = table.Column<bool>(type: "bit", nullable: false),
                    MetalToMetalSeal = table.Column<bool>(type: "bit", nullable: false),
                    HSS2Seal = table.Column<bool>(type: "bit", nullable: false),
                    HiStop = table.Column<bool>(type: "bit", nullable: false),
                    WeldOn = table.Column<bool>(type: "bit", nullable: false),
                    FlangeWithPlug = table.Column<bool>(type: "bit", nullable: false),
                    SureStop = table.Column<bool>(type: "bit", nullable: false),
                    BoltOn = table.Column<bool>(type: "bit", nullable: false),
                    PermanentValve = table.Column<bool>(type: "bit", nullable: false),
                    HTP = table.Column<bool>(type: "bit", nullable: false),
                    DrawWorksNotRequired = table.Column<bool>(type: "bit", nullable: false),
                    DrawWorksforSettingCompletion = table.Column<bool>(type: "bit", nullable: false),
                    PermanentDrawWorksHold = table.Column<bool>(type: "bit", nullable: false),
                    WeldOn7 = table.Column<bool>(type: "bit", nullable: false),
                    BoltOn7 = table.Column<bool>(type: "bit", nullable: false),
                    StandardFlange = table.Column<bool>(type: "bit", nullable: false),
                    LineStopFlange = table.Column<bool>(type: "bit", nullable: false),
                    BranchWithInternalThreads = table.Column<bool>(type: "bit", nullable: false),
                    ElbowLet = table.Column<bool>(type: "bit", nullable: false),
                    NozzleOnly7 = table.Column<bool>(type: "bit", nullable: false),
                    ShortRadius = table.Column<bool>(type: "bit", nullable: false),
                    LongRadius = table.Column<bool>(type: "bit", nullable: false),
                    ElbowOnHeel = table.Column<bool>(type: "bit", nullable: false),
                    ElbowStraight = table.Column<bool>(type: "bit", nullable: false),
                    Angle = table.Column<bool>(type: "bit", nullable: false),
                    AngleDegree = table.Column<int>(type: "int", nullable: false),
                    WeldOn9 = table.Column<bool>(type: "bit", nullable: false),
                    BoltOn9 = table.Column<bool>(type: "bit", nullable: false),
                    Size9 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Other9 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    WallThickness9 = table.Column<int>(type: "int", nullable: false),
                    RubberSeal = table.Column<bool>(type: "bit", nullable: false),
                    HSS2Seal9 = table.Column<bool>(type: "bit", nullable: false),
                    DrawWorksNotRequired9 = table.Column<bool>(type: "bit", nullable: false),
                    DrawWorksforSettingPlug9 = table.Column<int>(type: "int", nullable: false),
                    PermanentMachineAndValveRemoved = table.Column<int>(type: "int", nullable: false),
                    CEPercentage = table.Column<int>(type: "int", nullable: false),
                    YieldStrength = table.Column<int>(type: "int", nullable: false),
                    MaterialOrigin = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MaterialToughness = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MaximumHardness = table.Column<int>(type: "int", nullable: false),
                    AdditionalNotes = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PWHT = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BackingStrips = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Additional = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ThirdPartyInspection = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NoPressureTest = table.Column<bool>(type: "bit", nullable: false),
                    FieldTest = table.Column<bool>(type: "bit", nullable: false),
                    ShopTest = table.Column<bool>(type: "bit", nullable: false),
                    HoldTime = table.Column<int>(type: "int", nullable: false),
                    TestPressure = table.Column<int>(type: "int", nullable: false),
                    Primer = table.Column<bool>(type: "bit", nullable: false),
                    None = table.Column<bool>(type: "bit", nullable: false),
                    Other10 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BlindFlange = table.Column<bool>(type: "bit", nullable: false),
                    PortInFlange = table.Column<bool>(type: "bit", nullable: false),
                    Studs = table.Column<bool>(type: "bit", nullable: false),
                    Gasket = table.Column<bool>(type: "bit", nullable: false),
                    PortSize = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Grade = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    GasketType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Notes10 = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HtsEngineeringDataCoverSheets", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HtsEngineeringDataCoverSheets_DataSheets_Id",
                        column: x => x.Id,
                        principalTable: "DataSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "LeakRepairHardwareSpecificationSheets",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Site = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LineNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DistrictWorkOrderNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TeamCjNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Technician1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Technician2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LineMaterial = table.Column<bool>(type: "bit", nullable: false),
                    MainLineSize = table.Column<int>(type: "int", nullable: false),
                    BranchSize = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LineSchedule = table.Column<int>(type: "int", nullable: false),
                    LineContent = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LineContentPercentage = table.Column<int>(type: "int", nullable: false),
                    SystemDesignTemp = table.Column<int>(type: "int", nullable: false),
                    MaxSystemDesignTemp = table.Column<int>(type: "int", nullable: false),
                    SystemOpTemp = table.Column<int>(type: "int", nullable: false),
                    MaxSystemOpTemp = table.Column<int>(type: "int", nullable: false),
                    SysDesignPressure = table.Column<int>(type: "int", nullable: false),
                    SysOpPressure = table.Column<int>(type: "int", nullable: false),
                    MaxSysOpPressure = table.Column<int>(type: "int", nullable: false),
                    MDMT = table.Column<int>(type: "int", nullable: false),
                    Grade = table.Column<int>(type: "int", nullable: false),
                    Frequency = table.Column<int>(type: "int", nullable: false),
                    Vibration = table.Column<bool>(type: "bit", nullable: false),
                    SourService = table.Column<bool>(type: "bit", nullable: false),
                    DefectCause = table.Column<bool>(type: "bit", nullable: false),
                    DefectType = table.Column<bool>(type: "bit", nullable: false),
                    OtherDefect = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RemainingWallThickness = table.Column<int>(type: "int", nullable: false),
                    NdtDataProvided = table.Column<bool>(type: "bit", nullable: false),
                    ExpectedRemovalDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ReqRepairCorrosionAllowance = table.Column<int>(type: "int", nullable: false),
                    IfDosed = table.Column<bool>(type: "bit", nullable: false),
                    SuperheatedSteam = table.Column<bool>(type: "bit", nullable: false),
                    SprayTypeDesuperheater = table.Column<bool>(type: "bit", nullable: false),
                    Strongback = table.Column<bool>(type: "bit", nullable: false),
                    EnclosureQuantity = table.Column<int>(type: "int", nullable: false),
                    SealantSelection = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EnclosureMaterial = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ClientSpecificRequirements = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProcessingRequirements = table.Column<bool>(type: "bit", nullable: false),
                    ApprovalInformationDeadline = table.Column<DateTime>(type: "datetime2", nullable: false),
                    RequestedInstallationDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ApprovalEmails = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    QualityRequirements = table.Column<bool>(type: "bit", nullable: false),
                    EngineeringPriorityLevel = table.Column<bool>(type: "bit", nullable: false),
                    PrintName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Email = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Telephone = table.Column<int>(type: "int", nullable: false),
                    OrderNo = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LeakRepairHardwareSpecificationSheets", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LeakRepairHardwareSpecificationSheets_DataSheets_Id",
                        column: x => x.Id,
                        principalTable: "DataSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS100s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    FlgApp = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Flange1Width = table.Column<double>(type: "float", nullable: false),
                    Flange2Width = table.Column<double>(type: "float", nullable: false),
                    FirstDesiredEarLocationPoint = table.Column<int>(type: "int", nullable: false),
                    SecondDesiredEarLocationPoint = table.Column<int>(type: "int", nullable: false),
                    Flange1OuterDiameter12To6 = table.Column<double>(type: "float", nullable: false),
                    Flange1OuterDiameter1To7 = table.Column<double>(type: "float", nullable: false),
                    Flange1OuterDiameter2To8 = table.Column<double>(type: "float", nullable: false),
                    Flange1OuterDiameter3To9 = table.Column<double>(type: "float", nullable: false),
                    Flange1OuterDiameter4To10 = table.Column<double>(type: "float", nullable: false),
                    Flange1OuterDiameter5To11 = table.Column<double>(type: "float", nullable: false),
                    Flange2OuterDiameter12To6 = table.Column<double>(type: "float", nullable: false),
                    Flange2OuterDiameter1To7 = table.Column<double>(type: "float", nullable: false),
                    Flange2OuterDiameter2To8 = table.Column<double>(type: "float", nullable: false),
                    Flange2OuterDiameter3To9 = table.Column<double>(type: "float", nullable: false),
                    Flange2OuterDiameter4To10 = table.Column<double>(type: "float", nullable: false),
                    Flange2OuterDiameter5To11 = table.Column<double>(type: "float", nullable: false),
                    FirstStudDepthPoint = table.Column<int>(type: "int", nullable: false),
                    SecondStudDepthPoint = table.Column<int>(type: "int", nullable: false),
                    ThirdStudDepthPoint = table.Column<int>(type: "int", nullable: false),
                    FourthStudDepthPoint = table.Column<int>(type: "int", nullable: false),
                    FirstStudDepthForFlange1 = table.Column<double>(type: "float", nullable: false),
                    SecondStudDepthForFlange1 = table.Column<double>(type: "float", nullable: false),
                    ThirdStudDepthForFlange1 = table.Column<double>(type: "float", nullable: false),
                    FourthStudDepthForFlange1 = table.Column<double>(type: "float", nullable: false),
                    FirstStudDepthForFlange2 = table.Column<double>(type: "float", nullable: false),
                    SecondStudDepthForFlange2 = table.Column<double>(type: "float", nullable: false),
                    ThirdStudDepthForFlange2 = table.Column<double>(type: "float", nullable: false),
                    FourthStudDepthForFlange2 = table.Column<double>(type: "float", nullable: false),
                    DepthToGasket = table.Column<int>(type: "int", nullable: false),
                    NumberOfStuds = table.Column<int>(type: "int", nullable: false),
                    StudSize = table.Column<int>(type: "int", nullable: false),
                    StudMaterial = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FromFlange = table.Column<int>(type: "int", nullable: false),
                    Point1FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point2FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point3FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point4FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point5FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point6FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point7FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point8FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point9FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point10FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point11FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point12FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point1FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point2FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point3FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point4FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point5FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point6FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point7FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point8FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point9FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point10FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point11FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point12FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point1Amount = table.Column<int>(type: "int", nullable: false),
                    Point2Amount = table.Column<int>(type: "int", nullable: false),
                    Point3Amount = table.Column<int>(type: "int", nullable: false),
                    Point4Amount = table.Column<int>(type: "int", nullable: false),
                    Point5Amount = table.Column<int>(type: "int", nullable: false),
                    Point6Amount = table.Column<int>(type: "int", nullable: false),
                    Point7Amount = table.Column<int>(type: "int", nullable: false),
                    Point8Amount = table.Column<int>(type: "int", nullable: false),
                    Point9Amount = table.Column<int>(type: "int", nullable: false),
                    Point10Amount = table.Column<int>(type: "int", nullable: false),
                    Point11Amount = table.Column<int>(type: "int", nullable: false),
                    Point12Amount = table.Column<int>(type: "int", nullable: false),
                    Point1Gap = table.Column<int>(type: "int", nullable: false),
                    Point2Gap = table.Column<int>(type: "int", nullable: false),
                    Point3Gap = table.Column<int>(type: "int", nullable: false),
                    Point4Gap = table.Column<int>(type: "int", nullable: false),
                    Point5Gap = table.Column<int>(type: "int", nullable: false),
                    Point6Gap = table.Column<int>(type: "int", nullable: false),
                    Point7Gap = table.Column<int>(type: "int", nullable: false),
                    Point8Gap = table.Column<int>(type: "int", nullable: false),
                    Point9Gap = table.Column<int>(type: "int", nullable: false),
                    Point10Gap = table.Column<int>(type: "int", nullable: false),
                    Point11Gap = table.Column<int>(type: "int", nullable: false),
                    Point12Gap = table.Column<int>(type: "int", nullable: false),
                    Point1Overall = table.Column<int>(type: "int", nullable: false),
                    Point2Overall = table.Column<int>(type: "int", nullable: false),
                    Point3Overall = table.Column<int>(type: "int", nullable: false),
                    Point4Overall = table.Column<int>(type: "int", nullable: false),
                    Point5Overall = table.Column<int>(type: "int", nullable: false),
                    Point6Overall = table.Column<int>(type: "int", nullable: false),
                    Point7Overall = table.Column<int>(type: "int", nullable: false),
                    Point8Overall = table.Column<int>(type: "int", nullable: false),
                    Point9Overall = table.Column<int>(type: "int", nullable: false),
                    Point10Overall = table.Column<int>(type: "int", nullable: false),
                    Point11Overall = table.Column<int>(type: "int", nullable: false),
                    Point12Overall = table.Column<int>(type: "int", nullable: false),
                    SS016 = table.Column<bool>(type: "bit", nullable: false),
                    SS035 = table.Column<bool>(type: "bit", nullable: false),
                    CS035 = table.Column<bool>(type: "bit", nullable: false),
                    Capnuts = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CapnutsNumbersRequired = table.Column<int>(type: "int", nullable: false),
                    SlottedStuds = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SlottedStudsNumbersRequired = table.Column<int>(type: "int", nullable: false),
                    InjectionRings = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    InjectionRingsNumbersRequired = table.Column<int>(type: "int", nullable: false),
                    DTP = table.Column<bool>(type: "bit", nullable: false),
                    Elbows = table.Column<bool>(type: "bit", nullable: false),
                    OtherWireWrap = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NumberAndSizeRequired = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS100s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS100s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS101s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Flange1PointA = table.Column<int>(type: "int", nullable: false),
                    Flange1PointB = table.Column<int>(type: "int", nullable: false),
                    Flange1PointC = table.Column<int>(type: "int", nullable: false),
                    Flange1PointD = table.Column<int>(type: "int", nullable: false),
                    Flange1PointE = table.Column<int>(type: "int", nullable: false),
                    Flange1PointF = table.Column<int>(type: "int", nullable: false),
                    Flange1PointG = table.Column<int>(type: "int", nullable: false),
                    Flange1PointH = table.Column<int>(type: "int", nullable: false),
                    Flange1PointJ = table.Column<int>(type: "int", nullable: false),
                    Flange1PointK = table.Column<int>(type: "int", nullable: false),
                    Flange1PointL = table.Column<int>(type: "int", nullable: false),
                    Flange1PointM = table.Column<int>(type: "int", nullable: false),
                    Flange1PointN = table.Column<int>(type: "int", nullable: false),
                    Flange1PointP = table.Column<int>(type: "int", nullable: false),
                    Flange1PointQ = table.Column<int>(type: "int", nullable: false),
                    Flange1PointR = table.Column<int>(type: "int", nullable: false),
                    Flange1PointS = table.Column<int>(type: "int", nullable: false),
                    Flange1PointT = table.Column<int>(type: "int", nullable: false),
                    Flange1PointU = table.Column<int>(type: "int", nullable: false),
                    Flange1PointW = table.Column<int>(type: "int", nullable: false),
                    Flange1PointX = table.Column<int>(type: "int", nullable: false),
                    Flange2PointA = table.Column<int>(type: "int", nullable: false),
                    Flange2PointB = table.Column<int>(type: "int", nullable: false),
                    Flange2PointC = table.Column<int>(type: "int", nullable: false),
                    Flange2PointD = table.Column<int>(type: "int", nullable: false),
                    Flange2PointE = table.Column<int>(type: "int", nullable: false),
                    Flange2PointF = table.Column<int>(type: "int", nullable: false),
                    Flange2PointG = table.Column<int>(type: "int", nullable: false),
                    Flange2PointH = table.Column<int>(type: "int", nullable: false),
                    Flange2PointJ = table.Column<int>(type: "int", nullable: false),
                    Flange2PointK = table.Column<int>(type: "int", nullable: false),
                    Flange2PointL = table.Column<int>(type: "int", nullable: false),
                    Flange2PointM = table.Column<int>(type: "int", nullable: false),
                    Flange2PointN = table.Column<int>(type: "int", nullable: false),
                    Flange2PointP = table.Column<int>(type: "int", nullable: false),
                    Flange2PointQ = table.Column<int>(type: "int", nullable: false),
                    Flange2PointR = table.Column<int>(type: "int", nullable: false),
                    Flange2PointS = table.Column<int>(type: "int", nullable: false),
                    Flange2PointT = table.Column<int>(type: "int", nullable: false),
                    Flange2PointU = table.Column<int>(type: "int", nullable: false),
                    Flange2PointW = table.Column<int>(type: "int", nullable: false),
                    Flange2PointX = table.Column<int>(type: "int", nullable: false),
                    NoOfStuds = table.Column<int>(type: "int", nullable: false),
                    StudSize = table.Column<int>(type: "int", nullable: false),
                    StudMaterial = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DTG = table.Column<int>(type: "int", nullable: false),
                    Point1FlangeOver = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point2FlangeOver = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3FlangeOver = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4FlangeOver = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5FlangeOver = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6FlangeOver = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point7FlangeOver = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8FlangeOver = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point9FlangeOver = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10FlangeOver = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point11FlangeOver = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point12FlangeOver = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1FlangeUnder = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point2FlangeUnder = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3FlangeUnder = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4FlangeUnder = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5FlangeUnder = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6FlangeUnder = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point7FlangeUnder = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8FlangeUnder = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point9FlangeUnder = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10FlangeUnder = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point11FlangeUnder = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point12FlangeUnder = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1Amount = table.Column<int>(type: "int", nullable: false),
                    Point2Amount = table.Column<int>(type: "int", nullable: false),
                    Point3Amount = table.Column<int>(type: "int", nullable: false),
                    Point4Amount = table.Column<int>(type: "int", nullable: false),
                    Point5Amount = table.Column<int>(type: "int", nullable: false),
                    Point6Amount = table.Column<int>(type: "int", nullable: false),
                    Point7Amount = table.Column<int>(type: "int", nullable: false),
                    Point8Amount = table.Column<int>(type: "int", nullable: false),
                    Point9Amount = table.Column<int>(type: "int", nullable: false),
                    Point10Amount = table.Column<int>(type: "int", nullable: false),
                    Point11Amount = table.Column<int>(type: "int", nullable: false),
                    Point12Amount = table.Column<int>(type: "int", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS101s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS101s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS102s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Flange1PointA = table.Column<int>(type: "int", nullable: false),
                    Flange1PointB = table.Column<int>(type: "int", nullable: false),
                    Flange1PointC = table.Column<int>(type: "int", nullable: false),
                    Flange1PointD = table.Column<int>(type: "int", nullable: false),
                    Flange1PointE = table.Column<int>(type: "int", nullable: false),
                    Flange1PointF = table.Column<int>(type: "int", nullable: false),
                    Flange1PointG = table.Column<int>(type: "int", nullable: false),
                    Flange1PointH = table.Column<int>(type: "int", nullable: false),
                    Flange1PointJ = table.Column<int>(type: "int", nullable: false),
                    Flange1PointW = table.Column<int>(type: "int", nullable: false),
                    Flange1Point1X = table.Column<int>(type: "int", nullable: false),
                    Flange1Point2X = table.Column<int>(type: "int", nullable: false),
                    Flange1Point3X = table.Column<int>(type: "int", nullable: false),
                    Flange1Point4X = table.Column<int>(type: "int", nullable: false),
                    Flange1Point5X = table.Column<int>(type: "int", nullable: false),
                    Flange1Point6X = table.Column<int>(type: "int", nullable: false),
                    Flange1Point7X = table.Column<int>(type: "int", nullable: false),
                    Flange1Point8X = table.Column<int>(type: "int", nullable: false),
                    Flange1Point9X = table.Column<int>(type: "int", nullable: false),
                    Flange1Point10X = table.Column<int>(type: "int", nullable: false),
                    Flange1PointWX = table.Column<int>(type: "int", nullable: false),
                    Flange1Point1Y = table.Column<int>(type: "int", nullable: false),
                    Flange1Point2Y = table.Column<int>(type: "int", nullable: false),
                    Flange1Point3Y = table.Column<int>(type: "int", nullable: false),
                    Flange1Point4Y = table.Column<int>(type: "int", nullable: false),
                    Flange1Point5Y = table.Column<int>(type: "int", nullable: false),
                    Flange1PointWY = table.Column<int>(type: "int", nullable: false),
                    Flange2PointA = table.Column<int>(type: "int", nullable: false),
                    Flange2PointB = table.Column<int>(type: "int", nullable: false),
                    Flange2PointC = table.Column<int>(type: "int", nullable: false),
                    Flange2PointD = table.Column<int>(type: "int", nullable: false),
                    Flange2PointE = table.Column<int>(type: "int", nullable: false),
                    Flange2PointF = table.Column<int>(type: "int", nullable: false),
                    Flange2PointG = table.Column<int>(type: "int", nullable: false),
                    Flange2PointH = table.Column<int>(type: "int", nullable: false),
                    Flange2PointJ = table.Column<int>(type: "int", nullable: false),
                    Flange2PointW = table.Column<int>(type: "int", nullable: false),
                    Flange2Point1X = table.Column<int>(type: "int", nullable: false),
                    Flange2Point2X = table.Column<int>(type: "int", nullable: false),
                    Flange2Point3X = table.Column<int>(type: "int", nullable: false),
                    Flange2Point4X = table.Column<int>(type: "int", nullable: false),
                    Flange2Point5X = table.Column<int>(type: "int", nullable: false),
                    Flange2Point6X = table.Column<int>(type: "int", nullable: false),
                    Flange2Point7X = table.Column<int>(type: "int", nullable: false),
                    Flange2Point8X = table.Column<int>(type: "int", nullable: false),
                    Flange2Point9X = table.Column<int>(type: "int", nullable: false),
                    Flange2Point10X = table.Column<int>(type: "int", nullable: false),
                    Flange2PointWX = table.Column<int>(type: "int", nullable: false),
                    Flange2Point1Y = table.Column<int>(type: "int", nullable: false),
                    Flange2Point2Y = table.Column<int>(type: "int", nullable: false),
                    Flange2Point3Y = table.Column<int>(type: "int", nullable: false),
                    Flange2Point4Y = table.Column<int>(type: "int", nullable: false),
                    Flange2Point5Y = table.Column<int>(type: "int", nullable: false),
                    Flange2PointWY = table.Column<int>(type: "int", nullable: false),
                    NoOfStuds = table.Column<int>(type: "int", nullable: false),
                    StudSize = table.Column<int>(type: "int", nullable: false),
                    StudMaterial = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DTG = table.Column<int>(type: "int", nullable: false),
                    IncrOHalf = table.Column<bool>(type: "bit", nullable: false),
                    IncrOne = table.Column<bool>(type: "bit", nullable: false),
                    IncrOneAndHalf = table.Column<bool>(type: "bit", nullable: false),
                    Point1xLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point2xLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point3xLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point4xLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point5xLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point6xLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point7xLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point8xLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point9xLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point10xLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    PointWxLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point1yLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point2yLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point3yLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point4yLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point5yLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    PointWyLtrFlange1Section2 = table.Column<int>(type: "int", nullable: false),
                    Point1xLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point2xLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point3xLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point4xLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point5xLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point6xLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point7xLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point8xLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point9xLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point10xLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    PointWxLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point1yLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point2yLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point3yLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point4yLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point5yLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    PointWyLtrFlange2Section2 = table.Column<int>(type: "int", nullable: false),
                    Point1xLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point2xLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point3xLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point4xLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point5xLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point6xLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point7xLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point8xLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point9xLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point10xLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    PointWxLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point1yLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point2yLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point3yLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point4yLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point5yLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    PointWyLtrFlange1Section3 = table.Column<int>(type: "int", nullable: false),
                    Point1xLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point2xLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point3xLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point4xLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point5xLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point6xLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point7xLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point8xLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point9xLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point10xLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    PointWxLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point1yLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point2yLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point3yLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point4yLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point5yLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    PointWyLtrFlange2Section3 = table.Column<int>(type: "int", nullable: false),
                    Point1xLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point2xLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point3xLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point4xLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point5xLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point6xLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point7xLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point8xLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point9xLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point10xLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    PointWxLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point1yLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point2yLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point3yLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point4yLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point5yLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    PointWyLtrFlange1Section4 = table.Column<int>(type: "int", nullable: false),
                    Point1xLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point2xLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point3xLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point4xLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point5xLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point6xLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point7xLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point8xLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point9xLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point10xLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    PointWxLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point1yLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point2yLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point3yLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point4yLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point5yLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    PointWyLtrFlange2Section4 = table.Column<int>(type: "int", nullable: false),
                    Point2xFlangeOverSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4xFlangeOverSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6xFlangeOverSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8xFlangeOverSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10xFlangeOverSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1yFlangeOverSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3yFlangeOverSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5yFlangeOverSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point2xFlangeUnderSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4xFlangeUnderSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6xFlangeUnderSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8xFlangeUnderSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10xFlangeUnderSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1yFlangeUnderSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3yFlangeUnderSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5yFlangeUnderSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point2xAmountSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4xAmountSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6xAmountSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8xAmountSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10xAmountSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1yAmountSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3yAmountSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5yAmountSection1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point2xFlangeOverSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4xFlangeOverSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6xFlangeOverSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8xFlangeOverSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10xFlangeOverSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1yFlangeOverSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3yFlangeOverSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5yFlangeOverSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point2xFlangeUnderSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4xFlangeUnderSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6xFlangeUnderSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8xFlangeUnderSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10xFlangeUnderSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1yFlangeUnderSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3yFlangeUnderSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5yFlangeUnderSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point2xAmountSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4xAmountSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6xAmountSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8xAmountSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10xAmountSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1yAmountSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3yAmountSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5yAmountSection2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point2xFlangeOverSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4xFlangeOverSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6xFlangeOverSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8xFlangeOverSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10xFlangeOverSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1yFlangeOverSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3yFlangeOverSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5yFlangeOverSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point2xFlangeUnderSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4xFlangeUnderSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6xFlangeUnderSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8xFlangeUnderSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10xFlangeUnderSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1yFlangeUnderSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3yFlangeUnderSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5yFlangeUnderSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point2xAmountSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4xAmountSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6xAmountSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8xAmountSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10xAmountSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1yAmountSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3yAmountSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5yAmountSection3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point2xFlangeOverSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4xFlangeOverSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6xFlangeOverSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8xFlangeOverSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10xFlangeOverSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1yFlangeOverSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3yFlangeOverSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5yFlangeOverSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point2xFlangeUnderSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4xFlangeUnderSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6xFlangeUnderSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8xFlangeUnderSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10xFlangeUnderSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1yFlangeUnderSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3yFlangeUnderSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5yFlangeUnderSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point2xAmountSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point4xAmountSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point6xAmountSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point8xAmountSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point10xAmountSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point1yAmountSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point3yAmountSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Point5yAmountSection4 = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS102s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS102s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS103s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A = table.Column<int>(type: "int", nullable: false),
                    H = table.Column<int>(type: "int", nullable: false),
                    G = table.Column<int>(type: "int", nullable: false),
                    P = table.Column<int>(type: "int", nullable: false),
                    R = table.Column<int>(type: "int", nullable: false),
                    Q = table.Column<int>(type: "int", nullable: false),
                    T = table.Column<int>(type: "int", nullable: false),
                    U = table.Column<int>(type: "int", nullable: false),
                    V = table.Column<int>(type: "int", nullable: false),
                    DTG = table.Column<int>(type: "int", nullable: false),
                    MinClearanceBetweenNuts = table.Column<int>(type: "int", nullable: false),
                    NoOfStudsBetweenStud2and3 = table.Column<int>(type: "int", nullable: false),
                    StudSize = table.Column<int>(type: "int", nullable: false),
                    StudMaterial = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Stud1Flg1 = table.Column<int>(type: "int", nullable: false),
                    Stud2Flg1 = table.Column<int>(type: "int", nullable: false),
                    Stud3Flg1 = table.Column<int>(type: "int", nullable: false),
                    Stud1Flg2 = table.Column<int>(type: "int", nullable: false),
                    Stud2Flg2 = table.Column<int>(type: "int", nullable: false),
                    Stud3Flg3 = table.Column<int>(type: "int", nullable: false),
                    Flange1Width = table.Column<int>(type: "int", nullable: false),
                    Flange2Width = table.Column<int>(type: "int", nullable: false),
                    Holland = table.Column<bool>(type: "bit", nullable: false),
                    DandT = table.Column<bool>(type: "bit", nullable: false),
                    Other = table.Column<bool>(type: "bit", nullable: false),
                    Flange1OD = table.Column<int>(type: "int", nullable: false),
                    Flange2OD = table.Column<int>(type: "int", nullable: false),
                    FlgOverStud1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgOverStud2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgOverStud3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgOverStud4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgOverStud5 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgOverStud6 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgOverStud7 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgOverStud8 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgUnderStud1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgUnderStud2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgUnderStud3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgUnderStud4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgUnderStud5 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgUnderStud6 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgUnderStud7 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlgUnderStud8 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Amount1 = table.Column<int>(type: "int", nullable: false),
                    Amount2 = table.Column<int>(type: "int", nullable: false),
                    Amount3 = table.Column<int>(type: "int", nullable: false),
                    Amount4 = table.Column<int>(type: "int", nullable: false),
                    Amount5 = table.Column<int>(type: "int", nullable: false),
                    Amount6 = table.Column<int>(type: "int", nullable: false),
                    Amount7 = table.Column<int>(type: "int", nullable: false),
                    Amount8 = table.Column<int>(type: "int", nullable: false),
                    FlgGapAmount1 = table.Column<int>(type: "int", nullable: false),
                    FlgGapAmount2 = table.Column<int>(type: "int", nullable: false),
                    FlgGapAmount3 = table.Column<int>(type: "int", nullable: false),
                    FlgGapAmount4 = table.Column<int>(type: "int", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS103s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS103s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS104s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A = table.Column<int>(type: "int", nullable: false),
                    B = table.Column<int>(type: "int", nullable: false),
                    C = table.Column<int>(type: "int", nullable: false),
                    D = table.Column<int>(type: "int", nullable: false),
                    E = table.Column<int>(type: "int", nullable: false),
                    F = table.Column<int>(type: "int", nullable: false),
                    G = table.Column<int>(type: "int", nullable: false),
                    H = table.Column<int>(type: "int", nullable: false),
                    J = table.Column<int>(type: "int", nullable: false),
                    K = table.Column<int>(type: "int", nullable: false),
                    L = table.Column<int>(type: "int", nullable: false),
                    M = table.Column<int>(type: "int", nullable: false),
                    N = table.Column<int>(type: "int", nullable: false),
                    P = table.Column<int>(type: "int", nullable: false),
                    Q = table.Column<int>(type: "int", nullable: false),
                    R = table.Column<int>(type: "int", nullable: false),
                    S = table.Column<int>(type: "int", nullable: false),
                    T = table.Column<int>(type: "int", nullable: false),
                    U = table.Column<int>(type: "int", nullable: false),
                    V = table.Column<int>(type: "int", nullable: false),
                    W = table.Column<int>(type: "int", nullable: false),
                    X = table.Column<int>(type: "int", nullable: false),
                    Y = table.Column<int>(type: "int", nullable: false),
                    Z = table.Column<int>(type: "int", nullable: false),
                    AA = table.Column<int>(type: "int", nullable: false),
                    BB = table.Column<int>(type: "int", nullable: false),
                    CC = table.Column<int>(type: "int", nullable: false),
                    DD = table.Column<int>(type: "int", nullable: false),
                    EE = table.Column<int>(type: "int", nullable: false),
                    FF = table.Column<int>(type: "int", nullable: false),
                    GG = table.Column<int>(type: "int", nullable: false),
                    HH = table.Column<int>(type: "int", nullable: false),
                    JJ = table.Column<int>(type: "int", nullable: false),
                    KK = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    G2 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    K2 = table.Column<int>(type: "int", nullable: false),
                    L2 = table.Column<int>(type: "int", nullable: false),
                    M2 = table.Column<int>(type: "int", nullable: false),
                    N2 = table.Column<int>(type: "int", nullable: false),
                    P2 = table.Column<int>(type: "int", nullable: false),
                    Q2 = table.Column<int>(type: "int", nullable: false),
                    R2 = table.Column<int>(type: "int", nullable: false),
                    S2 = table.Column<int>(type: "int", nullable: false),
                    T2 = table.Column<int>(type: "int", nullable: false),
                    U2 = table.Column<int>(type: "int", nullable: false),
                    V2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    X2 = table.Column<int>(type: "int", nullable: false),
                    Y2 = table.Column<int>(type: "int", nullable: false),
                    Z2 = table.Column<int>(type: "int", nullable: false),
                    AA2 = table.Column<int>(type: "int", nullable: false),
                    BB2 = table.Column<int>(type: "int", nullable: false),
                    CC2 = table.Column<int>(type: "int", nullable: false),
                    DD2 = table.Column<int>(type: "int", nullable: false),
                    EE2 = table.Column<int>(type: "int", nullable: false),
                    FF2 = table.Column<int>(type: "int", nullable: false),
                    GG2 = table.Column<int>(type: "int", nullable: false),
                    HH2 = table.Column<int>(type: "int", nullable: false),
                    JJ2 = table.Column<int>(type: "int", nullable: false),
                    KK2 = table.Column<int>(type: "int", nullable: false),
                    Threaded = table.Column<bool>(type: "bit", nullable: false),
                    Welded = table.Column<bool>(type: "bit", nullable: false),
                    IdTab = table.Column<bool>(type: "bit", nullable: false),
                    LocationOfBlows = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Flg1ValvePos = table.Column<int>(type: "int", nullable: false),
                    Flg2ValvePos = table.Column<int>(type: "int", nullable: false),
                    ValveClkPt = table.Column<int>(type: "int", nullable: false),
                    TabClkPt = table.Column<int>(type: "int", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS104s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS104s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS105s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Temp1 = table.Column<int>(type: "int", nullable: false),
                    Temp2 = table.Column<int>(type: "int", nullable: false),
                    Temp3 = table.Column<int>(type: "int", nullable: false),
                    Temp4 = table.Column<int>(type: "int", nullable: false),
                    Pres1 = table.Column<int>(type: "int", nullable: false),
                    Pres2 = table.Column<int>(type: "int", nullable: false),
                    Pres3 = table.Column<int>(type: "int", nullable: false),
                    Pres4 = table.Column<int>(type: "int", nullable: false),
                    Dim1 = table.Column<int>(type: "int", nullable: false),
                    Dim2 = table.Column<int>(type: "int", nullable: false),
                    Dim3 = table.Column<int>(type: "int", nullable: false),
                    Dim4 = table.Column<int>(type: "int", nullable: false),
                    Flg1ODPt12to6F = table.Column<int>(type: "int", nullable: false),
                    Flg1ODPt1to7 = table.Column<int>(type: "int", nullable: false),
                    Flg1ODPt2to8 = table.Column<int>(type: "int", nullable: false),
                    Flg1ODPt3to9 = table.Column<int>(type: "int", nullable: false),
                    Flg1ODPt4to10 = table.Column<int>(type: "int", nullable: false),
                    Flg1ODPt5to11 = table.Column<int>(type: "int", nullable: false),
                    Flg1Width = table.Column<int>(type: "int", nullable: false),
                    Flg2ODPt12to6F = table.Column<int>(type: "int", nullable: false),
                    Flg2ODPt1to7 = table.Column<int>(type: "int", nullable: false),
                    Flg2ODPt2to8 = table.Column<int>(type: "int", nullable: false),
                    Flg2ODPt3to9 = table.Column<int>(type: "int", nullable: false),
                    Flg2ODPt4to10 = table.Column<int>(type: "int", nullable: false),
                    Flg2ODPt5to11 = table.Column<int>(type: "int", nullable: false),
                    Flg2Width = table.Column<int>(type: "int", nullable: false),
                    Flg3ODPt12to6F = table.Column<int>(type: "int", nullable: false),
                    Flg3ODPt1to7 = table.Column<int>(type: "int", nullable: false),
                    Flg3ODPt2to8 = table.Column<int>(type: "int", nullable: false),
                    Flg3ODPt3to9 = table.Column<int>(type: "int", nullable: false),
                    Flg3ODPt4to10 = table.Column<int>(type: "int", nullable: false),
                    Flg3ODPt5to11 = table.Column<int>(type: "int", nullable: false),
                    Flg3Width = table.Column<int>(type: "int", nullable: false),
                    StudDepth1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StudDepth2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StudDepth3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StudDepth4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Flg1StudDepth1 = table.Column<int>(type: "int", nullable: false),
                    Flg1StudDepth2 = table.Column<int>(type: "int", nullable: false),
                    Flg1StudDepth3 = table.Column<int>(type: "int", nullable: false),
                    Flg1StudDepth4 = table.Column<int>(type: "int", nullable: false),
                    Flg2StudDepth1 = table.Column<int>(type: "int", nullable: false),
                    Flg2StudDepth2 = table.Column<int>(type: "int", nullable: false),
                    Flg2StudDepth3 = table.Column<int>(type: "int", nullable: false),
                    Flg2StudDepth4 = table.Column<int>(type: "int", nullable: false),
                    Flg3StudDepth1 = table.Column<int>(type: "int", nullable: false),
                    Flg3StudDepth2 = table.Column<int>(type: "int", nullable: false),
                    Flg3StudDepth3 = table.Column<int>(type: "int", nullable: false),
                    Flg3StudDepth4 = table.Column<int>(type: "int", nullable: false),
                    NoOfStuds = table.Column<int>(type: "int", nullable: false),
                    StudSize = table.Column<int>(type: "int", nullable: false),
                    StudMaterial = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DTG = table.Column<int>(type: "int", nullable: false),
                    Point1FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point2FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point3FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point4FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point5FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point6FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point7FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point8FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point9FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point10FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point11FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point12FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point1FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point2FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point3FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point4FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point5FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point6FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point7FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point8FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point9FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point10FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point11FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point12FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point1Amount = table.Column<int>(type: "int", nullable: false),
                    Point2Amount = table.Column<int>(type: "int", nullable: false),
                    Point3Amount = table.Column<int>(type: "int", nullable: false),
                    Point4Amount = table.Column<int>(type: "int", nullable: false),
                    Point5Amount = table.Column<int>(type: "int", nullable: false),
                    Point6Amount = table.Column<int>(type: "int", nullable: false),
                    Point7Amount = table.Column<int>(type: "int", nullable: false),
                    Point8Amount = table.Column<int>(type: "int", nullable: false),
                    Point9Amount = table.Column<int>(type: "int", nullable: false),
                    Point10Amount = table.Column<int>(type: "int", nullable: false),
                    Point11Amount = table.Column<int>(type: "int", nullable: false),
                    Point12Amount = table.Column<int>(type: "int", nullable: false),
                    If3Point1FlangeOver = table.Column<int>(type: "int", nullable: false),
                    If3Point2FlangeOver = table.Column<int>(type: "int", nullable: false),
                    If3Point3FlangeOver = table.Column<int>(type: "int", nullable: false),
                    If3Point4FlangeOver = table.Column<int>(type: "int", nullable: false),
                    If3Point5FlangeOver = table.Column<int>(type: "int", nullable: false),
                    If3Point6FlangeOver = table.Column<int>(type: "int", nullable: false),
                    If3Point7FlangeOver = table.Column<int>(type: "int", nullable: false),
                    If3Point8FlangeOver = table.Column<int>(type: "int", nullable: false),
                    If3Point9FlangeOver = table.Column<int>(type: "int", nullable: false),
                    If3Point10FlangeOver = table.Column<int>(type: "int", nullable: false),
                    If3Point11FlangeOver = table.Column<int>(type: "int", nullable: false),
                    If3Point12FlangeOver = table.Column<int>(type: "int", nullable: false),
                    If3Point1FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    If3Point2FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    If3Point3FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    If3Point4FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    If3Point5FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    If3Point6FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    If3Point7FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    If3Point8FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    If3Point9FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    If3Point10FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    If3Point11FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    If3Point12FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    If3Point1Amount = table.Column<int>(type: "int", nullable: false),
                    If3Point2Amount = table.Column<int>(type: "int", nullable: false),
                    If3Point3Amount = table.Column<int>(type: "int", nullable: false),
                    If3Point4Amount = table.Column<int>(type: "int", nullable: false),
                    If3Point5Amount = table.Column<int>(type: "int", nullable: false),
                    If3Point6Amount = table.Column<int>(type: "int", nullable: false),
                    If3Point7Amount = table.Column<int>(type: "int", nullable: false),
                    If3Point8Amount = table.Column<int>(type: "int", nullable: false),
                    If3Point9Amount = table.Column<int>(type: "int", nullable: false),
                    If3Point10Amount = table.Column<int>(type: "int", nullable: false),
                    If3Point11Amount = table.Column<int>(type: "int", nullable: false),
                    If3Point12Amount = table.Column<int>(type: "int", nullable: false),
                    MinNutToNut = table.Column<int>(type: "int", nullable: false),
                    NutPointToPoint = table.Column<int>(type: "int", nullable: false),
                    NutFlatToFlat = table.Column<int>(type: "int", nullable: false),
                    Point1Flg1MinusFlg3 = table.Column<int>(type: "int", nullable: false),
                    Point2Flg1MinusFlg3 = table.Column<int>(type: "int", nullable: false),
                    Point3Flg1MinusFlg3 = table.Column<int>(type: "int", nullable: false),
                    Point4Flg1MinusFlg3 = table.Column<int>(type: "int", nullable: false),
                    Point5Flg1MinusFlg3 = table.Column<int>(type: "int", nullable: false),
                    Point6Flg1MinusFlg3 = table.Column<int>(type: "int", nullable: false),
                    Point7Flg1MinusFlg3 = table.Column<int>(type: "int", nullable: false),
                    Point8Flg1MinusFlg3 = table.Column<int>(type: "int", nullable: false),
                    Point10Flg1MinusFlg3 = table.Column<int>(type: "int", nullable: false),
                    Point11Flg1MinusFlg3 = table.Column<int>(type: "int", nullable: false),
                    Point12Flg1MinusFlg3 = table.Column<int>(type: "int", nullable: false),
                    Point1Flg3MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point2Flg3MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point3Flg3MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point4Flg3MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point5Flg3MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point6Flg3MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point7Flg3MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point8Flg3MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point10Flg3MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point11Flg3MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point12Flg3MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point1Flg1MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point2Flg1MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point3Flg1MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point4Flg1MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point5Flg1MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point6Flg1MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point7Flg1MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point8Flg1MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point10Flg1MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point11Flg1MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Point12Flg1MinusFlg2 = table.Column<int>(type: "int", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS105s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS105s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS110s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    K1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    WH1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    K2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    WH2 = table.Column<int>(type: "int", nullable: false),
                    F1a = table.Column<int>(type: "int", nullable: false),
                    F1b = table.Column<int>(type: "int", nullable: false),
                    F2a = table.Column<int>(type: "int", nullable: false),
                    F2b = table.Column<int>(type: "int", nullable: false),
                    G1a = table.Column<int>(type: "int", nullable: false),
                    G1b = table.Column<int>(type: "int", nullable: false),
                    G2a = table.Column<int>(type: "int", nullable: false),
                    G2b = table.Column<int>(type: "int", nullable: false),
                    L13 = table.Column<int>(type: "int", nullable: false),
                    L16 = table.Column<int>(type: "int", nullable: false),
                    L19 = table.Column<int>(type: "int", nullable: false),
                    L112 = table.Column<int>(type: "int", nullable: false),
                    L23 = table.Column<int>(type: "int", nullable: false),
                    L26 = table.Column<int>(type: "int", nullable: false),
                    L29 = table.Column<int>(type: "int", nullable: false),
                    L212 = table.Column<int>(type: "int", nullable: false),
                    Point1Gap = table.Column<int>(type: "int", nullable: false),
                    Point2Gap = table.Column<int>(type: "int", nullable: false),
                    Point3Gap = table.Column<int>(type: "int", nullable: false),
                    Point4Gap = table.Column<int>(type: "int", nullable: false),
                    Point5Gap = table.Column<int>(type: "int", nullable: false),
                    Point6Gap = table.Column<int>(type: "int", nullable: false),
                    Point7Gap = table.Column<int>(type: "int", nullable: false),
                    Point8Gap = table.Column<int>(type: "int", nullable: false),
                    Point9Gap = table.Column<int>(type: "int", nullable: false),
                    Point10Gap = table.Column<int>(type: "int", nullable: false),
                    Point11Gap = table.Column<int>(type: "int", nullable: false),
                    Point12Gap = table.Column<int>(type: "int", nullable: false),
                    Point1Overall = table.Column<int>(type: "int", nullable: false),
                    Point2Overall = table.Column<int>(type: "int", nullable: false),
                    Point3Overall = table.Column<int>(type: "int", nullable: false),
                    Point4Overall = table.Column<int>(type: "int", nullable: false),
                    Point5Overall = table.Column<int>(type: "int", nullable: false),
                    Point6Overall = table.Column<int>(type: "int", nullable: false),
                    Point7Overall = table.Column<int>(type: "int", nullable: false),
                    Point8Overall = table.Column<int>(type: "int", nullable: false),
                    Point9Overall = table.Column<int>(type: "int", nullable: false),
                    Point10Overall = table.Column<int>(type: "int", nullable: false),
                    Point11Overall = table.Column<int>(type: "int", nullable: false),
                    Point12Overall = table.Column<int>(type: "int", nullable: false),
                    OdFlangeOne12to6 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeOne1to7 = table.Column<DateTime>(type: "datetime2", nullable: false),
                    OdFlangeOne2to8 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeOne3to9 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeOne4to10 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeOne5to11 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeTwo12to6 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeTwo1to7 = table.Column<DateTime>(type: "datetime2", nullable: false),
                    OdFlangeTwo2to8 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeTwo3to9 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeTwo4to10 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeTwo5to11 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StudDepthPoint1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StudDepthPoint2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StudDepthPoint3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StudDepthPoint1Flange1 = table.Column<int>(type: "int", nullable: false),
                    StudDepthPoint2Flange1 = table.Column<int>(type: "int", nullable: false),
                    StudDepthPoint3Flange1 = table.Column<int>(type: "int", nullable: false),
                    StudDepthPoint1Flange2 = table.Column<int>(type: "int", nullable: false),
                    StudDepthPoint2Flange2 = table.Column<int>(type: "int", nullable: false),
                    StudDepthPoint3Flange2 = table.Column<int>(type: "int", nullable: false),
                    DepthToGasket = table.Column<int>(type: "int", nullable: false),
                    NoOfStuds = table.Column<int>(type: "int", nullable: false),
                    StudSize = table.Column<int>(type: "int", nullable: false),
                    StudMaterial = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FromFlange = table.Column<int>(type: "int", nullable: false),
                    CanStudsBeCut = table.Column<bool>(type: "bit", nullable: false),
                    Point1FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point2FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point3FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point4FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point5FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point6FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point7FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point8FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point9FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point10FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point11FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point12FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point1FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point2FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point3FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point4FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point5FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point6FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point7FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point8FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point9FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point10FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point11FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point12FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point1Amount = table.Column<int>(type: "int", nullable: false),
                    Point2Amount = table.Column<int>(type: "int", nullable: false),
                    Point3Amount = table.Column<int>(type: "int", nullable: false),
                    Point4Amount = table.Column<int>(type: "int", nullable: false),
                    Point5Amount = table.Column<int>(type: "int", nullable: false),
                    Point6Amount = table.Column<int>(type: "int", nullable: false),
                    Point7Amount = table.Column<int>(type: "int", nullable: false),
                    Point8Amount = table.Column<int>(type: "int", nullable: false),
                    Point9Amount = table.Column<int>(type: "int", nullable: false),
                    Point10Amount = table.Column<int>(type: "int", nullable: false),
                    Point11Amount = table.Column<int>(type: "int", nullable: false),
                    Point12Amount = table.Column<int>(type: "int", nullable: false),
                    MinNutToNut = table.Column<int>(type: "int", nullable: false),
                    NutPointToPoint = table.Column<int>(type: "int", nullable: false),
                    NutFlatToFlat = table.Column<int>(type: "int", nullable: false),
                    EarLocations = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Flange1Width = table.Column<int>(type: "int", nullable: false),
                    Flange2Width = table.Column<int>(type: "int", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS110s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS110s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS111s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SlipOn = table.Column<bool>(type: "bit", nullable: false),
                    LapJoint = table.Column<bool>(type: "bit", nullable: false),
                    ThreadedAndSocket = table.Column<bool>(type: "bit", nullable: false),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    K1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    WH1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    K2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    WH2 = table.Column<int>(type: "int", nullable: false),
                    F1a = table.Column<int>(type: "int", nullable: false),
                    F1b = table.Column<int>(type: "int", nullable: false),
                    F2a = table.Column<int>(type: "int", nullable: false),
                    F2b = table.Column<int>(type: "int", nullable: false),
                    G1a = table.Column<int>(type: "int", nullable: false),
                    G1b = table.Column<int>(type: "int", nullable: false),
                    G2a = table.Column<int>(type: "int", nullable: false),
                    G2b = table.Column<int>(type: "int", nullable: false),
                    L13 = table.Column<int>(type: "int", nullable: false),
                    L16 = table.Column<int>(type: "int", nullable: false),
                    L19 = table.Column<int>(type: "int", nullable: false),
                    L112 = table.Column<int>(type: "int", nullable: false),
                    L23 = table.Column<int>(type: "int", nullable: false),
                    L26 = table.Column<int>(type: "int", nullable: false),
                    L29 = table.Column<int>(type: "int", nullable: false),
                    L212 = table.Column<int>(type: "int", nullable: false),
                    Point1Gap = table.Column<int>(type: "int", nullable: false),
                    Point2Gap = table.Column<int>(type: "int", nullable: false),
                    Point3Gap = table.Column<int>(type: "int", nullable: false),
                    Point4Gap = table.Column<int>(type: "int", nullable: false),
                    Point5Gap = table.Column<int>(type: "int", nullable: false),
                    Point6Gap = table.Column<int>(type: "int", nullable: false),
                    Point7Gap = table.Column<int>(type: "int", nullable: false),
                    Point8Gap = table.Column<int>(type: "int", nullable: false),
                    Point9Gap = table.Column<int>(type: "int", nullable: false),
                    Point10Gap = table.Column<int>(type: "int", nullable: false),
                    Point11Gap = table.Column<int>(type: "int", nullable: false),
                    Point12Gap = table.Column<int>(type: "int", nullable: false),
                    Point1Overall = table.Column<int>(type: "int", nullable: false),
                    Point2Overall = table.Column<int>(type: "int", nullable: false),
                    Point3Overall = table.Column<int>(type: "int", nullable: false),
                    Point4Overall = table.Column<int>(type: "int", nullable: false),
                    Point5Overall = table.Column<int>(type: "int", nullable: false),
                    Point6Overall = table.Column<int>(type: "int", nullable: false),
                    Point7Overall = table.Column<int>(type: "int", nullable: false),
                    Point8Overall = table.Column<int>(type: "int", nullable: false),
                    Point9Overall = table.Column<int>(type: "int", nullable: false),
                    Point10Overall = table.Column<int>(type: "int", nullable: false),
                    Point11Overall = table.Column<int>(type: "int", nullable: false),
                    Point12Overall = table.Column<int>(type: "int", nullable: false),
                    OdFlangeOne12to6 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeOne1to7 = table.Column<DateTime>(type: "datetime2", nullable: false),
                    OdFlangeOne2to8 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeOne3to9 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeOne4to10 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeOne5to11 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeTwo12to6 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeTwo1to7 = table.Column<DateTime>(type: "datetime2", nullable: false),
                    OdFlangeTwo2to8 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeTwo3to9 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeTwo4to10 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OdFlangeTwo5to11 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StudDepthPoint1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StudDepthPoint2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StudDepthPoint3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StudDepthPoint1Flange1 = table.Column<int>(type: "int", nullable: false),
                    StudDepthPoint2Flange1 = table.Column<int>(type: "int", nullable: false),
                    StudDepthPoint3Flange1 = table.Column<int>(type: "int", nullable: false),
                    StudDepthPoint1Flange2 = table.Column<int>(type: "int", nullable: false),
                    StudDepthPoint2Flange2 = table.Column<int>(type: "int", nullable: false),
                    StudDepthPoint3Flange2 = table.Column<int>(type: "int", nullable: false),
                    DepthToGasket = table.Column<int>(type: "int", nullable: false),
                    NoOfStuds = table.Column<int>(type: "int", nullable: false),
                    StudSize = table.Column<int>(type: "int", nullable: false),
                    StudMaterial = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FromFlange = table.Column<int>(type: "int", nullable: false),
                    CanStudsBeCut = table.Column<bool>(type: "bit", nullable: false),
                    Point1FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point2FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point3FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point4FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point5FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point6FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point7FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point8FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point9FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point10FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point11FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point12FlangeOver = table.Column<int>(type: "int", nullable: false),
                    Point1FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point2FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point3FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point4FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point5FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point6FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point7FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point8FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point9FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point10FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point11FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point12FlangeUnder = table.Column<int>(type: "int", nullable: false),
                    Point1Amount = table.Column<int>(type: "int", nullable: false),
                    Point2Amount = table.Column<int>(type: "int", nullable: false),
                    Point3Amount = table.Column<int>(type: "int", nullable: false),
                    Point4Amount = table.Column<int>(type: "int", nullable: false),
                    Point5Amount = table.Column<int>(type: "int", nullable: false),
                    Point6Amount = table.Column<int>(type: "int", nullable: false),
                    Point7Amount = table.Column<int>(type: "int", nullable: false),
                    Point8Amount = table.Column<int>(type: "int", nullable: false),
                    Point9Amount = table.Column<int>(type: "int", nullable: false),
                    Point10Amount = table.Column<int>(type: "int", nullable: false),
                    Point11Amount = table.Column<int>(type: "int", nullable: false),
                    Point12Amount = table.Column<int>(type: "int", nullable: false),
                    MinNutToNut = table.Column<int>(type: "int", nullable: false),
                    NutPointToPoint = table.Column<int>(type: "int", nullable: false),
                    NutFlatToFlat = table.Column<int>(type: "int", nullable: false),
                    EarLocations = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Flange1Width = table.Column<int>(type: "int", nullable: false),
                    Flange2Width = table.Column<int>(type: "int", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS111s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS111s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS113s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    L1 = table.Column<int>(type: "int", nullable: false),
                    L2 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    W3 = table.Column<int>(type: "int", nullable: false),
                    M = table.Column<int>(type: "int", nullable: false),
                    N = table.Column<int>(type: "int", nullable: false),
                    P = table.Column<int>(type: "int", nullable: false),
                    Q1 = table.Column<int>(type: "int", nullable: false),
                    Q2 = table.Column<int>(type: "int", nullable: false),
                    R1 = table.Column<int>(type: "int", nullable: false),
                    R2 = table.Column<int>(type: "int", nullable: false),
                    S1 = table.Column<int>(type: "int", nullable: false),
                    S2 = table.Column<int>(type: "int", nullable: false),
                    CanStudsBeCut = table.Column<bool>(type: "bit", nullable: false),
                    LocationOfLeak = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS113s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS113s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS120s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    G = table.Column<int>(type: "int", nullable: false),
                    J = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    H = table.Column<int>(type: "int", nullable: false),
                    PttoPt = table.Column<int>(type: "int", nullable: false),
                    FtoF = table.Column<int>(type: "int", nullable: false),
                    Coupling = table.Column<bool>(type: "bit", nullable: false),
                    Union = table.Column<bool>(type: "bit", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Threaded = table.Column<bool>(type: "bit", nullable: false),
                    Welded = table.Column<bool>(type: "bit", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS120s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS120s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS124s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    G1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    G2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    K2 = table.Column<int>(type: "int", nullable: false),
                    L = table.Column<int>(type: "int", nullable: false),
                    Round = table.Column<bool>(type: "bit", nullable: false),
                    Square = table.Column<bool>(type: "bit", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Threaded = table.Column<bool>(type: "bit", nullable: false),
                    Welded = table.Column<bool>(type: "bit", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS124s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS124s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS128s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    WH1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    WH2 = table.Column<int>(type: "int", nullable: false),
                    E = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    G1 = table.Column<int>(type: "int", nullable: false),
                    G2 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS128s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS128s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS129s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    G1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    G2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    K1 = table.Column<int>(type: "int", nullable: false),
                    K2 = table.Column<int>(type: "int", nullable: false),
                    L = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Screwed = table.Column<bool>(type: "bit", nullable: false),
                    Welded = table.Column<bool>(type: "bit", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS129s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS129s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS133s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    G1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    G2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    K1 = table.Column<int>(type: "int", nullable: false),
                    K2 = table.Column<int>(type: "int", nullable: false),
                    L = table.Column<int>(type: "int", nullable: false),
                    M = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS133s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS133s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS134s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    G1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    G2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    A3 = table.Column<int>(type: "int", nullable: false),
                    B3 = table.Column<int>(type: "int", nullable: false),
                    C3 = table.Column<int>(type: "int", nullable: false),
                    D3 = table.Column<int>(type: "int", nullable: false),
                    F3 = table.Column<int>(type: "int", nullable: false),
                    G3 = table.Column<int>(type: "int", nullable: false),
                    W3 = table.Column<int>(type: "int", nullable: false),
                    H = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    K1 = table.Column<int>(type: "int", nullable: false),
                    K2 = table.Column<int>(type: "int", nullable: false),
                    L1 = table.Column<int>(type: "int", nullable: false),
                    L2 = table.Column<int>(type: "int", nullable: false),
                    M = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Screwed = table.Column<bool>(type: "bit", nullable: false),
                    Welded = table.Column<bool>(type: "bit", nullable: false),
                    C1D1 = table.Column<bool>(type: "bit", nullable: false),
                    C2D2 = table.Column<bool>(type: "bit", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS134s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS134s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS136s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    A3 = table.Column<int>(type: "int", nullable: false),
                    B3 = table.Column<int>(type: "int", nullable: false),
                    C3 = table.Column<int>(type: "int", nullable: false),
                    D3 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    E3 = table.Column<int>(type: "int", nullable: false),
                    W3 = table.Column<int>(type: "int", nullable: false),
                    W4 = table.Column<int>(type: "int", nullable: false),
                    F = table.Column<int>(type: "int", nullable: false),
                    G1 = table.Column<int>(type: "int", nullable: false),
                    G2 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    K = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Screwed = table.Column<bool>(type: "bit", nullable: false),
                    Welded = table.Column<bool>(type: "bit", nullable: false),
                    ScrewedSocket = table.Column<bool>(type: "bit", nullable: false),
                    SmlsButtWeldFitting = table.Column<bool>(type: "bit", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS136s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS136s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS137s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    WH1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    OepLength = table.Column<int>(type: "int", nullable: false),
                    IepLength = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS137s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS137s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS138s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    WH1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    WH2 = table.Column<int>(type: "int", nullable: false),
                    E = table.Column<int>(type: "int", nullable: false),
                    F = table.Column<int>(type: "int", nullable: false),
                    G = table.Column<int>(type: "int", nullable: false),
                    L3 = table.Column<int>(type: "int", nullable: false),
                    L6 = table.Column<int>(type: "int", nullable: false),
                    L9 = table.Column<int>(type: "int", nullable: false),
                    L12 = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS138s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS138s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS139s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    WH1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    WH2 = table.Column<int>(type: "int", nullable: false),
                    E = table.Column<int>(type: "int", nullable: false),
                    F = table.Column<int>(type: "int", nullable: false),
                    G = table.Column<int>(type: "int", nullable: false),
                    L3 = table.Column<int>(type: "int", nullable: false),
                    L6 = table.Column<int>(type: "int", nullable: false),
                    L9 = table.Column<int>(type: "int", nullable: false),
                    L12 = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS139s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS139s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS140s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    WH1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    WH2 = table.Column<int>(type: "int", nullable: false),
                    A3 = table.Column<int>(type: "int", nullable: false),
                    B3 = table.Column<int>(type: "int", nullable: false),
                    C3 = table.Column<int>(type: "int", nullable: false),
                    D3 = table.Column<int>(type: "int", nullable: false),
                    E3 = table.Column<int>(type: "int", nullable: false),
                    W3 = table.Column<int>(type: "int", nullable: false),
                    WH3 = table.Column<int>(type: "int", nullable: false),
                    W4 = table.Column<int>(type: "int", nullable: false),
                    F = table.Column<int>(type: "int", nullable: false),
                    W5 = table.Column<int>(type: "int", nullable: false),
                    G = table.Column<int>(type: "int", nullable: false),
                    H = table.Column<int>(type: "int", nullable: false),
                    J = table.Column<int>(type: "int", nullable: false),
                    K = table.Column<int>(type: "int", nullable: false),
                    L1 = table.Column<int>(type: "int", nullable: false),
                    L2 = table.Column<int>(type: "int", nullable: false),
                    M1 = table.Column<int>(type: "int", nullable: false),
                    M2 = table.Column<int>(type: "int", nullable: false),
                    N1 = table.Column<int>(type: "int", nullable: false),
                    N2 = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS140s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS140s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS141s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    G2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    A3 = table.Column<int>(type: "int", nullable: false),
                    B3 = table.Column<int>(type: "int", nullable: false),
                    C3 = table.Column<int>(type: "int", nullable: false),
                    D3 = table.Column<int>(type: "int", nullable: false),
                    E3 = table.Column<int>(type: "int", nullable: false),
                    F3 = table.Column<int>(type: "int", nullable: false),
                    G3 = table.Column<int>(type: "int", nullable: false),
                    W3 = table.Column<int>(type: "int", nullable: false),
                    H = table.Column<int>(type: "int", nullable: false),
                    J = table.Column<int>(type: "int", nullable: false),
                    K = table.Column<int>(type: "int", nullable: false),
                    L = table.Column<int>(type: "int", nullable: false),
                    M1 = table.Column<int>(type: "int", nullable: false),
                    M2 = table.Column<int>(type: "int", nullable: false),
                    N1 = table.Column<int>(type: "int", nullable: false),
                    N2 = table.Column<int>(type: "int", nullable: false),
                    P1 = table.Column<int>(type: "int", nullable: false),
                    P2 = table.Column<int>(type: "int", nullable: false),
                    Q = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Screwed = table.Column<bool>(type: "bit", nullable: false),
                    Welded = table.Column<bool>(type: "bit", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS141s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS141s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS150s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A = table.Column<int>(type: "int", nullable: false),
                    B3 = table.Column<int>(type: "int", nullable: false),
                    B6 = table.Column<int>(type: "int", nullable: false),
                    B9 = table.Column<int>(type: "int", nullable: false),
                    B12 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    C3 = table.Column<int>(type: "int", nullable: false),
                    C4 = table.Column<int>(type: "int", nullable: false),
                    C5 = table.Column<int>(type: "int", nullable: false),
                    C6 = table.Column<int>(type: "int", nullable: false),
                    C7 = table.Column<int>(type: "int", nullable: false),
                    C8 = table.Column<int>(type: "int", nullable: false),
                    C9 = table.Column<int>(type: "int", nullable: false),
                    C10 = table.Column<int>(type: "int", nullable: false),
                    C11 = table.Column<int>(type: "int", nullable: false),
                    C12 = table.Column<int>(type: "int", nullable: false),
                    VesselDiameter = table.Column<int>(type: "int", nullable: false),
                    VesselCirc = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS150s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS150s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS151s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A = table.Column<int>(type: "int", nullable: false),
                    B3 = table.Column<int>(type: "int", nullable: false),
                    B6 = table.Column<int>(type: "int", nullable: false),
                    B9 = table.Column<int>(type: "int", nullable: false),
                    B12 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    C3 = table.Column<int>(type: "int", nullable: false),
                    C4 = table.Column<int>(type: "int", nullable: false),
                    C5 = table.Column<int>(type: "int", nullable: false),
                    C6 = table.Column<int>(type: "int", nullable: false),
                    C7 = table.Column<int>(type: "int", nullable: false),
                    C8 = table.Column<int>(type: "int", nullable: false),
                    C9 = table.Column<int>(type: "int", nullable: false),
                    C10 = table.Column<int>(type: "int", nullable: false),
                    C11 = table.Column<int>(type: "int", nullable: false),
                    C12 = table.Column<int>(type: "int", nullable: false),
                    VesselDiameter = table.Column<int>(type: "int", nullable: false),
                    VesselCirc = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS151s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS151s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS194s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    A = table.Column<int>(type: "int", nullable: false),
                    B = table.Column<int>(type: "int", nullable: false),
                    C = table.Column<int>(type: "int", nullable: false),
                    D = table.Column<int>(type: "int", nullable: false),
                    NoOfStuds = table.Column<int>(type: "int", nullable: false),
                    SizeOfStuds = table.Column<int>(type: "int", nullable: false),
                    InjPortSize = table.Column<int>(type: "int", nullable: false),
                    WallThicknessBtoDby2 = table.Column<int>(type: "int", nullable: false),
                    NoOfInjectionPorts = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS194s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS194s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS195s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D = table.Column<int>(type: "int", nullable: false),
                    E = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    G1 = table.Column<int>(type: "int", nullable: false),
                    G2 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    K = table.Column<int>(type: "int", nullable: false),
                    L = table.Column<int>(type: "int", nullable: false),
                    M = table.Column<int>(type: "int", nullable: false),
                    N = table.Column<int>(type: "int", nullable: false),
                    P = table.Column<int>(type: "int", nullable: false),
                    Q = table.Column<int>(type: "int", nullable: false),
                    R = table.Column<int>(type: "int", nullable: false),
                    S = table.Column<int>(type: "int", nullable: false),
                    T = table.Column<int>(type: "int", nullable: false),
                    BoltDia = table.Column<int>(type: "int", nullable: false),
                    ThreadSize = table.Column<int>(type: "int", nullable: false),
                    ThrdSize1 = table.Column<int>(type: "int", nullable: false),
                    HoleDia = table.Column<int>(type: "int", nullable: false),
                    StudDia = table.Column<int>(type: "int", nullable: false),
                    ThreadDepth = table.Column<int>(type: "int", nullable: false),
                    SquareWidth = table.Column<int>(type: "int", nullable: false),
                    SquareDepth = table.Column<int>(type: "int", nullable: false),
                    HeadDia = table.Column<int>(type: "int", nullable: false),
                    FaceWidth = table.Column<int>(type: "int", nullable: false),
                    EyeBolt = table.Column<bool>(type: "bit", nullable: false),
                    StudThrdVlvBody = table.Column<bool>(type: "bit", nullable: false),
                    StudFlgVlvBody = table.Column<bool>(type: "bit", nullable: false),
                    CarriageOrStepBolt = table.Column<bool>(type: "bit", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS195s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS195s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS196s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A = table.Column<int>(type: "int", nullable: false),
                    B = table.Column<int>(type: "int", nullable: false),
                    C = table.Column<int>(type: "int", nullable: false),
                    D = table.Column<int>(type: "int", nullable: false),
                    E = table.Column<int>(type: "int", nullable: false),
                    F = table.Column<int>(type: "int", nullable: false),
                    G = table.Column<int>(type: "int", nullable: false),
                    H = table.Column<int>(type: "int", nullable: false),
                    J = table.Column<int>(type: "int", nullable: false),
                    K = table.Column<int>(type: "int", nullable: false),
                    RisingStem = table.Column<bool>(type: "bit", nullable: false),
                    Rising = table.Column<bool>(type: "bit", nullable: false),
                    Handle = table.Column<bool>(type: "bit", nullable: false),
                    Slotted = table.Column<bool>(type: "bit", nullable: false),
                    OvalFlats = table.Column<bool>(type: "bit", nullable: false),
                    OneFlat = table.Column<bool>(type: "bit", nullable: false),
                    Tapered = table.Column<bool>(type: "bit", nullable: false),
                    SqFlats = table.Column<bool>(type: "bit", nullable: false),
                    SqTaper = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS196s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS196s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS197s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Threaded = table.Column<bool>(type: "bit", nullable: false),
                    Smooth = table.Column<bool>(type: "bit", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D = table.Column<int>(type: "int", nullable: false),
                    E = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    G1 = table.Column<int>(type: "int", nullable: false),
                    G2 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    K = table.Column<int>(type: "int", nullable: false),
                    L = table.Column<int>(type: "int", nullable: false),
                    M = table.Column<int>(type: "int", nullable: false),
                    N = table.Column<int>(type: "int", nullable: false),
                    P = table.Column<int>(type: "int", nullable: false),
                    Q = table.Column<int>(type: "int", nullable: false),
                    R = table.Column<int>(type: "int", nullable: false),
                    S = table.Column<int>(type: "int", nullable: false),
                    NoOfStuds = table.Column<int>(type: "int", nullable: false),
                    SizeOfStuds = table.Column<int>(type: "int", nullable: false),
                    ThreadOD = table.Column<int>(type: "int", nullable: false),
                    NoOfThreadsPerInch = table.Column<int>(type: "int", nullable: false),
                    Lead = table.Column<int>(type: "int", nullable: false),
                    OtherValveStemData = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS197s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS197s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS200s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    He = table.Column<int>(type: "int", nullable: false),
                    KbE = table.Column<int>(type: "int", nullable: false),
                    LbF = table.Column<int>(type: "int", nullable: false),
                    KcE = table.Column<int>(type: "int", nullable: false),
                    LcF = table.Column<int>(type: "int", nullable: false),
                    M = table.Column<int>(type: "int", nullable: false),
                    N = table.Column<int>(type: "int", nullable: false),
                    P = table.Column<int>(type: "int", nullable: false),
                    Q = table.Column<int>(type: "int", nullable: false),
                    R = table.Column<int>(type: "int", nullable: false),
                    T = table.Column<int>(type: "int", nullable: false),
                    U = table.Column<int>(type: "int", nullable: false),
                    V = table.Column<int>(type: "int", nullable: false),
                    Y = table.Column<int>(type: "int", nullable: false),
                    Aa = table.Column<int>(type: "int", nullable: false),
                    Bb = table.Column<int>(type: "int", nullable: false),
                    Cc = table.Column<int>(type: "int", nullable: false),
                    Dd = table.Column<int>(type: "int", nullable: false),
                    Ee = table.Column<int>(type: "int", nullable: false),
                    Ff = table.Column<int>(type: "int", nullable: false),
                    Gg = table.Column<int>(type: "int", nullable: false),
                    X1At3 = table.Column<int>(type: "int", nullable: false),
                    X1At6 = table.Column<int>(type: "int", nullable: false),
                    X1At9 = table.Column<int>(type: "int", nullable: false),
                    X1At12 = table.Column<int>(type: "int", nullable: false),
                    X2At3 = table.Column<int>(type: "int", nullable: false),
                    X2At6 = table.Column<int>(type: "int", nullable: false),
                    X2At9 = table.Column<int>(type: "int", nullable: false),
                    X2At12 = table.Column<int>(type: "int", nullable: false),
                    RemoveHandle = table.Column<bool>(type: "bit", nullable: false),
                    CutStem = table.Column<bool>(type: "bit", nullable: false),
                    StemClockPt = table.Column<int>(type: "int", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<int>(type: "int", nullable: false),
                    CircularBonnet = table.Column<bool>(type: "bit", nullable: false),
                    SquareBonnet = table.Column<bool>(type: "bit", nullable: false),
                    OvalBonnet = table.Column<bool>(type: "bit", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    XX = table.Column<bool>(type: "bit", nullable: false),
                    YY = table.Column<bool>(type: "bit", nullable: false),
                    XY = table.Column<bool>(type: "bit", nullable: false),
                    YX = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS200s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS200s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS201s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    He = table.Column<int>(type: "int", nullable: false),
                    KbE = table.Column<int>(type: "int", nullable: false),
                    LbF = table.Column<int>(type: "int", nullable: false),
                    KcE = table.Column<int>(type: "int", nullable: false),
                    LcF = table.Column<int>(type: "int", nullable: false),
                    M = table.Column<int>(type: "int", nullable: false),
                    N = table.Column<int>(type: "int", nullable: false),
                    P = table.Column<int>(type: "int", nullable: false),
                    Q = table.Column<int>(type: "int", nullable: false),
                    R = table.Column<int>(type: "int", nullable: false),
                    T = table.Column<int>(type: "int", nullable: false),
                    U = table.Column<int>(type: "int", nullable: false),
                    V = table.Column<int>(type: "int", nullable: false),
                    X = table.Column<int>(type: "int", nullable: false),
                    Y = table.Column<int>(type: "int", nullable: false),
                    Z = table.Column<int>(type: "int", nullable: false),
                    Aa = table.Column<int>(type: "int", nullable: false),
                    Bb = table.Column<int>(type: "int", nullable: false),
                    Cc = table.Column<int>(type: "int", nullable: false),
                    Xa3 = table.Column<int>(type: "int", nullable: false),
                    Xa6 = table.Column<int>(type: "int", nullable: false),
                    Xa9 = table.Column<int>(type: "int", nullable: false),
                    Xa12 = table.Column<int>(type: "int", nullable: false),
                    Xd3 = table.Column<int>(type: "int", nullable: false),
                    Xd6 = table.Column<int>(type: "int", nullable: false),
                    Xd9 = table.Column<int>(type: "int", nullable: false),
                    Xd12 = table.Column<int>(type: "int", nullable: false),
                    RemoveHandle = table.Column<bool>(type: "bit", nullable: false),
                    CutStem = table.Column<bool>(type: "bit", nullable: false),
                    StemClockPt = table.Column<int>(type: "int", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<int>(type: "int", nullable: false),
                    CircularBonnet = table.Column<bool>(type: "bit", nullable: false),
                    SquareBonnet = table.Column<bool>(type: "bit", nullable: false),
                    OvalBonnet = table.Column<bool>(type: "bit", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    XX = table.Column<bool>(type: "bit", nullable: false),
                    YY = table.Column<bool>(type: "bit", nullable: false),
                    XY = table.Column<bool>(type: "bit", nullable: false),
                    YX = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS201s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS201s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS205s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    L = table.Column<int>(type: "int", nullable: false),
                    M = table.Column<int>(type: "int", nullable: false),
                    N = table.Column<int>(type: "int", nullable: false),
                    P = table.Column<int>(type: "int", nullable: false),
                    Q = table.Column<int>(type: "int", nullable: false),
                    R = table.Column<int>(type: "int", nullable: false),
                    S = table.Column<int>(type: "int", nullable: false),
                    T = table.Column<int>(type: "int", nullable: false),
                    U = table.Column<int>(type: "int", nullable: false),
                    V = table.Column<int>(type: "int", nullable: false),
                    X = table.Column<int>(type: "int", nullable: false),
                    Y = table.Column<int>(type: "int", nullable: false),
                    Z = table.Column<int>(type: "int", nullable: false),
                    Aa = table.Column<int>(type: "int", nullable: false),
                    Bb = table.Column<int>(type: "int", nullable: false),
                    Cc = table.Column<int>(type: "int", nullable: false),
                    X13 = table.Column<int>(type: "int", nullable: false),
                    X16 = table.Column<int>(type: "int", nullable: false),
                    X19 = table.Column<int>(type: "int", nullable: false),
                    X112 = table.Column<int>(type: "int", nullable: false),
                    X23 = table.Column<int>(type: "int", nullable: false),
                    X26 = table.Column<int>(type: "int", nullable: false),
                    X29 = table.Column<int>(type: "int", nullable: false),
                    X212 = table.Column<int>(type: "int", nullable: false),
                    AlongTheLine = table.Column<bool>(type: "bit", nullable: false),
                    AgainstTheLine = table.Column<bool>(type: "bit", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS205s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS205s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS206s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    Ha = table.Column<int>(type: "int", nullable: false),
                    K1A = table.Column<int>(type: "int", nullable: false),
                    K2A = table.Column<int>(type: "int", nullable: false),
                    L1B = table.Column<int>(type: "int", nullable: false),
                    L2B = table.Column<int>(type: "int", nullable: false),
                    Ma = table.Column<int>(type: "int", nullable: false),
                    Mb = table.Column<int>(type: "int", nullable: false),
                    N = table.Column<int>(type: "int", nullable: false),
                    P = table.Column<int>(type: "int", nullable: false),
                    T = table.Column<int>(type: "int", nullable: false),
                    U = table.Column<int>(type: "int", nullable: false),
                    V = table.Column<int>(type: "int", nullable: false),
                    X = table.Column<int>(type: "int", nullable: false),
                    Y = table.Column<int>(type: "int", nullable: false),
                    Aa = table.Column<int>(type: "int", nullable: false),
                    Bb = table.Column<int>(type: "int", nullable: false),
                    Cc = table.Column<int>(type: "int", nullable: false),
                    RemoveHandle = table.Column<bool>(type: "bit", nullable: false),
                    CutStem = table.Column<bool>(type: "bit", nullable: false),
                    StemClockPt = table.Column<int>(type: "int", nullable: false),
                    Screwed = table.Column<bool>(type: "bit", nullable: false),
                    Welded = table.Column<bool>(type: "bit", nullable: false),
                    Hex12at6 = table.Column<bool>(type: "bit", nullable: false),
                    Hex3at9 = table.Column<bool>(type: "bit", nullable: false),
                    OtherHexOrientation = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<int>(type: "int", nullable: false),
                    CircularBonnet = table.Column<bool>(type: "bit", nullable: false),
                    SquareBonnet = table.Column<bool>(type: "bit", nullable: false),
                    OvalBonnet = table.Column<bool>(type: "bit", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    XX = table.Column<bool>(type: "bit", nullable: false),
                    YY = table.Column<bool>(type: "bit", nullable: false),
                    XY = table.Column<bool>(type: "bit", nullable: false),
                    YX = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS206s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS206s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS209s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    G1 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    K1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    G2 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    K2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    E3 = table.Column<int>(type: "int", nullable: false),
                    F3 = table.Column<int>(type: "int", nullable: false),
                    L1 = table.Column<int>(type: "int", nullable: false),
                    L2 = table.Column<int>(type: "int", nullable: false),
                    M = table.Column<int>(type: "int", nullable: false),
                    N = table.Column<int>(type: "int", nullable: false),
                    P = table.Column<int>(type: "int", nullable: false),
                    Q = table.Column<int>(type: "int", nullable: false),
                    R = table.Column<int>(type: "int", nullable: false),
                    S = table.Column<int>(type: "int", nullable: false),
                    T = table.Column<int>(type: "int", nullable: false),
                    U = table.Column<int>(type: "int", nullable: false),
                    V = table.Column<int>(type: "int", nullable: false),
                    X = table.Column<int>(type: "int", nullable: false),
                    Y = table.Column<int>(type: "int", nullable: false),
                    Z = table.Column<int>(type: "int", nullable: false),
                    Aa = table.Column<int>(type: "int", nullable: false),
                    Bb = table.Column<int>(type: "int", nullable: false),
                    RemoveHandle = table.Column<bool>(type: "bit", nullable: false),
                    CutStem = table.Column<bool>(type: "bit", nullable: false),
                    StemClockPt = table.Column<int>(type: "int", nullable: false),
                    Screwed = table.Column<bool>(type: "bit", nullable: false),
                    Welded = table.Column<bool>(type: "bit", nullable: false),
                    Hex12at6 = table.Column<bool>(type: "bit", nullable: false),
                    Hex3at9 = table.Column<bool>(type: "bit", nullable: false),
                    OtherHexOrientation = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS209s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS209s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS210s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    G1 = table.Column<int>(type: "int", nullable: false),
                    H1 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    K1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    G2 = table.Column<int>(type: "int", nullable: false),
                    H2 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    K2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    W3 = table.Column<int>(type: "int", nullable: false),
                    W4 = table.Column<int>(type: "int", nullable: false),
                    M = table.Column<int>(type: "int", nullable: false),
                    N = table.Column<int>(type: "int", nullable: false),
                    P = table.Column<int>(type: "int", nullable: false),
                    R = table.Column<int>(type: "int", nullable: false),
                    T = table.Column<int>(type: "int", nullable: false),
                    U = table.Column<int>(type: "int", nullable: false),
                    V = table.Column<int>(type: "int", nullable: false),
                    Х = table.Column<int>(type: "int", nullable: false),
                    Y = table.Column<int>(type: "int", nullable: false),
                    Y1 = table.Column<int>(type: "int", nullable: false),
                    Z = table.Column<int>(type: "int", nullable: false),
                    RemoveHandle = table.Column<bool>(type: "bit", nullable: false),
                    CutStem = table.Column<bool>(type: "bit", nullable: false),
                    StemClockPt = table.Column<int>(type: "int", nullable: false),
                    Screwed = table.Column<bool>(type: "bit", nullable: false),
                    Welded = table.Column<bool>(type: "bit", nullable: false),
                    Hex12at6 = table.Column<bool>(type: "bit", nullable: false),
                    Hex3at9 = table.Column<bool>(type: "bit", nullable: false),
                    OtherHexOrientation = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS210s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS210s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS230s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A1 = table.Column<int>(type: "int", nullable: false),
                    B1 = table.Column<int>(type: "int", nullable: false),
                    C1 = table.Column<int>(type: "int", nullable: false),
                    D1 = table.Column<int>(type: "int", nullable: false),
                    E1 = table.Column<int>(type: "int", nullable: false),
                    F1 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    K1A = table.Column<int>(type: "int", nullable: false),
                    K2A = table.Column<int>(type: "int", nullable: false),
                    A2 = table.Column<int>(type: "int", nullable: false),
                    B2 = table.Column<int>(type: "int", nullable: false),
                    C2 = table.Column<int>(type: "int", nullable: false),
                    D2 = table.Column<int>(type: "int", nullable: false),
                    E2 = table.Column<int>(type: "int", nullable: false),
                    F2 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    L1A = table.Column<int>(type: "int", nullable: false),
                    L2A = table.Column<int>(type: "int", nullable: false),
                    Ha = table.Column<int>(type: "int", nullable: false),
                    Ja = table.Column<int>(type: "int", nullable: false),
                    Ma = table.Column<int>(type: "int", nullable: false),
                    Mb = table.Column<int>(type: "int", nullable: false),
                    N = table.Column<int>(type: "int", nullable: false),
                    P = table.Column<int>(type: "int", nullable: false),
                    Q = table.Column<int>(type: "int", nullable: false),
                    R = table.Column<int>(type: "int", nullable: false),
                    S = table.Column<int>(type: "int", nullable: false),
                    T = table.Column<int>(type: "int", nullable: false),
                    U = table.Column<int>(type: "int", nullable: false),
                    V1 = table.Column<int>(type: "int", nullable: false),
                    V2 = table.Column<int>(type: "int", nullable: false),
                    X1 = table.Column<int>(type: "int", nullable: false),
                    X2 = table.Column<int>(type: "int", nullable: false),
                    T1 = table.Column<int>(type: "int", nullable: false),
                    Y2 = table.Column<int>(type: "int", nullable: false),
                    Z = table.Column<int>(type: "int", nullable: false),
                    Aa = table.Column<int>(type: "int", nullable: false),
                    Yk = table.Column<int>(type: "int", nullable: false),
                    Screwed = table.Column<bool>(type: "bit", nullable: false),
                    Welded = table.Column<bool>(type: "bit", nullable: false),
                    Hex12at6 = table.Column<bool>(type: "bit", nullable: false),
                    Hex3at9 = table.Column<bool>(type: "bit", nullable: false),
                    OtherHexOrientation = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RemoveHandle = table.Column<bool>(type: "bit", nullable: false),
                    CutStem = table.Column<bool>(type: "bit", nullable: false),
                    StemClockPt = table.Column<int>(type: "int", nullable: false),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<int>(type: "int", nullable: false),
                    CircularBonnet = table.Column<bool>(type: "bit", nullable: false),
                    SquareBonnet = table.Column<bool>(type: "bit", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    XX = table.Column<bool>(type: "bit", nullable: false),
                    YY = table.Column<bool>(type: "bit", nullable: false),
                    XY = table.Column<bool>(type: "bit", nullable: false),
                    YX = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS230s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS230s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS300s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A3 = table.Column<int>(type: "int", nullable: false),
                    B3 = table.Column<int>(type: "int", nullable: false),
                    C3 = table.Column<int>(type: "int", nullable: false),
                    D3 = table.Column<int>(type: "int", nullable: false),
                    J1 = table.Column<int>(type: "int", nullable: false),
                    J2 = table.Column<int>(type: "int", nullable: false),
                    K1 = table.Column<int>(type: "int", nullable: false),
                    K2 = table.Column<int>(type: "int", nullable: false),
                    L1 = table.Column<int>(type: "int", nullable: false),
                    L2 = table.Column<int>(type: "int", nullable: false),
                    M = table.Column<int>(type: "int", nullable: false),
                    W1 = table.Column<int>(type: "int", nullable: false),
                    W2 = table.Column<int>(type: "int", nullable: false),
                    W3 = table.Column<int>(type: "int", nullable: false),
                    W4 = table.Column<int>(type: "int", nullable: false),
                    LocationOfBlow = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Obstructions = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS300s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS300s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DS901s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Flg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    A = table.Column<int>(type: "int", nullable: false),
                    B = table.Column<int>(type: "int", nullable: false),
                    C = table.Column<int>(type: "int", nullable: false),
                    D = table.Column<int>(type: "int", nullable: false),
                    E = table.Column<int>(type: "int", nullable: false),
                    F = table.Column<int>(type: "int", nullable: false),
                    G = table.Column<int>(type: "int", nullable: false),
                    H = table.Column<int>(type: "int", nullable: false),
                    J = table.Column<int>(type: "int", nullable: false),
                    K = table.Column<int>(type: "int", nullable: false),
                    L = table.Column<int>(type: "int", nullable: false),
                    M = table.Column<int>(type: "int", nullable: false),
                    N = table.Column<int>(type: "int", nullable: false),
                    P = table.Column<int>(type: "int", nullable: false),
                    Q = table.Column<int>(type: "int", nullable: false),
                    R = table.Column<int>(type: "int", nullable: false),
                    S = table.Column<int>(type: "int", nullable: false),
                    T = table.Column<int>(type: "int", nullable: false),
                    U = table.Column<int>(type: "int", nullable: false),
                    V = table.Column<int>(type: "int", nullable: false),
                    LocationOfLeakingTubeColumnNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LocationOfLeakingTubeRowNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlatToFlat = table.Column<int>(type: "int", nullable: false),
                    PointToPoint = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DS901s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DS901s_DimensionalSheets_Id",
                        column: x => x.Id,
                        principalTable: "DimensionalSheets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "DataSheetPackages",
                columns: new[] { "Id", "ClientName", "Date", "JssNumber", "TEAMClientNumber" },
                values: new object[] { 1, "Valero", new DateTime(2021, 9, 9, 17, 17, 8, 757, DateTimeKind.Utc).AddTicks(3726), "11", "567" });

            migrationBuilder.InsertData(
                table: "DataSheetPackages",
                columns: new[] { "Id", "ClientName", "Date", "JssNumber", "TEAMClientNumber" },
                values: new object[] { 2, "BP", new DateTime(2008, 5, 1, 8, 30, 52, 0, DateTimeKind.Unspecified), "32", "45" });

            migrationBuilder.InsertData(
                table: "DataSheets",
                columns: new[] { "Id", "PackageId" },
                values: new object[] { 1, 1 });

            migrationBuilder.InsertData(
                table: "DataSheets",
                columns: new[] { "Id", "PackageId" },
                values: new object[] { 3, 2 });

            migrationBuilder.InsertData(
                table: "DataSheets",
                columns: new[] { "Id", "PackageId" },
                values: new object[] { 2, 2 });

            migrationBuilder.InsertData(
                table: "DimensionalSheets",
                columns: new[] { "Id", "CheckedBy", "CheckedByDate", "GivenBy", "LineSize", "LineSkinTemp", "OtherSkinTemp", "Plant", "SealType", "SeverityOfLeak", "ShipTo", "SurfaceCondition", "Unit" },
                values: new object[] { 3, "Joseph Smith", new DateTime(2021, 5, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Billy Ben", "6", "6", "41", "XTO Energy", "Tubing, Other", 5, "MSY", "Ok", "" });

            migrationBuilder.InsertData(
                table: "HtsEngineeringDataCoverSheets",
                columns: new[] { "Id", "Additional", "AdditionalNotes", "Aflas", "Angle", "AngleDegree", "BackingStrips", "BallPark", "BallParkNo", "BlindFlange", "BoltOn", "BoltOn7", "BoltOn9", "BranchEndOption", "BranchHeight", "BranchHeightType", "BranchWithInternalThreads", "Buna", "ButtWeld", "CEPercentage", "CalculationPackage", "ClientSpecified", "ClientSpecified3", "ClientSpecified4", "ContactInfo", "CorrosionAllowance", "CutterDiameter", "DesignCode", "DesignFactor", "DesignPressure", "DesignTemperature", "DistrictProjectId", "DrawWorksNotRequired", "DrawWorksNotRequired9", "DrawWorksforSettingCompletion", "DrawWorksforSettingPlug9", "EPDM", "ElbowLet", "ElbowOnHeel", "ElbowStraight", "FieldTest", "FittingMaterial", "FlangeRF", "FlangeRTJ", "FlangeRating", "FlangeRatingList", "FlangeWithPlug", "FlowThroughPlugWithPigBars", "ForImmediateManufacture", "FullEncirclementSaddle", "FullEncorclementSaddleOnly", "Gasket", "GasketType", "Grade", "HSS2Seal", "HSS2Seal9", "HTP", "HiStop", "HoldTime", "HotTap", "HtsTechSupportRep", "LineLocation", "LineSchedule", "LineSize", "LineStopFlange", "Location", "LongRadius", "MDMT", "Material", "MaterialOrigin", "MaterialToughness", "MaximumHardness", "MetalToMetalSeal", "MinValveBore", "NoPressureTest", "None", "NotApplicable", "NotApplicable2", "NotRequired5", "Notes10", "NozzleOnly", "NozzleOnly7", "NozzleWithFullEncirclementSaddle", "NozzleWithRepad", "NptPortSize", "NuclearNaceRequired", "Other", "Other10", "Other9", "PWHT", "ParallelToRun", "Part1", "Part2", "Part3", "Part4", "Part5", "PartNumber", "PeStampRequired", "PerimeterSeal", "PermanentDrawWorksHold", "PermanentMachineAndValveRemoved", "PermanentValve", "PerpendicularToRun", "PortInFlange", "PortSize", "Pressure", "PressureRatainingSplitTee", "PriceQuote", "Primer", "Priority", "Quantity", "RepadOnly", "RequestedDeliveryDate", "Routine", "RubberSeal", "RunLength", "RunLengthType", "SafetyReviewNo", "SealantType", "SelfSeal", "Service", "ShopTest", "ShortRadius", "Size", "Size4", "Size9", "SolidCompletionPlugWithPigBars", "SphericalTee", "Standard", "Standard4", "StandardCompletionPlugWithScarfedNipple", "StandardCompletionPlugWithoutScarfedNipple", "StandardFlange", "StockFitting", "StrongbacksRequired", "Studs", "SureStop", "Temperature", "TestPressure", "ThirdPartyInspection", "Threaded", "Viton", "WaitforClientApproval", "WallThickness", "WallThickness4", "WallThickness9", "WeepHole", "WeldOLet", "WeldOn", "WeldOn7", "WeldOn9", "YieldStrength" },
                values: new object[] { 2, null, null, false, false, 0, null, false, "47", false, false, false, false, true, 12, true, false, false, false, 0, true, false, false, false, "6912734532", 2, true, false, true, 50, 90, "92", false, false, false, 0, false, false, false, false, false, "Carbon Steel", false, false, "13", true, false, 0, true, false, true, false, null, null, false, false, false, false, 0, "true", "Steph Curry", true, 4, 6, false, "Midland", false, 20, "Carbon Steel", null, null, 0, false, 0, false, false, false, false, false, null, true, false, false, false, true, true, "", null, null, null, false, true, false, false, false, false, "134", true, true, false, 0, false, false, false, null, 29, true, true, false, true, 5, false, new DateTime(2021, 5, 1, 7, 30, 52, 0, DateTimeKind.Unspecified), false, false, 33, true, "23", true, false, "Hot Tap", false, false, "4", "3", null, false, true, true, false, false, false, false, "false", false, false, false, 87, 0, null, false, false, false, 5, 0, 0, true, true, false, false, false, 0 });

            migrationBuilder.InsertData(
                table: "LeakRepairHardwareSpecificationSheets",
                columns: new[] { "Id", "ApprovalEmails", "ApprovalInformationDeadline", "BranchSize", "ClientSpecificRequirements", "DefectCause", "DefectType", "DistrictWorkOrderNo", "Email", "EnclosureMaterial", "EnclosureQuantity", "EngineeringPriorityLevel", "ExpectedRemovalDate", "Frequency", "Grade", "IfDosed", "LineContent", "LineContentPercentage", "LineMaterial", "LineNo", "LineSchedule", "MDMT", "MainLineSize", "MaxSysOpPressure", "MaxSystemDesignTemp", "MaxSystemOpTemp", "NdtDataProvided", "OrderNo", "OtherDefect", "PrintName", "ProcessingRequirements", "QualityRequirements", "RemainingWallThickness", "ReqRepairCorrosionAllowance", "RequestedInstallationDate", "SealantSelection", "Site", "SourService", "SprayTypeDesuperheater", "Strongback", "SuperheatedSteam", "SysDesignPressure", "SysOpPressure", "SystemDesignTemp", "SystemOpTemp", "TeamCjNo", "Technician1", "Technician2", "Telephone", "Vibration" },
                values: new object[] { 1, "<EMAIL>", new DateTime(2021, 9, 9, 11, 17, 8, 758, DateTimeKind.Local).AddTicks(3282), "5", "NA", true, true, "7", "<EMAIL>", "NA", 1, true, new DateTime(2021, 9, 9, 11, 17, 8, 758, DateTimeKind.Local).AddTicks(1068), 10, 2, false, "Condensate", 100, true, "56", 7, 25, 3, 20, 100, 50, true, "2", null, "Jeffery Smith", true, false, 2, 2, new DateTime(2021, 9, 9, 11, 17, 8, 758, DateTimeKind.Local).AddTicks(3515), "5X", "Midland", true, false, true, false, 15, 5, 75, 20, "42", "Alex Ramos", "Brian Turner", 2455687, true });

            migrationBuilder.InsertData(
                table: "DS200s",
                columns: new[] { "Id", "A1", "A2", "Aa", "B1", "B2", "Bb", "C1", "C2", "Cc", "CircularBonnet", "CutStem", "D1", "D2", "Dd", "E1", "E2", "Ee", "F1", "F2", "Ff", "Flg", "Gg", "H1", "H2", "He", "J1", "J2", "KbE", "KcE", "LbF", "LcF", "LocationOfBlow", "M", "N", "Notes", "Obstructions", "OvalBonnet", "P", "Q", "R", "RemoveHandle", "SquareBonnet", "StemClockPt", "T", "U", "V", "W1", "W2", "X1At12", "X1At3", "X1At6", "X1At9", "X2At12", "X2At3", "X2At6", "X2At9", "XX", "XY", "Y", "YX", "YY" },
                values: new object[] { 3, 0, 0, 0, 0, 0, 0, 7, 0, 0, true, false, 7, 0, 0, 2, 0, 0, 1, 0, 0, null, 0, 3, 0, 0, 3, 0, 4, 0, 1, 1, "Exterior", 1, 1, 0, 0, false, 2, 4, 0, true, false, 1, 0, 0, 0, 1, 0, 1, 3, 3, 3, 3, 3, 3, 7, true, false, 0, false, true });

            migrationBuilder.CreateIndex(
                name: "IX_DataSheets_PackageId",
                table: "DataSheets",
                column: "PackageId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DS100s");

            migrationBuilder.DropTable(
                name: "DS101s");

            migrationBuilder.DropTable(
                name: "DS102s");

            migrationBuilder.DropTable(
                name: "DS103s");

            migrationBuilder.DropTable(
                name: "DS104s");

            migrationBuilder.DropTable(
                name: "DS105s");

            migrationBuilder.DropTable(
                name: "DS110s");

            migrationBuilder.DropTable(
                name: "DS111s");

            migrationBuilder.DropTable(
                name: "DS113s");

            migrationBuilder.DropTable(
                name: "DS120s");

            migrationBuilder.DropTable(
                name: "DS124s");

            migrationBuilder.DropTable(
                name: "DS128s");

            migrationBuilder.DropTable(
                name: "DS129s");

            migrationBuilder.DropTable(
                name: "DS133s");

            migrationBuilder.DropTable(
                name: "DS134s");

            migrationBuilder.DropTable(
                name: "DS136s");

            migrationBuilder.DropTable(
                name: "DS137s");

            migrationBuilder.DropTable(
                name: "DS138s");

            migrationBuilder.DropTable(
                name: "DS139s");

            migrationBuilder.DropTable(
                name: "DS140s");

            migrationBuilder.DropTable(
                name: "DS141s");

            migrationBuilder.DropTable(
                name: "DS150s");

            migrationBuilder.DropTable(
                name: "DS151s");

            migrationBuilder.DropTable(
                name: "DS194s");

            migrationBuilder.DropTable(
                name: "DS195s");

            migrationBuilder.DropTable(
                name: "DS196s");

            migrationBuilder.DropTable(
                name: "DS197s");

            migrationBuilder.DropTable(
                name: "DS200s");

            migrationBuilder.DropTable(
                name: "DS201s");

            migrationBuilder.DropTable(
                name: "DS205s");

            migrationBuilder.DropTable(
                name: "DS206s");

            migrationBuilder.DropTable(
                name: "DS209s");

            migrationBuilder.DropTable(
                name: "DS210s");

            migrationBuilder.DropTable(
                name: "DS230s");

            migrationBuilder.DropTable(
                name: "DS300s");

            migrationBuilder.DropTable(
                name: "DS901s");

            migrationBuilder.DropTable(
                name: "HtsEngineeringDataCoverSheets");

            migrationBuilder.DropTable(
                name: "LeakRepairHardwareSpecificationSheets");

            migrationBuilder.DropTable(
                name: "DimensionalSheets");

            migrationBuilder.DropTable(
                name: "DataSheets");

            migrationBuilder.DropTable(
                name: "DataSheetPackages");
        }
    }
}
