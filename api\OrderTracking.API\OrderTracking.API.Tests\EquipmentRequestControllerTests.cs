﻿//using System;
//using System.Collections.Generic;
//using System.Text;
//using System.Threading.Tasks;
//using ClientPortal.Shared.Models;
//using ClientPortal.Shared.Services;
//using Microsoft.AspNetCore.Http;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.AspNetCore.SignalR;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.Logging;
//using Moq;
//using NUnit.Framework;
//using OrderTracking.API.Controllers;
//using OrderTracking.API.Hubs;
//using OrderTracking.API.Interfaces;
//using OrderTracking.API.Services;

//namespace OrderTracking.API.Tests
//{
//    [TestFixture]
//    public class EquipmentRequestControllerTests 
//    {
//        [Test]
//        public async Task Get_EmptyId_OkResult()
//        {
//            // Arrange
//            var mockEnvLogger = new Mock<ILogger<DeploymentEnvironment>>();
//            var userProfiles = new Mock<IUserProfilesService>(MockBehavior.Loose);
//            userProfiles.Setup(service => service.GetAsync("<EMAIL>"))
//                .ReturnsAsync(new UserProfile());
//            var equipmentService = new Mock<IEquipmentRequestsService>(MockBehavior.Loose);
//            var hub = new Mock<IHubContext<EDRHub>>(MockBehavior.Loose);
//            var emails = new Mock<IEmailService>(MockBehavior.Loose);
//            var config = new Mock<IConfiguration>(MockBehavior.Loose);
//            var env = new DeploymentEnvironment(config.Object, mockEnvLogger.Object);
//            var httpContext = new Mock<HttpContext>(MockBehavior.Loose);
//            httpContext.SetupGet(context => context.User.Identity.Name).Returns("<EMAIL>");
//            var equipmentRequestsController = new EquipmentRequestsController(userProfiles.Object,
//                equipmentService.Object, hub.Object, emails.Object, config.Object, env
//            ) {ControllerContext = new ControllerContext {HttpContext = httpContext.Object}};

//            // Act
//            var response = await equipmentRequestsController.Get(string.Empty);

//            // Assert
//            Assert.That(response is OkObjectResult);
//        }


//        [Test]
//        public async Task GetAll_NoUserProfile_Unauthorized()
//        {
//            // Arrange
//            var mockEnvLogger = new Mock<ILogger<DeploymentEnvironment>>();
//            var userProfiles = new Mock<IUserProfilesService>(MockBehavior.Loose);
//            userProfiles.Setup(service => service.GetAsync("<EMAIL>"))
//                .ReturnsAsync(new UserProfile());
//            var equipmentService = new Mock<IEquipmentRequestsService>(MockBehavior.Loose);
//            var hub = new Mock<IHubContext<EDRHub>>(MockBehavior.Loose);
//            var emails = new Mock<IEmailService>(MockBehavior.Loose);
//            var config = new Mock<IConfiguration>(MockBehavior.Loose);
//            var env = new DeploymentEnvironment(config.Object, mockEnvLogger.Object);
//            var httpContext = new Mock<HttpContext>(MockBehavior.Loose);
//            httpContext.SetupGet(context => context.User.Identity.Name).Returns("<EMAIL>");
//            var equipmentRequestsController = new EquipmentRequestsController(userProfiles.Object,
//                    equipmentService.Object, hub.Object, emails.Object, config.Object, env
//                )
//                { ControllerContext = new ControllerContext { HttpContext = httpContext.Object } };

//            // Act
//            var response = await equipmentRequestsController.Get();

//            // Assert
//            Assert.That(response is UnauthorizedResult);
//        }

//        [Test]
//        public async Task Get_NoUserProfile_ReturnsUnauthorized()
//        {
//            // Arrange
//            var mockEnvLogger = new Mock<ILogger<DeploymentEnvironment>>();
//            var userProfiles = new Mock<IUserProfilesService>(MockBehavior.Loose);
//            userProfiles.Setup(service => service.GetAsync("<EMAIL>"))
//                .ReturnsAsync(new UserProfile());
//            var equipmentService = new Mock<IEquipmentRequestsService>(MockBehavior.Loose);
//            var hub = new Mock<IHubContext<EDRHub>>(MockBehavior.Loose);
//            var emails = new Mock<IEmailService>(MockBehavior.Loose);
//            var config = new Mock<IConfiguration>(MockBehavior.Loose);
//            var env = new DeploymentEnvironment(config.Object, mockEnvLogger.Object);
//            var httpContext = new Mock<HttpContext>(MockBehavior.Loose);
//            httpContext.SetupGet(context => context.User.Identity.Name).Returns("<EMAIL>");
//            var equipmentRequestsController = new EquipmentRequestsController(userProfiles.Object,
//                equipmentService.Object, hub.Object, emails.Object, config.Object, env
//            ) {ControllerContext = new ControllerContext {HttpContext = httpContext.Object}};

//            // Act
//            var response = await equipmentRequestsController.Get(string.Empty);

//            // Assert
//            Assert.That(response is UnauthorizedResult);
//        }

//        [Test]
//        public async Task Post_EmptyForm_BadRequest()
//        {
//            // Arrange
//            var mockEnvLogger = new Mock<ILogger<DeploymentEnvironment>>();
//            var userProfiles = new Mock<IUserProfilesService>(MockBehavior.Loose);
//            userProfiles.Setup(service => service.GetAsync("<EMAIL>"))
//                .ReturnsAsync(new UserProfile());
//            var equipmentService = new Mock<IEquipmentRequestsService>(MockBehavior.Loose);
//            var hub = new Mock<IHubContext<EDRHub>>(MockBehavior.Loose);
//            var emails = new Mock<IEmailService>(MockBehavior.Loose);
//            var config = new Mock<IConfiguration>(MockBehavior.Loose);
//            var env = new DeploymentEnvironment(config.Object, mockEnvLogger.Object);
//            var httpContext = new Mock<HttpContext>(MockBehavior.Loose);
//            httpContext.SetupGet(context => context.User.Identity.Name).Returns("<EMAIL>");
//            var equipmentRequestsController = new EquipmentRequestsController(userProfiles.Object,
//                equipmentService.Object, hub.Object, emails.Object, config.Object, env
//            ) {ControllerContext = new ControllerContext {HttpContext = httpContext.Object}};

//            // Act
//            var response = await equipmentRequestsController.Post(new FormCollection(null));

//            // Assert
//            Assert.That(response is BadRequestObjectResult);
//        }
//    }
//}
