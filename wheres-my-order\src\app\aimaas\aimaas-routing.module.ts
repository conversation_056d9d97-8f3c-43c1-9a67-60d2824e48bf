import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {
    AIMaaSComponent,
    FixedEquipmentDrillDownComponent,
    FixedEquipmentInspectionDrilldownComponent
} from './pages';
import { AnomalyDrillDownComponent } from './pages/anomaly-drill-down/anomaly-drill-down.component';

const routes: Routes = [
    {
        path: 'dashboards',
        component: AIMaaSComponent,
        data: { pageTitle: 'Asset Integrity Hub' }
    },
    {
        path: '',
        redirectTo: '',
        pathMatch: 'full'
    },
    {
        path: 'drilldown',
        component: FixedEquipmentDrillDownComponent,
        data: { pageTitle: 'Assets' }
    },
    {
        path: 'inspection-drilldown',
        component: FixedEquipmentInspectionDrilldownComponent,
        data: { pageTitle: 'Inspections' }
    },
    {
        path: 'anomaly-drilldown',
        component: AnomalyDrillDownComponent,
        data: { pageTitle: 'Recommendations' }
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AIMaaSRoutingModule {}
