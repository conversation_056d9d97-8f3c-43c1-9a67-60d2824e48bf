﻿using System;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Extensions;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Authorization.Handlers
{
    /// <summary>
    ///     Handles the authorization of adding Asset Management Site Ids to a user profile for access
    ///     to data pertaining to that site.
    /// </summary>
    public class
        AssetManagementSiteAssignmentAuthorizationHandler : AuthorizationHandler<
            AssetManagementSiteAssignmentRequirement, UserProfile>
    {
        private readonly IUserProfilesService _userProfiles;

        /// <summary>
        ///     Constructor
        /// </summary>
        /// <param name="userProfiles"></param>
        public AssetManagementSiteAssignmentAuthorizationHandler(IUserProfilesService userProfiles)
        {
            _userProfiles = userProfiles;
        }

        /// <summary>
        ///     Handle the authorization requirement.
        /// </summary>
        /// <param name="context"></param>
        /// <param name="requirement"></param>
        /// <param name="resource"></param>
        /// <returns></returns>
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context,
            AssetManagementSiteAssignmentRequirement requirement, UserProfile resource)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (resource == null) throw new ArgumentNullException(nameof(resource));

            if (context.User.Identity?.Name != null)
                await AuthorizeChanges(context, requirement, resource);
            else
                context.Fail();
        }

        private async Task AuthorizeChanges(AuthorizationHandlerContext context, IAuthorizationRequirement requirement,
            UserProfile resource)
        {
            var originalProfile = await _userProfiles.GetAsync(resource.Id);

            if (!resource.HasRole("AIMaaS:View")) context.Fail();

            context.Succeed(requirement);
        }
    }
}