﻿using System;
using ClientPortal.Shared.Models;
using NUnit.Framework;
using OrderTracking.API.Services;

namespace OrderTracking.API.Tests
{
    [TestFixture]
    public class UserProfileServiceTests
    {
        [Test]
        public void CanRolesBeAdded_OnlyDemoRole_ShouldNotHaveError()
        {
            // Arrange
            var user = new UserProfile { Id = "<EMAIL>", Roles = { "App:Admin" } };

            // Act
            var error = UserProfilesService.CanRolesBeAdded(user, new string[] { "AIMaaS:Demo" },
                Array.Empty<string>());

            // Assert
            Assert.That(error.Error, Is.False);
        }

        [Test]
        public void CanRolesBeAdded_AIMaaSDemoWhenAdmin_ShouldHaveError()
        {
            // Arrange
            var user = new UserProfile{ Id="<EMAIL>", Roles = {"AIMaaS:Admin"}};
           
            // Act
            var error = UserProfilesService.CanRolesBeAdded(user, new string[] {"AIMaaS:Demo"},
                Array.Empty<string>());
            
            // Assert
            Assert.That(error.Error, Is.True);
        }

        [Test]
        public void CanRolesBeAdded_MultipleAIMaaSBeingAdded_ShouldHaveError()
        {
            // Arrange
            var user = new UserProfile { Id = "<EMAIL>", Roles = { "App:Admin" } };

            // Act
            var error = UserProfilesService.CanRolesBeAdded(user, new string[] { "AIMaaS:Demo", "AIMaaS:Admin" },
                Array.Empty<string>());

            // Assert
            Assert.That(error.Error, Is.True);
        }

        [Test]
        public void CanRolesBeAdded_AIMaaSDemoWhenAdminIsRemoved_ShouldNotHaveError()
        {
            // Arrange
            var user = new UserProfile { Id = "<EMAIL>", Roles = { "AIMaaS:Admin" } };

            // Act
            var error = UserProfilesService.CanRolesBeAdded(user, new string[] { "AIMaaS:Demo" },
                new string[]{"AIMaaS:Admin"});

            // Assert
            Assert.That(error.Error, Is.False);
        }

    }
}
