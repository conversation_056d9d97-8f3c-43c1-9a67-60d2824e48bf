﻿using System;
using System.Reflection;
using ClientPortal.Shared.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace ClientPortal.Shared.Models.MOS
{
    public class MOSContext : DbContext
    {
        private readonly string _connectionString;
        public DbSet<DataSheet> DataSheets { get; set; }
        public DbSet<DimensionalSheet> DimensionalSheets { get; set; }
        public DbSet<DataSheetPackage> DataSheetPackages { get; set; }

        public DbSet<LeakRepairHardwareSpecificationSheet> LRS { get; set; }
        public DbSet<HtsEngineeringDataCoverSheet> HTS { get; set; }

        public DbSet<DS100> BasicFlangeClamp { get; set; }
        public DbSet<DS101> SquareBonnet { get; set; }
        public DbSet<DS102> OvalBonnet { get; set; }
        public DbSet<DS103> IsolationBand { get; set; }

        public DbSet<DS104> OrifaceFlange { get; set; }
        public DbSet<DS105> ExchangerFlanges { get; set; }
        public DbSet<DS110> LineFlangeToFlangeWeldNeck { get; set; }
        public DbSet<DS111> LineFlangeToFlange { get; set; }
        public DbSet<DS113> LineFlangeTo90 { get; set; }
        public DbSet<DS120> CouplingAndOrUnionScrewedSocketFitting { get; set; }
        public DbSet<DS124> Sweep90SmlsButtWeld { get; set; }
        public DbSet<DS128> Smls45ButtWeld { get; set; }

        public DbSet<DS129> ScrewedScoket45Fitting { get; set; }
        public DbSet<DS133> TeeSmlsButtWeldFitting { get; set; }
        public DbSet<DS134> ScrewedSocketFitting { get; set; }
        public DbSet<DS136> TeeCouplet { get; set; }
        public DbSet<DS137> StraightLineEnclosure { get; set; }
        public DbSet<DS138> ConcentricReducerEnclosure { get; set; }
        public DbSet<DS139> EccentricReducerEnclosure { get; set; }
        public DbSet<DS140> LateralSmlsButtWeldFitting { get; set; }
        public DbSet<DS141> LateralScrewedSocketFitting { get; set; }
        public DbSet<DS150> NozzleCenteredOnVessel { get; set; }
        public DbSet<DS151> NozzleNotCenteredOnVessel { get; set; }
        public DbSet<DS194> ValvePackingCalculations { get; set; }
        public DbSet<DS195> AuxiliaryPackingGland { get; set; }
        public DbSet<DS196> AuxiliaryHandleGateGlobeValves { get; set; }
        public DbSet<DS197> StemGag { get; set; }
        public DbSet<DS200> GateGlobeFlangedBonnetOutsideSS { get; set; }
        public DbSet<DS201> GateGlobeFlangedBonnetInsideSS { get; set; }
        public DbSet<DS205> BallValveWithFlangedEnds { get; set; }
        public DbSet<DS206> GateGlobeScrewedWeldEndsFlangedBonnetInsideSS { get; set; }
        public DbSet<DS209> GateGlobeScrewedWeldEndsUnionBonnetInsideSS { get; set; }
        public DbSet<DS210> GateGlobeScrewedWeldEndsScrewedWeldedBonnetInsideSS { get; set; }
        public DbSet<DS230> GlobeY { get; set; }
        public DbSet<DS300> Sweep90SmlsButtWeldStubOut { get; set; }
        public DbSet<DS901> FinFanFeeder { get; set; }

        public MOSContext(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("MOSDatabase");
        }

        protected override void OnConfiguring(DbContextOptionsBuilder options)
        {
            options
                .UseSqlServer(
                    _connectionString, 
                    builder => builder.MigrationsAssembly("OrderTracking.API"))
                .LogTo(Console.WriteLine, LogLevel.Warning);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            modelBuilder
                .Entity<DimensionalSheet>()
                .Property(e => e.SealType)
                .HasConversion(
                    v => v.ToString(),
                    v => (SealType)Enum.Parse(typeof(SealType), v));
            modelBuilder
                .Entity<DS100>()
                .Property(e => e.FlgApp)
                .HasConversion(
                    v => v.ToString(),
                    v => (FlgApp)Enum.Parse(typeof(FlgApp), v));

            modelBuilder.Seed();
        }
    }   
}