﻿//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;
//using ClientPortal.Shared.Models;
//using DevExtreme.AspNet.Data.ResponseModel;
//using Microsoft.AspNetCore.Http;
//using OrderTracking.API.Models;

//namespace OrderTracking.API.Interfaces
//{
//    /// <summary>
//    ///     Interface for OrdersService
//    /// </summary>
//    public interface IOrdersService
//    {
//        #region Public Methods

//        /// <summary>
//        ///     Get Orders for a UserProfile based on their permissions
//        /// </summary>
//        /// <param name="user"></param>
//        /// <param name="includeArchive"></param>
//        /// <returns></returns>
//        IQueryable<Order> GetOrdersForUser(UserProfile user, bool includeArchive = false);

//        /// <summary>
//        ///     Upload 1 or more files to an order, uploading files to Azure blob Storage
//        ///     and creating associations to the order for subsequent joined queries.
//        /// </summary>
//        /// <param name="salesLineRecId"></param>
//        /// <param name="form"></param>
//        /// <param name="email"></param>
//        /// <returns></returns>
//        Task UploadFiles(long salesLineRecId, IFormFileCollection form, string email);

//        /// <summary>
//        ///     Delete a file associated with an Order and remove the association.
//        /// </summary>
//        /// <param name="fileId"></param>
//        /// <returns></returns>
//        Task DeleteFile(string fileId);

//        /// <summary>
//        ///     Get an Order based on a UserProfile's permissions.
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="user"></param>
//        /// <returns></returns>
//        Task<Order> GetItemAsync(string id, UserProfile user);

//        /// <summary>
//        ///     Download a blob file associated with an Order and send the file to the client.
//        /// </summary>
//        /// <param name="routeId"></param>
//        /// <param name="blobName"></param>
//        /// <returns></returns>
//        Task<CloudStorageDownloadedObject> DownloadBlobAsync(string routeId, string blobName);

//        /// <summary>
//        ///     Get the distinct list of client account numbers found in the Orders we have at the time.
//        /// </summary>
//        /// <returns></returns>
//        IQueryable<dynamic> GetExternalCustomers();

//        /// <summary>
//        ///     Get an Order by the id of an associated support document.
//        /// </summary>
//        /// <param name="salesLineRecId"></param>
//        /// <returns></returns>
//        IQueryable<Order> GetBySalesLineRecIdAsync(long salesLineRecId);

//        /// <summary>
//        ///     Deletes all orders whose ids are in the provided list
//        /// </summary>
//        /// <param name="ids"></param>
//        /// <returns></returns>
//        Task DeleteAsync(string[] ids);
        
//        #endregion

//        /// <summary>
//        ///     Removes information from order records that external users shouldn't see
//        /// </summary>
//        /// <param name="loadResult"></param>
//        /// <returns></returns>
//        IEnumerable<object> TrimOrderForClient(LoadResult loadResult);
//    }
//}