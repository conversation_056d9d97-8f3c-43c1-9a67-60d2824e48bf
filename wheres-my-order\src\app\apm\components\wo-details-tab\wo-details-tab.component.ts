import { Component, EventEmitter, Input, Output } from '@angular/core';
import cloneDeep from 'clone-deep';
import { DxFormComponent } from 'devextreme-angular/ui/form';
import { WorkOrderDetailTabInfo } from '../../models';

@Component({
    selector: 'app-wo-details-tab',
    templateUrl: './wo-details-tab.component.html',
    styleUrls: ['./wo-details-tab.component.scss']
})
export class WoDetailsTabComponent {
    private _woDetail = new WorkOrderDetailTabInfo();
    private _originalWODetail: WorkOrderDetailTabInfo | undefined;
    @Input() allowEditing: boolean;
    @Input() set woDetail(value: WorkOrderDetailTabInfo) {
        this._woDetail = value;
        this._originalWODetail = cloneDeep(this._woDetail);
        this.changes = {};
        this.isEditing = false;
    }
    get woDetail(): WorkOrderDetailTabInfo {
        return this._woDetail;
    }

    @Output() save = new EventEmitter<Partial<WorkOrderDetailTabInfo>>();

    isSaving = false;
    isEditing = false;

    changes: Partial<WorkOrderDetailTabInfo> = {};

    constructor() {}

    onFieldDataChanged(e: { dataField: string; value: any }) {
        this.changes[e.dataField] = e.value;
    }

    onEditClicked(e) {
        this.isEditing = true;
    }

    onSaveClicked(e) {
        this.isSaving = true;
        this.save.next({
            projectId: this._woDetail.projectId,
            id: this._woDetail.id,
            ...this.changes
        });
    }

    onCancelClicked(e, form: DxFormComponent) {
        this.isEditing = false;
        form.instance.option('formData', this._originalWODetail);
        this.changes = {};
    }
}
