﻿using System;
using System.Collections.Generic;
//using System.IO;
using System.Linq;
using System.Net.Http;
//using System.Text;
using System.Threading.Tasks;
//using System.Web;
using APMWebDataInterface;
using APMWebDataInterface.DataModel.LeakReport;
using APMWebDataInterface.ExampleDataModel;
//using APMWebDataInterface.Headless.Entities;
using CommonDataInterface;
using Microsoft.Extensions.Logging;
//using Newtonsoft.Json;
//using Newtonsoft.Json.Linq;
//using Newtonsoft.Json.Serialization;
using OrderTracking.API.Models.APM;
//using OrderTracking.API.Services.APM;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     Specific exception for when we aren't able to find a task by a specific ID
    /// </summary>
    public class TasksNotFoundException : Exception
    {
        /// <summary>
        ///     Constructor for exception
        /// </summary>
        /// <param name="taskIDs"></param>
        public TasksNotFoundException(params string[] taskIDs) : base(
            $"Task(s) not found: {string.Join(", ", taskIDs)}", null)
        {
            TaskIDs = taskIDs.ToList();
        }

        /// <summary>
        ///     IDs of tasks that were not able to be found.
        /// </summary>
        public List<string> TaskIDs { get; set; }
    }

    /// <summary>
    ///     Service class for requesting reports from JasperSoft
    /// </summary>
    public class JasperSoftService : IJasperSoftService
    {
        // TODO: Should probably go in configuration (appsettings.json)
        private const string Server =
            "http://jaspersoft-training.southcentralus.cloudapp.azure.com:8080/jasperserver-pro/rest_v2/reports/reports/";

        private const int PreferredImageSize = 325;
        private const int PreferredQuality = 95;

        private readonly DeploymentEnvironment _environment;
        private readonly IHttpClientFactory _httpClientFactory;

        private readonly ILogger<JasperSoftService> _logger;
        //private readonly IAPMReportingBlobStorageService _reportingStorage;

        /// <summary>
        ///     Constructs an instance of the JasperSoftService
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="reportingStorage"></param>
        /// <param name="httpClientFactory"></param>
        /// <param name="environment"></param>
        public JasperSoftService(
            ILogger<JasperSoftService> logger,
            //IAPMReportingBlobStorageService reportingStorage,
            IHttpClientFactory httpClientFactory,
            DeploymentEnvironment environment
        )
        {
            _logger = logger;
            //_reportingStorage = reportingStorage;
            _httpClientFactory = httpClientFactory;
            _environment = environment;
        }

        /// <summary>
        ///     Requests a report to be generated for an Internal, External, or WalkDown set of tasks
        /// </summary>
        /// <param name="reportTransport"></param>
        /// <param name="project"></param>
        /// <param name="location"></param>
        /// <param name="workOrder"></param>
        /// <returns></returns>
        public async Task<SingleTaskReportPackage> GenerateInspectionReport(
            ReportTransport reportTransport,
            Project project,
            Location location,
            WorkOrder workOrder
        )
        {
            // Prepare to build the payload for JasperSoft
            SingleTaskReportPackage payload = null;
            //string uploadedFileName = null;
            // Get all the photos for the work order to include in the payload along with the
            // photos from the task(s).
            var workOrderPhotos = workOrder.GetAllPhotos(false);
            FlatPhotoHolder[] allPhotos;
            switch (reportTransport.TaskType)
            {
                case "Asset Walkdown":
                    // Get the walkDown task
                    var walkDown = workOrder.tasks.FirstOrDefault(t => t.id == reportTransport.WalkDownTaskId);
                    if (walkDown == null) throw new TasksNotFoundException(reportTransport.WalkDownTaskId);

                    // Get the walkDown task photos + combine with work order photos + resolve them all
                    var walkDownPhotos = workOrder.asset.walkDown.GetAllPhotos(false);
                    allPhotos = walkDownPhotos.Concat(workOrderPhotos).ToArray();
                    foreach (var photo in allPhotos)
                        await photo.MediaEntry.ResolveWithResize(
                            PreferredImageSize,
                            PreferredImageSize,
                            PreferredQuality
                        );

                    // Create payload object
                    payload = new SingleTaskReportPackage
                    {
                        WorkOrder = workOrder,
                        Location = location,
                        Project = project,
                        Photos = allPhotos,
                        Asset = workOrder.asset,
                        Task = walkDown
                    };

                    // Set name of file to be uploaded to blob storage
                    //uploadedFileName =
                    //    $"{walkDown.taskAPMNumber.CurrentValue}_{_environment.GetName()}_{DateTime.UtcNow:yyyy-MM-dd HH-mm-ss}.json";
                    break;
            }

            //var jsonSourceURL = await UploadJSONPayload(payload, uploadedFileName);

            //var response = await SendJasperSoftReportRequest(reportTransport.ReportType, jsonSourceURL);

            // Don't delete blobs until we are confident in our reporting and don't need these for debugging purposes
            // await _reportingStorage.DeleteBlobAsync(fileName);

            return payload;
        }

        /// <summary>
        ///     Requests a report object to be generated for an Internal, External, or WalkDown set of tasks
        /// </summary>
        /// <param name="reportTransport"></param>
        /// <param name="project"></param>
        /// <param name="location"></param>
        /// <param name="workOrder"></param>
        /// <returns>AssetWalkdown and FullInspection</returns>
        public async Task<(SingleTaskReportPackage, FullPackage)> GenerateInspectionReportObject(
            ReportTransport reportTransport,
            Project project,
            Location location,
            WorkOrder workOrder
        )
        {
            // Prepare to build the payload for JasperSoft
            SingleTaskReportPackage assetWalkDown = null;
            FullPackage internalAndExternal = null;
            // Get all the photos for the work order to include in the payload along with the
            // photos from the task(s).
            var workOrderPhotos = workOrder.GetAllPhotos(false);
            FlatPhotoHolder[] allPhotos;
            APMTask internalTask;
            APMTask externalTask;
            switch (reportTransport.TaskType)
            {
                case "External Visual":
                    // Get the External Task
                    externalTask =
                        workOrder.tasks.FirstOrDefault(t => t.id == reportTransport.ExternalTaskId);
                    if (externalTask == null) throw new TasksNotFoundException(reportTransport.ExternalTaskId);

                    // Combine and resolve the photos
                    var taskPhotos = externalTask.GetAllPhotos(false);
                    allPhotos = taskPhotos.Concat(workOrderPhotos).ToArray();
                    foreach (var photo in allPhotos)
                        await photo.MediaEntry.ResolveWithResize(
                            PreferredImageSize,
                            PreferredImageSize,
                            PreferredQuality
                        );

                    // Create payload object
                    internalAndExternal = new FullPackage
                    {
                        Asset = workOrder.asset,
                        Location = location,
                        WorkOrder = workOrder,
                        ExternalTask = externalTask,
                        Photos = allPhotos.ToArray(),
                        Project = project
                    };
                    break;
                case "Internal Visual":
                    internalTask = workOrder.tasks.FirstOrDefault(t => t.id == reportTransport.InternalTaskId);

                    if (internalTask == null) throw new TasksNotFoundException(reportTransport.InternalTaskId);

                    var photos = internalTask.GetAllPhotos(false);

                    foreach (var photo in photos)
                        await photo.MediaEntry.ResolveWithResize(PreferredImageSize, PreferredImageSize,
                            PreferredQuality);

                    // Create payload object
                    internalAndExternal = new FullPackage
                    {
                        Location = location,
                        WorkOrder = workOrder,
                        Photos = photos.ToArray(),
                        ExternalTask = null,
                        Project = project,
                        Asset = workOrder.asset,
                        InternalTask = internalTask
                    };

                    break;
                default:
                    throw new NotImplementedException();
                    break;
            }

            return (assetWalkDown, internalAndExternal);
        }

        /// <summary>
        ///     Requests a report to be generated by JasperSoft for Leak Detection Repair
        /// </summary>
        /// <param name="report"></param>
        /// <returns></returns>
        public async Task<object> GenerateLeakReportingReport(LeakReport report)
        {
            var leakReportPhotos =
                report.leakReportPhotos.GetEntries().SelectMany(photoGroup => photoGroup.photos.Photos);
            foreach (var media in leakReportPhotos)
                await media.ResolveWithResize(PreferredImageSize, PreferredImageSize, PreferredQuality);

            var photos = report.report.GetAllPhotos(false).Concat(report.workDetail.GetAllPhotos(false));
            var allPhotos = photos as FlatPhotoHolder[] ?? photos.ToArray();
            foreach (var photo in allPhotos)
                await photo.MediaEntry.ResolveWithResize(PreferredImageSize, PreferredImageSize, PreferredQuality);

            var payload = new { LeakReport = report, Photos = allPhotos };

            //var uploadedFileName = report.apmNumber.CurrentValue + ".json";

            //var jsonSourceURL = await UploadJSONPayload(payload, uploadedFileName);

            //var response = await SendJasperSoftReportRequest("Leak_Detection_Repair.pdf", jsonSourceURL);

            // Don't delete blobs until we are confident in our reporting and don't need these for debugging purposes
            // await _reportingStorage.DeleteBlobAsync(fileName);

            return payload;
        }

        ///// <summary>
        /////     Makes a report request to JasperSoft with HTTP
        ///// </summary>
        ///// <param name="reportName"></param>
        ///// <param name="jsonSourceURL"></param>
        ///// <returns></returns>
        //private async Task<HttpResponseMessage> SendJasperSoftReportRequest(string reportName, string jsonSourceURL)
        //{
        //    // Prepare to make a call to JasperSoft
        //    using var httpClient = _httpClientFactory.CreateClient();

        //    // TODO: This is temporary.  We ran into an issue where this requests was taking longer
        //    // than 2 minutes (what this used to be set to) by a few seconds, but we don't have a
        //    // good way of deciding how small this number can be.  But we plan on decreasing the
        //    // overall wait time for this report to be generated in several ways, and when we get
        //    // there, we need to revisit this timeout and investigate a more appropriate number.
        //    // This will unblock people in production right now, however.
        //    const int timeout = 5;


        //    httpClient.Timeout = TimeSpan.FromMinutes(timeout);
        //    using var request = new HttpRequestMessage(new HttpMethod("GET"),
        //        Server + reportName + "?net.sf.jasperreports.json.source=" + jsonSourceURL);
        //    var base64Authorization = Convert.ToBase64String(Encoding.ASCII.GetBytes("jasperadmin:jasperadmin"));
        //    request.Headers.TryAddWithoutValidation("Authorization", $"Basic {base64Authorization}");

        //    // Make request to JasperSoft
        //    var response = await httpClient.SendAsync(request);
        //    return response;
        //}

        ///// <summary>
        /////     Serializes a payload to a json string and uploads to our report blob storage for JasperSoft to be able to
        /////     pull/download from.
        ///// </summary>
        ///// <param name="payload"></param>
        ///// <param name="uploadedFileName"></param>
        ///// <returns></returns>
        //private async Task<string> UploadJSONPayload(object payload, string uploadedFileName)
        //{
        //    // Serialize the payload object to json string.
        //    var payloadString = JObject.FromObject(
        //            payload,
        //            JsonSerializer.Create(
        //                new JsonSerializerSettings
        //                {
        //                    ContractResolver = new DefaultContractResolver(),
        //                    Formatting = Formatting.None,
        //                    NullValueHandling = NullValueHandling.Ignore
        //                }))
        //        .ToString();

        //    // Log out the byte size of the file we are about to upload
        //    var byteLength = Encoding.Unicode.GetByteCount(payloadString);
        //    _logger.LogInformation(
        //        $"Report file ({byteLength} bytes) being uploaded for JasperSoft Server consumption");

        //    // Upload the json file to blob storage
        //    var content = Encoding.UTF8.GetBytes(payloadString);
        //    await using var ms = new MemoryStream(content);
        //    uploadedFileName = HttpUtility.UrlEncode(uploadedFileName);
        //    var (uri, blob, blobName) = await _reportingStorage.UploadStreamAsync(ms, uploadedFileName);
        //    var reportingSas = _reportingStorage.GetSasToken();
        //    return uri + "?" + reportingSas;
        //}
    }

    /// <summary>
    ///     Specific exception for when we aren't able to find a task by a specific ID
    /// </summary>
    public interface IJasperSoftService
    {
        /// <summary>
        ///     Requests a report to be generated for an Internal, External, or WalkDown set of tasks
        /// </summary>
        /// <param name="reportTransport"></param>
        /// <param name="project"></param>
        /// <param name="location"></param>
        /// <param name="workOrder"></param>
        /// <returns></returns>
        Task<SingleTaskReportPackage> GenerateInspectionReport(
            ReportTransport reportTransport,
            Project project,
            Location location,
            WorkOrder workOrder
        );

        /// <summary>
        ///     Requests a report object to be generated for an Internal, External, or WalkDown set of tasks
        /// </summary>
        /// <param name="reportTransport"></param>
        /// <param name="project"></param>
        /// <param name="location"></param>
        /// <param name="workOrder"></param>
        /// <returns>AssetWalkdown and FullInspection</returns>
        Task<(SingleTaskReportPackage, FullPackage)> GenerateInspectionReportObject(
            ReportTransport reportTransport,
            Project project,
            Location location,
            WorkOrder workOrder);

        ///// <summary>
        /////     Requests a report to be generated by JasperSoft for Leak Detection Repair
        ///// </summary>
        ///// <param name="report"></param>
        ///// <returns></returns>
        Task<object> GenerateLeakReportingReport(LeakReport report);
    }
}