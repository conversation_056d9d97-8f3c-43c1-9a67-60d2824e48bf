import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxDataGridModule } from 'devextreme-angular/ui/data-grid';
import { DxiButtonModule } from 'devextreme-angular/ui/nested';
import { DxPopupModule } from 'devextreme-angular/ui/popup';
import { DxRangeSelectorModule } from 'devextreme-angular/ui/range-selector';
import { AuthHistoryComponent } from './auth-history.component';

describe('AuthHistoryComponent', () => {
    let component: AuthHistoryComponent;
    let fixture: ComponentFixture<AuthHistoryComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                HttpClientTestingModule,
                DxRangeSelectorModule,
                DxDataGridModule,
                DxPopupModule,
                DxiButtonModule
            ],
            declarations: [AuthHistoryComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(AuthHistoryComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
