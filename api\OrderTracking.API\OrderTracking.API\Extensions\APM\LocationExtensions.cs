﻿using System;
using APMWebDataInterface.ExampleDataModel;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Extensions.APM
{
    /// <summary>
    ///     Extension methods for the APM Location type
    /// </summary>
    public static class LocationExtensions
    {
        /// <summary>
        ///     Updates a location from a <see cref="ProjectTransportObject"/>
        /// </summary>
        /// <param name="location"></param>
        /// <param name="projectUpdate"></param>
        public static void Update(this Location location, ProjectTransportObject projectUpdate)
        {
            /*
             Currently not supporting this functionality.  Might revisit in the future

            if (projectUpdate.LocationId != project.locationId)
            {
                var pendingChange = new PendingChange<string>();
                pendingChange.Value = projectUpdate.LocationId;
                project.SetLocationId(pendingChange);
            }
            */

            if (projectUpdate.ProjectCity != null && projectUpdate.ProjectCity != location.city.CurrentValue)
                location.city.SetValue(projectUpdate.ProjectCity);

            if (projectUpdate.ProjectState != null && projectUpdate.ProjectState != location.region.CurrentValue)
                location.region.SetValue(projectUpdate.ProjectState);
        }

        /// <summary>
        ///     Updates a location from a <see cref="LocationTransportObject"/>
        /// </summary>
        /// <param name="location"></param>
        /// <param name="locationTransport"></param>
        public static void Update(this Location location, LocationTransportObject locationTransport)
        {
            if (locationTransport.Id == null)
            {
                if (locationTransport.City != null)
                    location.city.SetValue(locationTransport.City);

                if (locationTransport.Description != null)
                    location.description.SetValue(locationTransport.Description);

                if (locationTransport.Name != null)
                    location.name.SetValue(locationTransport.Name);

                if (locationTransport.Region != null)
                    location.region.SetValue(locationTransport.Region);

                if (locationTransport.Street1 != null)
                    location.street1.SetValue(locationTransport.Street1);

                if (locationTransport.Street2 != null)
                    location.street2.SetValue(locationTransport.Street2);

                if (locationTransport.PostalCode != null)
                    location.postalCode.SetValue(locationTransport.PostalCode);

                if (locationTransport.Elevation != null)
                    location.elevation.SetValue(locationTransport.Elevation);

                if (locationTransport.Latitude != null)
                    location.latitude.SetValue(locationTransport.Latitude);

                if (locationTransport.Longitude != null)
                    location.longitude.SetValue(locationTransport.Longitude);
            }
            else
            {
                if (locationTransport.Name != location.name.CurrentValue)
                    location.name.SetValue(locationTransport.Name);

                if (locationTransport.Description != location.description.CurrentValue)
                    location.description.SetValue(locationTransport.Description);

                if (locationTransport.City != location.city.CurrentValue)
                    location.city.SetValue(locationTransport.City);

                if (locationTransport.Region != location.region.CurrentValue)
                    location.region.SetValue(locationTransport.Region);

                if (locationTransport.Street1 != location.street1.CurrentValue)
                    location.street1.SetValue(locationTransport.Street1);

                if (locationTransport.Street2 != location.street2.CurrentValue)
                    location.street2.SetValue(locationTransport.Street2);

                if (locationTransport.PostalCode != location.postalCode.CurrentValue)
                    location.postalCode.SetValue(locationTransport.PostalCode);

                if (locationTransport.Longitude != null)
                {
                    if (location.longitude.CurrentValue != null)
                    {
                        if (Math.Abs(locationTransport.Longitude.Value - location.longitude.CurrentValue.Value) >
                            .00001)
                        {
                            location.longitude.SetValue(locationTransport.Longitude);
                        }
                    }
                    else
                    {
                        location.longitude.SetValue(locationTransport.Longitude);
                    }
                }

                if (locationTransport.Latitude != null)
                {
                    if (location.latitude.CurrentValue != null)
                    {
                        if (Math.Abs(locationTransport.Latitude.Value - location.latitude.CurrentValue.Value) >
                            .00001)
                        {
                            location.latitude.SetValue(locationTransport.Latitude);
                        }
                    }
                    else
                    {
                        location.latitude.SetValue(locationTransport.Latitude);
                    }
                }

                if (locationTransport.Elevation != null && location.elevation.CurrentValue != null)
                {
                    if (Math.Abs(locationTransport.Elevation.Value - location.elevation.CurrentValue.Value) >
                        .00001)
                    {
                        location.elevation.SetValue(locationTransport.Elevation);
                    }
                }
            }
        }
    }
}