﻿using System.IO;

namespace OrderTracking.API.Models
{
    /// <summary>
    /// Downloaded object from cloud storage (compatible with both GCP and Azure)
    /// </summary>
    public class CloudStorageDownloadedObject
    {
        /// <summary>
        /// Content stream
        /// </summary>
        public Stream Stream { get; set; }

        /// <summary>
        /// Downloaded Object metadata (can be GCP Object or Azure BlobClient)
        /// </summary>
        public object Object { get; set; }

        /// <summary>
        /// Content type of the downloaded object
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// Size of the downloaded object
        /// </summary>
        public long? Size { get; set; }

        /// <summary>
        /// Name of the downloaded object
        /// </summary>
        public string Name { get; set; }
    }
}
