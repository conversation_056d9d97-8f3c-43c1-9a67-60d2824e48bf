<div class="dx-card content-block responsive-paddings">

    <div class="data-selectors">
        <div class="projects-filter">
            <label for="projects"
                   class="dx-field-item-label dx-field-item-label-location-top">
                <span class="dx-field-item-label-content">
                    <span class="dx-field-item-label-text">
                        Selected Projects
                    </span>
                </span>
            </label>
            <dx-tag-box [items]="projects"
                        [placeholder]="'All Projects'"
                        [displayExpr]="'Name.Value'"
                        [searchEnabled]="true"
                        [showSelectionControls]="true"
                        [showClearButton]="true"
                        [showMultiTagOnly]="false"
                        [maxDisplayedTags]="3"
                        [applyValueMode]="'useButtons'"
                        (onValueChanged)="onProjectsValueChanged($event)">
            </dx-tag-box>
        </div>

        <dx-button class="refresh-button"
                   icon="refresh"
                   (onClick)="refresh.emit()"></dx-button>
    </div>
</div>
