# Azure DevOps Build Pipeline for Kraken Project
# Builds both Backend API and Frontend Application
 
trigger:
  branches:
    include:
      - main
      - migration/master
  paths:
    include:
      - api/*
      - wheres-my-order/*
 
 
pool:
  vmImage: 'ubuntu-latest'
 
variables:
  buildConfiguration: 'Release'
  dotNetVersion: '6.0.x'
  nodeVersion: '18.x'
 
stages:
- stage: Backend
  displayName: 'Build Backend API'
  jobs:
  - job: BuildBackend
    displayName: 'Build .NET API'
    steps:
    - task: UseDotNet@2
      displayName: 'Use .NET SDK $(dotNetVersion)'
      inputs:
        packageType: 'sdk'
        version: '$(dotNetVersion)'
 
    - task: NuGetAuthenticate@1
      displayName: 'Authenticate with NuGet Feeds'
 
    # Restore and build in dependency order
    - task: DotNetCoreCLI@2
      displayName: 'Restore ClientPortal.Shared'
      inputs:
        command: 'restore'
        projects: 'api/OrderTracking.API/OrderTracking.sln'
        feedsToUse: 'config'
        nugetConfigPath: 'api/OrderTracking.API/NuGet.Config'
        arguments: '--no-cache --verbosity detailed'
    
    - task: DotNetCoreCLI@2
      displayName: 'Build OrderTracking.API'
      inputs:
        command: 'build'
        projects: 'api/OrderTracking.API/OrderTracking.sln'
        arguments: '--configuration $(buildConfiguration) --no-restore'

     # Publish applications
    - task: DotNetCoreCLI@2
      displayName: 'Publish OrderTracking.API'
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: 'api/OrderTracking.API/OrderTracking.API/OrderTracking.API.csproj'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/api/OrderTracking.API'
        zipAfterPublish: false

    # - task: Docker@2
    #     displayName:'Docker Build & Publish'
    #   inputs:
    #       containerRegistry: ${{ variables.AzureServiceConnCI }}
    #       repository: ${{ variables.AzureContainerRegistryRepo }}
    #       command: 'buildAndPush'
    #       Dockerfile: '**/DockerFile'
    #       buildContext: '**'
    #       tags: ${{ variables.DockerTag }}

    # - task: Docker@2
    #   displayName: 'Build Backend Docker Image'
    #   inputs:
    #     command: 'build'
    #     dockerfile: 'api/Dockerfiles/Dockerfile-cpa-backend'
    #     buildContext: '.'
    #     repository: 'kraken-backend'
    #     tags: |
    #       $(Build.BuildId)
    #       latest
    # - task: Docker@2
    #   displayName: 'Build Backend Docker Image'
    #   inputs:
    #     command: 'build'
    #     dockerfile: 'api/Dockerfiles/Dockerfile-cpa-backend'
    #     buildContext: '.'
    #     repository: 'kraken-backend'
    #     tags: |
    #       e70c493f8af7
    #     arguments: >
          # --build-arg GITLAB_DEPLOY_USERNAME=dkhande1
          # --build-arg GITLAB_DEPLOY_TOKEN=1bfO0Jfb7TxGJ1QiUPOzOInlSmp8bvLa2u1EHgHYFn0friT821EOJQQJ99BGACAAAAANtmU4AAASAZDO3gLT


    - task: Docker@2
      displayName: 'Build Docker Image'
      inputs:
        command: 'build'
        dockerfile: 'api/Dockerfiles/Dockerfile-cpa-backend'
        buildContext: '.'
        repository: 'kraken-backend'
        tags: |
          $(Build.BuildId)
          latest
        arguments: |
          --build-arg FEED_ACCESSTOKEN=1bfO0Jfb7TxGJ1QiUPOzOInlSmp8bvLa2u1EHgHYFn0friT821EOJQQJ99BGACAAAAANtmU4AAASAZDO3gLT
