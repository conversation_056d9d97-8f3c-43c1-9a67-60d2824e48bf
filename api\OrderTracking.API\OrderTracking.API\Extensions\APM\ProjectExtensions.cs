﻿using System;
using System.Linq;
using APMWebDataInterface.ExampleDataModel;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Extensions.APM
{
    /// <summary>
    ///     Extension methods for the APM Project type
    /// </summary>
    public static class ProjectExtensions
    {
        /// <summary>
        ///     Removes a contact from the project
        /// </summary>
        /// <param name="project"></param>
        /// <param name="contactId"></param>
        public static void RemoveContact(this Project project, string contactId)
        {
            var projectContact = project.clientDetails.contacts.CurrentEntries.FirstOrDefault(c => c.DatabaseId == contactId);
            project.clientDetails.contacts.RemoveItem(projectContact);
        } 

        /// <summary>
        ///     Adds a contact to the project
        /// </summary>
        /// <param name="project"></param>
        /// <param name="contact"></param>
        public static Contact AddContact(this Project project, ClientContact contact)
        {
            project?.clientDetails.contacts.AddNewItem();
            var projectContact = project.clientDetails.contacts.PendingEntries.FirstOrDefault(c =>
                c.email.CurrentValue == null &&
                c.name.CurrentValue == null &&
                c.title.CurrentValue == null &&
                c.phoneNumber.CurrentValue == null
            );

            projectContact.email.SetValue(contact.ContactEmail);
            projectContact.name.SetValue(contact.ContactName);
            projectContact.phoneNumber.SetValue(contact.ContactPhoneNumber);
            projectContact.title.SetValue(contact.ContactTitle);

            return projectContact;
        }

        /// <summary>
        ///     Updates a project from a <see cref="AddRemoveProjectAsset" />
        /// </summary>
        /// <param name="project"></param>
        /// <param name="addRemoveProjectAsset"></param>
        public static void Update(this Project project, AddRemoveProjectAsset addRemoveProjectAsset)
        {
            if (addRemoveProjectAsset.AssetsToRemove != null && addRemoveProjectAsset.AssetsToRemove.Length > 0)
                foreach (var assetId in addRemoveProjectAsset.AssetsToRemove)
                    project.assetIds.RemoveValue(assetId);

            if (addRemoveProjectAsset.AssetsToAdd != null && addRemoveProjectAsset.AssetsToAdd.Length > 0)
                foreach (var assetId in addRemoveProjectAsset.AssetsToAdd)
                    project.assetIds.AddValue(assetId);
        }

        /// <summary>
        ///     Updates a project from a <see cref="ActivityUpdate" />
        /// </summary>
        /// <param name="project"></param>
        /// <param name="update"></param>
        public static void Update(this Project project, ActivityUpdate update)
        {
            if (string.IsNullOrWhiteSpace(update.DatabaseId))
            {
                var activity = project.activities.AddNewItem();

                if (update.Technician != null)
                    activity.user.SetValue(update.Technician);

                if (update.Date != null)
                    activity.date.SetValue(DateTime.Parse(update.Date));

                if (update.ClientWorkOrder != null)
                    activity.workOrderNumber.SetValue(update.ClientWorkOrder);

                var activityItem = activity.activities.FirstOrDefault(item => item.name == update.TaskType);
                if (activityItem != null)
                {
                    if (update.Count != null)
                        activityItem.count.SetValue(update.Count.Value);

                    if (update.Duration != null)
                        activityItem.duration.SetValue(update.Duration.Value);
                }
            }
            else
            {
                var activity =
                    project.activities.CurrentEntries.FirstOrDefault(act =>
                        act.DatabaseId == update.DatabaseId);
                if (activity == null)
                    throw new ProjectActivityNotFoundException(update.DatabaseId);

                var activityItem =
                    activity.activities.FirstOrDefault(item => string.Equals(item.name, update.TaskType));
                if (activityItem != null)
                {
                    if (update.Count == null && activityItem.count.CurrentValue != null)
                    {
                        activityItem.count.SetValue(null);
                    }
                    else if (update.Count != null && activityItem.count.CurrentValue == null)
                    {
                        activityItem.count.SetValue(update.Count);
                    }
                    else
                    {
                        if (update.Count != null && activityItem.count.CurrentValue != null)
                            if (Math.Abs(activityItem.count.CurrentValue.Value - update.Count.Value) >
                                .00001)
                                activityItem.count.SetValue(update.Count);
                    }

                    if (update.Duration == null && activityItem.duration.CurrentValue != null)
                    {
                        activityItem.duration.SetValue(null);
                    }
                    else if (update.Duration != null && activityItem.duration.CurrentValue == null)
                    {
                        activityItem.duration.SetValue(update.Duration);
                    }
                    else
                    {
                        if (update.Duration != null && activityItem.duration.CurrentValue != null)
                            if (Math.Abs(activityItem.duration.CurrentValue.Value - update.Duration.Value) >
                                .00001)
                                activityItem.duration.SetValue(update.Duration);
                    }
                }

                if (!string.Equals(update.PreviousTaskType, update.TaskType) && !update.IsInserting)
                {
                    var previousActivityItem =
                        activity.activities.FirstOrDefault(item => string.Equals(item.name, update.PreviousTaskType));

                    if (previousActivityItem != null)
                    {
                        previousActivityItem.count.SetValue(null);
                        previousActivityItem.duration.SetValue(null);
                    }
                }
            }
        }

        /// <summary>
        ///     Updates a project from a <see cref="ProjectTransportObject" />
        /// </summary>
        /// <param name="project"></param>
        /// <param name="projectUpdate"></param>
        public static void Update(this Project project, ProjectTransportObject projectUpdate)
        {
            // this should be done during project creation but will leave this here just in case
            if (project.clientDetails.contacts.GetEntries().Count == 0) project.clientDetails.contacts.AddNewItem();

            if (projectUpdate.ClientProjectNumber != null && projectUpdate.ClientProjectNumber !=
                project.accountingDetails.projectNumber.CurrentValue)
                project.accountingDetails.projectNumber.SetValue(projectUpdate.ClientProjectNumber);

            if (projectUpdate.ContactEmail != null && projectUpdate.ContactEmail !=
                project.clientDetails?.contacts?.GetEntries()?.First()?.email?.CurrentValue)
                project.clientDetails?.contacts?.GetEntries()?.First()?.email.SetValue(projectUpdate.ContactEmail);

            if (projectUpdate.ContactName != null && projectUpdate.ContactName !=
                project.clientDetails?.contacts?.GetEntries()?.First()?.name?.CurrentValue)
                project.clientDetails?.contacts?.GetEntries()?.First()?.name.SetValue(projectUpdate.ContactName);

            if (projectUpdate.ContactPhoneNumber != null && projectUpdate.ContactPhoneNumber !=
                project.clientDetails?.contacts?.GetEntries()?.First()?.phoneNumber.CurrentValue)
                project.clientDetails?.contacts?.GetEntries()?.First()?.phoneNumber
                    .SetValue(projectUpdate.ContactPhoneNumber);

            if (projectUpdate.ContactTitle != null && projectUpdate.ContactTitle !=
                project.clientDetails?.contacts?.GetEntries()?.First()?.title.CurrentValue)
                project.clientDetails?.contacts?.GetEntries()?.First()?.title.SetValue(projectUpdate.ContactTitle);

            if (projectUpdate.Description != null && projectUpdate.Description != project.description.CurrentValue)
                project.description.SetValue(projectUpdate.Description);

            DateTime.TryParse(project.plannedEnd.CurrentValue, out var plannedEnd);
            if (projectUpdate.PlannedEnd != null && projectUpdate.PlannedEnd != plannedEnd)
                project.plannedEnd.SetValue(projectUpdate.PlannedEnd);

            DateTime.TryParse(project.plannedStart.CurrentValue, out var plannedStart);
            if (projectUpdate.PlannedStart != null && projectUpdate.PlannedStart != plannedStart)
                project.plannedStart.SetValue(projectUpdate.PlannedStart);

            if (projectUpdate.ProjectName != null && projectUpdate.ProjectName != project.name.CurrentValue)
                project.name.SetValue(projectUpdate.ProjectName);

            if (projectUpdate.Status != null && projectUpdate.Status != project.status.CurrentValue)
                project.status.SetValue(projectUpdate.Status);

            if (projectUpdate.TeamDistrictNumber != null && projectUpdate.TeamDistrictNumber !=
                project.accountingDetails.teamDistrictNumber.CurrentValue)
                project.accountingDetails.teamDistrictNumber.SetValue(projectUpdate.TeamDistrictNumber);

            if (projectUpdate.TeamProjectNumber != null && projectUpdate.TeamProjectNumber !=
                project.accountingDetails.teamProjectNumber.CurrentValue)
                project.accountingDetails.teamProjectNumber.SetValue(projectUpdate.TeamProjectNumber);
        }

        /// <summary>
        ///     Update an APM project from a <see cref="WorkOrderTransportObject" />
        /// </summary>
        /// <param name="project"></param>
        /// <param name="update"></param>
        public static void Update(this Project project, WorkOrderTransportObject update)
        {
            var shouldUpdateTEAMProjectNumber = update.TEAMProjectNumber != null &&
                                                update.TEAMProjectNumber != project.accountingDetails.teamProjectNumber
                                                    .CurrentValue;
            if (shouldUpdateTEAMProjectNumber)
                project.accountingDetails.teamProjectNumber.SetValue(update.TEAMProjectNumber);

            var shouldUpdateAPMProjectNumber = update.APMProjectNumber != null &&
                                               update.APMProjectNumber != project.accountingDetails.apmProjectNumber
                                                   .CurrentValue;
            if (shouldUpdateAPMProjectNumber)
                project.accountingDetails.apmProjectNumber.SetValue(update.APMProjectNumber);
        }

        /// <summary>
        ///     Update an APM project from a <see cref="NewProjectTransportObject" />
        /// </summary>
        /// <param name="project"></param>
        /// <param name="newProject"></param>
        public static void Update(this Project project, NewProjectTransportObject newProject)
        {
            if (newProject.ApmNumber != null)
                project.accountingDetails.apmProjectNumber.SetValue(newProject.ApmNumber);

            if (newProject.TeamProjectNumber != null)
                project.accountingDetails.teamProjectNumber.SetValue(newProject.TeamProjectNumber);

            if (newProject.ProjectName != null)
                project.name.SetValue(newProject.ProjectName);

            if (newProject.PlannedEnd != null)
                project.plannedEnd.SetValue(newProject.PlannedEnd);

            if (newProject.PlannedStart != null)
                project.plannedStart.SetValue(newProject.PlannedStart);

            if (project.clientDetails.contacts.GetEntries().Count == 0) project.clientDetails.contacts.AddNewItem();
        }
    }

    /// <summary>
    ///     Custom exception for when a project activity is not found
    /// </summary>
    public class ProjectActivityNotFoundException : Exception
    {
        /// <summary>
        ///     Constructor for custom exception
        /// </summary>
        /// <param name="updateDatabaseId"></param>
        public ProjectActivityNotFoundException(string? updateDatabaseId) : base(
            $"Unable to find Project Activity with id: {updateDatabaseId}")
        {
            DatabaseID = updateDatabaseId;
        }

        /// <summary>
        ///     The database Id that was used to find the project activity
        /// </summary>
        public string DatabaseID { get; set; }
    }
}