﻿using System;
using ClientPortal.Shared.Models.MOS;
using Microsoft.EntityFrameworkCore;

namespace ClientPortal.Shared.Extensions
{
    public static class ModelBuilderExtensions
    {
        public static void Seed(this ModelBuilder modelBuilder)
        {
            var valeroDataSheetPackage = new DataSheetPackage
            { Id = 1, ClientName = "Valero", TEAMClientNumber = "567", Date = DateTime.UtcNow, JssNumber = "11" };
            var leakRepairHardwareSpecificationSheet = new LeakRepairHardwareSpecificationSheet
            {
                Id = 1,
                PackageId = valeroDataSheetPackage.Id,
                Site = "Midland",
                LineNo = "56",
                DistrictWorkOrderNo = "7",
                TeamCjNo = "42",
                Technician1 = "<PERSON> Ramos",
                Technician2 = "<PERSON> Turner",
                LineMaterial = true,
                MainLineSize = 3,
                BranchSize = "5",
                LineSchedule = 7,
                LineContent = "Condensate",
                LineContentPercentage = 100,
                SystemDesignTemp = 75,
                MaxSystemDesignTemp = 100,
                SystemOpTemp = 20,
                MaxSystemOpTemp = 50,
                SysDesignPressure = 15,
                SysOpPressure = 5,
                MaxSysOpPressure = 20,
                MDMT = 25,
                Grade = 2,
                Frequency = 10,
                Vibration = true,
                SourService = true,
                DefectCause = true,
                DefectType = true,
                RemainingWallThickness = 2,
                NdtDataProvided = true,
                ExpectedRemovalDate = DateTime.Now,
                ReqRepairCorrosionAllowance = 2,
                IfDosed = false,
                SuperheatedSteam = false,
                SprayTypeDesuperheater = false,
                Strongback = true,
                EnclosureQuantity = 1,
                SealantSelection = "5X",
                EnclosureMaterial = "NA",
                ClientSpecificRequirements = "NA",
                ProcessingRequirements = true,
                ApprovalInformationDeadline = DateTime.Now,
                RequestedInstallationDate = DateTime.Now,
                ApprovalEmails = "<EMAIL>",
                QualityRequirements = false,
                EngineeringPriorityLevel = true,
                PrintName = "Jeffery Smith",
                Email = "<EMAIL>",
                Telephone = 2455687,
                OrderNo = "2"
            };

            var bpDataSheetPackage = new DataSheetPackage
            {
                Id = 2,
                TEAMClientNumber = "45",
                Date = new DateTime(2008, 5, 1, 8, 30, 52),
                JssNumber = "32",
                ClientName = "BP"
            };

            var htsEngineeringDataCoverSheet = new HtsEngineeringDataCoverSheet
            {
                Id = 2,
                PackageId = bpDataSheetPackage.Id,
                DistrictProjectId = "92",
                SafetyReviewNo = "23",
                Location = "Midland",
                HtsTechSupportRep = "Steph Curry",
                BallParkNo = "47",
                ContactInfo = "6912734532",
                RequestedDeliveryDate = new DateTime(2021, 5, 1, 7, 30, 52),
                ForImmediateManufacture = true,
                PriceQuote = true,
                CalculationPackage = true,
                Routine = false,
                WaitforClientApproval = false,
                BallPark = false,
                PeStampRequired = true,
                Priority = true,
                LineSize = 6,
                Pressure = 29,
                Material = "Carbon Steel",
                LineSchedule = 4,
                Temperature = 87,
                Service = "Hot Tap",
                FlangeRating = "13",
                NuclearNaceRequired = true,
                LineLocation = true,
                DesignPressure = 50,
                FittingMaterial = "Carbon Steel",
                Quantity = 5,
                DesignTemperature = 90,
                FlangeRatingList = true,
                MDMT = 20,
                Other = "",
                CorrosionAllowance = 2,
                CutterDiameter = true,
                DesignCode = false,
                DesignFactor = true,
                RunLengthType = true,
                BranchHeightType = true,
                RunLength = 33,
                BranchHeight = 12,
                HotTap = "true",
                StockFitting = "false",
                PartNumber = "134",
                PressureRatainingSplitTee = true,
                NozzleWithRepad = false,
                SphericalTee = true,
                NozzleWithFullEncirclementSaddle = false,
                NozzleOnly = true,
                RepadOnly = false,
                WeldOLet = true,
                FullEncirclementSaddle = false,
                FullEncorclementSaddleOnly = true,
                Part1 = true,
                Part2 = false,
                Part3 = false,
                Part4 = false,
                Part5 = false,
                WeepHole = true,
                NptPortSize = true,
                NotApplicable2 = false,
                PerimeterSeal = true,
                SelfSeal = false,
                StrongbacksRequired = false,
                SealantType = true,
                ClientSpecified = false,
                Size = "4",
                BranchEndOption = true,
                WallThickness = 5,
                Standard = true,
                ClientSpecified3 = false,
                Size4 = "3"
            };
            var ds200 = new DS200
            {
                Id = 3,
                PackageId = bpDataSheetPackage.Id,
                GivenBy = "Billy Ben",
                Plant = "XTO Energy",
                Unit = "",
                CheckedBy = "Joseph Smith",
                CheckedByDate = new DateTime(2021, 5, 1),
                SurfaceCondition = "Ok",
                LineSize = "6",
                SeverityOfLeak = 5,
                ShipTo = "MSY",
                SealType = SealType.Tubing | SealType.Other,
                LineSkinTemp = "6",
                OtherSkinTemp = "41",
                C1 = 7,
                D1 = 7,
                E1 = 2,
                F1 = 1,
                H1 = 3,
                J1 = 3,
                W1 = 1,
                KbE = 4,
                LbF = 1,
                LcF = 1,
                M = 1,
                N = 1,
                P = 2,
                Q = 4,
                X1At3 = 3,
                X1At6 = 3,
                X1At9 = 3,
                X1At12 = 1,
                X2At3 = 3,
                X2At6 = 3,
                X2At9 = 7,
                X2At12 = 3,
                RemoveHandle = true,
                CutStem = false,
                StemClockPt = 1,
                CircularBonnet = true,
                SquareBonnet = false,
                OvalBonnet = false,
                XX = true,
                YY = true,
                XY = false,
                YX = false,
                LocationOfBlow = "Exterior"
            };

            // Seed data
            modelBuilder
                .Entity<DataSheetPackage>()
                .HasData(valeroDataSheetPackage, bpDataSheetPackage);
            modelBuilder
                .Entity<LeakRepairHardwareSpecificationSheet>()
                .HasData(leakRepairHardwareSpecificationSheet);
            modelBuilder
                .Entity<HtsEngineeringDataCoverSheet>()
                .HasData(htsEngineeringDataCoverSheet);
            modelBuilder
                .Entity<DS200>()
                .HasData(ds200);
        }
    }
}
