[{"id": "22c712d0-530a-47fe-90e5-3fd322532691", "old": {"roleName": "five04", "description": "Entering description for testing  purpose", "id": "User:five04", "group": "User"}, "new": null, "user": {"customerAccounts": [], "givenName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roles": ["App:Admin", "APM:Admin"], "verificationToken": null, "active": true, "acceptedTeamDisclaimerVersion": null, "lastLoginDate": {}, "selectedBusinessUnit": null, "acceptedClientDisclaimerVersion": "3", "acceptedDisclaimerDate": {}, "lastClientPortalVersion": "1.7.5", "surname": "MN", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTeamEmployee": false, "acceptedAgreement": true, "remoteMonitoringSiteIds": [], "districtIds": [], "lastVerificationDate": {}, "email": "<EMAIL>"}, "createdAt": "2024-05-10T12:31:01.16016Z", "type": "Role", "summary": {"added": {}, "deleted": {}, "updated": null}}]