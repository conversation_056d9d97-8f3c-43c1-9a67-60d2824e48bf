﻿using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Moq;
using NUnit.Framework;
using OrderTracking.API.Authorization.Handlers;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Tests.Authorization.Handlers
{
    [TestFixture]
    public class ClientAccountAssignmentAuthorizationHandlerTests
    {
        [SetUp]
        public void SetUp()
        {
            _mockService = new Mock<IUserProfilesService>(MockBehavior.Strict);
            _handler = new ClientAccountAssignmentAuthorizationHandler(_mockService.Object);
        }

        private Mock<IUserProfilesService> _mockService;
        private ClientAccountAssignmentAuthorizationHandler _handler;
        private AuthorizationHandlerContext _context;
        private UserProfile _me;
        private UserProfile _newThem;

        [Test]
        public async Task HandleAsync_ClientAccountsChangingAndSufficientRoles_ContextHasSucceeded()
        {
            _me = new UserProfile {Id = "<EMAIL>", Roles = {"WMO:DistrictManager"}};
            _newThem = new UserProfile { Id = "<EMAIL>", CustomerAccounts = { "SomeCustomer" } };
            var originalThem = new UserProfile { Id = "<EMAIL>" };
            _mockService.Setup(service => service.GetAsync(It.Is<string>(s=>s=="<EMAIL>"))).ReturnsAsync(_me);
            _mockService.Setup(service => service.GetAsync(It.Is<string>(s => s == "<EMAIL>"))).ReturnsAsync(originalThem);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_me, _newThem, new[]
            {
                new ClientAccountAssignmentRequirement()
            });
            await _handler.HandleAsync(_context);

            Assert.That(_context.HasSucceeded);
        }

        [Test]
        public async Task HandleAsync_UserIdentityNameIsNull_Fails()
        {
            _me = new UserProfile {Id = "<EMAIL>", Roles = {"WMO:DistrictManager"}};
            _newThem = new UserProfile {Id = "<EMAIL>", CustomerAccounts = {"SomeCustomer"}};
            var originalThem = new UserProfile {Id = "<EMAIL>"};
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContextNotAuthenticated(_me, originalThem,
                new[] {new ClientAccountAssignmentRequirement()});

            await _handler.HandleAsync(_context);

            Assert.That(_context.HasFailed, Is.True);
        }

        [Test]
        public async Task HandleAsync_ClientAccountsChangedButRequesterLacksPermission_Fails()
        {
            _me = new UserProfile {Id = "<EMAIL>", Roles = {"Not:Admin"}};
            _newThem = new UserProfile {Id = "<EMAIL>", CustomerAccounts = {"SomeCustomer"}};
            var originalThem = new UserProfile {Id = "<EMAIL>"};
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_me, _newThem,
                new[] {new ClientAccountAssignmentRequirement()});
            _mockService.Setup(u => u.GetAsync("<EMAIL>")).ReturnsAsync(_me);
            _mockService.Setup(u => u.GetAsync("<EMAIL>")).ReturnsAsync(originalThem);

            await _handler.HandleAsync(_context);

            Assert.That(_context.HasFailed, Is.True);
        }

        [Test]
        public async Task HandleAsync_ClientAccountsDidNotChange_Succeeds()
        {
            _me = new UserProfile {Id = "<EMAIL>", Roles = {"App:Admin"}};
            _newThem = new UserProfile {Id = "<EMAIL>"};
            var originalThem = new UserProfile {Id = "<EMAIL>"};
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_me, _newThem,
                new[] {new ClientAccountAssignmentRequirement()});
            _mockService.Setup(u => u.GetAsync("<EMAIL>")).ReturnsAsync(_me);
            _mockService.Setup(u => u.GetAsync("<EMAIL>")).ReturnsAsync(originalThem);

            await _handler.HandleAsync(_context);

            Assert.That(_context.HasSucceeded, Is.True);
        }
    }
}