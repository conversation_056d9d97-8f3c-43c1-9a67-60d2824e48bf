import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';
import { DxButtonModule } from 'devextreme-angular/ui/button';
import { DxDataGridModule } from 'devextreme-angular/ui/data-grid';
import { DxFormModule } from 'devextreme-angular/ui/form';
import { DxTagBoxModule } from 'devextreme-angular/ui/tag-box';
import { DxTooltipModule } from 'devextreme-angular/ui/tooltip';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import { ProfileModule } from '../../../profile/profile.module';
import { BreadcrumbsComponent } from '../../../shared/components';
import {
    HasRolePipe,
    HasRolesPipe,
    WorkEmailPipe
} from '../../../shared/pipes';
import { UsersService } from '../../../shared/services';
import { UserProfile } from './../../../profile/models/user-profile';
import { UsersComponent } from './users.component';

describe('UsersComponent', () => {
    let component: UsersComponent;
    let fixture: ComponentFixture<UsersComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [
                HttpClientTestingModule,
                ReactiveFormsModule,
                RouterTestingModule,
                ToastrModule.forRoot(),
                ProfileModule,
                DxButtonModule,
                DxDataGridModule,
                DxFormModule,
                DxTooltipModule,
                DxTagBoxModule
            ],
            providers: [
                {
                    provide: UsersService,
                    useValue: {
                        currentProfile$: of(new UserProfile()),
                        getAll: () => of([])
                    }
                }
            ],
            declarations: [
                UsersComponent,
                BreadcrumbsComponent,
                WorkEmailPipe,
                HasRolePipe,
                HasRolesPipe
            ]
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(UsersComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should have a list of observable districts', () => {
        expect(component.districts$).toBeTruthy();
    });

    it('should be disabled when a user is not selected', () => {
        expect(component.isUserSelected).toBeFalsy();
    });

    describe('User Selected', () => {
        const event = {
            component: undefined,
            element: undefined,
            rowElement: undefined,
            rowIndex: 1,
            row: {
                data: { id: '<EMAIL>' },
                key: undefined,
                rowIndex: undefined,
                rowType: undefined,
                values: undefined
            }
        };

        beforeEach(() => {
            component.userSelected(event);
        });

        it('should select and assign data to userEdit', () => {
            expect(component.userEdit).toBeInstanceOf(UserProfile);
            expect(component.userEdit.id).toBe('<EMAIL>');
        });

        it('Should not be disabled when a user is selected', () => {
            expect(component.isUserSelected).toBeTruthy();
        });
    });
});
