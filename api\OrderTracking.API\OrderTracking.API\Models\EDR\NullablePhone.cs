﻿using System.ComponentModel.DataAnnotations;

namespace OrderTracking.API.Models.EDR
{
    /// <summary>
    ///     Validation attribute for phone numbers.
    /// </summary>
    /// <seealso cref="ValidationAttribute" />
    public class NullablePhoneAttribute : ValidationAttribute
    {
        /// <summary>
        ///     Returns true if phone is empty or valid.
        /// </summary>
        /// <param name="value">The value of the object to validate.</param>
        /// <returns>
        ///     <see langword="true" /> if the specified value is valid; otherwise, <see langword="false" />.
        /// </returns>
        public override bool IsValid(object value)
        {
            if (value == null) return true;
            if (string.IsNullOrEmpty(value.ToString())) return true;
            var phone = new PhoneAttribute();
            return phone.IsValid(value);
        }
    }
}