import { IAttribute } from '../../../shared/models/attributes';

export interface Location {
    city: IAttribute<string>;
    comment: IAttribute<string>;
    country: IAttribute<string>;
    description: IAttribute<string>;
    elevation: IAttribute<number>;
    id: string;
    latitude: IAttribute<number>;
    longitude: IAttribute<number>;
    mediaItems: any; // TODO:
    name: IAttribute<string>;
    postalCode: IAttribute<string>;
    region: IAttribute<string>;
    street1: IAttribute<string>;
    street2: IAttribute<string>;
    businessUnitId: IAttribute<string>;
}
