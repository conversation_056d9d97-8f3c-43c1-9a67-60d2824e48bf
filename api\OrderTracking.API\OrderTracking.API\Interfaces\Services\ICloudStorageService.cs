﻿using Microsoft.AspNetCore.Http;
using OrderTracking.API.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderTracking.API.Interfaces.Services
{
    /// <summary>
    /// Cloud Storage Service Type
    /// </summary>
    public interface ICloudStorageService
    {
        /// <summary>
        /// Get signed url for accessing objects of the bucket
        /// </summary>
        /// <returns></returns>
        Task<string> GetSignedUrl();

        /// <summary>
        /// Get signed url for accessing a specific object in a bucket
        /// </summary>
        /// <param name="objectName"></param>
        /// <returns></returns>
        Task<string> GetSignedUrl(string objectName);

        /// <summary>
        /// deletes objects from the buckets
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        Task DeleteObjectAsync(string folderName, string objectName);

        /// <summary>
        /// downloads object from the bucket
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        Task<CloudStorageDownloadedObject> DownloadObjectAsync(string folderName, string objectName);

        /// <summary>
        /// List objects from the storage containers
        /// </summary>
        /// <param name="folderName"></param>
        /// <returns></returns>
        Task<IEnumerable<object>> ListObjectAsync(string folderName);

        /// <summary>
        /// Uploads objects to the storage container
        /// </summary>
        /// <param name="folderName"></param>
        /// <param name="file"></param>
        /// <returns></returns>
        Task<object> UploadAttachmentObjectAsync(string folderName, IFormFile file);
    }
}
