import {
    Component,
    EventEmitter,
    Input,
    Output,
    ViewChild
} from '@angular/core';
import cloneDeep from 'clone-deep';
import {
    DxFormComponent,
    DxGalleryComponent,
    DxPopupComponent,
    DxTextAreaComponent
} from 'devextreme-angular';
import { confirm } from 'devextreme/ui/dialog';
import { FieldDataChangedEvent } from 'devextreme/ui/form';
import { ValidationCallbackData } from 'devextreme/ui/validation_rules';
import { saveAs } from 'file-saver';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, firstValueFrom } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { get, isNullOrUndefined } from '../../../shared/helpers';
import {
    AssetDetailsPhoto,
    AssetDetailsPhotoTransport,
    AssetPath,
    FiveSeventyAssetDetails
} from '../../models';
import { ApmService } from '../../services';

@Component({
    selector: 'app-apm-asset-details-five-seventy',
    templateUrl: './apm-asset-details-five-seventy.component.html',
    styleUrls: ['./apm-asset-details-five-seventy.component.scss']
})
export class ApmAssetDetailsFiveSeventyComponent {
    private _assetDetails: FiveSeventyAssetDetails;
    private _original: any;
    private _changes: {};
    private _originalPhotoDescription: string;
    private _isEditing = new BehaviorSubject<boolean>(false);
    private _isEditingPhotoDescription = new BehaviorSubject<boolean>(false);
    private _isSaving = new BehaviorSubject<boolean>(false);

    assetPathLoadingCompleted = false;
    assetPathsArray: AssetPath = {
        allPhotos: {},
        frontPhotos: {},
        backPhotos: {},
        leftPhotos: {},
        rightPhotos: {}
    };

    popupGallerySelectedIndex = 0;

    readonly isEditingPhotoDescription$ =
        this._isEditingPhotoDescription.asObservable();
    readonly isEditing$ = this._isEditing.asObservable();
    readonly isSaving$ = this._isSaving.asObservable();

    @Output() photoDelete = new EventEmitter<AssetDetailsPhotoTransport>();
    @Output() photoDescriptionUpdate =
        new EventEmitter<AssetDetailsPhotoTransport>();

    @Input() allowEditing: boolean;
    @Input()
    set assetDetails(value: FiveSeventyAssetDetails) {
        this._isEditing.next(false);
        this._assetDetails = value;
        this.updateAssetPaths();
        this._original = cloneDeep(this._assetDetails);
        this._changes = {};
        if (this._assetDetails) this.allPhotos = this._assetDetails.allPhotos;
    }
    get assetDetails() {
        return this._assetDetails;
    }
    showPhotoPopup: boolean;
    allPhotos: AssetDetailsPhoto[];

    locations = ['On-Plot (Facility)', 'Off-Plot (Field)'];
    pipeClasses = ['Class1', 'Class2', 'Class3', 'Class4', 'N/A'];
    flangeRatings = ['150', '300', '400', '600', '900', '1500', '2500'];
    constructionMethods = [
        'Welded',
        'Threaded',
        'Slip Joint (Bell & Spigot)',
        'Flanged-Bolted',
        'other'
    ];
    inspectionCodes = ['API 510', 'API 570', 'API 653'];
    operatingStatus = ['In-Service', 'Out-Of-Service', 'Standby'];
    pipeSizes = [
        '0.5',
        '0.75',
        '1',
        '1.25',
        '1.5',
        '2',
        '2.5',
        '3',
        '3.5',
        '4',
        '4.5',
        '5',
        '6',
        '8',
        '10',
        '12',
        '14',
        '16',
        '18',
        '20',
        '24',
        '30',
        '36',
        '42'
    ];
    pipeSchedule = [
        '5',
        '10',
        '20',
        '30',
        '40',
        '50',
        '60',
        '70',
        '80',
        '100',
        '120',
        '140',
        '160',
        'STD',
        'EH',
        'DBL.EH'
    ];

    @ViewChild(DxFormComponent) form: DxFormComponent;

    validateStartGIS = (e: ValidationCallbackData) =>
        this.validationCallback('startGisLocationLat', 'startGisLocationLong');

    validateEndGIS = (e: ValidationCallbackData) =>
        this.validationCallback('endGisLocationLat', 'endGisLocationLong');

    positiveLatitude = () => 90;

    negativeLatitude = () => -90;

    positiveLongitude = () => 180;

    negativeLongitude = () => -180;

    constructor(
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService
    ) {}

    onFieldDataChanged(e: FieldDataChangedEvent) {
        if (
            e.dataField === 'startGisLocationLat' ||
            e.dataField === 'startGisLocationLong' ||
            e.dataField === 'endGisLocationLat' ||
            e.dataField === 'endGisLocationLong'
        ) {
            e.component.validate();
        }

        let value;
        if (e.value === true) value = 'Yes';
        else if (e.value === false) value = 'No';
        else value = e.value;

        if (e.dataField === 'pipes') return;
        if (e.dataField.includes(']') && e.dataField.includes('[')) {
            const split = e.dataField.split('[');
            const elementAttributeName = e.dataField.split('.').reverse()[0];
            const arrayAttributeName = split[0];
            const arrayIndex = split[1][0];
            if (!this._changes[arrayAttributeName]) {
                this._changes[arrayAttributeName] = new Array(
                    this.assetDetails[arrayAttributeName].length
                );
            }
            this._changes[arrayAttributeName][arrayIndex] = {
                ...this._changes[arrayAttributeName][arrayIndex],
                [elementAttributeName]: { value }
            };
            const component = get(this.assetDetails, e.dataField.split('.')[0]);
            this._changes[arrayAttributeName][arrayIndex].databaseId =
                component.databaseId;
        } else {
            this._changes[e.dataField] = { value };
        }
    }

    onSaveClicked(e) {
        const result = this.form.instance.validate();
        if (!result.isValid) return;
        this._isSaving.next(true);
        this._apm
            .updateFiveSeventyAssetDetails({
                ...this._changes,
                workOrderId: this.assetDetails.workOrderId,
                projectId: this.assetDetails.projectId
            })
            .pipe(finalize(() => this._isSaving.next(false)))
            .subscribe(() => {
                this._isEditing.next(false);
                this._toasts.success(
                    'Asset task data updated successfully',
                    'Update successful'
                );
                this._changes = {};
                this._original = cloneDeep(this.assetDetails);
            });
    }

    onEditClicked(e) {
        this._isEditing.next(true);
    }

    onCancelClicked(e, form: DxFormComponent) {
        this._isEditing.next(false);
        form.instance.option('formData', cloneDeep(this._original));
        this._changes = {};
    }

    onPopupGalleryContentReady(
        e,
        popup: DxPopupComponent,
        gallery: DxGalleryComponent
    ) {
        const title = gallery.selectedItem?.section;
        if (title && popup.title !== title) {
            popup.instance.option('title', title);
        }
    }

    onPopupGallerySelectionChanged(
        e,
        popup: DxPopupComponent,
        gallery: DxGalleryComponent
    ) {
        const title = gallery.selectedItem?.section;
        if (title && popup.title !== title) {
            popup.instance.option('title', title);
        }
    }

    onItemClick(e) {
        this.showPhotoPopup = true;

        this.popupGallerySelectedIndex = this.allPhotos.findIndex(
            (somePhoto) => somePhoto.photo.databaseId === e.itemData.databaseId
        );
    }

    onEditDescriptionClicked(e, currentValue: string) {
        this._isEditingPhotoDescription.next(true);
        this._originalPhotoDescription = currentValue;
    }

    onDescriptionSave(e, photoInfo: AssetDetailsPhoto, description: string) {
        const update: AssetDetailsPhotoTransport = {
            assetType: '570',
            projectId: this._assetDetails.projectId,
            workOrderId: this._assetDetails.workOrderId,
            photoDatabaseId: photoInfo.photo.databaseId,
            taskId: photoInfo.taskId,
            section: photoInfo.section,
            description
        };
        this.photoDescriptionUpdate.next(update);
        this._isEditingPhotoDescription.next(false);
    }

    onDescriptionCancel(e, editor: DxTextAreaComponent) {
        this._isEditingPhotoDescription.next(false);
        editor.instance.option('value', this._originalPhotoDescription);
    }

    async onDeletePhotoClicked(e, photoInfo: AssetDetailsPhoto) {
        const photoTransport: AssetDetailsPhotoTransport = {
            assetType: '570',
            projectId: this._assetDetails.projectId,
            workOrderId: this._assetDetails.workOrderId,
            photoDatabaseId: photoInfo.photo.databaseId,
            taskId: photoInfo.taskId,
            section: photoInfo.section
        };
        const result = await confirm(
            'Are you sure you want to remove this photo?',
            'Are you sure?'
        );
        if (result) this.photoDelete.next(photoTransport);
    }

    async onDownloadClicked(fileName: string) {
        const url = await firstValueFrom(this._apm.getSignedUrl(fileName));
        const blob = await firstValueFrom(this._apm.downloadFileFromUrl(url));
        saveAs(blob, fileName);
    }

    getAssetImage(type, blobPath) {
        return this.assetPathsArray[type][blobPath]
            ? this.assetPathsArray[type][blobPath]
            : '';
    }
    async updateAssetPaths() {
        const _assets = this._assetDetails;
        if (_assets?.frontPhotos) {
            _assets.frontPhotos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.frontPhotos[photo.blobName] = assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        }
        if (_assets?.backPhotos) {
            _assets.backPhotos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.backPhotos[photo.blobName] = assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        }

        if (_assets?.leftPhotos) {
            _assets.leftPhotos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.leftPhotos[photo.blobName] = assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        }

        if (_assets?.rightPhotos) {
            _assets.rightPhotos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.rightPhotos[photo.blobName] = assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        }
        this.assetPathLoadingCompleted = true;
    }

    private validationCallback(
        latitudeFieldName: string,
        longitudeFieldName: string
    ) {
        const startLat = this.form.instance
            .getEditor(latitudeFieldName)
            .option('value');
        const startLong = this.form.instance
            .getEditor(longitudeFieldName)
            .option('value');
        if (isNullOrUndefined(startLat) && isNullOrUndefined(startLong))
            return true;
        if (isNullOrUndefined(startLat) && !isNullOrUndefined(startLong))
            return false;
        if (!isNullOrUndefined(startLat) && isNullOrUndefined(startLong))
            return false;
        return true;
    }
}
