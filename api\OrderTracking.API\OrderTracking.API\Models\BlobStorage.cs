namespace OrderTracking.API.Models
{
    /// <summary>
    ///     Model to represent the Azure Blob Storage app settings
    /// </summary>
    public class BlobStorage
    {
        /// <summary>
        ///     Endpoint for Azure Blob Storage Container service
        /// </summary>
        public string BlobEndpoint { get; set; }

        /// <summary>
        ///     Name of account for Container service
        /// </summary>
        public string AccountName { get; set; }

        /// <summary>
        ///     The connection string to the Container service
        /// </summary>
        public string ConnectionString { get; set; }

        /// <summary>
        ///     The Key for the Container service
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        ///     The name of the folder for WMO related files
        /// </summary>
        public string WMOContainer { get; set; }

        /// <summary>
        ///     The name of the folder for EDR related files
        /// </summary>
        public string EDRContainer { get; set; }

        /// <summary>
        ///     The name of the folder for APM related files
        /// </summary>
        public string APMContainer { get; set; }

        /// <summary>
        ///     The name of the container for APM Report file (temporary blobs with anonymous read access)
        /// </summary>
        public string APMReportingContainer { get; set; }

        /// <summary>
        ///     The key for accessing apm containers
        /// </summary>
        public string APMKey{ get; set; }

        /// <summary>
        ///     The key for letting jaspersoft access the apm reporting container
        /// </summary>
        public string APMReportingKey { get; set; }

        /// <summary>
        /// APM Storage Account Name (Azure)
        /// </summary>
        public string APMStorageAccountName { get; set; }

        /// <summary>
        /// APM Blob Container Name (Azure)
        /// </summary>
        public string APMBlobContainerName { get; set; }

        /// <summary>
        /// APM Workorder Blob Container Name (Azure)
        /// </summary>
        public string APMWOBlobContainerName { get; set; }

        /// <summary>
        /// APM Workorder Storage Account Name (Azure)
        /// </summary>
        public string APMWOStorageAccountName { get; set; }

        /// <summary>
        /// Azure Key Vault Name for secrets
        /// </summary>
        public string KeyVaultName { get; set; }
    }
}