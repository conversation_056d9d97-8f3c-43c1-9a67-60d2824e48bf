import {
    IAttribute,
    PredefinedValueAttribute
} from '../../../shared/models/attributes';
import { Asset } from './asset';
import { Task } from './task';

export interface WorkOrder {
    asset: Asset;
    apmWorkOrderNumber: IAttribute<string>;
    dueDate: IAttribute<Date>;
    facilityName: IAttribute<string>;
    gisLocation: IAttribute<string>;
    id: string;
    plannedStart: IAttribute<Date>;
    plannedEnd: IAttribute<Date>;
    fieldWorkCompleted: IAttribute<Date>;
    primaryContactName: IAttribute<string>;
    primaryContactPhone: IAttribute<string>;
    projectId: string;
    status: PredefinedValueAttribute;
    tasks: Task[];
    publishedTime: any;
    applicableDamage: IAttribute<string>;
    inspectionSummary: IAttribute<string>;
    jobScope: IAttribute<string>;
    recommendations: IAttribute<string>;
    releventIndications: IAttribute<string>;
}
