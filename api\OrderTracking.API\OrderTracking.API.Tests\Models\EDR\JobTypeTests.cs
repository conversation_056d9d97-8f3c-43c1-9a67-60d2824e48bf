﻿using System.Collections.Generic;
using System.Linq;
using NUnit.Framework;
using OrderTracking.API.Models.EDR;

namespace OrderTracking.API.Tests.Models.EDR
{
    [TestFixture]
    public class JobTypeTests
    {
        [Test]
        public void DetectChanges_EmptyJobTypes_HasChangesIsFalse()
        {
            var originalJobTypes = new List<JobType>();
            var newJobTypes = new List<JobType>();

            var summary = JobType.DetectEquipmentChanges(originalJobTypes, newJobTypes);

            Assert.That(summary.HasChanges, Is.False);
        }

        [Test]
        public void DetectChanges_EmptyOriginalJobTypes_HasChangesIsTrue()
        {
            var originalJobTypes = new List<JobType>();
            var newJobTypes = new List<JobType> { new() };

            var summary = JobType.DetectEquipmentChanges(originalJobTypes, newJobTypes);

            Assert.That(summary.HasChanges, Is.True);
        }

        [Test]
        public void DetectChanges_EmptyNewJobTypes_HasChangesIsTrue()
        {
            var originalJobTypes = new List<JobType> { new() };
            var newJobTypes = new List<JobType>();

            var summary = JobType.DetectEquipmentChanges(originalJobTypes, newJobTypes);

            Assert.That(summary.HasChanges, Is.True);
        }

        [Test]
        public void DetectChanges_NonEmptyJobTypesWithNoChanges_HasChangesIsFalse()
        {
            var originalJobTypes = new List<JobType> { new() { JSSNumber = "123" } };
            var newJobTypes = new List<JobType> { new() { JSSNumber = "123" } };

            var summary = JobType.DetectEquipmentChanges(originalJobTypes, newJobTypes);

            Assert.That(summary.HasChanges, Is.False);
        }

        [Test]
        public void DetectChanges_FieldChangesPresent_HasChangesIsTrue()
        {
            var originalJobTypes = new List<JobType>
            {
                new()
                {
                    Id = 1,
                    JSSNumber = "123",
                    FlangeRating = "great"
                }
            };
            var newJobTypes = new List<JobType>
            {
                new()
                {
                    Id = 1,
                    JSSNumber = "123",
                    FlangeRating = "just fine"
                }
            };

            var summary = JobType.DetectEquipmentChanges(originalJobTypes, newJobTypes);

            Assert.That(summary.HasChanges, Is.True);
        }

        [Test]
        public void DetectChanges_FieldChangesPresent_VariancesPresent()
        {
            var originalJobTypes = new List<JobType>
            {
                new()
                {
                    Id = 1,
                    JSSNumber = "123",
                    FlangeRating = "great"
                }
            };
            var newJobTypes = new List<JobType>
            {
                new()
                {
                    Id = 1,
                    JSSNumber = "123",
                    FlangeRating = "just fine"
                }
            };

            var summary = JobType.DetectEquipmentChanges(originalJobTypes, newJobTypes);

            Assert.That(summary.JSSNumbers, Does.Contain("123"));
            Assert.That(summary["123"].Count, Is.EqualTo(1));
            Assert.That(summary["123"].First().PropertyName, Is.EqualTo(nameof(JobType.FlangeRating)));
            Assert.That(summary["123"].First().OldValue, Is.EqualTo("great"));
            Assert.That(summary["123"].First().NewValue, Is.EqualTo("just fine"));
        }

        [Test]
        public void DetectChanges_JSSNumberChanged_VarianceIncludesNewJSSNumberNotOldJSSNumber()
        {
            var originalJobTypes = new List<JobType> { new() { Id = 1, JSSNumber = "123" } };
            var newJobTypes = new List<JobType> { new() { Id = 1, JSSNumber = "321" } };

            var summary = JobType.DetectEquipmentChanges(originalJobTypes, newJobTypes);

            Assert.That(summary.JSSNumbers, Does.Contain("321"));
            Assert.That(summary.JSSNumbers, Does.Not.Contain("123"));
        }

        [Test]
        public void CheckRevertedStatus_RevertedStatus_ReturnsTrue()
        {
            // Arrange
            var originalJobType =  new JobType { Id = 1, JSSNumber = "123", RequestStatus = "Shipped"} ;
            var newJobType =  new JobType { Id = 1, JSSNumber = "321",RequestStatus = "Ready for Pickup"} ;

            // Act / Assert
            Assert.That(newJobType.CheckRevertedStatus(originalJobType), Is.True);
        }
        
        [Test]
        public void CheckRevertedStatus_NotRevertedStatus_ReturnsFalse()
        {
            // Arrange
            var newJobType = new JobType { Id = 1, JSSNumber = "123", RequestStatus = "Shipped" };
            var originalJobType = new JobType { Id = 1, JSSNumber = "321", RequestStatus = "Ready for Pickup" };

            // Act / Assert
            Assert.That(newJobType.CheckRevertedStatus(originalJobType), Is.False);
        }

        [TestCase("Hot Tap", true)]
        [TestCase("Pipeline Hot Tap", true)]
        [TestCase("Line Stop", false)]
        [TestCase("Pipeline Line Stop", false)]
        [TestCase("Sure Stop", false)]
        public void InitialDescription_ContainsTapSizeOrNot(string type, bool shouldContain)
        {
            // Arrange
            const string tapSize = "[TAP-SIZE]";
            var jobType = new JobType {Type = type, TapSize = tapSize};

            // Act
            var description = JobType.InitialDescription(jobType);

            // Assert
            Assert.That(description, shouldContain ? Does.Contain(tapSize) : Does.Not.Contain(tapSize));
        }
    }
}