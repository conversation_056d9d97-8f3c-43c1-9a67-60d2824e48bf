﻿using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class NewTask
    {
        [Required] [JsonProperty("taskType")] public string TaskType { get; set; }

        [JsonProperty("clientWorkOrderDescription")]
        public string ClientWorkOrderDescription { get; set; }

        [Required]
        [Json<PERSON>roperty("clientWorkOrderNumber")]
        public string ClientWorkOrderNumber { get; set; }

        [<PERSON>son<PERSON>roperty("leadTechnician")] public string LeadTechnician { get; set; }

        [JsonProperty("plannedEnd")] public DateTime? PlannedEnd { get; set; }

        [JsonProperty("taskAssignees")] public string[] TaskAssignees { get; set; }

        [JsonProperty("plannedStart")] public DateTime? PlannedStart { get; set; }

        [JsonProperty("status")] public string Status { get; set; }

        [Json<PERSON>roperty("supervisor")] public string Supervisor { get; set; }

        [JsonProperty("workOrderId")] public string WorkOrderID { get; set; }

        [JsonProperty("projectId")] public string ProjectID { get; set; }

        [JsonProperty("purchaseOrderAFE")] public string PurchaseOrderAFE { get; set; }

        [JsonProperty("clientCostCode")] public string ClientCostCode { get; set; }

        [JsonProperty("dueDate")] public DateTime? DueDate { get; set; }
    }
}