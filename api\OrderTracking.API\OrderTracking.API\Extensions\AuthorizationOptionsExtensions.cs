﻿using System;
using Microsoft.AspNetCore.Authorization;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Requirements;

namespace OrderTracking.API.Extensions
{
    /// <summary>
    ///     Extension methods to simply Startup
    /// </summary>
    public static class AuthorizationOptionsExtensions
    {
        private static AuthorizationOptions AddRoleBasedPolicy(this AuthorizationOptions options, string policyName,
            params string[] roles)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            options.AddPolicy(policyName, policy => policy.Requirements.Add(new ClientPortalRoleRequirement(roles)));

            return options;
        }

        /// <summary>
        ///     Register WMO specific policies
        /// </summary>
        /// <param name="options"></param>
        public static AuthorizationOptions AddWMOPolicies(this AuthorizationOptions options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            options.AddRoleBasedPolicy("WMO:Edit", "App:Admin", "WMO:Admin", "WMO:Edit", "WMO:ManufacturingUser",
                "WMO:EngineeringUser");
            options.AddPolicy("AssignDistrict",
                policy => policy.Requirements.Add(new DistrictAssignmentRequirement()));
            options.AddPolicy("AssignClientAccount",
                policy => policy.Requirements.Add(new ClientAccountAssignmentRequirement()));

            return options;
        }

        /// <summary>
        ///     Register CredoSoft-specific policies
        /// </summary>
        /// <param name="options"></param>
        /// <returns></returns>
        public static AuthorizationOptions AddCredoSoftPolicies(this AuthorizationOptions options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            options.AddRoleBasedPolicy("CREDO", "App:Admin", "AIMaaS:Edit", "AIMaaS:Admin", "AIMaaS:View",
                "AIMaaS:Demo");

            options.AddPolicy("AssignAssetManagementSite",
                policy => policy.Requirements.Add(new AssetManagementSiteAssignmentRequirement()));

            return options;
        }

        /// <summary>
        ///     Register PipelineInspection-specific policies
        /// </summary>
        /// <param name="options"></param>
        /// <returns></returns>
        public static AuthorizationOptions AddPipelineInspectionPolicies(this AuthorizationOptions options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            options.AddRoleBasedPolicy("PIA", "App:Admin", "PIA:Admin", "PIA:Edit", "PIA:View", "PIA:Scheduler",
                    "PIA:Client", "PIA:Technician", "PIA:QA")
                .AddRoleBasedPolicy("GetPipelineClients", "App:Admin", "PIA:Admin", "PIA:View", "PIA:Client",
                    "PIA:Technician", "PIA:QA", "PIA:Scheduler")
                .AddRoleBasedPolicy("GetPipelineProjects", "App:Admin", "PIA:Scheduler", "PIA:Client", "PIA:View",
                    "PIA:Technician", "PIA:QA", "PIA:Admin")
                .AddRoleBasedPolicy("GetPipelineDigsites", "App:Admin", "PIA:Scheduler", "PIA:Client", "PIA:View",
                    "PIA:Technician", "PIA:QA", "PIA:Admin")
                .AddRoleBasedPolicy("CreatePipelineClients", "App:Admin", "PIA:Admin")
                .AddRoleBasedPolicy("CreatePipelineDigsites", "App:Admin", "PIA:Scheduler")
                .AddRoleBasedPolicy("CreatePipelineProjects", "App:Admin", "PIA:Scheduler")
                .AddRoleBasedPolicy("UpdatePipelineDigsites", "App:Admin", "PIA:Scheduler")
                .AddRoleBasedPolicy("UpdatePipelineProjects", "App:Admin", "PIA:Scheduler")
                .AddRoleBasedPolicy("UpdatePipelineClients", "App:Admin", "PIA:Admin")
                .AddRoleBasedPolicy("GetPipelineInspections", "App:Admin", "PIA:View", "PIA:Client", "PIA:Technician",
                    "PIA:QA", "PIA:Admin", "PIA:Scheduler")
                .AddRoleBasedPolicy("GetPipelineInspectionMedia", "App:Admin", "PIA:View", "PIA:Client",
                    "PIA:Technician", "PIA:QA")
                .AddRoleBasedPolicy("UpdatePipelineInspectionData", "App:Admin", "PIA:Technician", "PIA:QA")
                .AddRoleBasedPolicy("GetPipelineInspectionReports", "App:Admin", "PIA:QA");

            return options;
        }


        /// <summary>
        ///     Add core policies, meant to be available in multiple feature contexts
        /// </summary>
        /// <param name="options"></param>
        /// <returns></returns>
        public static AuthorizationOptions AddCorePolicies(this AuthorizationOptions options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            options.AddRoleBasedPolicy("App:QA", "App:QA")
                .AddRoleBasedPolicy("App:Admin", "App:Admin");
            options.AddPolicy("UserIsActive", policy =>
                policy.Requirements.Add(new UserIsActiveRequirement()));
            options.AddPolicy("IsTeamEmployee",
                policy => policy.Requirements.Add(new IsTeamEmployeeRequirement()));
            options.AddPolicy("UserIsModuleAdmin",
                policy => policy.Requirements.Add(new UserIsModuleAdminRequirement()));

            return options;
        }

        /// <summary>
        ///     Register Bolting Calc/Flange Manager specific policies
        /// </summary>
        /// <param name="options"></param>
        public static AuthorizationOptions AddCalculationPolicies(this AuthorizationOptions options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            options.AddRoleBasedPolicy("JOINTS:Edit", "App:Admin", "JOINTS:Edit");

            return options;
        }

        /// <summary>
        ///     Register APM specific policies
        /// </summary>
        /// <param name="options"></param>
        public static AuthorizationOptions AddApmPolicies(this AuthorizationOptions options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            options.AddRoleBasedPolicy("APM - Edit", "App:Admin", "APM:Edit", "APM:Admin")
                .AddRoleBasedPolicy("APM - View", "App:Admin", "APM:Edit", "APM:Admin", "APM:QAQC", "APM:View")
                .AddRoleBasedPolicy("APM - ChangePublishedState", "App:Admin", "APM:QAQC", "APM:Admin")
                .AddRoleBasedPolicy("APM - ManageClients", "App:Admin", "APM:Admin");

            return options;
        }


        /// <summary>
        ///     Register CRD specific policies
        /// </summary>
        /// <param name="options"></param>
        public static AuthorizationOptions AddCRDPolicies(this AuthorizationOptions options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            options.AddRoleBasedPolicy("CRD:Admin", "App:Admin", "CRD:Admin");

            return options;
        }
    }
}