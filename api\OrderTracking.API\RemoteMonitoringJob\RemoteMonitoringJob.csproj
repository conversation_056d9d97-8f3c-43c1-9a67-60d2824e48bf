﻿<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <UserSecretsId>dotnet-RemoteMonitoringJob-25D99AC3-8DF3-445B-8619-3E9D3BA39D9E</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AngleSharp" Version="0.17.1" />
    <PackageReference Include="CsvHelper" Version="27.2.1" />
    <PackageReference Include="FluentMigrator" Version="3.3.2" />
    <PackageReference Include="FluentMigrator.Extensions.SqlServer" Version="3.3.2" />
    <PackageReference Include="FluentMigrator.Runner" Version="3.3.2" />
	<!-- Replaced Google Cloud Diagnostics with Azure Application Insights -->
	<PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.21.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
    <PackageReference Include="Z.Dapper.Plus" Version="4.0.29" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ClientPortal.Shared\ClientPortal.Shared.csproj" />
  </ItemGroup>
</Project>
