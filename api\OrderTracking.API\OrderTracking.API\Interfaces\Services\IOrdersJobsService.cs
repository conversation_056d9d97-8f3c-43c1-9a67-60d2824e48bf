﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;

namespace OrderTracking.API.Interfaces.Services
{
    /// <summary>
    ///     Interface for OrdersJobsService
    /// </summary>
    public interface IOrdersJobsService
    {
        /// <summary>
        ///     Returns a queryable object for ETL runs (order jobs)
        /// </summary>
        /// <returns></returns>
        IQueryable<OrdersJob> GetJobs();
        
        /// <summary>
        ///     Get multiple OrdersJobs
        /// </summary>
        /// <param name="queryString"></param>
        /// <returns></returns>
        Task<IEnumerable<OrdersJob>> GetItemsAsync(string queryString);
    }
}