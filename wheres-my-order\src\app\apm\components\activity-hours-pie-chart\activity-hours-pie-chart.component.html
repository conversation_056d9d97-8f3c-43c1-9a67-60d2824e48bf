<div id="activity-hours-card"
     class="dx-card responsive-paddings content-block pie-container"
     style="position: relative">
    <dx-load-indicator *ngIf="loading"
                       id="large-indicator"
                       style="position: absolute; left: calc(50% - 30px); top: calc(50% - 30px)"
                       height="60"
                       width="60"></dx-load-indicator>

    <div>
        <dx-pie-chart [dataSource]="activitySummary"
                      [palette]="'Soft Pastel'"
                      [resolveLabelOverlapping]="'shift'">
            <dxo-title [text]="'Activity Hours'"></dxo-title>
            <dxi-series [argumentField]="'workType'"
                        [valueField]="'hours'">
                <dxo-label [visible]="true"
                           [customizeText]="customizeLabel">
                    <dxo-connector [visible]="true">
                    </dxo-connector>
                </dxo-label>
            </dxi-series>
            <dxo-legend [visible]="true"
                        [horizontalAlignment]="'center'"
                        [verticalAlignment]="'bottom'"></dxo-legend>
        </dx-pie-chart>
    </div>
    <div>
        <p class="total-hours">Total Activity Hours: {{totalHours$ | async}}</p>
    </div>
</div>
