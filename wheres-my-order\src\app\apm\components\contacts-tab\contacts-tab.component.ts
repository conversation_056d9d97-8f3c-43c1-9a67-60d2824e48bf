import { ChangeDetectorRef, Component, Input, ViewChild } from '@angular/core';
import { DxFormComponent, DxListComponent } from 'devextreme-angular';
import DataSource from 'devextreme/data/data_source';
import { confirm } from 'devextreme/ui/dialog';
import { ToastrService } from 'ngx-toastr';
import { Contact, ProjectVm } from '../../models';
import { ApmService } from '../../services';

@Component({
    selector: 'app-contacts-tab',
    templateUrl: './contacts-tab.component.html',
    styleUrls: ['./contacts-tab.component.scss']
})
export class ContactsTabComponent {
    private _selectedProject: ProjectVm;
    private _contacts: Contact[];

    @ViewChild(DxListComponent) dxList: DxListComponent;
    @ViewChild('editContactForm') editContactForm: DxFormComponent;

    @Input() allowEditing: boolean;
    @Input()
    set selectedProject(proj: ProjectVm) {
        this._selectedProject = proj;
    }
    get selectedProject() {
        return this._selectedProject;
    }
    @Input()
    set contacts(contacts: Contact[]) {
        this.contactDataSource = new DataSource({
            store: { type: 'array', data: contacts, key: 'databaseId' }
        });
        this._contacts = contacts;
    }
    get contacts() {
        return this._contacts;
    }

    editPopupVisible: boolean;
    editContact: Contact = {
        contactEmail: null,
        contactName: null,
        contactPhoneNumber: null,
        contactTitle: null,
        databaseId: null
    };
    newContact: boolean;

    constructor(
        private _changeDetector: ChangeDetectorRef,
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService
    ) {}

    contactTabComponent = this;
    contactDataSource = new DataSource({
        store: { type: 'array', key: 'databaseId' }
    });
    selectedContact: Contact;

    selectionChanged(e) {
        this.selectedContact = e?.addedItems[0];
        this._changeDetector.detectChanges();
    }

    editClicked() {
        this.editContact = { ...this.selectedContact };
        this.newContact = false;
        this.editPopupVisible = true;
    }

    deleteClicked() {
        const result = confirm(
            `Are you sure you want to delete contact: ${this.selectedContact.contactName}?`,
            'Confirm Delete'
        ).then((dialogResult: any) => {
            if (dialogResult) {
                this.deleteContact();
            }
        });
    }

    createClicked() {
        this.editPopupVisible = true;
        this.newContact = true;
        this.editContact = new Contact();
    }

    cancelEditClicked() {
        this.editPopupVisible = false;
    }
    saveClicked() {
        if (this.editContactForm.instance.validate().isValid) {
            if (this.newContact) {
                this.createNewContact();
            } else {
                this.updateContact();
            }
            this.editPopupVisible = false;
        }
    }

    private updateContact() {
        this._apm
            .updateClientContact(this.editContact, this.selectedProject.id)
            .subscribe((c: any) => {
                this._toasts.success(
                    'Changes to Contact successfully saved',
                    'Contact updated'
                );
                this.selectedContact.contactEmail = c.email.currentValue;
                this.selectedContact.contactName = c.name.currentValue;
                this.selectedContact.contactPhoneNumber =
                    c.phoneNumber.currentValue;
                this.selectedContact.contactTitle = c.title.currentValue;
                const apmIndex = this.getAPMIndex();
                if (apmIndex > -1) {
                    this.selectedProject.contacts.currentEntries.splice(
                        apmIndex,
                        1,
                        c
                    );
                }
            });
    }

    private getAPMIndex() {
        return this.selectedProject.contacts.currentEntries.indexOf(
            this.selectedProject.contacts.currentEntries.find(
                (contact) =>
                    contact.databaseId == this.selectedContact.databaseId
            )
        );
    }

    private createNewContact() {
        this._apm
            .createClientContact(this.editContact, this.selectedProject.id)
            .subscribe((c: any) => {
                this._toasts.success(
                    'New Contact Added to project',
                    'Contact updated'
                );
                this.contacts.push({
                    contactEmail: c.email?.currentValue,
                    contactName: c.name?.currentValue,
                    contactPhoneNumber: c.phoneNumber?.currentValue,
                    contactTitle: c.title?.currentValue,
                    databaseId: c.databaseId
                } as Contact);
                this.selectedProject.contacts.currentEntries.push(c);
                this.contactDataSource = new DataSource({
                    store: {
                        type: 'array',
                        data: this.contacts,
                        key: 'databaseId'
                    }
                });
            });
    }

    private deleteContact() {
        this._apm
            .deleteClientContact(
                this.selectedContact.databaseId,
                this.selectedProject.id
            )
            .subscribe(() => {
                this._toasts.success(
                    'Contact Removed from Project',
                    'Contact Deleted'
                );

                const index = this.contacts.indexOf(this.selectedContact);
                if (index > -1) {
                    this.contacts.splice(index, 1);
                }
                const apmIndex = this.getAPMIndex();
                if (apmIndex > -1) {
                    this.selectedProject.contacts.currentEntries.splice(
                        index,
                        1
                    );
                }
                this.selectedContact = null;
                this.contactDataSource = new DataSource({
                    store: {
                        type: 'array',
                        data: this.contacts,
                        key: 'databaseId'
                    }
                });
            });
    }
}
