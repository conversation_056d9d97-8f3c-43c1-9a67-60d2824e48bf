import { Component, Input, OnInit } from '@angular/core';
import { AssetInspection } from '../../models';

@Component({
  selector: 'app-general-inspection-details',
  templateUrl: './general-inspection-details.component.html',
  styleUrls: ['./general-inspection-details.component.scss']
})
export class GeneralInspectionDetailsComponent implements OnInit {
  @Input() generalinspection: AssetInspection;
  constructor() { }

  ngOnInit(): void {
  }
  convertHtmlToText(html: string): string {
    if (html == "null" || html==null) {
        return '';
      }
    const doc = new DOMParser().parseFromString(html, 'text/html');
    return doc.documentElement.textContent ?? ' ';
  }
  getResultClass(result: string): string {
    switch (result) {
      case '1 - No Concern':
        return 'no-concern';
      case '2 - Minor Concern':
        return 'minor-concern';
      case '3 - Moderate Concern':
        return 'moderate-concern';
      case '4 - Major Concern':
        return 'major-concern';
      case '5 - Severe':
        return 'severe';
      case 'Pending Review':
        return 'pending-review';
      default:
        return ''; // Default or unhandled case
    }
  }
  
  
}
