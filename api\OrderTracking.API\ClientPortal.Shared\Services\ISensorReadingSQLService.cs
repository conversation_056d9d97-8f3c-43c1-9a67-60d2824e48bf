﻿using System.Collections.Generic;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Models.RemoteMonitoring;

namespace ClientPortal.Shared.Services
{
    /// <summary>
    ///     Service class for making SQL operations on SensorReadings
    /// </summary>
    public interface ISensorReadingsSQLService
    {
        void MergeSensorReadings(IEnumerable<SensorReading> sensorReadings);
        Task<IEnumerable<SensorReading>> GetSensorReadingsAsync();
        Task<IEnumerable<SensorReading>> GetSensorReadingsForUserAsync(UserProfile user);
    }
}