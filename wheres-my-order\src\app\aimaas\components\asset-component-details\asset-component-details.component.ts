import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-asset-component-details',
  templateUrl: './asset-component-details.component.html',
  styleUrls: ['./asset-component-details.component.scss']
})
export class AssetComponentDetailsComponent implements OnInit {

  @Input() assetComponent: any; 
  constructor() {}

  ngOnInit(): void {
  }
  calculateLimitThickness(component : any): string{
    
      const nominalThickness = parseFloat(component.nominalthickness);
      const corrosionAllowance = parseFloat(component.corrosionallowance);

      const limitThickness =
          isNaN(nominalThickness) || isNaN(corrosionAllowance)
              ? ' '
              : (nominalThickness - corrosionAllowance)
                    .toFixed(3)
                    .toString() + ' in';

      return limitThickness;
           
  }
  convertE(value){
    if(value!= null){
      return (
        parseFloat(value.replace(/\\/g, '').replace(/in/g, '')).toFixed(3) + ' in'
      );
    } else{
      return('0.000 in')
    }
      
  }
  convertHtmlToText(html: string): string {
    if (html == null) {
      return ' ';
    }
    const doc = new DOMParser().parseFromString(html, 'text/html');
    return doc.documentElement.textContent ?? ' ';
  }
}
