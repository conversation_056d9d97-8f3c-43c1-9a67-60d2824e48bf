﻿using System;
using System.Linq;
using System.Security;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using OrderTracking.API.Models;
using OrderTracking.API.Models.PowerBI;

namespace OrderTracking.API.Services.PowerBI
{
    public class AadService
    {
        private readonly IOptions<AzureAd> azureAd;

        public AadService(IOptions<AzureAd> azureAd)
        {
            this.azureAd = azureAd;
        }

        /// <summary>
        ///     Generates and returns Access token
        /// </summary>
        /// <returns>AAD token</returns>
        public async Task<string> GetAccessToken()
        {
            AuthenticationResult authenticationResult = null;
            if (azureAd.Value.AuthenticationMode.Equals("masteruser", StringComparison.InvariantCultureIgnoreCase))
            {
                // Create a public client to authorize the app with the AAD app
                var clientApp = PublicClientApplicationBuilder.Create(azureAd.Value.ClientId)
                    .WithAuthority(azureAd.Value.AuthorityUri).Build();
                var userAccounts = clientApp.GetAccountsAsync().Result;
                try
                {
                    // Retrieve Access token from cache if available
                    authenticationResult = clientApp
                        .AcquireTokenSilent(azureAd.Value.Scope, userAccounts.FirstOrDefault()).ExecuteAsync().Result;
                }
                catch (MsalUiRequiredException)
                {
                    var password = new SecureString();
                    foreach (var key in azureAd.Value.PbiPassword) password.AppendChar(key);
                    authenticationResult = clientApp
                        .AcquireTokenByUsernamePassword(azureAd.Value.Scope, azureAd.Value.PbiUsername, password)
                        .ExecuteAsync().Result;
                }
            }

            // Service Principal auth is the recommended by Microsoft to achieve App Owns Data Power BI embedding
            else if (azureAd.Value.AuthenticationMode.Equals("serviceprincipal",
                         StringComparison.InvariantCultureIgnoreCase))
            {
                // For app only authentication, we need the specific tenant id in the authority url
                var tenantSpecificUrl = azureAd.Value.AuthorityUri.Replace("organizations", azureAd.Value.TenantId);

                // Create a confidential client to authorize the app with the AAD app
                var clientApp = ConfidentialClientApplicationBuilder
                    .Create(azureAd.Value.ClientId)
                    .WithClientSecret(azureAd.Value.ClientSecret)
                    .WithAuthority(tenantSpecificUrl)
                    .Build();
                // Make a client call if Access token is not available in cache
                authenticationResult = await clientApp.AcquireTokenForClient(azureAd.Value.Scope).ExecuteAsync();
            }

            return authenticationResult.AccessToken;
        }
    }
}