.image {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    height: 100%;
    width: 100%;
}

::ng-deep .dx-gallery .dx-gallery-nav-button-prev,
::ng-deep .dx-gallery .dx-gallery-nav-button-next {
    width: 0% !important;
}

.popup-button-div {
    display: flex;
    flex-direction: row-reverse;
    position: absolute;
    bottom: 2rem;
    right: 1rem;
}

.popup-outer-div {
    display: flex;
    align-items: center;
    height: 100%;
}

.gallery-item-outer-div {
    width: 100%;
    display: flex;
    flex-direction: row;
}

.gallery-image {
    max-height: 100%;
    max-width: 100%;
    display: block;
    margin: auto;
}

.popup-right-div {
    text-align: left;
    display: flex;
    flex-direction: column;
    width: 45%;
    margin-right: 5%;
}
