---
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: run-clientportal-frontend-prod-usc1
  annotations:
    run.googleapis.com/description: run-clientportal-frontend-prod-usc1
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: '1'
        autoscaling.knative.dev/maxScale: '2'
        run.googleapis.com/startup-cpu-boost: 'true'
    spec:
      serviceAccountName: <EMAIL>
      containers:
      - name: run-clientportal-frontend-prod-usc1
        image: us-docker.pkg.dev/oneinsight-stage-051d/ar-dotnet-us/cpa-frontend:latest
        ports:
        - name: http1
          containerPort: 80
        env:
        - name: GOOGLE_CLOUD_PROJECT
          value: oneinsight-prod-7998
        resources:
          limits:
            cpu: 1000m
            memory: 1Gi
        startupProbe:
          timeoutSeconds: 240
          periodSeconds: 240
          failureThreshold: 1
          tcpSocket:
            port: 80

     