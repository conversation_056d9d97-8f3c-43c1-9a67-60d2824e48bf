﻿using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class LeakReportInfo
    {
        [JsonProperty("equipmentID")]
        public LeakReportField<string> EquipmentID { get; set; }
        [JsonProperty("equipmentDescription")]
        public LeakReportField<string> EquipmentDescription { get; set; }
        [JsonProperty("equipmentIDAtLineStart")]
        public LeakReportField<string> EquipmentIDAtLineStart { get; set; }
        [JsonProperty("equipmentIDAtLineEnd")]
        public LeakReportField<string> EquipmentIDAtLineEnd { get; set; }
        [JsonProperty("pipeSize")]
        public LeakReportField<string> PipeSize { get; set; }
        [JsonProperty("pipeSchedule")]
        public LeakReportField<string> PipeSchedule { get; set; }
        [JsonProperty("processService")]
        public LeakReportField<string> ProcessService { get; set; }
        [JsonProperty("pipeCover")]
        public LeakReportField<string> PipeCover { get; set; }
        [JsonProperty("distanceBetweenTieInPoints")]
        public LeakReportField<double?> DistanceBetweenTieInPoints { get; set; }
        [JsonProperty("corrosionType")]
        public LeakReportField<string[]> CorrosionType { get; set; }
        [JsonProperty("estimatedLossRate")]
        public LeakReportField<int?> EstimatedLossRate { get; set; }
        [JsonProperty("existingClampCount")]
        public LeakReportField<int?> ExistingClampCount { get; set; }
        [JsonProperty("featureFittingCount")]
        public LeakReportField<int?> FeatureFittingCount { get; set; }
        [JsonProperty("observationSummary")]
        public LeakReportField<string> ObservationSummary { get; set; }
        [JsonProperty("affectedLength")]
        public LeakReportField<int?> AffectedLength { get; set; }
    }
}