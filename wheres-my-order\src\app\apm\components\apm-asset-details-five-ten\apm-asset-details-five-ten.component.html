<div *ngIf="!assetDetails">
    <dx-load-indicator style="position: relative; left: 50%;"
                       id="large-indicator"
                       height="60"
                       width="60"></dx-load-indicator>
</div>
<div *ngIf="assetDetails"
     class="content-block repsonse-paddings">
    <dx-form [formData]="assetDetails"
             [readOnly]="(isEditing$ | async) === false"
             (onFieldDataChanged)="onFieldDataChanged($event)">
        <dxi-item itemType="group"
                  caption="Identification"
                  [colCount]="3">
            <dxi-item dataField="identificationName">
                <dxo-label text="Name"></dxo-label>
            </dxi-item>
            <dxi-item dataField="identificationNumber">
                <dxo-label text="Number or ID"></dxo-label>
            </dxi-item>
            <dxi-item dataField="assetType"></dxi-item>
            <dxi-item dataField="equipmentDescription"></dxi-item>
            <dxi-item dataField="lastKnownInspectionDate"
                      editorType="dxDateBox"></dxi-item>
            <dxi-item dataField="location"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: overallLocationOptions}">
            </dxi-item>
            <dxi-item itemType="group"
                      caption="GIS Location"
                      [colCount]="2">
                <dxi-item dataField="gisLocationLat"
                          editorType="dxNumberBox">
                    <dxo-label text="Latitude"></dxo-label>
                    <dxi-validation-rule type="custom"
                                         [reevaluate]="true"
                                         [validationCallback]="validateGIS"
                                         message="Both latitude and longitude must be provided">
                    </dxi-validation-rule>
                    <dxi-validation-rule type="compare"
                                         comparisonType="<="
                                         [comparisonTarget]="positiveLatitude"
                                         message="Latitude must be less than or equal to 90">
                    </dxi-validation-rule>
                    <dxi-validation-rule type="compare"
                                         comparisonType=">="
                                         [comparisonTarget]="negativeLatitude"
                                         message="Latitude must be greater than or equal to -90">
                    </dxi-validation-rule>
                </dxi-item>
                <dxi-item dataField="gisLocationLong"
                          editorType="dxNumberBox">
                    <dxo-label text="Longitude"></dxo-label>
                    <dxi-validation-rule type="custom"
                                         [reevaluate]="true"
                                         [validationCallback]="validateGIS"
                                         message="Both latitude and longitude must be provided">
                    </dxi-validation-rule>
                    <dxi-validation-rule type="compare"
                                         comparisonType="<="
                                         [comparisonTarget]="positiveLongitude"
                                         message="Longitude must be less than or equal to 180">
                    </dxi-validation-rule>
                    <dxi-validation-rule type="compare"
                                         comparisonType=">="
                                         [comparisonTarget]="negativeLongitude"
                                         message="Longitude must be greater than or equal to -180">
                    </dxi-validation-rule>
                </dxi-item>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="General Information"
                  [colCount]="3">
            <dxi-item dataField="orientation"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: pos}"></dxi-item>
            <dxi-item dataField="rt"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: rtOptions}">
                <dxo-label text="RT"></dxo-label>
            </dxi-item>
            <dxi-item dataField="installationDate"
                      editorType="dxDateBox"></dxi-item>
            <dxi-item dataField="inServiceDate"
                      editorType="dxDateBox"></dxi-item>
            <dxi-item dataField="pIDNumber">
                <dxo-label text="P&ID Number"></dxo-label>
            </dxi-item>
            <dxi-item dataField="designDrawingNumber">
                <dxo-label text="Construction/Design Drawing Number">
                </dxo-label>
            </dxi-item>
            <dxi-item dataField="lowestFlangeRating"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: lowestFlangeItems}"></dxi-item>
            <dxi-item dataField="hydroTestPressure"
                      [editorOptions]="{format: '#'}"
                      editorType="dxNumberBox"></dxi-item>
            <dxi-item dataField="typeOfConstruction"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: constructionTypeItems}">
            </dxi-item>
            <dxi-item dataField="pwht"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: yesNoUnknownItems}">
                <dxo-label text="Post Weld Heat Treatment"></dxo-label>
            </dxi-item>
            <dxi-item dataField="ratingChanged"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: yesNoUnknownItems}">
                <dxo-label text="Has the equipment been de-rated or re-rated?">
                </dxo-label>
            </dxi-item>
            <dxi-item dataField="isFiredPressureVessel"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: yesNoItems}">
                <dxo-label text="Is this a fired pressure vessel?"></dxo-label>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="Photos">
            <dxi-item>
                <dxo-label text="Front"></dxo-label>
                <div *dxTemplate>
                    <dx-tile-view [items]="assetDetails?.frontPhotos"
                                  [baseItemHeight]="120"
                                  [baseItemWidth]="120"
                                  [itemMargin]="10"
                                  [direction]="'horizontal'"
                                  [noDataText]="'No photos in this section'"
                                  [height]="(assetDetails?.frontPhotos?.length > 0 ? 180 : 40)"
                                  (onItemClick)="onItemClick($event)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem?.blobName"
                                 class="image-tile">

                                <img class="tile-image"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('frontPhotos',galleryItem.blobName)"
                                     alt="">
                            </div>
                        </div>
                    </dx-tile-view>
                </div>
            </dxi-item>
            <dxi-item>
                <dxo-label text="Back"></dxo-label>
                <div *dxTemplate>
                    <dx-tile-view [items]="assetDetails?.backPhotos"
                                  [baseItemHeight]="120"
                                  [baseItemWidth]="120"
                                  [itemMargin]="10"
                                  [direction]="'horizontal'"
                                  [noDataText]="'No photos in this section'"
                                  [height]="(assetDetails.backPhotos?.length > 0 ? 180 : 40)"
                                  (onItemClick)="onItemClick($event)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem?.blobName"
                                 class="image-tile">
                                <img class="tile-image"
                                     [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('backPhotos',galleryItem.blobName)"
                                     alt="">
                            </div>
                        </div>
                    </dx-tile-view>
                </div>
            </dxi-item>
            <dxi-item>
                <dxo-label text="Left"></dxo-label>
                <div *dxTemplate>
                    <dx-tile-view #leftPhotos
                                  [items]="assetDetails?.leftPhotos"
                                  [baseItemHeight]="120"
                                  [baseItemWidth]="120"
                                  [itemMargin]="10"
                                  [direction]="'horizontal'"
                                  [noDataText]="'No photos in this section'"
                                  [height]="(assetDetails.leftPhotos?.length > 0 ? 180 : 40)"
                                  (onItemClick)="onItemClick($event)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem?.blobName"
                                 class="image-tile">
                                <img class="tile-image"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('leftPhotos',galleryItem.blobName)"
                                     alt="">
                            </div>
                        </div>
                    </dx-tile-view>
                </div>
            </dxi-item>
            <dxi-item>
                <dxo-label text="Right"></dxo-label>
                <div *dxTemplate>
                    <dx-tile-view [items]="assetDetails?.rightPhotos"
                                  [baseItemHeight]="120"
                                  [baseItemWidth]="120"
                                  [itemMargin]="10"
                                  [direction]="'horizontal'"
                                  [noDataText]="'No photos in this section'"
                                  [height]="(assetDetails.rightPhotos?.length > 0 ? 180 : 40)"
                                  (onItemClick)="onItemClick($event)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem?.blobName"
                                 class="image-tile">
                                <img class="tile-image"
                                     [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('rightPhotos',galleryItem.blobName)"
                                     alt="">
                            </div>
                        </div>
                    </dx-tile-view>
                </div>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="Design"
                  [colCount]="3">
            <dxi-item dataField="designCode">
                <dxo-label text="Code"></dxo-label>
            </dxi-item>
            <dxi-item dataField="designYear">
                <dxo-label text="Year"></dxo-label>
            </dxi-item>
            <dxi-item dataField="designAddendum">
                <dxo-label text="Addendum"></dxo-label>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="Inspection"
                  [colCount]="3">
            <dxi-item dataField="inspectionCode">
                <dxo-label text="Code"></dxo-label>
            </dxi-item>
            <dxi-item dataField="inspectionYear">
                <dxo-label text="Year"></dxo-label>
            </dxi-item>
            <dxi-item dataField="inspectionAddendum">
                <dxo-label text="Addendum"></dxo-label>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="Data Plate"
                  [colCount]="3">
            <dxi-item dataField="dataPlateAttached"
                      editorType="dxCheckBox"></dxi-item>
            <dxi-item dataField="dataPlateLegible"
                      editorType="dxCheckBox"></dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="Manufacturer"
                  [colCount]="3">
            <dxi-item dataField="manufacturerName">
                <dxo-label text="Name"></dxo-label>
            </dxi-item>
            <dxi-item dataField="manufacturerDate"
                      editorType="dxDateBox">
                <dxo-label text="Date"></dxo-label>
            </dxi-item>
            <dxi-item dataField="manufacturerSerialNumber">
                <dxo-label text="Serial Number"></dxo-label>
            </dxi-item>
            <dxi-item dataField="nationalBoardNumber"></dxi-item>
            <dxi-item dataField="rtNumber">
                <dxo-label text="RT Number"></dxo-label>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="Service"
                  [colCount]="3">
            <dxi-item dataField="serviceProductContents">
                <dxo-label text="Service/Product/Contents"></dxo-label>
            </dxi-item>
            <dxi-item dataField="specificGravity"
                      editorType="dxNumberBox"></dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="Inspection Openings">
            <dxi-item itemType="group"
                      *ngFor="let inspectionOpening of assetDetails?.inspectionOpenings; let i = index"
                      [caption]="inspectionOpening.displayName"
                      [colCount]="3">
                <dxi-item [dataField]="'inspectionOpenings[' + i + '].type'">
                    <dxo-label text="Type"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'inspectionOpenings[' + i + '].number'"
                          editorType="dxNumberBox">
                    <dxo-label text="Number"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'inspectionOpenings[' + i + '].size'">
                    <dxo-label text="Size"></dxo-label>
                </dxi-item>
            </dxi-item>
        </dxi-item>
        <dxi-item itemType="group"
                  caption="Repair Record"
                  [colCount]="3">
            <dxi-item dataField="hasRepairOrAlterationPlate">
                <dxo-label
                           text="Does the asset have a repair or alteration plate?">
                </dxo-label>
            </dxi-item>
            <dxi-item dataField="repairOrAlterationPlateLegible"
                      editorType="dxCheckBox">
                <dxo-label text="Repair/Alteration Plate(s) Legible?">
                </dxo-label>
            </dxi-item>
        </dxi-item>
        <dxi-item itemType="group"
                  caption="Repairs">
            <dxi-item itemType="group"
                      *ngFor="let repair of assetDetails?.repairs; let i = index"
                      [colCount]="3">
                <dxi-item [dataField]="'repairs[' + i + '].dateRepairedOrAltered'"
                          editorType="dxDateBox">
                    <dxo-label text="Date Repaired or Altered"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'repairs[' + i + '].repairAlterationOrganization'">
                    <dxo-label text="Repair/Alteration Organization">
                    </dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'repairs[' + i + '].purposeOfRepairAlteration'">
                    <dxo-label text="Purpose of Repair/Alteration"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'repairs[' + i + '].isNBFormR1Available'"
                          editorType="dxCheckBox">
                    <dxo-label text="Is NB Form R-1 Available"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'repairs[' + i + '].isNBFormR2Available'"
                          editorType="dxCheckBox">
                    <dxo-label text="Is NB Form R-2 Available"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'repairs[' + i + '].nbRCertificateNumber'">
                    <dxo-label text="NB 'R' Certification Number"></dxo-label>
                </dxi-item>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="Operating/Design Conditions"
                  [colCount]="3">
            <dxi-item dataField="designConditionsOperatingTemp"
                      [editorOptions]="{format: '#'}"
                      editorType="dxNumberBox">
                <dxo-label text="Operating Temperature (°F)"></dxo-label>
            </dxi-item>

            <dxi-item dataField="operationStatus"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: operationStatusOptions}">
            </dxi-item>

        </dxi-item>

        <dxi-item itemType="group"
                  caption="Shell Side"
                  [colCount]="3">
            <dxi-item dataField="shellSideDesignMAWP"
                      editorType="dxNumberBox">
                <dxo-label text="Design MAWP (psi)"></dxo-label>
            </dxi-item>
            <dxi-item dataField="shellSideDesignTemp"
                      editorType="dxNumberBox">
                <dxo-label text="Design Temperature (°F)"></dxo-label>
            </dxi-item>
            <dxi-item dataField="shellSideOperatingTemp"
                      editorType="dxNumberBox">
                <dxo-label text="Operating Temperature (°F)"></dxo-label>
            </dxi-item>
            <dxi-item dataField="shellSideOperatingPressure"
                      editorType="dxNumberBox">
                <dxo-label text="Operating Pressure (psi)"></dxo-label>
            </dxi-item>
            <dxi-item dataField="shellSideSetPressure"
                      editorType="dxNumberBox">
                <dxo-label text="PRV Set Pressure (psi)"></dxo-label>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="Tube Side"
                  [colCount]="3">
            <dxi-item dataField="tubeSideDesignMAWP"
                      editorType="dxNumberBox">
                <dxo-label text="Design MAWP (psi)"></dxo-label>
            </dxi-item>
            <dxi-item dataField="tubeSideDesignTemp"
                      editorType="dxNumberBox">
                <dxo-label text="Design Temperature (°F)"></dxo-label>
            </dxi-item>
            <dxi-item dataField="tubeSideOperatingTemp"
                      [editorOptions]="{format: '#'}"
                      editorType="dxNumberBox">
                <dxo-label text="Operating Temperature (°F)"></dxo-label>
            </dxi-item>
            <dxi-item dataField="tubeSideOperatingPressure"
                      editorType="dxNumberBox">
                <dxo-label text="Operating Pressure (psi)"></dxo-label>
            </dxi-item>
            <dxi-item dataField="tubeSideSetPressure"
                      editorType="dxNumberBox">
                <dxo-label text="PRV Set Pressure (psi)"></dxo-label>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="Dimensions"
                  [colCount]="3">
            <dxi-item dataField="diameterMeasurement"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: diamterMeasurementOptions}">
            </dxi-item>
            <dxi-item dataField="diameter"
                      editorType="dxNumberBox">
                <dxo-label text="Diameter (in.)"></dxo-label>
            </dxi-item>
            <dxi-item dataField="hasMultipleDiameters"
                      editorType="dxCheckBox">
                <dxo-label text="Does the shell have multiple diameters?">
                </dxo-label>
            </dxi-item>
            <dxi-item dataField="overallLengthHeight"
                      editorType="dxNumberBox">
                <dxo-label text="Overall Length/Height (in)"></dxo-label>
            </dxi-item>
            <dxi-item dataField="hasToriconicalSections"
                      editorType="dxCheckBox">
                <dxo-label
                           text="Are there toriconical transition sections in the shell?">
                </dxo-label>
            </dxi-item>

        </dxi-item>

        <dxi-item itemType="group"
                  caption="Components">
            <dxi-item itemType="group"
                      *ngFor="let shellCourse of assetDetails?.shellCourses; let i = index"
                      [colCount]="3"
                      caption="Shell Courses">
                <dxi-item [dataField]="'shellCourses[' + i + '].number'"
                          editorType="dxTextBox">
                    <dxo-label text="Number"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'shellCourses[' + i + '].materialSpecAndGrade'">
                    <dxo-label text="Material Spec and Grade"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'shellCourses[' + i + '].allowableStressAtTemp'">
                    <dxo-label text="Allowable Stress at Temperature">
                    </dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'shellCourses[' + i + '].nominalThickness'">
                    <dxo-label text="Nominal Thickness"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'shellCourses[' + i + '].corrosionAllowance'">
                    <dxo-label text="Corrosion Allowance"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'shellCourses[' + i + '].lengthOrHeight'">
                    <dxo-label text="Length or Height"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'shellCourses[' + i + '].jointEfficiency'">
                    <dxo-label text="Joint Efficiency"></dxo-label>
                </dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      *ngFor="let channel of assetDetails?.channels; let i = index"
                      caption="Channels"
                      [colCount]="3">
                <dxi-item [dataField]="'channels[' + i + '].number'"
                          editorType="dxTextBox">
                    <dxo-label text="Number"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'channels[' + i + '].location'"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: locationOptions}">
                    <dxo-label text="Location"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'channels[' + i + '].materialSpecAndGrade'">
                    <dxo-label text="Material Spec and Grade"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'channels[' + i + '].allowableStressAtTemp'">
                    <dxo-label text="Allowable Stress at Temperature">
                    </dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'channels[' + i + '].nominalThickness'">
                    <dxo-label text="Nominal Thickness"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'channels[' + i + '].corrosionAllowance'">
                    <dxo-label text="Corrosion Allowance"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'channels[' + i + '].length'">
                    <dxo-label text="Length"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'channels[' + i + '].jointEfficiency'">
                    <dxo-label text="Joint Efficiency"></dxo-label>
                </dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      *ngFor="let head of assetDetails?.heads; let i = index"
                      caption="Heads"
                      [colCount]="3">
                <dxi-item [dataField]="'heads[' + i + '].number'"
                          editorType="dxTextBox">
                    <dxo-label text="Number"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'heads[' + i + '].location'"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: locationOptions}">
                    <dxo-label text="Location"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'heads[' + i + '].geometry'"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: geometryOptions, displayExpr: 'value', valueExpr: 'value'}">
                    <dxo-label text="Geometry"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'heads[' + i + '].materialSpecAndGrade'">
                    <dxo-label text="Material Spec and Grade"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'heads[' + i + '].allowableStressAtTemp'">
                    <dxo-label text="Allowable Stress at Temperature (psi)">
                    </dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'heads[' + i + '].nominalThickness'">
                    <dxo-label text="Nominal Thickness (in.)"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'heads[' + i + '].corrosionAllowance'">
                    <dxo-label text="Corrosion Allowance (in.)"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'heads[' + i + '].jointEfficiency'">
                    <dxo-label text="Joint Efficiency"></dxo-label>
                </dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      *ngFor="let nozzle of assetDetails?.nozzles; let i = index"
                      caption="Nozzles"
                      [colCount]="3">
                <dxi-item [dataField]="'nozzles[' + i + '].number'"
                          editorType="dxTextBox">
                    <dxo-label text="Number"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'nozzles[' + i + '].type'">
                    <dxo-label text="Type"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'nozzles[' + i + '].materialSpecAndGrade'">
                    <dxo-label text="Material Spec and Grade"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'nozzles[' + i + '].pipeSize'"
                          editorType="dxTagBox"
                          [editorOptions]="{items: pipeSizeItems}">
                    <dxo-label text="Pipe Size(s)"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'nozzles[' + i + '].pipeSchedule'"
                          editorType="dxTagBox"
                          [editorOptions]="{items: pipeScheduleItems}">
                    <dxo-label text="Pipe Schedule(s)"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'nozzles[' + i + '].flangeRating'"
                          editorType="dxTagBox"
                          [editorOptions]="{items: flangeRatingItems}">
                    <dxo-label text="Flange Rating(s)"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'nozzles[' + i + '].reinforcementPadType'">
                    <dxo-label text="Reinforcement Pad Type"></dxo-label>
                </dxi-item>
                <dxi-item
                          [dataField]="'nozzles[' + i + '].reinforcementPadDimensions'">
                    <dxo-label text="Reinforcement Pad Dimensions"></dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'nozzles[' + i + '].reinforcementPadThickness'"
                          editorType="dxNumberBox">
                    <dxo-label text="Reinforcement Pad Thickness (in.)">
                    </dxo-label>
                </dxi-item>
            </dxi-item>
        </dxi-item>
    </dx-form>


    <div class="content-block responsive-paddings form-buttons">
        <dx-button *ngIf="(isEditing$ | async) === false"
                   (onClick)="onEditClicked($event)"
                   text="Edit"
                   type="default"
                   icon="fa fa-pencil"
                   hint="Edit"
                   [disabled]="!allowEditing"></dx-button>
        <dx-button *ngIf="isEditing$ | async"
                   (onClick)="onCancelClicked($event)"
                   text="Cancel"
                   [stylingMode]="'text'"
                   type="danger"
                   hint="Cancel"
                   [disabled]="isSaving$ | async">
        </dx-button>
        <dx-button *ngIf="isEditing$ | async"
                   (onClick)="onSaveClicked($event)"
                   text="Save"
                   type="success"
                   icon="fa fa-floppy-o"
                   hint="Save"
                   [disabled]="!allowEditing || (isSaving$ | async)">
        </dx-button>
    </div>
</div>

<dx-popup [(visible)]="showPhotoPopup"
          [showTitle]="true"
          [showCloseButton]="true"
          [hideOnOutsideClick]="true"
          #popup>
    <dx-gallery #gallery
                id="gallery"
                [dataSource]="allPhotos"
                [loop]="true"
                width="100%"
                height="100%"
                [showNavButtons]="true"
                [showIndicator]="true"
                [(selectedIndex)]="popupGallerySelectedIndex"
                (onContentReady)="onPopupGalleryContentReady($event, popup, gallery)"
                (onSelectionChanged)="onPopupGallerySelectionChanged($event, popup, gallery)">

        <div *dxTemplate="let photoInfo of 'item'"
             class="popup-template">

            <div *ngIf="photoInfo?.photo?.blobName"
                 class="image-container dx-swatch-additional">
                <img *ngIf="assetPathLoadingCompleted"
                     [src]="getAssetImage('allPhotos',photoInfo?.photo?.blobName)"
                     alt="">


            </div>
            <div class="info">
                <div class="text-content">
                    <h6>Description</h6>
                    <dx-text-area #descriptionEditor
                                  [readOnly]="(isEditingPhotoDescription$ | async) === false"
                                  [value]="photoInfo?.photo?.description?.currentValue"
                                  [autoResizeEnabled]="true">
                    </dx-text-area>
                    <div class="description-buttons">
                        <dx-button *ngIf="(isEditingPhotoDescription$ | async) === false; else saveCancelPhotoDescription"
                                   icon="edit"
                                   text="Edit"
                                   (onClick)="onEditDescriptionClicked($event, descriptionEditor.value)"
                                   [disabled]="!allowEditing">
                        </dx-button>
                        <ng-template #saveCancelPhotoDescription>
                            <dx-button icon="save"
                                       text="Save"
                                       type="success"
                                       (onClick)="onDescriptionSave($event, photoInfo, descriptionEditor.value)"
                                       [disabled]="!allowEditing">
                            </dx-button>
                            <dx-button text="Cancel"
                                       type="danger"
                                       (onClick)="onDescriptionCancel($event, descriptionEditor)">
                            </dx-button>
                        </ng-template>
                    </div>
                </div>
                <div class="buttons">

                    <dx-button icon="remove"
                               text="Delete"
                               type="danger"
                               (onClick)="onDeletePhotoClicked($event, photoInfo)"
                               [disabled]="!allowEditing">
                    </dx-button>

                    <dx-button icon="download"
                               text="Download"
                               type="default"
                               (onClick)="onDownloadClicked(photoInfo?.photo?.blobName)">
                    </dx-button>
                </div>
            </div>
        </div>
    </dx-gallery>
</dx-popup>