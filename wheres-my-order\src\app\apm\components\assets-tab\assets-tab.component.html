<div class="responsive-paddings content-block">
    <dx-data-grid #assetsGrid
                  [dataSource]="assetsDataSource"
                  [allowColumnReordering]="true"
                  [allowColumnResizing]="true"
                  [columnResizingMode]="'widget'"
                  [(selectedRowKeys)]="selectedRowKeys"
                  (onRowInserted)="onRowInserted($event)"
                  (onEditorPreparing)="onEditorPreparing($event)">

        <dxo-toolbar>
            <dxi-item widget="dxButton"
                      location="after"
                      [options]="{type: 'default', stylingMode: 'contained', locateInMenu: 'auto', text: 'Remove Asset(s) from Project', disabled: selectedRowKeys?.length <= 0, onClick: removeAssetsFromProject}">
            </dxi-item>
            <dxi-item widget="dxButton"
                      location="after"
                      locateInmenu="auto"
                      [options]="{type: 'default', stylingMode: 'contained', text: 'Create New Asset for Project', disabled: !allowEditing || (createNewAssetDisabled$ | async), onClick: createNewAssetClicked}">
            </dxi-item>
            <dxi-item widget="dxButton"
                      location="after"
                      locateInMenu="auto"
                      [options]="{type: 'default', stylingMode: 'contained', text: 'Create new Asset at Location', disabled: !allowEditing || (createNewAssetDisabled$ | async), onClick: createNewAssetAtLocationClicked}">
            </dxi-item>
            <dxi-item widget="dxButton"
                      location="after"
                      locateInMenu="auto"
                      [options]="{type: 'default', stylingMode: 'contained', text: 'Add Asset(s) to Project', disabled: !allowEditing, onClick: addAssetClicked}">
            </dxi-item>
            <dxi-item name="groupPanel"></dxi-item>
            <dxi-item name="searchPanel"></dxi-item>
            <dxi-item name="columnChooserButton"></dxi-item>
            <dxi-item name="exportButton"></dxi-item>
            <dxi-item widget="dxButton"
                      location="after"
                      [options]="{icon: 'fa fa-undo', hint: 'Restore Grid Defaults', onClick: restoreDefaultsClicked}">
            </dxi-item>
        </dxo-toolbar>

        <dxo-editing mode="popup"
                     [allowAdding]="allowEditing"
                     [allowUpdating]="false"
                     [allowDeleting]="false">
            <dxo-popup title="Create New Asset"
                       [showTitle]="true"></dxo-popup>
            <dxo-form [colCount]="2"
                      [onInitialized]="onEditFormInitialized">
                <dxi-item dataField="assetId">
                    <dxo-label text="Asset ID"></dxo-label>
                </dxi-item>
                <dxi-item dataField="assetName">
                    <dxo-label text="Asset Name"></dxo-label>
                </dxi-item>
                <dxi-item dataField="category"
                          editorType="dxSelectBox"
                          [editorOptions]="{dataSource: assetCategories}">
                </dxi-item>
                <dxi-item *ngIf="!isAssetAtLocation"
                          itemType="empty"></dxi-item>
                <dxi-item *ngIf="isAssetAtLocation"
                          dataField="locationId"
                          editorType="dxSelectBox"
                          [editorOptions]="{dataSource: locations, valueExpr:'id', displayExpr: 'name.currentValue'}">
                    <dxo-label text="Location Name (Facility)"></dxo-label>
                </dxi-item>
                <dxi-item dataField="startLatitude"
                          editorType="dxNumberBox">
                    <dxo-label
                               [text]="isPiping ? 'Start Latitude' : 'Latitude'">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="startLongitude"
                          editorType="dxNumberBox">
                    <dxo-label
                               [text]="isPiping ? 'Start Longitude' : 'Longitude'">
                    </dxo-label>
                </dxi-item>
                <dxi-item *ngIf="isPiping"
                          dataField="endLatitude"
                          editorType="dxNumberBox">
                    <dxo-label text="End Latitude"></dxo-label>
                </dxi-item>
                <dxi-item *ngIf="isPiping"
                          dataField="endLongitude"
                          editorType="dxNumberBox">
                    <dxo-label text="End Longitude"></dxo-label>
                </dxi-item>
            </dxo-form>
        </dxo-editing>
        <dxo-selection [selectAllMode]="'allPages'"
                       [showCheckBoxesMode]="'always'"
                       mode="multiple"></dxo-selection>
        <dxo-group-panel [visible]="true"></dxo-group-panel>
        <dxo-scrolling [useNative]="true"></dxo-scrolling>
        <dxo-paging [pageSize]="5"></dxo-paging>
        <dxo-pager [showPageSizeSelector]="true"
                   [visible]="true"
                   [allowedPageSizes]="[5, 10, 25, 50]"></dxo-pager>
        <dxo-header-filter [visible]="true"></dxo-header-filter>
        <dxo-filter-row [visible]="true"></dxo-filter-row>
        <dxo-search-panel [visible]="true"
                          [highlightCaseSensitive]="true">
        </dxo-search-panel>
        <dxo-filter-panel [visible]="true"></dxo-filter-panel>
        <dxo-column-chooser [enabled]="true"
                            mode="dragAndDrop">
        </dxo-column-chooser>
        <dxo-state-storing [enabled]="true"
                           type="localStorage"
                           storageKey="apmProjectAssetsGridState">
        </dxo-state-storing>
        <dxo-export [enabled]="true"
                    [allowExportSelectedData]="true"></dxo-export>

        <!-- COLUMNS -->
        <dxi-column dataField="assetId"
                    caption="Asset ID"
                    dataType="string">
            <dxi-validation-rule type="required"
                                 message="Asset ID is required">
            </dxi-validation-rule>
        </dxi-column>
        <dxi-column dataField="assetName"
                    dataType="string">
            <dxi-validation-rule type="required"
                                 message="Asset Name is required">
            </dxi-validation-rule>
        </dxi-column>
        <dxi-column dataField="category"
                    dataType="string">
            <dxi-validation-rule type="required"
                                 message="Category is required">
            </dxi-validation-rule>
        </dxi-column>
        <dxi-column dataField="id"
                    caption="Asset Database ID"
                    [visible]="false"></dxi-column>
        <dxi-column [visible]="false"
                    [showInColumnChooser]="false"
                    dataField="startLatitude"
                    dataType="number">
            <dxi-validation-rule type="custom"
                                 [reevaluate]="true"
                                 [validationCallback]="validateStartGIS"
                                 message="Both latitude and longitude must be provided">
            </dxi-validation-rule>
            <dxi-validation-rule type="custom"
                                 [reevaluate]="true"
                                 [validationCallback]="startLatitudeValidation"
                                 message="Latitude must be between -90 and 90">
            </dxi-validation-rule>
        </dxi-column>
        <dxi-column [visible]="false"
                    [showInColumnChooser]="false"
                    dataField="startLongitude"
                    dataType="number">
            <dxi-validation-rule type="custom"
                                 [reevaluate]="true"
                                 [validationCallback]="validateStartGIS"
                                 message="Both latitude and longitude must be provided">
            </dxi-validation-rule>
            <dxi-validation-rule type="custom"
                                 [reevaluate]="true"
                                 [validationCallback]="startLongitudeValidation"
                                 message="Longitude must be between -180 and 180">
            </dxi-validation-rule>
        </dxi-column>
        <dxi-column [visible]="false"
                    [showInColumnChooser]="false"
                    dataField="endLatitude"
                    dataType="number">
            <dxi-validation-rule type="custom"
                                 [reevaluate]="true"
                                 [validationCallback]="validateEndGIS"
                                 message="Both latitude and longitude must be provided">
            </dxi-validation-rule>
            <dxi-validation-rule type="custom"
                                 [reevaluate]="true"
                                 [validationCallback]="endLatitudeValidation"
                                 message="Latitude must be between -90 and 90">
            </dxi-validation-rule>
        </dxi-column>
        <dxi-column [visible]="false"
                    [showInColumnChooser]="false"
                    dataField="endLongitude"
                    dataType="number">
            <dxi-validation-rule type="custom"
                                 [reevaluate]="true"
                                 [validationCallback]="validateEndGIS"
                                 message="Both latitude and longitude must be provided">
            </dxi-validation-rule>
            <dxi-validation-rule type="custom"
                                 [reevaluate]="true"
                                 [validationCallback]="endLongitudeValidation"
                                 message="Longitude must be between -180 and 180">
            </dxi-validation-rule>
        </dxi-column>
        <dxi-column caption="GIS Location"
                    cellTemplate="gisTemplate"
                    dataType="object">
            <div *dxTemplate="let cell of 'gisTemplate'">
                <div *ngIf="cell.data.category === 'Piping'">
                    <span class="middle">{{displayPipingGIS(cell.data)}}</span>
                </div>
                <div
                     *ngIf="cell.data.category === 'Tank' || cell.data.category === 'Vessel'">
                    <span class="middle">{{displayGIS(cell.data)}}</span>
                </div>
            </div>
        </dxi-column>
        <dxi-column dataField="locationId"
                    [visible]="false"
                    [showInColumnChooser]="false"
                    dataType="string">
            <dxi-validation-rule type="required"
                                 message="Location is required">
            </dxi-validation-rule>
        </dxi-column>
    </dx-data-grid>
</div>

<dx-popup [(visible)]="addAssetPopupVisible"
          #addAssetPopup
          [showCloseButton]="true"
          [hideOnOutsideClick]="true"
          title="Add Asset(s) to Project">
    <div *dxTemplate="let data of 'content'"
         class="popup-content">
        <dx-data-grid #availableAssetsGrid
                      style="height: 95%; width: 100%;"
                      [dataSource]="availableAssets">
            <dxo-selection [selectAllMode]="'allPages'"
                           [showCheckBoxesMode]="'always'"
                           mode="multiple"></dxo-selection>
            <dxo-group-panel [visible]="true"></dxo-group-panel>
            <dxo-scrolling [useNative]="true"></dxo-scrolling>
            <dxo-paging [pageSize]="5"></dxo-paging>
            <dxo-pager [showPageSizeSelector]="true"
                       [visible]="true"
                       [allowedPageSizes]="[5, 10, 25, 50]"></dxo-pager>
            <dxo-header-filter [visible]="true"></dxo-header-filter>
            <dxo-filter-row [visible]="true"></dxo-filter-row>
            <dxo-search-panel [visible]="true"
                              [highlightCaseSensitive]="true">
            </dxo-search-panel>
            <dxo-filter-panel [visible]="true"></dxo-filter-panel>
            <dxo-column-chooser [enabled]="true"
                                mode="dragAndDrop">
            </dxo-column-chooser>
            <dxo-state-storing [enabled]="true"
                               type="localStorage"
                               storageKey="apmProjectAvailableAssetsGridState">
            </dxo-state-storing>
            <dxo-export [enabled]="true"
                        [allowExportSelectedData]="true"></dxo-export>

            <dxi-column dataField="assetName"></dxi-column>
            <dxi-column dataField="assetId"></dxi-column>
            <dxi-column dataField="category"></dxi-column>
            <dxi-column caption="GIS Location"
                        cellTemplate="gisTemplate">
                <div *dxTemplate="let cell of 'gisTemplate'">
                    <div *ngIf="cell.data.category === 'Piping'">
                        <span
                              class="middle">{{displayPipingGIS(cell.data)}}</span>
                    </div>
                    <div
                         *ngIf="cell.data.category === 'Tank' || cell.data.category === 'Vessel'">
                        <span class="middle">{{displayGIS(cell.data)}}</span>
                    </div>
                </div>

            </dxi-column>
        </dx-data-grid>

        <div class="actions">
            <dx-button text="Cancel"
                       type="danger"
                       (onClick)="cancelAddAssetClicked($event)"></dx-button>
            <dx-button text="Add Selected to Project"
                       (onClick)="addSelectedAssetsClicked($event)"
                       type="default"></dx-button>
        </div>
    </div>
</dx-popup>
