<div class="dx-card content-block responsive-paddings">
    <dx-data-grid #grid
                  [dataSource]="projectsDataSource"
                  [remoteOperations]="true"
                  [allowColumnReordering]="true"
                  [allowColumnResizing]="true"
                  [columnResizingMode]="'widget'"
                  (onRowInserted)="onRowInserted($event)"
                  (onSelectionChanged)="onSelectionChanged($event)">

        <dxo-toolbar>
            <dxi-item name="groupPanel"></dxi-item>
            <dxi-item name="addRowButton"
                      [showText]="'always'"
                      [options]="addRowButtonOptions$ | async">
            </dxi-item>
            <dxi-item widget="dxButton"
                      location="after"
                      [options]="{icon: 'fa fa-undo', hint: 'Restore Grid Defaults', onClick: restoreDefaultsClicked}">
            </dxi-item>
            <dxi-item name="columnChooserButton"></dxi-item>
            <dxi-item name="exportButton"></dxi-item>
            <dxi-item name="searchPanel"></dxi-item>
        </dxo-toolbar>

        <!-- CONFIG -->
        <dxo-filter-row [visible]="true"></dxo-filter-row>
        <dxo-header-filter [visible]="true"></dxo-header-filter>
        <dxo-group-panel [visible]="true"></dxo-group-panel>
        <dxo-column-chooser [enabled]="true"
                            mode="dragAndDrop">
        </dxo-column-chooser>
        <dxo-search-panel [visible]="true"
                          [highlightCaseSensitive]="true">
        </dxo-search-panel>
        <dxo-load-panel [enabled]="true"></dxo-load-panel>
        <dxo-paging [pageSize]="5"></dxo-paging>
        <dxo-pager [showPageSizeSelector]="true"
                   [visible]="true"
                   [allowedPageSizes]="[5, 10, 25, 50]"></dxo-pager>
        <dxo-selection mode="single"></dxo-selection>
        <dxo-filter-panel [visible]="true"></dxo-filter-panel>
        <dxo-state-storing [enabled]="true"
                           type="localStorage"
                           storageKey="apmProjectsGridState">
        </dxo-state-storing>
        <dxo-export [enabled]="true"
                    [allowExportSelectedData]="true"></dxo-export>

        <!-- COLUMNS -->
        <dxi-column dataField="id"
                    caption="Project Database ID"
                    [visible]="false"></dxi-column>
        <dxi-column dataField="apmProjectNumber"
                    caption="APM Number"
                    cellTemplate="apmNumberTemplate">
            <div *dxTemplate="let cell of 'apmNumberTemplate'">
                <a href="javascript://void"
                   class="middle"
                   (click)="onAPMNumberClicked(cell.data)">{{cell.data.apmProjectNumber}}</a>
            </div>
        </dxi-column>
        <dxi-column dataField="plannedStart"
                    dataType="date"
                    caption="Planned Start"></dxi-column>
        <dxi-column dataField="plannedEnd"
                    dataType="date"
                    caption="Planned End"></dxi-column>
        <dxi-column dataField="name"
                    caption="Name"></dxi-column>
        <dxi-column dataField="teamProjectNumber"
                    caption="TEAM Project Number">
        </dxi-column>
        <dxi-column dataField="status"
                    caption="Status"></dxi-column>
        <dxi-column dataField="locationId"
                    caption="Location">
            <dxo-lookup [dataSource]="locations$ | async"
                        valueExpr="id"
                        displayExpr="name.currentValue"></dxo-lookup>
        </dxi-column>

        <!-- EDITING -->
        <dxo-editing mode="popup"
                     [allowAdding]="allowEditing"
                     [allowUpdating]="false"
                     [allowDeleting]="false">
            <dxo-popup title="Create New Project"
                       [showTitle]="true"
                       [width]="700"
                       [height]="525"></dxo-popup>
            <dxo-form [colCount]="2">
                <dxi-item dataField="teamProjectNumber">
                    <dxo-label text="Team Project Number"></dxo-label>
                </dxi-item>
                <dxi-item dataField="client"
                          [editorOptions]="{disabled: 'true', value: 'Chevron'}">
                    <dxo-label text="Client Name"></dxo-label>
                </dxi-item>
                <dxi-item dataField="locationId"
                          editorType="dxSelectBox"
                          [editorOptions]="locationSelectOptions$ | async">
                    <dxi-validation-rule type="required"
                                         message="Location is required">
                    </dxi-validation-rule>
                    <dxo-label text="Location Name (Facility)"></dxo-label>
                </dxi-item>
                <dxi-item dataField="plannedStart"
                          editorType="dxDateBox">
                    <dxo-label text="Planned Start"></dxo-label>
                </dxi-item>
                <dxi-item dataField="plannedEnd"
                          editorType="dxDateBox">
                    <dxo-label text="Planned End"></dxo-label>
                </dxi-item>
                <dxi-item dataField="name"
                          [editorOptions]="{maxLength:30}">
                    <dxo-label text="Project Name"></dxo-label>
                </dxi-item>
            </dxo-form>

        </dxo-editing>

    </dx-data-grid>


</div>
<dx-popup minWidth="450"
          width="auto"
          height="auto"
          [showTitle]="true"
          title="Location"
          [dragEnabled]="true"
          [hideOnOutsideClick]="false"
          [showCloseButton]="false"
          [(visible)]="createLocationPopupVisible">
    <dx-form #createLocationForm
             [(formData)]="newLocation">
        <dxi-item dataField="name">
            <dxi-validation-rule type="required"
                                 message="Location Name is required">
            </dxi-validation-rule>
            <dxo-label text="Location Name"></dxo-label>
        </dxi-item>
        <dxi-item dataField="description">
            <dxo-label text="Description"></dxo-label>
        </dxi-item>
        <dxi-item dataField="street1">
            <dxo-label text="Street 1"></dxo-label>
        </dxi-item>
        <dxi-item dataField="street2">
            <dxo-label text="Street 2"></dxo-label>
        </dxi-item>
        <dxi-item dataField="city">
            <dxi-validation-rule type="required"
                                 message="City is required">
            </dxi-validation-rule>

            <dxo-label text="City"></dxo-label>
        </dxi-item>

        <dxi-item dataField="region">
            <dxo-label text="State/Province"></dxo-label>
        </dxi-item>

        <dxi-item dataField="postalCode">
            <dxo-label text="Zip/Postal Code"></dxo-label>
        </dxi-item>

        <dxi-item itemType="group"
                  [colCount]="2">
            <dxi-item dataField="latitude">
                <dxo-label text="Latitude"></dxo-label>
            </dxi-item>
            <dxi-item dataField="longitude">
                <dxo-label text="Longitude"></dxo-label>
            </dxi-item>
        </dxi-item>
    </dx-form>
    <div class="content-block responsive-paddings form-buttons">
        <dx-button text="cancel"
                   type="error"
                   hint="cancel"
                   (onClick)="cancelEditClicked()">
        </dx-button>
        <dx-button text="Save"
                   type="success"
                   hint="save"
                   (onClick)="saveEditLocationClicked()">
        </dx-button>

    </div>

</dx-popup>
