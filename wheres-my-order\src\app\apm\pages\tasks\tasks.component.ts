import {
    Component,
    EventEmitter,
    Output,
    QueryList,
    ViewChild
} from '@angular/core';
import { DxDataGridComponent } from 'devextreme-angular';
import CustomStore from 'devextreme/data/custom_store';
import { EditorPreparingEvent } from 'devextreme/ui/data_grid';
import { ToastrService } from 'ngx-toastr';
import {
    BehaviorSubject,
    combineLatest,
    Observable,
    of,
    ReplaySubject
} from 'rxjs';
import { finalize, map, switchMap, tap } from 'rxjs/operators';
import { DataGridService } from '../../../shared/services';
import {
    AssetAccessComponent,
    AssetPpeComponent,
    InspectionInformationComponent,
    InspectionResultsComponent
} from '../../components';
import {
    APMStatus,
    Asset,
    AssetAccess,
    AssetDetailsPhotoTransport,
    AssetPPEViewModel,
    availableStatusesToChangeTo,
    cannotChangeStatusFromCanceledOrCompleted,
    InspectionInfo,
    PublishUnpublishWorkOrderUpdate,
    Task,
    TaskUpdate,
    TaskVM,
    VisualInspectionPhotoTransport,
    WorkOrder,
    WorkOrderDetail
} from '../../models';
import { ApmService } from '../../services';

@Component({
    selector: 'app-tasks',
    templateUrl: './tasks.component.html',
    styleUrls: ['./tasks.component.scss']
})
export class TasksComponent {
    private readonly _workOrderSubject = new ReplaySubject<WorkOrder>();
    private _loading = new BehaviorSubject<boolean>(false);
    private _fullWorkOrder: WorkOrder | undefined;

    get workOrder$(): Observable<WorkOrder> {
        return this._workOrderSubject.asObservable();
    }

    tasksDataSource = new CustomStore({
        key: 'id',
        byKey: (key: string) => this._apm.getTask(key).toPromise(),
        load: (options) => this._apm.loadTasks(options).toPromise()
    });
    users$ = this._apm.getUsers();

    @Output() selectedTask = new EventEmitter<TaskVM>();

    private _selectedTask = new ReplaySubject<TaskVM>();

    private readonly taskAndWorkOrder = combineLatest([
        this._selectedTask.asObservable(),
        this.workOrder$
    ]);

    @ViewChild('tasks') dataGrid: DxDataGridComponent;
    @ViewChild('headerTasks') headerDataGrid: DxDataGridComponent;
    @ViewChild(AssetPpeComponent) assetPPE: AssetPpeComponent;
    @ViewChild(AssetAccessComponent) assetAccess: AssetAccessComponent;
    @ViewChild(InspectionInformationComponent)
    inspectionInformation: InspectionInformationComponent;
    selectedWorkOrderId: string;
    selectedProjectId: string;
    workOrderDetail: WorkOrderDetail = {} as WorkOrderDetail;
    statusOptions: string[];
    inspectionResultsComponents: QueryList<InspectionResultsComponent>;
    allowEditing$ = this._apm.allowEditing$;
    internalInspectionResultsVisible$ = this.taskAndWorkOrder.pipe(
        map(([t, w]) => ['Internal Visual', 'Full'].includes(t?.taskType))
    );
    externalInspectionResultsVisible$ = this.taskAndWorkOrder.pipe(
        map(([t, w]) => ['External Visual', 'Full'].includes(t?.taskType))
    );

    taskId$ = this._selectedTask.pipe(
        map((t) => {
            if (t == null || t == undefined) {
                return;
            }

            return t.id;
        })
    );
    loading$ = this._loading.asObservable();
    selectedBU: string = '';

    constructor(
        private readonly _apm: ApmService,
        private readonly _grid: DataGridService,
        private readonly _toasts: ToastrService
    ) {
        this.selectedTask
            .pipe(
                tap(() => this._loading.next(true)),
                switchMap((t) =>
                    t
                        ? this._apm.getWorkOrder(t.workOrderId, t.projectId)
                        : of(undefined)
                ),
                tap((wo) => this._workOrderSubject.next(wo)),
                tap(() => this._loading.next(false))
            )
            .subscribe();
        this.workOrder$.subscribe((wo) => {
            if (wo == null || wo == undefined) {
                return;
            }

            this.setupWorkOrderDetail(wo);
            this.selectedWorkOrderId = wo.id;
            this.selectedProjectId = wo.projectId;
        });

        this._apm.selectedBU$.subscribe((selected: string) => {
            if (!this.selectedBU) {
                this.selectedBU = selected;
                return;
            }
            if (selected != this.selectedBU) {
                this.selectedBU = selected;
                this.refreshGrid();
            }
        });
    }

    onToolbarPreparing(e) {
        const restoreGridDefaultsButton = {
            widget: 'dxButton',
            options: {
                icon: 'fa fa-undo',
                hint: 'Restore Grid Defaults',
                onClick: async () => {
                    const result = await this._grid.resetGridState(
                        this.dataGrid
                    );
                    if (result) {
                        localStorage.removeItem('apmTasksPageGridState');
                    }
                }
            },
            location: 'after'
        };
        e.toolbarOptions.items.unshift(restoreGridDefaultsButton);
    }

    onSelectionChanged(e) {
        const selectedTask = e.selectedRowsData[0] as TaskVM;
        this.selectedTask.emit(selectedTask);
        this._selectedTask.next(selectedTask);
    }

    refreshGrid() {
        // we need this timeout because of the caching that the nuget package is doing.
        // without the timeout we will see the cached value (previous value) in the grid
        setTimeout(() => this.dataGrid?.instance?.refresh(), 300);
    }

    onAssetDetailsPhotoDelete(e: AssetDetailsPhotoTransport) {
        this._apm
            .deleteAssetDetailsPhoto(e)
            .pipe(
                tap(() =>
                    this._toasts.success(
                        'Photo successfully deleted',
                        'Photo deleted'
                    )
                ),
                switchMap(() =>
                    this._apm.getWorkOrder(
                        this.selectedWorkOrderId,
                        this.selectedProjectId
                    )
                ),
                tap((wo) => this._workOrderSubject.next(wo))
            )
            .subscribe();
    }

    onAssetDetailsPhotoDescriptionUpdate(e: AssetDetailsPhotoTransport) {
        this._apm
            .updateAssetDetailsPhotoDescription(e)
            .pipe(
                tap(() => {
                    this._toasts.success(
                        'Photo description was updated successfully',
                        'Description Saved'
                    );
                })
            )
            .subscribe();
    }

    onAssetPPESave(e: Partial<AssetPPEViewModel>) {
        this.assetPPE.isSaving = true;
        this._apm
            .updateAssetPPE(e)
            .pipe(
                switchMap((wo) =>
                    this._apm.getWorkOrder(
                        this.selectedWorkOrderId,
                        this.selectedProjectId
                    )
                ),
                tap((wo) => this._workOrderSubject.next(wo)),
                tap(() => {
                    this.assetPPE.isEditing = false;
                    this.assetPPE.isSaving = false;
                }),
                tap((a) =>
                    this._toasts.success(
                        'Asset PPE successfully updated',
                        'Update successful!'
                    )
                )
            )
            .subscribe();
    }

    onRowUpdated(e) {
        var update = {
            projectId: this.workOrderDetail.projectId,
            workOrderId: this.workOrderDetail.id,
            status: e.data?.status,
            supervisor: e.data?.supervisor,
            taskAssignees: e.data.taskAssignees,
            databaseId: e.data.id
        } as TaskUpdate;
        this.headerDataGrid.instance.beginCustomLoading('Loading...');
        this._apm
            .updateWorkOrderHeader(update)
            .pipe(
                switchMap((_) =>
                    this._apm.getWorkOrder(update.workOrderId, update.projectId)
                ),
                tap((wo) => this._workOrderSubject.next(wo)),
                tap(() =>
                    this._toasts.success(
                        'Work Order successfully updated',
                        'Update successful'
                    )
                ),
                finalize(() => this.headerDataGrid.instance.endCustomLoading())
            )
            .subscribe();
    }

    assetAccessUpdated(update: AssetAccess) {
        this.assetAccess.isSaving = true;
        const assetUpdate$ = this._apm.updateAssetAccess({
            accessForAerialLiftForAllLocationsAtHeight:
                update.accessForAerialLiftForAllLocationsAtHeight
                    ? 'Yes'
                    : 'No',
            additionalPortsNeeded: update.additionalPortsNeeded ? 'Yes' : 'No',
            additionalPortsNeededComment: update.additionalPortsNeededComment,
            aerialLiftNeeded: update.aerialLiftNeeded ? 'Yes' : 'No',
            aerialLiftNeededComment: update.aerialLiftNeededComment,
            allComponentsUnder4FeetInHeight:
                update.allComponentsUnder4FeetInHeight ? 'Yes' : 'No',
            allComponentsUnder4FeetInHeightComment:
                update.allComponentsUnder4FeetInHeightComment,
            assetOutOfService: update.assetOutOfService ? 'Yes' : 'No',
            batteryPoweredPermitted: update.batteryPoweredPermitted
                ? 'Yes'
                : 'No',
            cleaningRecommendations: update.cleaningRecommendations,
            cleaningRecommendationsComment:
                update.cleaningRecommendationsComment,
            cleaningServiceReview: update.cleaningServiceReview,
            cleaningServiceReviewComment: update.cleaningServiceReviewComment,
            clientProvidedOperator: update.clientProvidedOperator
                ? 'Yes'
                : 'No',
            clientRequiredProofOfTraining: update.clientRequiredProofOfTraining
                ? 'Yes'
                : 'No',
            coatingCondition: update.coatingCondition,
            coatingConditionsObserved: update.coatingConditionsObserved,
            coatingConditionsObservedComment:
                update.coatingConditionsObservedComment,
            coatingLinerConditions: update.coatingLinerConditions,
            coatingLinerConditionsObserved:
                update.coatingLinerConditionsObserved,
            coatingLinerConditionsObservedComment:
                update.coatingLinerConditionsObservedComment,
            coatingLinerType: update.coatingLinerType,
            coatingLinerTypeComment: update.coatingLinerTypeComment,
            coatingRemovalRequired: update.coatingRemovalRequired
                ? 'Yes'
                : 'No',
            coatingRemovalRequiredComment: update.coatingRemovalRequiredComment,
            coatingType: update.coatingType,
            coatingTypeComment: update.coatingTypeComment,
            corrosionIdentified: update.corrosionIdentified,
            corrosionIdentifiedComment: update.corrosionIdentifiedComment,
            corrosionRemovalRecommendation:
                update.corrosionRemovalRecommendation,
            corrosionRemovalRecommendationComment:
                update.corrosionRemovalRecommendationComment,
            estimatedDistanceToAnyLiveElectricalOverheadLines:
                update.estimatedDistanceToAnyLiveElectricalOverheadLines,
            existingInspectionPorts: update.existingInspectionPorts
                ? 'Yes'
                : 'No',
            fixedEquipmentLaddersStairwaysPlatformsInstalled:
                update.fixedEquipmentLaddersStairwaysPlatformsInstalled
                    ? 'Yes'
                    : 'No',
            fixedEquipmentLaddersStairwaysPlatformsInstalledComment:
                update.fixedEquipmentLaddersStairwaysPlatformsInstalledComment,
            gasPoweredPermitted: update.gasPoweredPermitted ? 'Yes' : 'No',
            hasInsulation: update.hasInsulation,
            heatTracing: update.heatTracing,
            heatTracingComment: update.heatTracingComment,
            id: update.id,
            inspectionOpeningTypes: update.inspectionOpeningTypes,
            inspectionOpeningTypesComment: update.inspectionOpeningTypesComment,
            inspectionOpeningsPresent: update.inspectionOpeningsPresent
                ? 'Yes'
                : 'No',
            insulationPlugsMissing: update.insulationPlugsMissing
                ? 'Yes'
                : 'No',
            insulationPlugsMissingComment: update.insulationPlugsMissingComment,
            insulationRemovalRequired: update.insulationRemovalRequired
                ? 'Yes'
                : 'No',
            insulationRemovalRequiredComment:
                update.insulationRemovalRequiredComment,
            insulationType: update.insulationType,
            insulationTypeComment: update.insulationTypeComment,
            jacketingType: update.jacketingType,
            ladderRequirements: update.ladderRequirements,
            ladderRequirementsComment: update.ladderRequirementsComment,
            possibleAsbestos: update.possibleAsbestos ? 'Yes' : 'No',
            possibleAsbestosComment: update.possibleAsbestosComment,
            ropeAccessRequired: update.ropeAccessRequired ? 'Yes' : 'No',
            ropeAccessRequiredComment: update.ropeAccessRequiredComment,
            scaffoldingRequired: update.scaffoldingRequired ? 'Yes' : 'No',
            scaffoldingRequiredComment: update.scaffoldingRequiredComment,
            sizeOfAllAccessOpenings: update.sizeOfAllAccessOpenings,
            ventilationRequirements: update.ventilationRequirements,
            ventilationRequirementsComment:
                update.ventilationRequirementsComment
        });

        assetUpdate$
            .pipe(
                switchMap((wo) =>
                    this._apm.getWorkOrder(
                        this.selectedWorkOrderId,
                        this.selectedProjectId
                    )
                ),
                tap((wo) => this._workOrderSubject.next(wo)),
                tap(() => {
                    this.assetAccess.isSaving = false;
                    this.assetAccess.isEditing = false;
                }),
                tap((a) =>
                    this._toasts.success(
                        'Asset Access successfully updated',
                        'Update successful!'
                    )
                )
            )
            .subscribe();
    }

    onSaveInspectionInfo(inspectionInfo: InspectionInfo) {
        this.inspectionInformation.isSaving = true;
        this._apm
            .updateWorkOrder(inspectionInfo)
            .pipe(
                tap((wo) => this._workOrderSubject.next(wo)),
                tap(() => {
                    this.inspectionInformation.isEditing = false;
                    this.inspectionInformation.isSaving = false;
                }),
                tap((wo) =>
                    this._toasts.success(
                        'Work Order successfully updated',
                        'Update successful'
                    )
                )
            )
            .subscribe();
    }

    onPhotoDelete(e: VisualInspectionPhotoTransport) {
        this._apm.deleteVisualInspectionPhoto(e).pipe(
            tap(() =>
                this._toasts.success(
                    'Photo successfully deleted',
                    'Photo deleted'
                )
            ),
            switchMap(() =>
                this._apm.getWorkOrder(
                    this.selectedWorkOrderId,
                    this.selectedProjectId
                )
            ),
            tap((wo) => this._workOrderSubject.next(wo))
        );
    }

    onPhotoDescriptionUpdate(e: VisualInspectionPhotoTransport) {
        this._apm
            .updateVisualInspectionPhotoDescription(e)
            .pipe(
                tap(() => {
                    this._toasts.success(
                        'Photo description was updated successfully',
                        'Description Saved'
                    );
                })
            )
            .subscribe();
    }

    onPublishedUnpublished(update: PublishUnpublishWorkOrderUpdate) {
        this._apm
            .publishUnpublishWorkOrder(update)
            .pipe(
                tap((wo) => {
                    const toastText =
                        update.isPublishing === true
                            ? 'published'
                            : 'unpublished';
                    const toastTextUppercase =
                        update.isPublishing === true
                            ? 'Published'
                            : 'Unpublished';
                    this._toasts.success(
                        'Work Order was successfully ' + toastText,
                        'Successfully ' + toastTextUppercase
                    );
                }),
                tap((wo) => this._workOrderSubject.next(wo))
            )
            .subscribe();
    }

    onTabSelectionChanged(e) {
        // We want these two tabs to collapse all but the first section of their
        // accordions because if someone decides to expand all of them for both tabs,
        // and then switch between them, the issue still remains.  So we are going
        // to avoid the bug, for now, by collapsing all but the first section to
        // avoid the rendering bug mentioned here:
        // https://supportcenter.devexpress.com/ticket/details/t1037592/dxtabpanel-rendering-the-template-from-two-items-at-once-on-top-of-each-other
        if (
            e.addedItems.some(
                (i) =>
                    i.template === 'internal-inspection-results' ||
                    i.template === 'external-inspection-results' ||
                    i.title === 'Internal Inspection Results' ||
                    i.title === 'External Inspection Results'
            )
        ) {
            this.inspectionResultsComponents?.forEach((c) =>
                setTimeout(() => c.restoreDefaultCollapseState())
            );
        }
    }

    onHeaderTaskGridEditorPreparing(e: EditorPreparingEvent) {
        if (e.parentType === 'dataRow' && e.dataField === 'status') {
            e.editorOptions.editorName = 'dxSelectBox';
            e.editorOptions.readOnly =
                cannotChangeStatusFromCanceledOrCompleted(
                    e.row.data,
                    this.workOrderDetail
                );
            if (this._fullWorkOrder)
                e.editorOptions.items = availableStatusesToChangeTo(
                    this._fullWorkOrder,
                    e.row.data.taskId,
                    e.row.data.taskType,
                    e.row.data.status
                );
        }
    }

    private setupWorkOrderDetail(wo: WorkOrder) {
        this._fullWorkOrder = wo;
        var walkdown = wo.asset.walkDown;
        var rowData = this.dataGrid.instance.getSelectedRowsData();
        this.workOrderDetail = {
            id: wo.id,
            projectId: wo.projectId,
            assetID: Asset.findAssetNumber(
                wo.asset?.walkDown,
                wo.asset?.assetCategory
            ),
            assetDescription:
                walkdown?.sectionIdentification?.attributeEquipment_Description
                    ?.currentValue,
            client: 'Chevron',
            location: wo.facilityName?.currentValue,
            inspectionTypes: [],
            assetCategory: wo.asset.assetCategory,
            status: wo.status?.currentValue,
            publishTime: wo.publishedTime,
            afeNumber: this.generateAFENumberString(wo.tasks),
            projectName: rowData[0].projectName,
            fieldWorkCompleted: wo.fieldWorkCompleted.currentValue
        };
        wo.tasks?.forEach((task) =>
            this.workOrderDetail.inspectionTypes.push({
                taskType: task.taskType,
                id: task.id,
                taskId: task.taskAPMNumber.currentValue,
                taskAssignees: task.assignedUsers,
                supervisor: task.taskDetails.supervisor?.currentValue,
                status: task.status?.currentValue as APMStatus
            })
        );
        this.statusOptions = wo.status?.options
            ?.map((status) => status.value)
            .filter((value) => value.toLowerCase() != 'published');
    }
    private generateAFENumberString(tasks: Task[]) {
        let output = '';

        tasks.forEach((task) => {
            if (task.purchaseOrderAFE.currentValue !== null) {
                output += task.purchaseOrderAFE.currentValue + '; ';
            }
        });

        output = output.trim();
        output = output.substring(0, output.length - 1);

        return output;
    }
}
