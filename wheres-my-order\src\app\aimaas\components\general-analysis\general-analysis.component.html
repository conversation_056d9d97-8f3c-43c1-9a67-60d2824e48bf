<section class="scrollsection"
         id="top-container"
         *ngIf="generalanalysis">

    <p style="font-size: 16px;
    font-weight: 500;">ANALYSIS</p>
    <div>
        <div class="container"
             *ngFor="let component of generalanalysis">
            <div class="analysis">
                <div class="left">
                    <label><b>Retirement Date</b></label>
                    <span>{{component?.retirementdate ? component.retirementdate
                        : 'Cannot calculate'}}</span>
                    <label><b>Remaining Life</b></label>
                    <span>{{
                        calculateRemainingLife(component?.remaininglife,component?.retirementdate)}}</span>
                    <label><b>Driving Component</b></label>
                    <span>{{component?.retirementdate ?
                        component?.drivingcomponent : 'N/A'
                        }}</span>
                    <label><b>Driving CML</b></label>
                    <span>{{component?.retirementdate ?
                        component?.drivingcml : 'N/A'}}</span>
                </div>
                <div class="right">
                    <label><b>LT Corrosion Rate</b></label>
                    <span>{{calculateCorrosionRate(component?.ltcorrosionrate,component?.retirementdate)}}</span>
                    <label><b>ST Corrosion Rate</b></label>
                    <span>{{calculateCorrosionRate(component?.stcorrosionrate,component?.retirementdate)}}</span>
                    <label><b>Nominal</b></label>
                    <span>{{calculateThickness(component?.nominalthickness,component?.retirementdate)}}</span>
                    <label><b>Retirement Thickness</b></label>
                    <span>{{calculateThickness(component?.retirementthickness,component?.retirementdate)}}</span>
                </div>
            </div>
            <div class="data">
                <div class="row">
                    <label><b>Last</b></label>
                    <span>{{ component?.retirementdate ? component?.lastdate :
                        '' }}</span>
                    <span>{{calculateThickness(component?.lastvalue,component?.retirementdate)}}</span>
                </div>
                <div class="row">
                    <label><b>Previous</b></label>
                    <span>{{component?.retirementdate ? component?.previousdate
                        : '' }}</span>
                    <span>{{calculateThickness(component?.previousvalue,component?.retirementdate)}}</span>
                </div>
                <div class="row">
                    <label><b>First</b></label>
                    <span>{{component?.retirementdate ? component?.firstdate :
                        ''}}</span>
                    <span>{{calculateThickness(component?.firstvalue,component?.retirementdate)}}</span>
                </div>
            </div>
        </div>
        <section *ngIf="generalanalysis?.length > 0"
                 class="container">
            <p> <strong>Disclaimer : </strong>Values may be slightly different
                than
                those found in TEAM
                inspection reports. </p>
        </section>
    </div>
</section>
<section *ngIf="generalanalysis?.length == 0"
         style="padding-left: 500px;">
    <p> No Data</p>
</section>
<dx-load-panel #loadPanel
               shadingColor="rgba(0,0,0,0.4)"
               [position]="{ of: '#top-container' }"
               [(visible)]="loadingVisible"
               [showIndicator]="true"
               [showPane]="true"
               [shading]="false"
               [hideOnOutsideClick]="false">
</dx-load-panel>