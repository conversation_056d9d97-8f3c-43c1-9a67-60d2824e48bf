import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import ArrayStore from 'devextreme/data/array_store';
import DataSource from 'devextreme/data/data_source';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { environment } from '../../../environments/environment';
import { UserProfile } from '../../profile/models';
import { Role } from '../models';

@Injectable({
    providedIn: 'root',
})
export class RolesService {
    constructor(private readonly _httpService: HttpClient) {}

    create(role: Role): Observable<any> {
        return this._httpService
            .post<Role>(`${environment.api.url}/roles`, role, {
                observe: 'response',
            })
            .pipe(
                map((response) => {
                    // Placeholder for now ..demonstrates how to get the header
                    //  even though we are not using it
                    const createdAtLocation = response.headers.get('location');
                })
            );
    }

    delete(roleId: string): Observable<any> {
        return this._httpService.delete<Role>(
            `${environment.api.url}/roles/${roleId}`
        );
    }

    update(role: Role, originalRoleId: string): Observable<any> {
        return this._httpService.put<Role>(
            `${environment.api.url}/roles/?originalId=${originalRoleId}`,
            role
        );
    }

    getRoles(): Observable<Role[]> {
        return this._httpService.get<Role[]>(`${environment.api.url}/roles`);
    }

    getUsersForRole(role: string): Observable<UserProfile[]> {
        return this._httpService.get<UserProfile[]>(
            `${environment.api.url}/roles/${role}/users`
        );
    }

    getRoleKeys(): Observable<string[]> {
        return this.getRoles().pipe(map((r) => r.map((s) => s.id)));
    }

    getAllAsDataSource(): Observable<DataSource> {
        return this.getRoles().pipe(
            map((roles) => {
                return new DataSource({
                    store: new ArrayStore({
                        data: roles,
                        key: 'id',
                    }),
                    sort: 'id',
                });
            })
        );
    }

    addUserToRole(role: string, user: string): Observable<any> {
        return this._httpService.put(
            `${environment.api.url}/roles/${role}/users/${user}`,
            null
        );
    }

    deleteUserFromRole(role: string, user: string): Observable<any> {
        return this._httpService.delete<UserProfile>(
            `${environment.api.url}/roles/${role}/users/${user}`
        );
    }
}
