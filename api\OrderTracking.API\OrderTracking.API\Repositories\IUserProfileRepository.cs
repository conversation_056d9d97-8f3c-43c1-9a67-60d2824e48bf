﻿using System.Collections.Generic;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    ///     Repository for UserProfiles (migrated to Azure Cosmos DB)
    /// </summary>
    public interface IUserProfileRepository : IAsyncCosmosRepository<UserProfile, string>
    {
        #region Public Methods

        /// <summary>
        ///     Get users that belong to a specific group
        /// </summary>
        /// <param name="group"></param>
        /// <returns></returns>
        Task<IEnumerable<UserProfile>> GetUsersForGroupAsync(string group);

        /// <summary>
        ///     Get users that have a specific role
        /// </summary>
        /// <param name="role"></param>
        /// <returns></returns>
        Task<IEnumerable<UserProfile>> GetUsersForRoleAsync(string role);

        #endregion
    }
}