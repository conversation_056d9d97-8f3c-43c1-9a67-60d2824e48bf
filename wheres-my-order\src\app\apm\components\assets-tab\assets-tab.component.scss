.popup-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .actions {
        display: flex;
        justify-content: flex-end;
        dx-button {
            margin-left: 1rem;
        }
    }
}

::ng-deep
    .dx-item.dx-list-item.dx-toolbar-menu-custom.dx-toolbar-menu-action.dx-toolbar-hidden-button
    .dx-button-default {
    color: black !important;
}
