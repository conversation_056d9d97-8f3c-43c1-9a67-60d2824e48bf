<dx-form *ngIf="formData"
         [(formData)]="formData">
    <dxi-item itemType="group"
              [colCount]="2">
        <dxi-item dataField="projectId"
                  editorType="dxSelectBox"
                  [editorOptions]="{showClearButton: true, searchEnabled: true, searchExpr: ['name'], hint: 'Search by Project Name', dataSource: projectsDataSource, valueExpr: 'id', displayExpr: 'name', onValueChanged: onProjectSelected}">
            <dxo-label text="Project"></dxo-label>
            <dxi-validation-rule type="required">
            </dxi-validation-rule>
        </dxi-item>
        <dxi-item dataField="assetId"
                  editorType="dxSelectBox"
                  [editorOptions]="{showClearButton: true, searchEnabled: true, searchExpr: ['assetCategory', 'equipmentId'], hint: 'Search by Asset Category or Asset ID', dataSource: assetsForDropdown, valueExpr: 'id', paginate: true, displayExpr: assetDisplayExpr}">
            <dxo-label text="Asset"></dxo-label>
            <dxi-validation-rule type="required">
            </dxi-validation-rule>
        </dxi-item>
    </dxi-item>
    <dxi-item itemType="group"
              caption="Work Order"
              [colCount]="2">
        <dxi-item dataField="dueDate"
                  editorType="dxDateBox">
            <dxo-label text="Due Date"></dxo-label>
        </dxi-item>
        <dxi-item dataField="facilityName">
            <dxo-label text="Facility Name"></dxo-label>
        </dxi-item>
        <dxi-item dataField="plannedStart"
                  editorType="dxDateBox">
            <dxo-label text="Planned Start"></dxo-label>
        </dxi-item>
        <dxi-item dataField="plannedEnd"
                  editorType="dxDateBox">
            <dxo-label text="Planned End"></dxo-label>
        </dxi-item>
        <dxi-item dataField="status"
                  editorType="dxSelectBox"
                  [editorOptions]="{items: ['Scheduled', 'In Progress', 'Completed']}">
        </dxi-item>
    </dxi-item>
</dx-form>
