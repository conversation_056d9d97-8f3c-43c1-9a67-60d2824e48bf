<app-breadcrumbs [crumbs]="crumbs"></app-breadcrumbs>

<!-- Main content -->
<div class="dx-card content-block responsive-paddings">

    <!-- Grid -->
    <dx-data-grid #grid
                  [dataSource]="users$ | async"
                  [rowAlternationEnabled]="true"
                  [showBorders]="true"
                  [wordWrapEnabled]="true"
                  (onToolbarPreparing)="onToolbarPreparing($event)">

        <!-- Options -->
        <dxo-paging [pageSize]="10"></dxo-paging>
        <dxo-pager [showPageSizeSelector]="true"
                   [allowedPageSizes]="[5, 10, 20, 50]"
                   [showInfo]="true"></dxo-pager>
        <dxo-sorting mode="multiple"></dxo-sorting>
        <dxo-filter-row [visible]="true"></dxo-filter-row>
        <dxo-header-filter [visible]="true"></dxo-header-filter>
        <dxo-search-panel [visible]="true"
                          [width]="240"
                          placeholder="Search..."></dxo-search-panel>
        <dxo-selection [selectAllMode]="'allPages'"
                       mode="multiple"></dxo-selection>
        <dxo-export [enabled]="true"
                    [allowExportSelectedData]="true"></dxo-export>
        <dxo-filter-panel [visible]="true"></dxo-filter-panel>

        <!-- Columns -->
        <dxi-column dataField="name"
                    dataType="string"></dxi-column>
        <dxi-column dataField="id"
                    dataType="string"></dxi-column>
        <dxi-column dataField="givenName"
                    dataType="string"></dxi-column>
        <dxi-column dataField="surname"
                    dataType="string"></dxi-column>
        <dxi-column dataField="roles"
                    [calculateDisplayValue]="rolesDisplayValue"
                    [calculateFilterExpression]="rolesFilterExpression"
                    [lookup]="{ dataSource: allRoles$ | async }">
        </dxi-column>
        <dxi-column dataField="districtIds"
                    [calculateDisplayValue]="districtIdsDisplayValue"
                    [calculateFilterExpression]="districtFilterExpression"
                    [lookup]="{ dataSource: allDistrictIds$ | async }">
        </dxi-column>
        <dxi-column dataField="customerAccounts"
                    [calculateDisplayValue]="customerAccountsDisplayValue"
                    [calculateFilterExpression]="customerAccountsFilterExpression"
                    [lookup]="{ dataSource: allCustomerAccounts$ | async }">
        </dxi-column>
    </dx-data-grid>
</div>
