import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
    DxFormModule,
    DxGalleryModule,
    DxLoadIndicatorModule,
    DxPopupModule
} from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { PhotoSasPipe } from '../../pipes';
import { ApmService } from '../../services';
import { ApmAssetDetailsSixFiftyThreeComponent } from './apm-asset-details-six-fifty-three.component';

describe('ApmAssetDetailsSixFiftyThreeComponent', () => {
    let component: ApmAssetDetailsSixFiftyThreeComponent;
    let fixture: ComponentFixture<ApmAssetDetailsSixFiftyThreeComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxFormModule,
                DxPopupModule,
                DxGalleryModule,
                DxLoadIndicatorModule,
                HttpClientTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [ApmAssetDetailsSixFiftyThreeComponent],
            providers: [{ provide: ApmService, useValue: {} }, PhotoSasPipe]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(
            ApmAssetDetailsSixFiftyThreeComponent
        );
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
