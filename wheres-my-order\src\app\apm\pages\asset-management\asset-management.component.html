<div class="dx-card content-block responsive-paddings">
    <h4>Asset Management</h4>
    <dx-data-grid #assetsGrid
                  [dataSource]="assetsDataSource"
                  [remoteOperations]="true"
                  [columnAutoWidth]="true"
                  [allowColumnResizing]="true"
                  columnResizingMode="widget"
                  [allowColumnReordering]="true"
                  (onSelectionChanged)="onAssetSelected($event)"
                  (onRowUpdating)="onRowUpdating($event)"
                  (onRowUpdated)="onAssetUpdated($event)">

        <dxo-toolbar>
            <dxi-item name="groupPanel"></dxi-item>
            <dxi-item widget="dxButton"
                      location="after"
                      [options]="{icon: null, text: 'Create', type: 'success', stylingMode: 'contained', disabled: disableAddButton$ | async, onClick: customAssetAddClicked}">
            </dxi-item>
            <dxi-item widget="dxButton"
                      location="after"
                      [options]="{icon: 'fa fa-undo', hint: 'Restore Grid Defaults', onClick: restoreAssetsDefaultsClicked}">
            </dxi-item>
            <dxi-item name="columnChooserButton"></dxi-item>
            <dxi-item name="exportButton"></dxi-item>
            <dxi-item name="searchPanel"></dxi-item>
        </dxo-toolbar>

        <dxo-state-storing [enabled]="true"
                           type="localStorage"
                           storageKey="assetManagementAssetsGrid">
        </dxo-state-storing>

        <dxo-paging [pageSize]="10"></dxo-paging>
        <dxo-pager [showInfo]="true"
                   [showPageSizeSelector]="true"
                   [allowedPageSizes]="[5, 10, 20, 50]"
                   [showNavigationButtons]="true">
        </dxo-pager>

        <dxo-editing [allowUpdating]="allowEditing$ | async"
                     [allowAdding]="false"
                     [allowDeleting]="false"
                     [mode]="'row'">
        </dxo-editing>

        <dxo-filter-row [visible]="true"></dxo-filter-row>
        <dxo-header-filter [visible]="true"></dxo-header-filter>
        <dxo-search-panel [visible]="true"></dxo-search-panel>
        <dxo-filter-panel [visible]="true"></dxo-filter-panel>

        <dxo-grouping [contextMenuEnabled]="true"></dxo-grouping>
        <dxo-group-panel [visible]="'auto'"></dxo-group-panel>

        <dxo-column-chooser [enabled]="true"
                            mode="dragAndDrop">
        </dxo-column-chooser>

        <dxo-export [enabled]="true"
                    [allowExportSelectedData]="false"></dxo-export>

        <dxo-selection mode="single"></dxo-selection>

        <!-- COLUMNS -->
        <dxi-column dataField="id"
                    caption="Asset Database ID"
                    [visible]="false"
                    [allowEditing]="false"></dxi-column>
        <dxi-column dataField="assetCategory"
                    [allowEditing]="false"></dxi-column>
        <dxi-column dataField="equipmentId"
                    caption="Asset ID"></dxi-column>
        <dxi-column dataField="assetName"></dxi-column>
        <dxi-column dataField="assetType"></dxi-column>
        <dxi-column dataField="assetDescription"></dxi-column>
        <dxi-column dataField="unit"></dxi-column>
        <dxi-column dataField="area"></dxi-column>
        <dxi-column dataField="gisLocation"
                    caption="GIS Location"
                    [allowEditing]="false"></dxi-column>
        <dxi-column dataField="workOrderIds"
                    caption="Work Order Database IDs"
                    [visible]="false"
                    [allowEditing]="false"></dxi-column>

    </dx-data-grid>
</div>

<div class="dx-card content-block responsive-paddings">
    <dx-tab-panel>
        <dxi-item *ngIf="assetsGrid.selectedRowKeys?.length > 0"
                  title="Work Orders">
            <div class="responsive-paddings">
                <dx-data-grid #woGrid
                              [dataSource]="workOrdersDataSource"
                              [remoteOperations]="true"
                              [columnAutoWidth]="true"
                              [allowColumnResizing]="true"
                              columnResizingMode="widget"
                              [allowColumnReordering]="true"
                              (onInitialized)="onWOGridInitialized($event)"
                              (onRowUpdating)="onRowUpdating($event)"
                              (onRowUpdated)="onWorkOrderUpdated($event)">

                    <dxo-toolbar>
                        <dxi-item name="groupPanel"></dxi-item>
                        <dxi-item widget="dxButton"
                                  location="after"
                                  [options]="{text: 'Create', type: 'success', stylingMode: 'contained', onClick: customAddWorkOrderClicked}">
                        </dxi-item>
                        <dxi-item widget="dxButton"
                                  location="after"
                                  [options]="{icon: 'fa fa-undo', hint: 'Restore Grid Defaults', onClick: restoreWorkOrderDefaultsClicked}">
                        </dxi-item>
                        <dxi-item name="columnChooserButton"></dxi-item>
                        <dxi-item name="exportButton"></dxi-item>
                        <dxi-item name="searchPanel"></dxi-item>
                    </dxo-toolbar>

                    <dxo-state-storing [enabled]="true"
                                       type="localStorage"
                                       storageKey="assetManagementWorkOrdersGrid">
                    </dxo-state-storing>

                    <dxo-editing [allowUpdating]="allowEditing$ | async"
                                 [allowAdding]="false"
                                 [allowDeleting]="false"
                                 mode="row"></dxo-editing>

                    <dxo-filter-row [visible]="true"></dxo-filter-row>
                    <dxo-header-filter [visible]="true"></dxo-header-filter>
                    <dxo-search-panel [visible]="true"></dxo-search-panel>
                    <dxo-filter-panel [visible]="true"></dxo-filter-panel>

                    <dxo-grouping [contextMenuEnabled]="true"></dxo-grouping>
                    <dxo-group-panel [visible]="'auto'"></dxo-group-panel>

                    <dxo-column-chooser [enabled]="true"
                                        mode="dragAndDrop"></dxo-column-chooser>

                    <dxo-export [enabled]="true"
                                [allowExportSelectedData]="false"></dxo-export>

                    <!-- COLUMNS -->
                    <dxi-column dataField="projectId"
                                caption="Project"
                                [allowFiltering]="true"
                                [allowEditing]="false">
                        <dxo-lookup [dataSource]="projectDataSource"
                                    valueExpr="id"
                                    displayExpr="name"></dxo-lookup>
                    </dxi-column>
                    <dxi-column dataField="projectId"
                                caption="Project Database ID"
                                [visible]="false"
                                [allowEditing]="false"></dxi-column>
                    <dxi-column dataField="id"
                                caption="Work Order Database ID"
                                [visible]="false"
                                [allowEditing]="false"></dxi-column>
                    <dxi-column dataField="apmWorkOrderNumber"
                                caption="APM Work Order Number"
                                [allowGrouping]="false"
                                [allowEditing]="false"
                                [visible]="false"
                                cellTemplate="apmWorkOrderNumberTemplate">
                    </dxi-column>
                    <dxi-column dataField="facilityName"></dxi-column>
                    <dxi-column dataField="plannedStart"
                                dataType="date"></dxi-column>
                    <dxi-column dataField="plannedEnd"
                                dataType="date"></dxi-column>
                    <dxi-column dataField="dueDate"
                                dataType="date"></dxi-column>
                    <dxi-column dataField="status">
                        <dxo-lookup
                                    [dataSource]="['Scheduled', 'In Progress', 'Completed']">
                        </dxo-lookup>
                    </dxi-column>
                    <dxi-column type="buttons">
                        <dxi-button name="edit"></dxi-button>
                        <dxi-button icon="fa fa-tasks"
                                    hint="Work Order Details"
                                    [onClick]="workOrderDetailsClicked">
                        </dxi-button>
                    </dxi-column>

                    <div *dxTemplate="let cell of 'apmWorkOrderNumberTemplate'">
                        <a [routerLink]="['/apm/work-order-details', cell.data.id]"
                           [queryParams]="{projectId: cell.data.projectId}">{{cell.text}}</a>
                    </div>

                </dx-data-grid>
            </div>
        </dxi-item>
    </dx-tab-panel>
</div>

<app-creation-workflow (assetCreated)="onAssetCreated()"
                       (workOrderCreated)="onWorkOrderCreated()">
</app-creation-workflow>