﻿using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Identity.Web;
using Microsoft.PowerBI.Api;
using Microsoft.Rest;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     A view model class to pass the data needed to embed a single report.
    /// </summary>
    public class EmbeddedReportViewModel
    {
        public string Id;
        public string Name;
        public string EmbedUrl;
        public string Token;
    }

	public class PowerBiServiceApi
    {
        private ITokenAcquisition tokenAcquisition { get; }
        private string urlPowerBiServiceApiRoot { get; }

        public PowerBiServiceApi(IConfiguration configuration, ITokenAcquisition tokenAcquisition)
        {
            this.urlPowerBiServiceApiRoot = configuration["PowerBi:ServiceRootUrl"];
            this.tokenAcquisition = tokenAcquisition;
        }

        public static readonly string[] RequiredScopes = {
            "https://analysis.windows.net/powerbi/api/Report.Read.All"
        };

        // A method to get the Azure AD token (also known as 'access token')
        public string GetAccessToken()
        {
            return tokenAcquisition.GetAccessTokenForUserAsync(RequiredScopes).Result;
        }

        public PowerBIClient GetPowerBiClient()
        {
            var tokenCredentials = new TokenCredentials(GetAccessToken(), "Bearer");
            return new PowerBIClient(new Uri(urlPowerBiServiceApiRoot), tokenCredentials);
        }

        public async Task<EmbeddedReportViewModel> GetReport(Guid WorkspaceId, Guid ReportId)
        {
            var pbiClient = GetPowerBiClient();

            // Call the Power BI Service API to get embedding data
            var report = await pbiClient.Reports.GetReportInGroupAsync(WorkspaceId, ReportId);

            // Return report embedding data to caller
            return new EmbeddedReportViewModel
            {
                Id = report.Id.ToString(),
                EmbedUrl = report.EmbedUrl,
                Name = report.Name,
                Token = GetAccessToken()
            };
        }
    }
}
