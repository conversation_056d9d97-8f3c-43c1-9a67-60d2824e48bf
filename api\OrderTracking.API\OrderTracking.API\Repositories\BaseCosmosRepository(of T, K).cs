﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Net;
//using System.Threading.Tasks;
//using ClientPortal.Shared.Models;
//using Microsoft.Azure.Cosmos;
//using Newtonsoft.Json;

//namespace OrderTracking.API.Repositories
//{
//    /// <summary>
//    ///     Base class that implements standard CRUD operations for an Entity within the specified container.  Derived Classes
//    ///     need to specify the entity, and the types of the Id and the Partition.  This class, however, does not
//    ///     know how to handle situation where no PartitionId is supplied (and there is no Entity), so those methods are
//    ///     declared as
//    ///     abstract members.  For example, GetAsync(id).
//    /// </summary>
//    /// <typeparam name="TEntity">Data type of the Entity to be operated on</typeparam>
//    /// <typeparam name="TKey">Data type of the "id" field of the Container</typeparam>
//    public abstract class
//        BaseCosmosRepository<TEntity, TKey> : IAsyncCosmosRepository<TEntity, TKey>
//        where TEntity : ICosmosEntity<TKey>
//    {
//        #region Public Methods

//        /// <summary>
//        ///     Get PartitionKey property name from the partition key path.
//        /// </summary>
//        /// <param name="path"></param>
//        protected void SetPartitionPropertyFromPath(string path)
//        {
//            if (path == null) throw new ArgumentNullException(nameof(path));

//            var t = typeof(TEntity);
//            var name = path.Replace("/", "", StringComparison.InvariantCulture);

//            // Check Json property values that match
//            var partitionPropertyName = (
//                from pInfo in t.GetProperties()
//                let pMatch =
//                    (pInfo.GetCustomAttributes(typeof(JsonPropertyAttribute), false) as JsonPropertyAttribute[])
//                    ?.FirstOrDefault(a => a.PropertyName == name)
//                where pMatch != null
//                select pInfo.Name
//            ).FirstOrDefault();

//            // Check based on string -- Pascal or Camel case
//            if (partitionPropertyName == null)
//            {
//                // Convert to PascalCase
//                var pascalName = $"{name.Substring(0, 1).ToUpper()}{name.Substring(1)}";
//                var prop = t.GetProperties().FirstOrDefault(p => p.Name == name || p.Name == pascalName);
//                partitionPropertyName = prop?.Name;
//            }

//            PartitionProperty = partitionPropertyName ??
//                                throw new KeyNotFoundException(
//                                    $"A property matching the specified path ({path} ) could not be found.");
//        }

//        #endregion

//        #region Constructors

//        /// <summary>
//        ///     Constructs a BaseCosmosRepository via a cosmos db container
//        /// </summary>
//        /// <param name="container"></param>
//        protected BaseCosmosRepository(Container container)
//        {
//            Container = container;
//        }

//        /// <summary>
//        ///     Default constructor
//        /// </summary>
//        protected BaseCosmosRepository()
//        {
//        }

//        #endregion

//        #region Properties

//        /// <summary>
//        ///     The appropriate cosmos db container to operate on.
//        /// </summary>
//        protected Container Container { get; set; }

//        /// <summary>
//        ///     The partition key property name that was chosen for the appropriate cosmos db container
//        /// </summary>
//        protected string PartitionProperty { get; set; }

//        #endregion

//        #region Interface Implementation

//        /// <summary>
//        ///     Add an entity to the appropriate cosmos db container
//        /// </summary>
//        /// <param name="entity"></param>
//        /// <returns></returns>
//        public async Task<TEntity> AddAsync(TEntity entity)
//        {
//            try
//            {
//                var partitionKey = entity.GetPartitionKey(PartitionProperty);
//                var response = await Container.CreateItemAsync(entity, partitionKey);
//                return response.Resource;
//            }
//            catch (CosmosException e)
//            {
//                if (e.StatusCode == HttpStatusCode.Conflict) return default;
//                throw;
//            }
//        }

//        /// <summary>
//        ///     Get all entities from the appropriate cosmos db container
//        /// </summary>
//        /// <returns></returns>
//        public virtual async Task<IEnumerable<TEntity>> GetAllAsync()
//        {
//            using var query = Container.GetItemQueryIterator<TEntity>(
//                new QueryDefinition("select * from c"));
//            var entities = new List<TEntity>();
//            while (query.HasMoreResults) entities.AddRange(await query.ReadNextAsync());
//            return entities;
//        }

//        /// <summary>
//        ///     Get an individual entity from the appropriate cosmos db container
//        /// </summary>
//        /// <param name="entityId"></param>
//        /// <returns></returns>
//        public abstract Task<TEntity> GetAsync(TKey entityId);

//        /// <summary>
//        ///     Get an individual entity from the appropriate cosmos db container, and use the partition key to make the query
//        ///     faster
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="partitionId"></param>
//        /// <returns></returns>
//        public virtual async Task<TEntity> GetAsync(TKey id, string partitionId)
//        {
//            try
//            {
//                var response = await Container.ReadItemAsync<TEntity>(id.ToString().ToLower(), new PartitionKey(partitionId.ToLower()));
//                return response.Resource;
//            }
//            catch (CosmosException e)
//            {
//                if (e.StatusCode == HttpStatusCode.NotFound) return default;
//                Console.WriteLine(e);
//                throw;
//            }
//        }

//        /// <summary>
//        ///     Remove an entity from the appropriate cosmos db container
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="partitionId"></param>
//        /// <returns></returns>
//        public async Task RemoveAsync(TKey id, string partitionId)
//        {
//            try
//            {
//                await Container.DeleteItemAsync<TEntity>(id.ToString(), new PartitionKey(partitionId));
//            }
//            catch (CosmosException e)
//            {
//                // Let NotFound pass through -- don't need to raise an error for that
//                if (e.StatusCode != HttpStatusCode.NotFound)
//                {
//                    Console.WriteLine(e);
//                    throw;
//                }
//            }
//        }

//        /// <summary>
//        ///     Remove an entity from the appropriate cosmos db container via its id property.
//        /// </summary>
//        /// <param name="id"></param>
//        /// <returns></returns>
//        public abstract Task RemoveAsync(TKey id);

//        /// <summary>
//        ///     Remove an entity from the appropriate cosmos db container via the entity itself.
//        /// </summary>
//        /// <param name="entity"></param>
//        /// <returns></returns>
//        public async Task RemoveAsync(TEntity entity)
//        {
//            var partitionKey = entity.GetPartitionKey(PartitionProperty);
//            await Container.DeleteItemAsync<TEntity>(entity.Id.ToString(), partitionKey);
//        }

//        /// <summary>
//        ///     Update an entity in the appropriate cosmos db container.
//        /// </summary>
//        /// <param name="entity"></param>
//        /// <returns></returns>
//        public async Task<TEntity> UpdateAsync(TEntity entity)
//        {
//            try
//            {
//                var partitionKey = entity.GetPartitionKey(PartitionProperty);
//                return await Container.ReplaceItemAsync(entity, entity.Id.ToString(), partitionKey);
//            }
//            catch (CosmosException e)
//            {
//                if (e.StatusCode == HttpStatusCode.NotFound) return default;
//                Console.WriteLine(e);
//                throw;
//            }
//        }

//        /// <summary>
//        ///     Update an entity in the appropriate cosmos db container, only if different than <paramref name="originalEntity" />
//        /// </summary>
//        /// <param name="entity"></param>
//        /// <param name="originalEntity"></param>
//        /// <returns></returns>
//        public async Task<TEntity> UpdateAsync(TEntity entity, TEntity originalEntity)
//        {
//            if (entity.Id.Equals(originalEntity.Id))
//                return await UpdateAsync(entity);
//            var partitionId = originalEntity.GetPropertyValue(PartitionProperty).ToString();
//            return await UpdateAsync(entity, originalEntity.Id, partitionId);
//        }

//        /// <summary>
//        ///     Update an entity in the appropriate cosmos db container via the id.
//        /// </summary>
//        /// <param name="entity"></param>
//        /// <param name="originalId"></param>
//        /// <returns></returns>
//        public abstract Task<TEntity> UpdateAsync(TEntity entity, TKey originalId);

//        /// <summary>
//        ///     Update an entity in the appropriate cosmos db container via the id and partition key.
//        /// </summary>
//        /// <param name="entity"></param>
//        /// <param name="originalId"></param>
//        /// <param name="originalPartitionId"></param>
//        /// <returns></returns>
//        public async Task<TEntity> UpdateAsync(TEntity entity, TKey originalId, string originalPartitionId)
//        {
//            //If no Id change, handle like regular update
//            if (entity.Id.Equals(originalId)) return await UpdateAsync(entity);

//            //Handle update logic to test if new Id already exists.  We do not want to overwrite the
//            //  existing record
//            var newId = entity.Id;
//            var existingEntity = GetAsync(newId).Result;
//            if (existingEntity == null)
//            {
//                var taskAdd = AddAsync(entity);
//                var taskRemove = RemoveAsync(originalId, originalPartitionId);

//                await Task.WhenAll(taskAdd, taskRemove);
//                return taskAdd.Result;
//            }

//            // Conflict, return null to indicate cannot update
//            return default;
//        }

//        #endregion
//    }
//}