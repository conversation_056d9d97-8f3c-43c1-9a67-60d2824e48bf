#region Copyright Quest Integrity Group, LLC 2020

// www.QuestIntegrity.com
// +1-303-415-1475
// 
// All rights are reserved. Reproduction or transmission in whole or in part, in
// any form or by any means, electronic, mechanical or otherwise, is prohibited
// without the prior written consent of the copyright owner.
// 
// File Created: 2020-01-14 7:33 AM
// Updated:      2020-01-14 7:33 AM
// Created by:   <PERSON><PERSON><PERSON><PERSON>, Sean

#endregion

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using ClientPortal.Shared.Extensions;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.EDR
{
    /// <summary>
    ///     Equipment request for EDR module
    /// </summary>
    public class EquipmentRequest
    {
        /// <summary>
        ///     Unique identifier for each equipment request
        /// </summary>
        [JsonProperty(PropertyName = "id")] public string Id { get; set; }

        /// <summary>
        ///     Which client the equipment request is associated with
        /// </summary>
        [JsonProperty(PropertyName = "client")]
        public string Client { get; set; }

        /// <summary>
        ///     Job Date
        /// </summary>
        [JsonProperty(PropertyName = "jobDate")]
        [DisplayName("Job Date")]
        public DateTime JobDate { get; set; }
        
        /// <summary>
        ///     Shipping address
        /// </summary>
        [JsonProperty(PropertyName = "shippingAddress")]
        public Address ShippingAddress { get; set; }

        /// <summary>
        ///     Job Number
        /// </summary>
        [JsonProperty(PropertyName = "jobNumber")]
        public string JobNumber { get; set; }
        
        /// <summary>
        ///     Whether it was shipped or not
        /// </summary>
        [JsonProperty(PropertyName = "shipped")]
        public bool Shipped { get; set; }

        /// <summary>
        ///     Name of district
        /// </summary>
        [JsonProperty(PropertyName = "districtName")]
        [DisplayName("District Name")]
        [Obsolete("Do not use this property.  Use District ID instead")]
        public string DistrictName { get; set; }

        /// <summary>
        ///     ID or number of a district
        /// </summary>
        [JsonProperty(PropertyName = "districtId")]
        [DisplayName("District ID")]
        public string DistrictId { get; set; }

        /// <summary>
        ///     Technician assigned to quality control
        /// </summary>
        [JsonProperty(PropertyName = "qualityControlTechnician")]
        public string QualityControlTechnician { get; set; }

        /// <summary>
        ///     Status of Quality Control
        /// </summary>
        [JsonProperty(PropertyName = "qualityControlStatus")]
        public string QualityControlStatus { get; set; }

        /// <summary>
        ///     Whether the equipment is returned or not
        /// </summary>
        [JsonProperty(PropertyName = "equipmentReturned")]
        public bool EquipmentReturned { get; set; }
        
        /// <summary>
        ///     Note during the return process
        /// </summary>
        [JsonProperty(PropertyName = "returnNote")]
        public string ReturnNote { get; set; }

        /// <summary>
        ///     What equipment center the equipment is associated with
        /// </summary>
        [JsonProperty(PropertyName = "equipmentCenter")]
        [DisplayName("Equipment Center")]
        public string EquipmentCenter { get; set; }

        /// <summary>
        ///     Who to send the equipment request to
        /// </summary>
        [JsonProperty(PropertyName = "sendTo")]
        public ICollection<string> SendTo { get; set; }

        /// <summary>
        ///     Job types of the equipment
        /// </summary>
        [JsonProperty(PropertyName = "jobTypes")]
        [MinLength(1, ErrorMessage = "Equipment Requests must have at least 1 Job Type")]
        public ICollection<JobType> JobTypes { get; set; }


        /// <summary>
        ///     Files that are associated with this equipment request
        /// </summary>
        [JsonProperty("files")] public ICollection<EquipmentRequestBlobFile> Files { get; set; }

        /// <summary>
        ///     Date received
        /// </summary>
        [JsonProperty("receivedDate")] public DateTime ReceivedDate { get; set; }

        /// <summary>
        ///     Shipping point of contact (name and phone number)
        /// </summary>
        [JsonProperty(PropertyName = "shippingPointOfContact")]
        public ShippingPointOfContact ShippingPointOfContact { get; set; }

        /// <summary>
        ///     Email of user who created the request
        /// </summary>
        [JsonProperty(PropertyName = "requester")]
        public string Requester { get; set; }

        /// <summary>
        ///     Whether or not the request will be picked up at will call at the EQC
        /// </summary>
        [JsonProperty(PropertyName = "willCallOrder")]
        public bool WillCallOrder { get; set; }

        /// <summary>
        ///     Override of the ToString method.
        /// </summary>
        /// <returns></returns>
        public override string ToString() => JsonConvert.SerializeObject(this);

        /// <summary>
        ///     Create an instance of EquipmentRequest from a section of an IFormCollection
        /// </summary>
        /// <param name="formValues"></param>
        /// <returns></returns>
        public static EquipmentRequest From(StringValues formValues)=>
            JsonConvert.DeserializeObject<EquipmentRequest>(formValues);


        /// <summary>
        ///    Creates a list of fields missing from shipping info (ignoring address line 2)
        /// </summary>
        /// <param></param>
        /// <returns name="missingFields"></returns>
        public IEnumerable<string> GetMissingShippingInfo()
        {
            var missingFields = new List<string>();
            if(string.IsNullOrWhiteSpace(ShippingAddress?.Line1))
                missingFields.Add("Street Address");
            if (string.IsNullOrWhiteSpace(ShippingAddress?.City))
                missingFields.Add("City");
            if (string.IsNullOrWhiteSpace(ShippingAddress?.State))
                missingFields.Add("State");
            if (string.IsNullOrWhiteSpace(ShippingAddress?.ZipCode))
                missingFields.Add("Zip Code");
            if(string.IsNullOrWhiteSpace(ShippingPointOfContact?.Name))
                missingFields.Add("Shipping Point of Contact Name");
            if (string.IsNullOrWhiteSpace(ShippingPointOfContact?.Phone))
                missingFields.Add("Shipping Point of Contact Phone Number");
            
            return missingFields;
        }

        /// <summary>
        ///     Returns true if there are differences between the original job types collection and the new job types collection
        /// </summary>
        /// <param name="updatedRequest"></param>
        /// <returns></returns>
        public bool HasFileChanges(EquipmentRequest updatedRequest)
        {
            return !(this.Files.Count == updatedRequest.Files.Count &&
                   this.Files.All(file => updatedRequest.Files.Any(otherFile => otherFile.Id == file.Id)) &&
                   updatedRequest.Files.All(file => this.Files.Any(otherFile => otherFile.Id == file.Id)));
        }

        /// <summary>
        ///     Returns true if there are differences between the original job types collection and the new job types collection
        /// </summary>
        /// <param name="updatedRequest"></param>
        /// <returns></returns>
        public bool HasFieldChanges(EquipmentRequest updatedRequest)
        {
            var topLevelFields = new[]
            {
                nameof(DistrictName),
                nameof(Client),
                nameof(JobNumber),
                nameof(JobDate),
                nameof(EquipmentCenter)
            };

            var topLevelChanges = this.DetailedPropertyCompare(updatedRequest)
                .Where(change => topLevelFields.Contains(change.PropertyName)).ToList();

            var hasShippingChanges = this.ShippingAddress != null && updatedRequest.ShippingAddress != null
                ? this.ShippingAddress.DetailedPropertyCompare(updatedRequest.ShippingAddress).Any()
                : this.ShippingAddress == null ^ updatedRequest.ShippingAddress == null;

            var hasShippingPOCChanges =
                this.ShippingPointOfContact != null && updatedRequest.ShippingPointOfContact != null
                    ? this.ShippingPointOfContact.DetailedPropertyCompare(updatedRequest.ShippingPointOfContact).Any()
                    : this.ShippingPointOfContact == null ^ updatedRequest.ShippingPointOfContact == null;
            
            return topLevelChanges.Any() || hasShippingChanges || hasShippingPOCChanges;
            
        }

        /// <summary>
        ///     Returns true if there are differences between the original job types collection and the new job types collection
        /// </summary>
        /// <param name="originalRequest"></param>
        /// <param name="updatedRequest"></param>
        /// <returns name="changes"></returns>
        public static EquipmentRequestFieldChangeSummary DetectJobInfoChanges(EquipmentRequest originalRequest, EquipmentRequest updatedRequest)
        {
            // Throw an exception if we don't have enough information
            if (originalRequest == null) throw new ArgumentNullException(nameof(originalRequest));
            if (updatedRequest == null) throw new ArgumentNullException(nameof(updatedRequest));

            // Prepare the field change summary
            var changes = new EquipmentRequestFieldChangeSummary()
            {
                OriginalRequest = originalRequest,
                UpdatedRequest = updatedRequest
            };
            var fieldsWeCareAbout = new[]
            {nameof(Type),
                nameof(ReceivedDate),
                nameof(JobDate)
            };

            changes.Changes  = originalRequest.DetailedPropertyCompare(updatedRequest)
                .Where(change => fieldsWeCareAbout.Contains(change.PropertyName)).ToList();
            changes.JobTypeChanges = JobType.DetectEquipmentChanges(originalRequest.JobTypes.ToList(), updatedRequest.JobTypes.ToList());


            return changes;
        }

        

    }
}
