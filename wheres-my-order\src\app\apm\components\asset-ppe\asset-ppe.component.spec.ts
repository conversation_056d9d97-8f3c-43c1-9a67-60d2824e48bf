import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxFormModule, DxLoadIndicatorModule } from 'devextreme-angular';
import { AssetPpeComponent } from './asset-ppe.component';

describe('AssetPpeComponent', () => {
    let component: AssetPpeComponent;
    let fixture: ComponentFixture<AssetPpeComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DxLoadIndicatorModule, DxFormModule],
            declarations: [AssetPpeComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(AssetPpeComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
