<div class="dx-card responsive-paddings content-block">
    <h4>Work Order Management</h4>
    <dx-data-grid [dataSource]="workOrdersDataSource"
                  [remoteOperations]="true"
                  [allowColumnReordering]="true"
                  [allowColumnResizing]="true"
                  [columnResizingMode]="'widget'"
                  [autoNavigateToFocusedRow]="true"
                  [focusedRowEnabled]="true"
                  width="100%"
                  [filterSyncEnabled]="true"
                  (onSelectionChanged)="onSelectionChanged($event)"
                  (onRowInserted)="onRowInserted($event)"
                  (onEditorPreparing)="onEditorPreparing($event)"
                  (onContentReady)="onContentReady($event)">

        <dxo-toolbar>
            <dxi-item name="addRowButton"
                      [showText]="'always'"
                      [options]="{ icon: null, text: 'Create', type: 'success', stylingMode: 'contained', disabled: disableAddRowButton$ | async }">
            </dxi-item>
            <dxi-item name="groupPanel"></dxi-item>
            <dxi-item widget="dxButton"
                      location="after"
                      [options]="{icon: 'fa fa-undo', hint: 'Restore Grid Defaults', onClick: restoreDefaultsClicked}">
            </dxi-item>
            <dxi-item name="columnChooserButton"></dxi-item>
            <dxi-item name="exportButton"></dxi-item>
            <dxi-item name="searchPanel"></dxi-item>
        </dxo-toolbar>

        <dxo-selection mode="single">
        </dxo-selection>
        <dxo-group-panel [visible]="true"></dxo-group-panel>
        <dxo-scrolling [useNative]="true"></dxo-scrolling>
        <dxo-paging [pageSize]="5"></dxo-paging>
        <dxo-pager [showPageSizeSelector]="true"
                   [visible]="true"
                   [allowedPageSizes]="[5, 10, 25, 50]"></dxo-pager>
        <dxo-header-filter [visible]="true"></dxo-header-filter>
        <dxo-filter-row [visible]="true"></dxo-filter-row>
        <dxo-search-panel [visible]="true"
                          [highlightCaseSensitive]="true">
        </dxo-search-panel>
        <dxo-keyboard-navigation [enabled]="false">

        </dxo-keyboard-navigation>
        <dxo-filter-panel [visible]="true"
                          [filterEnabled]="true"></dxo-filter-panel>
        <dxo-column-chooser [enabled]="true"
                            mode="dragAndDrop">
        </dxo-column-chooser>
        <dxo-state-storing [enabled]="true"
                           type="localStorage"
                           storageKey="apmWorkOrdersGridState">
        </dxo-state-storing>
        <dxo-export [enabled]="true"
                    [allowExportSelectedData]="true"></dxo-export>

        <!-- EDITING -->
        <dxo-editing mode="popup"
                     [allowAdding]="allowEditing"
                     [allowUpdating]="false"
                     [allowDeleting]="false">
            <dxo-popup title="Create New Work Order"
                       [showTitle]="true"></dxo-popup>
            <dxo-form>
                <dxi-item itemType="group"
                          caption="Project">
                    <dxi-item dataField="projectId"
                              editorType="dxSelectBox"
                              [editorOptions]="projectEditorOptions">
                        <dxi-validation-rule type="required">
                        </dxi-validation-rule>
                    </dxi-item>
                </dxi-item>
                <dxi-item itemType="group"
                          caption="Asset">
                    <dxi-item dataField="asset.id"
                              editorType="dxSelectBox"
                              [editorOptions]="assetEditorOptions">
                        <dxi-validation-rule type="required">
                        </dxi-validation-rule>
                    </dxi-item>
                </dxi-item>
                <dxi-item itemType="group"
                          caption="Work Order">
                    <dxi-item dataField="dueDate"
                              editorType="dxDateBox">
                        <dxo-label text="Due Date"></dxo-label>
                    </dxi-item>
                    <dxi-item dataField="facilityName">
                        <dxo-label text="Facility Name"></dxo-label>
                    </dxi-item>
                    <dxi-item dataField="plannedStart"
                              editorType="dxDateBox">
                        <dxo-label text="Planned Start"></dxo-label>
                    </dxi-item>
                    <dxi-item dataField="plannedEnd"
                              editorType="dxDateBox">
                        <dxo-label text="Planned End"></dxo-label>
                    </dxi-item>
                    <dxi-item dataField="status"
                              editorType="dxSelectBox"
                              [editorOptions]="{items: ['Scheduled', 'In Progress', 'Completed']}">
                    </dxi-item>
                </dxi-item>
            </dxo-form>
        </dxo-editing>

        <!-- COLUMNS -->
        <dxi-column dataField="projectId"
                    caption="Project"
                    [setCellValue]="setProjectCellValue">
            <dxo-lookup [dataSource]="projectsDataSource"
                        [valueExpr]="'id'"
                        [displayExpr]="'name'"></dxo-lookup>
        </dxi-column>
        <dxi-column dataField="asset.id"
                    [caption]="'Asset Database ID'"
                    [visible]="false"></dxi-column>
        <dxi-column dataField="projectId"
                    [caption]="'Project Database ID'"
                    [visible]="false"></dxi-column>
        <dxi-column dataField="id"
                    [caption]="'Work Order Database ID'"
                    [visible]="false"></dxi-column>
        <dxi-column dataField="assetId"
                    [caption]="'Asset Number or Identification'"></dxi-column>
        <dxi-column dataField="apmWorkOrderNumber"
                    caption="APM Work Order Number">
        </dxi-column>
        <dxi-column dataField="status"
                    caption="Status"></dxi-column>
        <dxi-column dataField="facilityName"
                    caption="Facility">
        </dxi-column>
        <dxi-column dataField="assetCategory"
                    caption="Asset Category"></dxi-column>
        <dxi-column dataField="plannedStart"
                    caption="Planned Start"
                    dataType="date">
        </dxi-column>
        <dxi-column dataField="plannedEnd"
                    caption="Planned End"
                    dataType="date"></dxi-column>
        <dxi-column dataField="dueDate"
                    caption="Due Date"
                    dataType="date"></dxi-column>

        <dxi-column type="buttons">
            <dxi-button icon="fa fa-tasks"
                        hint="Work Order Details"
                        [onClick]="workOrderDetailsClicked">
            </dxi-button>
        </dxi-column>
    </dx-data-grid>
</div>