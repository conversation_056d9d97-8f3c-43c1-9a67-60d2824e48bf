export interface LeakReportInfo {
    id: string;
    equipmentID: LeakReportInfoField<string>;
    equipmentDescription: LeakReportInfoField<string>;
    equipmentIDAtLineStart: LeakReportInfoField<string>;
    equipmentIDAtLineEnd: LeakReportInfoField<string>;
    pipeSize: LeakReportInfoField<string>;
    pipeSchedule: LeakReportInfoField<string>;
    processService: LeakReportInfoField<string>;
    pipeCover: LeakReportInfoField<string>;
    distanceBetweenTieInPoints: LeakReportInfoField<number>;
    corrosionType: LeakReportInfoField<string[]>;
    estimatedLossRate: LeakReportInfoField<number>;
    existingClampCount: LeakReportInfoField<number>;
    featureFittingCount: LeakReportInfoField<number>;
    observationSummary: LeakReportInfoField<string>;
    affectedLength: LeakReportInfoField<number>;
}

export interface LeakReportInfoField<T> {
    value: T;
    comment: string;
    photos: any[]; // TODO: type better
    options?: string[];
}
