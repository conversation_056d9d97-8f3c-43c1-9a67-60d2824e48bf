﻿using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Moq;
using NUnit.Framework;
using OrderTracking.API.Authorization.Handlers;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Tests.Authorization.Handlers
{
    [TestFixture]
    public class IsTeamEmployeeAuthorizationHandlerTests
    {
        [SetUp]
        public void SetUp()
        {
            _mockService = new Mock<IUserProfilesService>(MockBehavior.Strict);
            _handler = new IsTeamEmployeeHandler(_mockService.Object);
        }

        private Mock<IUserProfilesService> _mockService;
        private IsTeamEmployeeHandler _handler;
        private AuthorizationHandlerContext _context;
        private UserProfile _them;

        [Test]
        public async Task HandleAsync_IsTeamEmployee_ContextHasSucceeded()
        {
            _them = new UserProfile {Id = "<EMAIL>"};
            _mockService.Setup(service => service.GetAsync(It.Is<string>(s => s == "<EMAIL>")))
                .ReturnsAsync(_them);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_them, null, new[]
            {
                new IsTeamEmployeeRequirement()
            });
            await _handler.HandleAsync(_context);

            Assert.That(_context.HasSucceeded);
        }

        [Test]
        public async Task HandleAsync_IsTeamEmployee_ContextHasFailed()
        {
            _them = new UserProfile {Id = "<EMAIL>"};
            _mockService.Setup(service => service.GetAsync(It.Is<string>(s => s == "<EMAIL>")))
                .ReturnsAsync(_them);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_them, null, new[]
            {
                new IsTeamEmployeeRequirement()
            });
            await _handler.HandleAsync(_context);

            Assert.That(!_context.HasSucceeded);
        }

        [Test]
        public async Task HandleAsync_UserIsNull_ContextNotSucceeded()
        {
            _them = new UserProfile {Id = "<EMAIL>"};
            _mockService.Setup(u => u.GetAsync("<EMAIL>")).ReturnsAsync((UserProfile) null);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_them, null,
                new[] {new IsTeamEmployeeRequirement()});

            await _handler.HandleAsync(_context);

            Assert.That(_context.HasSucceeded, Is.False);
        }

        [Test]
        public async Task HandleAsync_UserIsAuthenticatedIsFalse_ContextNotSucceeded()
        {
            _them = new UserProfile {Id = "<EMAIL>"};
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContextNotAuthenticated(null, null,
                new[] {new IsTeamEmployeeRequirement()});

            await _handler.HandleAsync(_context);

            Assert.That(_context.HasFailed, Is.True);
        }
    }
}