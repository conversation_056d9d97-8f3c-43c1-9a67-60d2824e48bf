﻿using System.Collections.Generic;
using System.Threading.Tasks;
using AIMaaS.Models;

namespace AIMaaS.Services
{
    /// <summary>
    ///     Interface definition for a CredoSoftService
    /// </summary>
    public interface ICredoSoftService
    {
        /// <summary>
        ///     Get assets from CredoSoft
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<Asset>> GetAssetsAsync();

        /// <summary>
        ///     Get a specific asset from CredoSoft
        /// </summary>
        /// <param name="objId"></param>
        /// <returns></returns>
        Task<Asset> GetAssetAsync(long objId);

        /// <summary>
        ///     Get components from CredoSoft
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<Component>> GetComponentsAsync();

        /// <summary>
        ///     Get components for a specific asset from CredoSoft
        /// </summary>
        /// <param name="assetObjId"></param>
        /// <returns></returns>
        Task<IEnumerable<Component>> GetComponentsForAssetAsync(long assetObjId);

        /// <summary>
        ///     Get a specific component
        /// </summary>
        /// <param name="objId"></param>
        /// <returns></returns>
        Task<Component> GetComponentAsync(long objId);

        /// <summary>
        ///     Get inspections
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<Inspection>> GetInspectionsAsync();

        /// <summary>
        ///     Get a specific inspection
        /// </summary>
        /// <param name="objId"></param>
        /// <returns></returns>
        Task<Inspection> GetInspectionAsync(long objId);

        /// <summary>
        ///     Get alarm calculations for a specific component
        /// </summary>
        /// <param name="componentObjId"></param>
        /// <returns></returns>
        Task<AlarmCalculation> GetAlarmCalculationForComponentAsync(long componentObjId);

        /// <summary>
        ///     Get all asset attachments
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<AssetAttachment>> GetAllAssetAttachmentsAsync();

        /// <summary>
        ///     Get asset attachments for a specific asset
        /// </summary>
        /// <param name="objId"></param>
        /// <returns></returns>
        Task<IEnumerable<AssetAttachment>> GetAssetAttachmentsAsync(long objId);

        /// <summary>
        ///     Get all inspection attachments
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<InspectionAttachment>> GetAllInspectionAttachmentsAsync();

        /// <summary>
        ///     Get inspection attachments for a specific inspection
        /// </summary>
        /// <param name="emid"></param>
        /// <returns></returns>
        Task<IEnumerable<InspectionAttachment>> GetInspectionAttachmentsAsync(long emid);

        /// <summary>
        ///     Get all alarm calculations
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<AlarmCalculation>> GetAllAlarmCalculations();

        /// <summary>
        ///     Get all asset management sites
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<AssetManagementSite>> GetAllAssetManagementSitesAsync();
    }
}