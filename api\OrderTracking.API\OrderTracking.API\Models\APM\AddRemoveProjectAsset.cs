﻿using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class AddRemoveProjectAsset
    {
        [JsonProperty(PropertyName = "projectId")]
        public string ProjectId { get; set; }

        [JsonProperty(PropertyName = "assetsToRemove")]
        public string[] AssetsToRemove { get; set; }

        [JsonProperty(PropertyName = "assetsToAdd")]
        public string[] AssetsToAdd { get; set; }
    }
}
