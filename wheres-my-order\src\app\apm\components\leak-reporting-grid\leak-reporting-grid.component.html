<div class="dx-card responsive-paddings content-block">
    <h4>Leak Reporting</h4>
    <dx-data-grid [dataSource]="rows"
                  keyExpr="id"
                  [allowColumnReordering]="true"
                  [allowColumnResizing]="true"
                  [columnResizingMode]="'widget'"
                  (onRowInserting)="onLeakReportCreating($event)"
                  (onSelectionChanged)="onSelectionChanged($event)">

        <dxo-toolbar>
            <dxi-item name="addRowButton"
                      [showText]="'always'"
                      [options]="{icon: null, text: 'Create', type: 'success', stylingMode: 'contained', disabled: (addRowButtonDisabled$ | async)}">
            </dxi-item>
            <dxi-item widget="dxButton"
                      location="after"
                      [options]="{icon: 'fa fa-undo', hint: 'Restore Grid Defaults', onClick: restoreGridDefaults}">
            </dxi-item>
            <dxi-item name="columnChooserButton"></dxi-item>
            <dxi-item name="exportButton"></dxi-item>
            <dxi-item name="groupPanel"></dxi-item>
            <dxi-item name="searchPanel"></dxi-item>
        </dxo-toolbar>

        <dxo-selection mode="single">
        </dxo-selection>

        <dxo-group-panel [visible]="true"></dxo-group-panel>
        <dxo-scrolling [useNative]="true"></dxo-scrolling>
        <dxo-paging [pageSize]="5"></dxo-paging>
        <dxo-pager [showPageSizeSelector]="true"
                   [visible]="true"
                   [allowedPageSizes]="[5, 10, 25, 50]"></dxo-pager>
        <dxo-header-filter [visible]="true"></dxo-header-filter>
        <dxo-filter-row [visible]="true"></dxo-filter-row>
        <dxo-search-panel [visible]="true"
                          [highlightCaseSensitive]="true">
        </dxo-search-panel>
        <dxo-filter-panel [visible]="true"></dxo-filter-panel>
        <dxo-column-chooser [enabled]="true"
                            mode="dragAndDrop">
        </dxo-column-chooser>
        <dxo-state-storing [enabled]="true"
                           type="localStorage"
                           storageKey="apmLeakReportsGridState">
        </dxo-state-storing>
        <dxo-export [enabled]="true"
                    [allowExportSelectedData]="true"></dxo-export>

        <!-- EDITING -->
        <dxo-editing mode="popup"
                     [allowAdding]="allowEditing$ | async"
                     [allowUpdating]="false"
                     [allowDeleting]="false">
            <dxo-popup title="Create New Leak Report"
                       [showTitle]="true"
                       [width]="700"
                       [height]="525"></dxo-popup>
            <dxo-form>
                <dxi-item itemType="group"
                          [colCount]="2"
                          [colSpan]="2">
                    <dxi-item dataField="area">
                    </dxi-item>
                    <dxi-item dataField="lease">
                    </dxi-item>
                    <dxi-item dataField="equipmentId">
                    </dxi-item>
                    <dxi-item dataField="jobDescription">
                    </dxi-item>
                    <dxi-item dataField="status"
                              editorType="dxRadioGroup"
                              [editorOptions]="statusEditorOptions">
                    </dxi-item>
                </dxi-item>
            </dxo-form>
        </dxo-editing>

        <!-- COLUMNS -->
        <dxi-column dataField="leakReportId"
                    caption="ID"></dxi-column>
        <dxi-column dataField="id"
                    caption="Leak Report Database ID"
                    [visible]="false"></dxi-column>
        <dxi-column dataField="area"></dxi-column>
        <dxi-column dataField="lease">
        </dxi-column>
        <dxi-column dataField="equipmentId"
                    caption="Equipment ID"></dxi-column>
        <dxi-column dataField="jobDescription"></dxi-column>
        <dxi-column dataField="status"></dxi-column>
    </dx-data-grid>
</div>
