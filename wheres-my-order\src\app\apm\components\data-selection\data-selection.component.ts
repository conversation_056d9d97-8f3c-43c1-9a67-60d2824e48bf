import {
    Component,
    EventEmitter,
    Input,
    Output,
    ViewChild
} from '@angular/core';
import { DxTagBoxComponent } from 'devextreme-angular';
import { FirebaseProject } from '../../models';

@Component({
    selector: 'app-data-selection',
    templateUrl: './data-selection.component.html',
    styleUrls: ['./data-selection.component.scss']
})
export class DataSelectionComponent {
    private _projects: FirebaseProject[];

    @ViewChild(DxTagBoxComponent) tagBox: DxTagBoxComponent;

    /** INPUTS */
    @Input() set projects(value: FirebaseProject[]) {
        this._projects = value;
        if (this.tagBox) {
            if (
                !this.tagBox.selectedItems.every((project) =>
                    value.includes(project)
                )
            ) {
                this.tagBox.value = [];
            }
            this.selectedProjects.emit(this.tagBox.selectedItems);
        }
    }

    get projects(): FirebaseProject[] {
        return this._projects;
    }

    /** OUTPUTS */

    /**
     * emits the currently selected projects when the selected projects are changed.  Emits
     * an empty array when either no projects are selected or all projects are selected
     */
    @Output() selectedProjects = new EventEmitter<FirebaseProject[]>();

    @Output() refresh = new EventEmitter<void>();

    constructor() {}

    onProjectsValueChanged(e) {
        const projects = e.value;
        this.selectedProjects.emit(
            this.tagBox.selectedItems.length === this.tagBox.items.length
                ? []
                : projects
        );
    }
}
