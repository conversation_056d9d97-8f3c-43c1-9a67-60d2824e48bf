﻿using System;
using System.IO;
using System.Threading.Tasks;

namespace ClientPortal.Shared.Helpers
{
    /// <summary>
    ///     Generic email helpers
    /// </summary>
    public static class EmailSetup
    {
        /// <summary>
        ///     Creates the default html header for all our emails
        /// </summary>
        /// <param name="clientPortalAddress"></param>
        /// <returns></returns>
        public static async Task<string> CreateHeaderHtmlContentAsync(string clientPortalAddress)
        {
            var base64String = await CreateLogoStringAsync();
            var headerHtmlContent = $@"
<a href=""{clientPortalAddress}"">
  <img alt=""OneInsight"" src=""data:image/png;base64,{base64String}"">
</a>
<p><small>This is an automatic message from OneInsight</small></p>
";
            return headerHtmlContent;
        }

        /// <summary>
        ///     Creates the base 64 string that is the TEAM logo
        /// </summary>
        /// <returns></returns>
        /// <exception cref="InvalidOperationException"></exception>
        private static async Task<string> CreateLogoStringAsync()
        {
            var logo = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory ??
                throw new InvalidOperationException("Unable to obtain AppDomain.CurrentDomain.BaseDirectory"),
                "Files/OneInsight-Final.png");
            var base64String = Convert.ToBase64String(await File.ReadAllBytesAsync(logo));
            return base64String;
        }
    }
}