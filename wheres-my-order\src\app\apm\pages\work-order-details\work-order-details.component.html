<div class="dx-card responsive-paddings content-block">
    <h4>Work Order Details</h4>
    <div>
        <dx-load-indicator *ngIf="isLoadingWODetail"
                           style="z-index: 100; position: absolute; left: 40%; top:10%"
                           id="large-indicator"
                           height="60"
                           width="60"></dx-load-indicator>
    </div>
    <dx-form [colCount]="5"
             [(formData)]="workOrderDetail">
        <dxi-item itemType="group"
                  [colSpan]="2"
                  [colCount]="2">
            <dxi-item itemType="group">
                <dxi-item dataField="assetID"
                          [editorOptions]="{readOnly:true}">
                    <dxo-label text="Asset Number or Identification">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="assetDescription"
                          [editorOptions]="{hint: workOrderDetail?.assetDescription, readOnly:true}">
                    <dxo-label text="Asset Description"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item itemType="group">
                <dxi-item dataField="client"
                          [editorOptions]="{readOnly:true}"></dxi-item>
                <dxi-item dataField="location"
                          [editorOptions]="{readOnly:true}">
                    <dxo-label text="Facility Name"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      [colSpan]="2"
                      [colCount]="2">
                <dxi-item dataField="projectName"
                          [editorOptions]="{readOnly:true}"></dxi-item>
                <dxi-item dataField="afeNumber"
                          [editorOptions]="{hint: workOrderDetail?.afeNumber, readOnly:true}">

                    <dxo-label text="AFE Number"></dxo-label>
                </dxi-item>
            </dxi-item>
        </dxi-item>
        <dxi-item itemType="group"
                  [colSpan]="3">
            <dx-data-grid style="max-height: 11rem;"
                          (onRowUpdated)="onRowUpdated($event)"
                          [dataSource]="workOrderDetail?.inspectionTypes"
                          (onEditorPreparing)="onHeaderTaskGridEditorPreparing($event)">
                <dxo-editing mode="cell"
                             [allowUpdating]="true">

                </dxo-editing>

                <dxo-column-fixing [enabled]="false"></dxo-column-fixing>
                <dxo-paging [enabled]="false"></dxo-paging>

                <dxi-column [allowEditing]="false"
                            dataField="taskType"></dxi-column>
                <dxi-column dataField="taskId"
                            [allowEditing]="false"
                            caption="Task ID"></dxi-column>
                <dxi-column dataField="taskAssignees"
                            [allowEditing]="allowEditing$ | async"
                            editCellTemplate="tagBoxEditor"></dxi-column>
                <dxi-column dataField="supervisor"
                            [allowEditing]="allowEditing$ | async"
                            editCellTemplate="selectBoxEditor"></dxi-column>
                <dxi-column dataField="status"
                            [allowEditing]="allowEditing$ | async"
                            [editorOptions]="{itemTemplate: 'statusTemplate'}">
                    <dxo-lookup></dxo-lookup>
                </dxi-column>
                <div *dxTemplate="let status of 'statusTemplate'">
                    <span class="middle">{{status}}</span>
                </div>

                <div *dxTemplate="let cellInfo of 'tagBoxEditor'">
                    <dx-tag-box [dataSource]="users$ | async | userEmails"
                                [value]="cellInfo.value"
                                [showSelectionControls]="true"
                                [showMultiTagOnly]="false"
                                applyValueMode="useButtons"
                                [width]="200"
                                [searchEnabled]="true"
                                [disabled]="(allowEditing$ | async) === false"
                                (onValueChanged)="cellInfo.setValue($event.value)"
                                (onSelectionChanged)="cellInfo.component.updateDimensions()">
                    </dx-tag-box>
                </div>

                <div *dxTemplate="let cellInfo of 'selectBoxEditor'">
                    <dx-select-box [dataSource]="users$ | async | userEmails"
                                   [value]="cellInfo.value"
                                   [showSelectionControls]="true"
                                   applyValueMode="useButtons"
                                   [width]="200"
                                   [searchEnabled]="true"
                                   [readOnly]="(allowEditing$ | async) === false"
                                   (onValueChanged)="cellInfo.setValue($event.value)"
                                   (onSelectionChanged)="cellInfo.component.updateDimensions()">
                    </dx-select-box>
                </div>

            </dx-data-grid>
        </dxi-item>
    </dx-form>

    <dx-tab-panel [showNavButtons]="true"
                  (onSelectionChanged)="onTabSelectionChanged($event)">

        <dxi-item title="Asset Details">
            <app-apm-asset-details [workOrder]="workOrder$ | async"
                                   (photoDelete)="onAssetDetailsPhotoDelete($event)"
                                   (photoDescriptionUpdate)="onAssetDetailsPhotoDescriptionUpdate($event)"
                                   [allowEditing]="allowEditing$ | async">
            </app-apm-asset-details>
        </dxi-item>
        <dxi-item title="Asset PPE">
            <app-asset-ppe [assetPPE]="workOrder$ | async | assetPpe"
                           (save)="onAssetPPESave($event)"
                           [allowEditing]="allowEditing$ | async">
            </app-asset-ppe>
        </dxi-item>
        <dxi-item title="Asset Access">
            <app-asset-access [assetAccess]="workOrder$ | async | assetAccess"
                              (assetAccessUpdated)="assetAccessUpdated($event)"
                              [allowEditing]="allowEditing$ | async">
            </app-asset-access>
        </dxi-item>
        <dxi-item title="Inspection Information">
            <app-inspection-information [inspectionInfo]="workOrder$ | async | inspectionInfo"
                                        (save)="onSaveInspectionInfo($event)"
                                        [allowEditing]="allowEditing$ | async">
            </app-inspection-information>
        </dxi-item>
        <dxi-item title="Internal Inspection Results"
                  [visible]="internalInspectionResultsVisible$ | async">

            <div class="content-block">
                <dx-select-box style="width: fit-content"
                               placeholder="Select task..."
                               [items]="internalTasks$ | async"
                               [(selectedItem)]="selectedInternalTask"
                               displayExpr="taskType"
                               valueExpr="id"
                               (onOptionChanged)="onTaskSelectorOptionChanged($event)">
                </dx-select-box>
                <app-inspection-results *ngIf="selectedInternalTask"
                                        [inspectionResult]="workOrder$ | async | inspectionResults: 'Internal Visual' : selectedInternalTask.id"
                                        (photoDelete)="onPhotoDelete($event)"
                                        (photoDescriptionUpdate)="onPhotoDescriptionUpdate($event)"
                                        [allowEditing]="allowEditing$ | async">
                </app-inspection-results>
            </div>

        </dxi-item>
        <dxi-item title="External Inspection Results"
                  [visible]="externalInspectionResultsVisible$ | async">
            <div class="content-block">
                <dx-select-box style="width: fit-content"
                               placeholder="Select task..."
                               [items]="externalTasks$ | async"
                               [(selectedItem)]="selectedExternalTask"
                               displayExpr="taskType"
                               valueExpr="id"
                               (onOptionChanged)="onTaskSelectorOptionChanged($event)">
                </dx-select-box>
                <app-inspection-results *ngIf="selectedExternalTask"
                                        [inspectionResult]="workOrder$ | async | inspectionResults: 'External Visual' : selectedExternalTask.id"
                                        (photoDelete)="onPhotoDelete($event)"
                                        (photoDescriptionUpdate)="onPhotoDescriptionUpdate($event)"
                                        [allowEditing]="allowEditing$ | async">
                </app-inspection-results>
            </div>

        </dxi-item>
        <dxi-item title="Attachments">
            <app-attachments-tab [workOrder]="workOrder$ | async"
                                 [allowEditing]="allowEditing$ | async">
            </app-attachments-tab>
        </dxi-item>
        <dxi-item title="Reports">
            <app-report-tab [workOrderDetail]="workOrderDetail"
                            [allowEditing]="allowEditing$ | async"
                            (publishedUnpublished)="onPublishedUnpublished($event)">
            </app-report-tab>
        </dxi-item>
    </dx-tab-panel>
</div>