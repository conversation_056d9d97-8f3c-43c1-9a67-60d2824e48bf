using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Sas;
using Azure.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderTracking.API.Helpers;
using OrderTracking.API.Interfaces.Services;
using OrderTracking.API.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace OrderTracking.API.Services
{
    /// <summary>
    /// Azure Blob Storage Service (migrated from Google Cloud Storage)
    /// This class contains operations related list, add, download, upload and delete blobs
    /// </summary>
    public class CloudStorageService : ICloudStorageService
    {
        protected readonly ILogger _logger;
        private readonly IOptions<BlobStorage> _options;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="options"></param>
        /// <param name="logger"></param>
        public CloudStorageService(IOptions<BlobStorage> options, ILogger logger)
        {
            _logger = logger;
            _options = options;
        }

        /// <summary>
        /// Generate SAS URL for container access
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetSignedUrl()
        {
            try
            {
                string storageAccountName = _options.Value.APMStorageAccountName;
                string containerName = _options.Value.APMBlobContainerName;
                
                var credential = new DefaultAzureCredential();
                var blobServiceClient = new BlobServiceClient(new Uri($"https://{storageAccountName}.blob.core.windows.net"), credential);
                var containerClient = blobServiceClient.GetBlobContainerClient(containerName);

                if (containerClient.CanGenerateSasUri)
                {
                    BlobSasBuilder sasBuilder = new BlobSasBuilder()
                    {
                        BlobContainerName = containerName,
                        Resource = "c", // Container
                        ExpiresOn = DateTimeOffset.UtcNow.AddHours(1)
                    };
                    sasBuilder.SetPermissions(BlobContainerSasPermissions.Read | BlobContainerSasPermissions.Write);

                    _logger.LogInformation($"Generate SAS URL for container {containerName}");
                    return containerClient.GenerateSasUri(sasBuilder).ToString();
                }

                throw new InvalidOperationException("Cannot generate SAS URI for container");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while generating SAS URL");
                throw;
            }
        }

        /// <summary>
        /// Generate SAS URL for specific blob
        /// </summary>
        /// <param name="blobName"></param>
        /// <returns></returns>
        public async Task<string> GetSignedUrl(string blobName)
        {
            try
            {
                string storageAccountName = _options.Value.APMStorageAccountName;
                string containerName = _options.Value.APMBlobContainerName;
                
                var credential = new DefaultAzureCredential();
                var blobServiceClient = new BlobServiceClient(new Uri($"https://{storageAccountName}.blob.core.windows.net"), credential);
                var blobClient = blobServiceClient.GetBlobContainerClient(containerName).GetBlobClient(blobName);

                if (blobClient.CanGenerateSasUri)
                {
                    BlobSasBuilder sasBuilder = new BlobSasBuilder()
                    {
                        BlobContainerName = containerName,
                        BlobName = blobName,
                        Resource = "b", // Blob
                        ExpiresOn = DateTimeOffset.UtcNow.AddHours(1)
                    };
                    sasBuilder.SetPermissions(BlobSasPermissions.Read);

                    _logger.LogInformation($"Generate SAS URL for blob {blobName} in container {containerName}");
                    return blobClient.GenerateSasUri(sasBuilder).ToString();
                }

                throw new InvalidOperationException($"Cannot generate SAS URI for blob {blobName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error occurred while generating SAS URL for blob {blobName}");
                throw;
            }
        }

        /// <summary>
        /// List blobs in a folder (prefix)
        /// </summary>
        /// <param name="folderName"></param>
        /// <returns></returns>
        public async Task<IEnumerable<object>> ListObjectAsync(string folderName)
        {
            try
            {
                string storageAccountName = _options.Value.APMWOStorageAccountName;
                string containerName = _options.Value.APMWOBlobContainerName;
                
                var credential = new DefaultAzureCredential();
                var blobServiceClient = new BlobServiceClient(new Uri($"https://{storageAccountName}.blob.core.windows.net"), credential);
                var containerClient = blobServiceClient.GetBlobContainerClient(containerName);

                List<object> blobs = new List<object>();
                string prefix = folderName + "/";
                
                _logger.LogInformation($"Listing blobs in container {containerName}, folder - {folderName}");
                
                await foreach (BlobItem blobItem in containerClient.GetBlobsAsync(prefix: prefix))
                {
                    blobs.Add(blobItem);
                    Console.WriteLine(blobItem.Name);
                }
                
                return blobs;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while listing blobs from Azure Blob Storage");
                throw;
            }
        }

        /// <summary>
        /// Upload file to Azure Blob Storage
        /// </summary>
        /// <param name="folderName"></param>
        /// <param name="file"></param>
        /// <returns></returns>
        public async Task<object> UploadAttachmentObjectAsync(string folderName, IFormFile file)
        {
            try
            {
                if (file == null) throw new ArgumentNullException(nameof(file));
                
                string storageAccountName = _options.Value.APMWOStorageAccountName;
                string containerName = _options.Value.APMWOBlobContainerName;
                
                var credential = new DefaultAzureCredential();
                var blobServiceClient = new BlobServiceClient(new Uri($"https://{storageAccountName}.blob.core.windows.net"), credential);
                var containerClient = blobServiceClient.GetBlobContainerClient(containerName);
                
                string blobName = folderName + "/" + Uri.EscapeDataString(file.FileName);
                var blobClient = containerClient.GetBlobClient(blobName);

                using var fileStream = file.OpenReadStream();
                _logger.LogInformation($"Upload started for container {containerName}, folder - {folderName}");
                
                await blobClient.UploadAsync(fileStream, overwrite: true);
                return blobClient;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error occurred while uploading file {file.Name} to Azure Blob Storage");
                throw;
            }
        }

        /// <summary>
        /// Delete blob from Azure Blob Storage
        /// </summary>
        /// <param name="folderName"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        public async Task DeleteObjectAsync(string folderName, string objectName)
        {
            try
            {
                string storageAccountName = _options.Value.APMWOStorageAccountName;
                string containerName = _options.Value.APMWOBlobContainerName;
                
                var credential = new DefaultAzureCredential();
                var blobServiceClient = new BlobServiceClient(new Uri($"https://{storageAccountName}.blob.core.windows.net"), credential);
                var containerClient = blobServiceClient.GetBlobContainerClient(containerName);
                
                string blobName = folderName + "/" + Uri.EscapeDataString(objectName);
                var blobClient = containerClient.GetBlobClient(blobName);

                _logger.LogInformation($"Deleting {objectName} from container {containerName}, folder - {folderName}");
                await blobClient.DeleteIfExistsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error occurred while deleting blob {objectName} from Azure Blob Storage");
                throw;
            }
        }

        /// <summary>
        /// Download blob from Azure Blob Storage
        /// </summary>
        /// <param name="folderName"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        public async Task<CloudStorageDownloadedObject> DownloadObjectAsync(string folderName, string objectName)
        {
            try
            {
                string storageAccountName = _options.Value.APMWOStorageAccountName;
                string containerName = _options.Value.APMWOBlobContainerName;
                
                var credential = new DefaultAzureCredential();
                var blobServiceClient = new BlobServiceClient(new Uri($"https://{storageAccountName}.blob.core.windows.net"), credential);
                var containerClient = blobServiceClient.GetBlobContainerClient(containerName);
                
                string blobName = folderName + "/" + Uri.EscapeDataString(objectName);
                var blobClient = containerClient.GetBlobClient(blobName);

                Stream stream = new MemoryStream();
                CloudStorageDownloadedObject downloadedObject = new();
                
                _logger.LogInformation($"Downloading {objectName} from container {containerName}");
                
                var response = await blobClient.DownloadToAsync(stream);
                var properties = await blobClient.GetPropertiesAsync();
                
                downloadedObject.Stream = stream;
                downloadedObject.Object = blobClient;
                downloadedObject.ContentType = properties.Value.ContentType;
                downloadedObject.Size = properties.Value.ContentLength;
                downloadedObject.Name = objectName;

                if (stream.CanSeek)
                {
                    stream.Seek(0, SeekOrigin.Begin);
                }

                return downloadedObject;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error occurred while downloading blob {objectName} from Azure Blob Storage");
                throw;
            }
        }
    }
}
