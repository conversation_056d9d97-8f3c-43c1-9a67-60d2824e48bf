//using System;
//using System.Collections.Generic;
//using System.Data.SqlClient;
//using System.Linq;
//using System.Threading.Tasks;
//using Azure;
//using Azure.Storage.Blobs.Models;
//using ClientPortal.Shared.Extensions;
//using ClientPortal.Shared.Models;
//using Dapper;
//using Dapper.Contrib.Extensions;
//using DevExtreme.AspNet.Data.ResponseModel;
//using Microsoft.AspNetCore.Http;
//using Microsoft.EntityFrameworkCore;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.Logging;
//using OrderTracking.API.Extensions;
//using OrderTracking.API.Interfaces;
//using OrderTracking.API.Models;
//using Z.Dapper.Plus;

//namespace OrderTracking.API.Services
//{
//    /// <summary>
//    ///     Service to provide queryable access to orders
//    /// </summary>
//    public class OrdersService : IOrdersService
//    {
//        private readonly string _connectionString;
//        private readonly OrderContext _context;
//        private readonly ILogger<IOrdersService> _logger;
//        private readonly IWMOBlobStorageService _storage;

//        #region Constructors

//        /// <summary>
//        ///     Constructs service class to handle CRUD actions on orders and their associated blob files
//        /// </summary>
//        /// <param name="logger"></param>
//        /// <param name="config"></param>
//        /// <param name="storage"></param>
//        /// <param name="context"></param>
//        public OrdersService(ILogger<IOrdersService> logger, IConfiguration config, IWMOBlobStorageService storage,
//            OrderContext context)
//        {
//            if (config == null) throw new ArgumentNullException(nameof(config));

//            _logger = logger;
//            _storage = storage;
//            _context = context;
//            _connectionString = config.GetSection("SQLConnections")["Orders"];
//        }

//        #endregion

//        #region Public Methods

//        #region Interface Implementations

//        /// <summary>
//        ///     Provides an <see cref="IQueryable" /> for the orders that a particular UserProfile has access to.
//        /// </summary>
//        /// <param name="user"></param>
//        /// <param name="includeArchive"></param>
//        /// <returns></returns>
//        public IQueryable<Order> GetOrdersForUser(UserProfile user, bool includeArchive = false)
//        {
//            if (user == null) throw new ArgumentNullException(nameof(user));

//            var query = _context.Orders.Include(x => x.Files).Where(a => true);
//            if (!user.HasRole("wmo:manufacturinguser") && !user.HasRole("wmo:engineeringuser") && !user.HasRole("app:admin"))
//                query = query.Where(o => user.DistrictIds.Contains(o.INTERNALDISTRICTNUMBER) ||
//                                         user.CustomerAccounts.Contains(o.EXTERNALCUSTOMER));

//            if (!includeArchive)
//                query = query.Where(o =>
//                    o.SALESSTATUS == "Cancel" && o.FINISHEDDATE >= DateTime.Now.AddDays(-120) ||
//                    o.PACKINGSLIPID == null && o.SALESSTATUS != "Cancel" ||
//                    o.DELIVERYDATE >= DateTime.Now.AddDays(-120));
            
//            return query;
//        }

//        /// <summary>
//        ///     Get an individual Order based on its id and the user the request is on behalf of.  This
//        ///     is so that we can check if that user is allowed access to that order.
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="user"></param>
//        /// <returns></returns>
//        public async Task<Order> GetItemAsync(string id, UserProfile user)
//        {
//            if (user == null) throw new ArgumentNullException(nameof(user));

//            var includableQueryable = _context.Orders.Include(x => x.Files);
//            var queryable = includableQueryable.Where(a => true);
//            if (!user.HasRole("wmo:manufacturinguser") && !user.HasRole("wmo:engineeringuser") && !user.HasRole("app:admin"))
//                queryable = queryable.Where(o => user.DistrictIds.Contains(o.INTERNALDISTRICTNUMBER) ||
//                                                 user.CustomerAccounts.Contains(o.EXTERNALCUSTOMER));

//            return await queryable.FirstOrDefaultAsync(o => o.Id == id);
//        }

//        /// <summary>
//        ///     Gets an order by the idea of an support document upload record.
//        /// </summary>
//        /// <param name="salesLineRecId"></param>
//        /// <returns></returns>
//        public IQueryable<Order> GetBySalesLineRecIdAsync(long salesLineRecId)
//        {
//            return Queryable.Where(_context.Orders, o => o.SALESLINE_RECID == salesLineRecId);
//            //return _context.Orders.Where(o => o.SALESLINE_RECID == salesLineRecId);
//        }

//        /// <summary>
//        ///     Downloads the blob file associated with an upload record and returns the file to the client.
//        /// </summary>
//        /// <param name="routeId"></param>
//        /// <param name="blobName"></param>
//        /// <returns></returns>
//        public async Task<CloudStorageDownloadedObject> DownloadBlobAsync(string routeId, string blobName)
//        {
//            _logger.LogInformation($"Downloading file {blobName} for order with Support Document ID {routeId}");
//            //Azure Blob Storage Migration
//            return await _storage.DownloadObjectAsync(routeId, blobName);   
//        }

//        /// <summary>
//        ///     Returns all distinct client account numbers found in the current set of orders.
//        /// </summary>
//        /// <returns></returns>
//        public IQueryable<dynamic> GetExternalCustomers()
//        {
//            var queryable = Queryable.Where(_context.Orders, o => o.EXTERNALCUSTOMERNAME != null && o.EXTERNALCUSTOMER != null)
//            //var queryable = _context.Orders
//            //    .Where(o => o.EXTERNALCUSTOMERNAME != null && o.EXTERNALCUSTOMER != null)
//                .Select(o => new {o.EXTERNALCUSTOMER, o.EXTERNALCUSTOMERNAME})
//                .Distinct();

//            return queryable;
//        }

//        /// <summary>
//        ///     Uploads 1 or more files to Azure Blob Storage, creates meta data records to represent the
//        ///     association to the order the file is being uploaded for.
//        /// </summary>
//        /// <param name="salesLineRecId"></param>
//        /// <param name="files"></param>
//        /// <param name="email"></param>
//        /// <returns></returns>
//        public async Task UploadFiles(long salesLineRecId, IFormFileCollection files, string email)
//        {
//            if (files == null) throw new ArgumentNullException(nameof(files));

//            // Upload files contents
//            _logger.LogInformation($"Uploading {files.Count} files for order with ID {salesLineRecId}");
//            foreach (var file in files) await _storage.UploadAttachmentAsync(salesLineRecId.ToString(), file);

//            // Create file meta data records
//            var blobFiles = files.Select(file => new OrderBlobFile
//            {
//                SalesLineRecId = salesLineRecId,
//                Name = file.FileName,
//                Size = file.Length,
//                Type = file.ContentType,
//                UploadedAt = DateTime.UtcNow,
//                UploadedBy = email
//            }).ToList();

//            await using var connection = new SqlConnection(_connectionString);
//            await connection.OpenAsync();

//            var existingFiles =
//                await connection.QueryAsync<OrderBlobFile>(
//                    "SELECT * FROM OrderFiles WHERE salesLineRecId = @salesLineRecId",
//                    new {salesLineRecId});
//            var existingFilesToOverwrite = existingFiles.Where(existingFile =>
//                blobFiles.Select(blobFile => blobFile.Name).Contains(existingFile.Name)).ToList();

//            DapperPlusManager.Entity<OrderBlobFile>().Table("OrderFiles");

//            if (existingFilesToOverwrite.Any())
//                _logger.LogInformation($"Overwriting {existingFilesToOverwrite.Count} existing files from upload");

//            foreach (var file in existingFilesToOverwrite) await connection.DeleteAsync(file);

//            // Inserting file meta data to database for Order/OrderFile associations
//            _logger.LogInformation(
//                $"Saving meta data for {blobFiles.Count} files on order with Support Documents ID {salesLineRecId}");

//            await connection.BulkActionAsync(x => x.BulkInsert(blobFiles.WithNewIds()));
//        }

//        /// <summary>
//        ///     Delete blob file associated with an order and remove the association to the meta data
//        ///     for that file.
//        /// </summary>
//        /// <param name="fileId"></param>
//        /// <returns></returns>
//        public async Task DeleteFile(string fileId)
//        {
//            await using var connection = new SqlConnection(_connectionString);
//            await connection.OpenAsync();

//            var file = await connection.QuerySingleAsync<OrderBlobFile>("SELECT * FROM OrderFiles WHERE Id = @id",
//                new {id = fileId});

//            if (file != null)
//            {
//                try
//                {
//                    _logger.LogInformation(
//                        $"Deleting file named {file.Name} on order with Support Documents ID {file.SalesLineRecId}");

//                    // Azure Blob Storage Migration
//                    //await _storage.DeleteBlobAsync(file.SalesLineRecId.ToString(), file.Name);
//                    await _storage.DeleteObjectAsync(file.SalesLineRecId.ToString(), file.Name);
//                }
//                catch (RequestFailedException rfe)
//                {
//                    if (rfe.Status != 404) throw;
//                }

//                await connection.DeleteAsync(file);
//            }
//        }

//        /// <summary>
//        ///     Deletes all orders whose ids are in the provided list
//        /// </summary>
//        /// <param name="ids"></param>
//        /// <returns></returns>
//        public async Task DeleteAsync(string[] ids)
//        {
//            var records = ids.Select(id => new Order {Id = id});
//            _context.RemoveRange(records);
//            await _context.SaveChangesAsync();
//        }

//        #endregion

//        #region Helper Functions
        
//        /// <summary>
//        ///     Removes information from Order records that external users should not be able to see
//        /// </summary>
//        /// <param name="loadResult"></param>
//        /// <returns></returns>
//        public IEnumerable<dynamic> TrimOrderForClient(LoadResult loadResult)
//        {
//            if (loadResult == null) throw new ArgumentNullException(nameof(loadResult));

//            return loadResult.data.Cast<dynamic>().Select(order =>
//                ClientOrder.CreateClientOrder(order));
//        }

//        #endregion

//        #endregion
//    }
//}