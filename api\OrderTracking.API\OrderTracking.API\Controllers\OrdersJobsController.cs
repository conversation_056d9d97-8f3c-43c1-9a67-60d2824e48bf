﻿//using System.Threading.Tasks;
//using DevExtreme.AspNet.Data;
//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Mvc;
//using OrderTracking.API.Interfaces.Services;
//using OrderTracking.API.Models;

//namespace OrderTracking.API.Controllers
//{
//    /// <summary>
//    ///     API Controller for accessing WMO ETL jobs
//    /// </summary>
//    [Authorize]
//    [Route("api/[controller]")]
//    [ApiController]
//    [Authorize(Policy = "UserIsActive")]
//    public class OrdersJobsController : ControllerBase
//    {
//        private readonly IOrdersJobsService _service;

//        /// <summary>
//        ///     Constructor that injects an IOrdersJobsService to get OrdersJobs records
//        /// </summary>
//        /// <param name="service"></param>
//        public OrdersJobsController(IOrdersJobsService service)
//        {
//            _service = service;
//        }

//        /// <summary>
//        ///     Get the etl job meta data records for WMO ETL jobs.
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet]
//        [Authorize(Policy = "App:Admin")]
//        public async Task<IActionResult> Get(DataSourceLoadOptions loadOptions)
//        {
//            var jobsQueryable = _service.GetJobs();
//            return Ok(await DataSourceLoader.LoadAsync(jobsQueryable, loadOptions));
//        }
//    }
//}