<div class="dx-card responsive-paddings content-block">
    <!-- Don't want to display tab panel unless a leak report is selected -->
    <dx-tab-panel [items]="tabs"
                  [disabled]="!report">

        <div *dxTemplate="let data of 'general'">
            <app-general-info-tab [report]="report"
                                  [allowEditing]="allowEditing$ | async"
                                  (statusChanged)="onStatusChanged($event)">
            </app-general-info-tab>
        </div>
        <div class="responsive-paddings"
             *dxTemplate="let data of 'workDetails'">
            <app-work-details-tab [workDetails]="report | leakReportWorkDetails"
                                  [allowEditing]="allowEditing$ | async"
                                  (saving)="onLeakReportWorkDetailsSaving($event)"
                                  (photoDescriptionUpdate)="onPhotoDescriptionUpdate($event)"
                                  (photoDelete)="onPhotoDelete($event)">
            </app-work-details-tab>
        </div>
        <div *dxTemplate="let data of 'leakReport'">
            <app-leak-report-tab [report]="report | leakReportInfo"
                                 [allowEditing]="allowEditing$ | async"
                                 (saving)="onLeakReportInfoSaving($event)"
                                 (photoDescriptionUpdate)="onPhotoDescriptionUpdate($event)"
                                 (photoDelete)="onPhotoDelete($event)">
            </app-leak-report-tab>
        </div>
        <div *dxTemplate="let data of 'photos'">
            <app-leak-repair-photos [reportID]="report?.id"
                                    [photoGroups]="report | leakReportPhotos"
                                    [allowEditing]="allowEditing$ | async">
            </app-leak-repair-photos>
        </div>
        <!-- <div *dxTemplate="let data of 'reports'">
            <app-report-tab-button [report]="report"></app-report-tab-button>
        </div> -->
    </dx-tab-panel>
</div>