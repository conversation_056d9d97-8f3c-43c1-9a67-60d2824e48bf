﻿using System;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using OrderTracking.API.Services;

namespace OrderTracking.API.Controllers
{
    /// <summary>
    ///     Status update
    /// </summary>
    public class StatusUpdate
    {
        /// <summary>
        ///     New status of work request
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        ///     Work request ID / Name / Number
        /// </summary>
        public string WorkRequestNumber { get; set; }

        /// <summary>
        ///     Start time of the work request
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        ///     End time of the work request
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        ///     Arrival time of the work request
        /// </summary>
        public DateTime ArrivalTime { get; set; }
    }

    /// <summary>
    ///     User comment
    /// </summary>
    public class UserComment
    {
        /// <summary>
        ///     Work request ID / Name / Number
        /// </summary>
        public string WorkRequestNumber { get; set; }

        /// <summary>
        ///     Comment (text)
        /// </summary>
        public string Comment { get; set; }
    }

    /// <summary>
    ///     Controller for Field Service Management
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class FieldServiceManagementController : ControllerBase
    {
        private readonly IFieldServiceManagementService _fsm;
        private readonly IUserProfilesService _users;

        /// <summary>
        ///     Constructor
        /// </summary>
        /// <param name="service"></param>
        /// <param name="users"></param>
        public FieldServiceManagementController(IFieldServiceManagementService service, IUserProfilesService users)
        {
            _fsm = service;
            _users = users;
        }

        /// <summary>
        ///     Get all work requests of TRADE type
        /// </summary>
        /// <returns></returns>
        [HttpGet("All")]
        public async Task<IActionResult> GetAll()
        {
            var workOrders = await _fsm.GetWorkOrders();
            return Ok(workOrders);
        }

        /// <summary>
        ///     Get annotations for a particular work request
        /// </summary>
        /// <param name="requestId"></param>
        /// <returns></returns>
        [HttpGet("Annotations/{requestId}")]
        public async Task<IActionResult> GetAnnotations(string requestId)
        {
            var parsed = Guid.TryParse(requestId, out var id);
            if (!parsed) return UnprocessableEntity($"Unable to parse the work request ID: {requestId}");

            var annotations = await _fsm.GetAnnotationsForWorkOrder(id);
            return Ok(annotations);
        }

        /// <summary>
        ///     Update the status of a work request
        /// </summary>
        /// <param name="statusUpdate"></param>
        /// <returns></returns>
        [HttpPost("Status")]
        public async Task<IActionResult> UpdateStatus([FromBody] StatusUpdate statusUpdate)
        {
            if (statusUpdate == null) throw new ArgumentNullException(nameof(statusUpdate));

            var message = statusUpdate.Status switch
            {
                "Open - Unscheduled" => await _fsm.MoveToUnscheduled(statusUpdate.WorkRequestNumber),
                "Open - Scheduled" => await _fsm.MoveToScheduled(statusUpdate.WorkRequestNumber),
                "Open - In Progress" => await _fsm.MoveToInProgress(statusUpdate),
                "Open - Completed" => await _fsm.MoveToComplete(statusUpdate.WorkRequestNumber),
                _ => string.Empty
            };

            return Ok(message);
        }

        /// <summary>
        ///     Add a comment to a work request (by adding an annotation)
        /// </summary>
        /// <param name="userComment"></param>
        /// <returns></returns>
        [HttpPost("Comments")]
        public async Task<IActionResult> AddComment(UserComment userComment)
        {
            if (userComment == null) throw new ArgumentNullException(nameof(userComment));
            var user = await _users.GetAsync(User.Identity.Name.ToLower());
            var message = await _fsm.AddComment(userComment.WorkRequestNumber, userComment.Comment, user);
            return Ok(message);
        }
    }
}
