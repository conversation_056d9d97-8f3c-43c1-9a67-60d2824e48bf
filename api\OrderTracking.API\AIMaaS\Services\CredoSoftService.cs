﻿using System.Collections.Generic;
using System.Threading.Tasks;
using AIMaaS.Models;
using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;

namespace AIMaaS.Services
{
    /// <summary>
    ///     Service class the provides access to CredoSoft data
    /// </summary>
    public class CredoSoftService : ICredoSoftService
    {
        private readonly IOptions<CredoSoftData> _dataOptions;

        /// <summary>
        ///     Constructs the CredoSoftService class, injecting CredoSoftData configuration
        /// </summary>
        /// <param name="dataOptions"></param>
        public CredoSoftService(IOptions<CredoSoftData> dataOptions)
        {
            _dataOptions = dataOptions;
        }

        /// <summary>
        ///     Get assets from CredoSoft
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<Asset>> GetAssetsAsync()
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            var assets = await connection.QueryAsync<Asset>(@"
SELECT
asset.RSITE_NAME_ as RSITE_NAME,
asset.OBJGROUPID,
asset.OBJNAME,
asset.OBJDESC,
asset.OBJTYPECODE,
asset.OBJSERVICE,
asset.RSTREAM_NAME,
asset.TM_PV_SPEC_GRAVITY,
asset.OBJCOMMENT,
asset.EQINSPCODE,
asset.EQINSPCODE_CUSTOM,
asset.EQDESIGNCODE,
asset.EQMANUFACTURER,
asset.EQMANUFDATE,
asset.OBJCOMMISSION,
asset.TM_PV_NAT_BOARD_NO,
asset.EQSERIALNUM,
asset.TM_PV_DATE_REPAIRED,
asset.TM_PV_DATE_ALTERED,
asset.TM_PV_R_CERT_NO,
asset.TM_PV_CERT_HOLDER,
asset.TM_PV_ORIENTATION,
asset.DIM_DIAMOUTSIDE,
asset.DIM_DIAMUNITS,
asset.DIM_LENGTHUNIT,
asset.DIM_LENGTH,
asset.PRESSUNITS,
asset.PRESSDESMAX,
asset.TM_PV_RLF_DEV_PRESS,
asset.PRESSOPERNORM,
asset.TEMPUNITS,
asset.TEMPDESMAX,
asset.TEMPOPERNORM,
asset.EQ_IS_PWHT,
asset.OBJCORRCIRC_ as OBJCORRCIRC,
asset.EQLININGEXT_ as EQLININGEXT,
asset.EQLININGINT_ as EQLININGINT,
asset.OBJRISKCODE_ as OBJRISKCODE,
asset.OBJSTATUS_ as OBJSTATUS,
asset.OBJUNIQUEID,
asset.OBJID,
asset.RSITE_RID,
om.OBJCAT,
om.OBJSYS_SORT_1,
om.OBJSYS_SORT_2,
iat.ALY_OM_APSC_AREA_ID,
iat.ALY_OM_APSC_REGION,
iat.ALY_OM_SPEC_LOC,
iat.ALY_OM_LOC_DESC,
iat.ALY_OM_BUILDING,
iat.ALY_OM_APSC_JURIS_NO,
iat.ALY_OM_STATE_LOCID,
ia.ALY_OM_APSC_BOARD_NO,
iat.ALY_OM_STATE_CRT_EXP,
iat.ALY_OM_STATE_CRT_INT,
iat.ALY_OM_NDE_INSP_DUE,
iat.ALY_OM_NDE_INSP_LAST,
prop.ALY_OM_VERT_HORIZ,
iat.ALY_OM_APRX_VOL_FEET,
iat.ALY_OM_PV_CAP,
iat.ALY_OM_BLR_1_CAP,
iat.ALY_OM_BLR_2_CAP,
iat.ALY_OM_BLR_3_CAP,
iat.ALY_OM_RISK_NOTES,
iat.ALY_OM_ACC_TYPE,
iat.ALY_OM_INT_TIER,
iat.ALY_OM_EXT_TIER,
iat.ALY_OM_INT_SCAFF,
iat.ALY_OM_EXT_SCAFF,
iat.ALY_OM_INT_INS_RMV,
iat.ALY_OM_EXT_INS_RMV,
iat.ALY_OM_CONF_ENT,
iat.ALY_OM_MANWAY_DET,
iat.ALY_OM_INT_ACC_NOTES,
iat.ALY_OM_EXT_ACC_NOTES,
iat.TM_PV_ROPE_ACCESS,
iat.TM_PV_RMT_VIS_INSP_TYP,
iat.TM_PV_SPEC_PPE_REQ,
iat.ALY_OM_REDUNANCY,
iat.ALY_OM_EXPOSURE,
iat.ALY_OM_EST_CONS,
iat.ALY_OM_CRITICALITY,
iat.ALY_OM_EST_INTERR

from C8.VE_OM_TM_GEN_TOP asset
left join C8.VE_OM_TM_GEN_SUB c on c.OBJID = asset.OBJID
inner join C8.OBJ_MASTER om on om.OBJID = asset.OBJID
inner join C8.VE_OM_ALY_INSP_ACC_TOP iat on iat.OBJID = asset.OBJID
inner join C8.VE_OM_ALY_INSP_ACCESS ia on ia.OBJID = asset.OBJID
inner join C8.VE_OM_ALY_PROPERTIES prop on prop.OBJID = asset.OBJID
where c.OBJID is null 
");

            return assets;
        }

        /// <summary>
        ///     Get a specific asset from CredoSoft
        /// </summary>
        /// <param name="objId"></param>
        /// <returns></returns>
        public async Task<Asset> GetAssetAsync(long objId)
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            var asset = await connection.QuerySingleAsync<Asset>($@"
SELECT
asset.RSITE_NAME_ as RSITENAME,
asset.OBJGROUPID,
asset.OBJNAME,
asset.OBJDESC,
asset.OBJTYPECODE,
asset.OBJSERVICE,
asset.RSTREAM_NAME,
asset.TM_PV_SPEC_GRAVITY,
asset.OBJCOMMENT,
asset.EQINSPCODE,
asset.EQINSPCODE_CUSTOM,
asset.EQDESIGNCODE,
asset.EQMANUFACTURER,
asset.EQMANUFDATE,
asset.OBJCOMMISSION,
asset.TM_PV_NAT_BOARD_NO,
asset.EQSERIALNUM,
asset.TM_PV_DATE_REPAIRED,
asset.TM_PV_DATE_ALTERED,
asset.TM_PV_R_CERT_NO,
asset.TM_PV_CERT_HOLDER,
asset.TM_PV_ORIENTATION,
asset.DIM_DIAMOUTSIDE,
asset.DIM_DIAMUNITS,
asset.DIM_LENGTHUNIT,
asset.DIM_LENGTH,
asset.PRESSUNITS,
asset.PRESSDESMAX,
asset.TM_PV_RLF_DEV_PRESS,
asset.PRESSOPERNORM,
asset.TEMPUNITS,
asset.TEMPDESMAX,
asset.TEMPOPERNORM,
asset.EQ_IS_PWHT,
asset.OBJCORRCIRC_ as OBJCORRCIRC,
asset.EQLININGEXT_ as EQLININGEXT,
asset.EQLININGINT_ as EQLININGINT,
asset.OBJRISKCODE_ as OBJRISKCODE,
asset.OBJSTATUS_ as OBJSTATUS,
asset.OBJUNIQUEID,
asset.OBJID,
asset.RSITE_RID,
om.OBJCAT,
om.OBJSYS_SORT_1,
om.OBJSYS_SORT_2,
iat.ALY_OM_APSC_AREA_ID,
iat.ALY_OM_APSC_REGION,
iat.ALY_OM_SPEC_LOC,
iat.ALY_OM_LOC_DESC,
iat.ALY_OM_BUILDING,
iat.ALY_OM_APSC_JURIS_NO,
iat.ALY_OM_STATE_LOCID,
ia.ALY_OM_APSC_BOARD_NO,
iat.ALY_OM_STATE_CRT_EXP,
iat.ALY_OM_STATE_CRT_INT,
iat.ALY_OM_NDE_INSP_DUE,
iat.ALY_OM_NDE_INSP_LAST,
prop.ALY_OM_VERT_HORIZ,
iat.ALY_OM_APRX_VOL_FEET,
iat.ALY_OM_PV_CAP,
iat.ALY_OM_BLR_1_CAP,
iat.ALY_OM_BLR_2_CAP,
iat.ALY_OM_BLR_3_CAP,
iat.ALY_OM_RISK_NOTES,
iat.ALY_OM_ACC_TYPE,
iat.ALY_OM_INT_TIER,
iat.ALY_OM_EXT_TIER,
iat.ALY_OM_INT_SCAFF,
iat.ALY_OM_EXT_SCAFF,
iat.ALY_OM_INT_INS_RMV,
iat.ALY_OM_EXT_INS_RMV,
iat.ALY_OM_CONF_ENT,
iat.ALY_OM_MANWAY_DET,
iat.ALY_OM_INT_ACC_NOTES,
iat.ALY_OM_EXT_ACC_NOTES,
iat.TM_PV_ROPE_ACCESS,
iat.TM_PV_RMT_VIS_INSP_TYP,
iat.TM_PV_SPEC_PPE_REQ,
iat.ALY_OM_REDUNANCY,
iat.ALY_OM_EXPOSURE,
iat.ALY_OM_EST_CONS,
iat.ALY_OM_CRITICALITY,
iat.ALY_OM_EST_INTERR

from C8.VE_OM_TM_GEN_TOP asset
left join C8.VE_OM_TM_GEN_SUB c on c.OBJID = asset.OBJID
inner join C8.OBJ_MASTER om on om.OBJID = asset.OBJID
inner join C8.VE_OM_ALY_INSP_ACC_TOP iat on iat.OBJID = asset.OBJID
inner join C8.VE_OM_ALY_INSP_ACCESS ia on ia.OBJID = asset.OBJID
inner join C8.VE_OM_ALY_PROPERTIES prop on prop.OBJID = asset.OBJID
where c.OBJID is null and asset.[OBJID] = {objId}
");

            return asset;
        }

        /// <summary>
        ///     Get components from CredoSoft
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<Component>> GetComponentsAsync()
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            var components = await connection.QueryAsync<Component>($@"
select
    om.OBJPARENT,
	s.RSITE_NAME as RSITE_NAME,
	omp.OBJGROUPID as OBJGROUPID,
	omp.OBJNAME as OBJNAME_PARENT,
	om.OBJNAME,
	om.OBJDESC,
	omp.OBJTYPECODE as OBJTYPECODE,
	om.OBJSERVICE,
	stream.RSTREAM_NAME,
	pv.TM_PV_SPEC_GRAVITY,
	om.OBJCOMMENT,
	eqp.EQDESIGNCODE as EQDESIGNCODE,
	eqp.EQINSPCODE as EQINSPCODE,
	eq.EQMANUFACTURER,
	eq.EQMANUFDATE,
	om.OBJCOMMISSION,
	pvp.TM_PV_NAT_BOARD_NO as TM_PV_NAT_BOARD_NO,
	eqp.EQSERIALNUM as EQSERIALNUM,
	pv.TM_PV_DATE_REPAIRED,
	pv.TM_PV_DATE_ALTERED,
	pv.TM_PV_R_CERT_NO,
	pv.TM_PV_CERT_HOLDER,
	pvp.TM_PV_ORIENTATION as TM_PV_ORIENTATION,
	pv.TM_PV_CONFIG_GEOM,
	dm.DIM_DIAMOUTSIDE,
	dmp.DIM_DIAMUNITS as DIM_DIAMUNITS,
	dm.DIM_LENGTH,
	dmp.DIM_LENGTHUNIT as DIM_LENGTHUNIT,
	rmat.RMAT_NAME01 AS XPICK_OBJREF_MATID,
	rmat.RMAT_SPEC as MATSPEC,
	rmat.RMAT_GRADE as MATGRADE,
	rmat.RMAT_DAMAGE_GROUP as RMAT_DAMAGE_GROUP,
	prp.PRESSUNITS as PRESSUNITS,
	pr.PRESSDESMAX,
	pr.PRSSRELIEF,
	pr.PRESSOPERNORM,
	tmpp.TEMPUNITS as TEMPUNITS,
	tmp.TEMPDESMAX,
	tmp.TEMPOPERNORM,
	eqp.EQ_IS_PWHT as EQ_IS_PWHT,
	om.OBJCORRCIRC as OBJCORRCIRC,
	eq.EQLININGEXT as EQLININGEXT,
	eq.EQLININGINT as EQLININGINT,
	om.OBJRISKCODE as OBJRISKCODE,
	om.OBJSTATUS as OBJSTATUS,
	om.OBJUNIQUEID,
	om.OBJID,
	s.RSITE_RID,
    om.OBJSYS_SORT_1,
    om.OBJSYS_SORT_2

from c8.obj_master om
join c8.OBJ_MASTER omp on omp.OBJID = om.OBJPARENT
join c8.cdoref_site s on s.RSITE_RID = om.OBJSYS_SITE_RID
left join c8.OBJREL_PROPGEN_EQUIP eq on eq.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_EQUIP eqp on eqp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_TM_PV pv on pv.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_TM_PV pvp on pvp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_PRESSURE pr on pr.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_PRESSURE prp on prp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_DIMENSION dm on dm.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_DIMENSION dmp on dmp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_TEMP tmp on tmp.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_TEMP tmpp on tmpp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_MATERIAL mt on mt.OBJMASTERID = om.OBJID
left join c8.CDOREF_MATERIAL rmat on rmat.RMAT_RID = om.OBJREF_MATID
left join c8.CDOREF_STREAM stream on stream.RSTREAM_ID = om.OBJREF_STREAMID
            ");

            return components;
        }

        /// <summary>
        ///     Get components associated with a specific asset from CredoSoft
        /// </summary>
        /// <param name="assetObjId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<Component>> GetComponentsForAssetAsync(long assetObjId)
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            // The view defined for components does not include the parent id, so it makes it difficult
            // to get the components for a specific asset.  Querying with the definition of the view
            // as of 10/20/2020 so that we can filter on the OBJID of the parent (omp.OBJID = om.OBJPARENTID)
            var components = await connection.QueryAsync<Component>($@"
select
    om.OBJPARENT,
	s.RSITE_NAME as RSITE_NAME,
	omp.OBJGROUPID as OBJGROUPID,
	omp.OBJNAME as OBJNAME_PARENT,
	om.OBJNAME,
	om.OBJDESC,
	omp.OBJTYPECODE as OBJTYPECODE,
	om.OBJSERVICE,
	stream.RSTREAM_NAME,
	pv.TM_PV_SPEC_GRAVITY,
	om.OBJCOMMENT,
	eqp.EQDESIGNCODE as EQDESIGNCODE,
	eqp.EQINSPCODE as EQINSPCODE,
	eq.EQMANUFACTURER,
	eq.EQMANUFDATE,
	om.OBJCOMMISSION,
	pvp.TM_PV_NAT_BOARD_NO as TM_PV_NAT_BOARD_NO,
	eqp.EQSERIALNUM as EQSERIALNUM,
	pv.TM_PV_DATE_REPAIRED,
	pv.TM_PV_DATE_ALTERED,
	pv.TM_PV_R_CERT_NO,
	pv.TM_PV_CERT_HOLDER,
	pvp.TM_PV_ORIENTATION as TM_PV_ORIENTATION,
	pv.TM_PV_CONFIG_GEOM,
	dm.DIM_DIAMOUTSIDE,
	dmp.DIM_DIAMUNITS as DIM_DIAMUNITS,
	dm.DIM_LENGTH,
	dmp.DIM_LENGTHUNIT as DIM_LENGTHUNIT,
	rmat.RMAT_NAME01 AS XPICK_OBJREF_MATID,
	rmat.RMAT_SPEC as MATSPEC,
	rmat.RMAT_GRADE as MATGRADE,
	rmat.RMAT_DAMAGE_GROUP as RMAT_DAMAGE_GROUP,
	prp.PRESSUNITS as PRESSUNITS,
	pr.PRESSDESMAX,
	pr.PRSSRELIEF,
	pr.PRESSOPERNORM,
	tmpp.TEMPUNITS as TEMPUNITS,
	tmp.TEMPDESMAX,
	tmp.TEMPOPERNORM,
	eqp.EQ_IS_PWHT as EQ_IS_PWHT,
	om.OBJCORRCIRC as OBJCORRCIRC,
	eq.EQLININGEXT as EQLININGEXT,
	eq.EQLININGINT as EQLININGINT,
	om.OBJRISKCODE as OBJRISKCODE,
	om.OBJSTATUS as OBJSTATUS,
	om.OBJUNIQUEID,
	om.OBJID,
	s.RSITE_RID,
    om.OBJSYS_SORT_1,
    om.OBJSYS_SORT_2

from c8.obj_master om
join c8.OBJ_MASTER omp on omp.OBJID = om.OBJPARENT
join c8.cdoref_site s on s.RSITE_RID = om.OBJSYS_SITE_RID
left join c8.OBJREL_PROPGEN_EQUIP eq on eq.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_EQUIP eqp on eqp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_TM_PV pv on pv.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_TM_PV pvp on pvp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_PRESSURE pr on pr.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_PRESSURE prp on prp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_DIMENSION dm on dm.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_DIMENSION dmp on dmp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_TEMP tmp on tmp.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_TEMP tmpp on tmpp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_MATERIAL mt on mt.OBJMASTERID = om.OBJID
left join c8.CDOREF_MATERIAL rmat on rmat.RMAT_RID = om.OBJREF_MATID
left join c8.CDOREF_STREAM stream on stream.RSTREAM_ID = om.OBJREF_STREAMID
where omp.OBJID = {assetObjId}
");

            return components;
        }

        /// <summary>
        ///     Get a specific component from CredoSoft
        /// </summary>
        /// <param name="objId"></param>
        /// <returns></returns>
        public async Task<Component> GetComponentAsync(long objId)
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            var component = await connection.QuerySingleAsync<Component>($@"
select
    om.OBJPARENT,
	s.RSITE_NAME as RSITE_NAME,
	omp.OBJGROUPID as OBJGROUPID,
	omp.OBJNAME as OBJNAME_PARENT,
	om.OBJNAME,
	om.OBJDESC,
	omp.OBJTYPECODE as OBJTYPECODE,
	om.OBJSERVICE,
	stream.RSTREAM_NAME,
	pv.TM_PV_SPEC_GRAVITY,
	om.OBJCOMMENT,
	eqp.EQDESIGNCODE as EQDESIGNCODE,
	eqp.EQINSPCODE as EQINSPCODE,
	eq.EQMANUFACTURER,
	eq.EQMANUFDATE,
	om.OBJCOMMISSION,
	pvp.TM_PV_NAT_BOARD_NO as TM_PV_NAT_BOARD_NO,
	eqp.EQSERIALNUM as EQSERIALNUM,
	pv.TM_PV_DATE_REPAIRED,
	pv.TM_PV_DATE_ALTERED,
	pv.TM_PV_R_CERT_NO,
	pv.TM_PV_CERT_HOLDER,
	pvp.TM_PV_ORIENTATION as TM_PV_ORIENTATION,
	pv.TM_PV_CONFIG_GEOM,
	dm.DIM_DIAMOUTSIDE,
	dmp.DIM_DIAMUNITS as DIM_DIAMUNITS,
	dm.DIM_LENGTH,
	dmp.DIM_LENGTHUNIT as DIM_LENGTHUNIT,
	rmat.RMAT_NAME01 AS XPICK_OBJREF_MATID,
	rmat.RMAT_SPEC as MATSPEC,
	rmat.RMAT_GRADE as MATGRADE,
	rmat.RMAT_DAMAGE_GROUP as RMAT_DAMAGE_GROUP,
	prp.PRESSUNITS as PRESSUNITS,
	pr.PRESSDESMAX,
	pr.PRSSRELIEF,
	pr.PRESSOPERNORM,
	tmpp.TEMPUNITS as TEMPUNITS,
	tmp.TEMPDESMAX,
	tmp.TEMPOPERNORM,
	eqp.EQ_IS_PWHT as EQ_IS_PWHT,
	om.OBJCORRCIRC as OBJCORRCIRC,
	eq.EQLININGEXT as EQLININGEXT,
	eq.EQLININGINT as EQLININGINT,
	om.OBJRISKCODE as OBJRISKCODE,
	om.OBJSTATUS as OBJSTATUS,
	om.OBJUNIQUEID,

	om.OBJID,
	s.RSITE_RID,
    om.OBJSYS_SORT_1,
    om.OBJSYS_SORT_2

from c8.obj_master om
join c8.OBJ_MASTER omp on omp.OBJID = om.OBJPARENT
join c8.cdoref_site s on s.RSITE_RID = om.OBJSYS_SITE_RID
left join c8.OBJREL_PROPGEN_EQUIP eq on eq.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_EQUIP eqp on eqp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_TM_PV pv on pv.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_TM_PV pvp on pvp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_PRESSURE pr on pr.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_PRESSURE prp on prp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_DIMENSION dm on dm.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_DIMENSION dmp on dmp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_TEMP tmp on tmp.OBJMASTERID = om.OBJID
left join c8.OBJREL_PROPGEN_TEMP tmpp on tmpp.OBJMASTERID = omp.OBJID
left join c8.OBJREL_PROPGEN_MATERIAL mt on mt.OBJMASTERID = om.OBJID
left join c8.CDOREF_MATERIAL rmat on rmat.RMAT_RID = om.OBJREF_MATID
left join c8.CDOREF_STREAM stream on stream.RSTREAM_ID = om.OBJREF_STREAMID
where om.[OBJID] = {objId}
");

            return component;
        }

        /// <summary>
        ///     Get inspections from CredoSoft
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<Inspection>> GetInspectionsAsync()
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            var inspections = await connection.QueryAsync<Inspection>(@"
select

insp.RSITE_NAME_ as RSITE_NAME,
insp.OBJGROUPID_ as OBJGROUPID,
insp.OBJNAME_ as OBJNAME,
insp.OBJDESC_ as OBJDESC,
insp.OBJTYPECODE_ as OBJTYPECODE,
insp.OBJSERVICE_ as OBJSERVICE,
insp.OBJCOMMENT_ as OBJCOMMENT,
insp.EQDESIGNCODE_ as EQDESIGNCODE,
insp.EQINSPCODE_ as EQINSPCODE,
insp.TM_PV_ORIENTATION_ as TM_PV_ORIENTATION,
insp.DIM_DIAMOUTSIDE_ as DIM_DIAMOUTSIDE,
insp.DIM_DIAMUNITS_ as DIM_DIAMUNITS,
insp.DIM_LENGTH_ as DIM_LENGTH,
insp.DIM_LENGTHUNIT_ as DIM_LENGTHUNIT,
insp.PRESSUNITS_ as PRESSUNITS,
insp.PRESSDESMAX_ as PRESSDESMAX,
insp.TM_PV_RLF_DEV_PRESS_ as TM_PV_RLF_DEV_PRESS,
insp.PRESSOPERNORM_ as PRESSOPERNORM,
insp.TEMPUNITS_ as TEMPUNITS,
insp.TEMPDESMAX_ as TEMPDESMAX,
insp.TEMPOPERNORM_ as TEMPOPERNORM,
insp.EQ_IS_PWHT_ as EQ_IS_PWHT,
insp.OBJCORRCIRC_ as OBJCORRCIRC,
insp.EQLININGEXT_ as EQLININGEXT,
insp.EQLININGINT_ as EQLININGINT,
insp.OBJRISKCODE_ as OBJRISKCODE,
insp.OBJSTATUS_ as OBJSTATUS,
insp.ALY_OM_APRX_VOL_FEET,
insp.ALY_OM_PV_CAP,
insp.ALY_OM_BLR_1_CAP,
insp.ALY_OM_BLR_2_CAP,
insp.ALY_OM_BLR_3_CAP,
insp.ALY_OM_RISK_NOTES,
insp.ALY_OM_ACC_TYPE,
insp.ALY_OM_INT_TIER,
insp.ALY_OM_EXT_TIER,
insp.ALY_OM_INT_SCAFF,
insp.ALY_OM_EXT_SCAFF,
insp.ALY_OM_INT_INS_RMV,
insp.ALY_OM_EXT_INS_RMV,
insp.ALY_OM_CONF_ENT,
insp.ALY_OM_MANWAY_DET,
insp.ALY_OM_INT_ACC_NOTES,
insp.ALY_OM_EXT_ACC_NOTES,
insp.TM_PV_ROPE_ACCESS,
insp.TM_PV_RMT_VIS_INSP_TYP,
insp.TM_PV_SPEC_PPE_REQ,
insp.ALY_OM_REDUNANCY,
insp.ALY_OM_EXPOSURE,
insp.ALY_OM_EST_CONS,
insp.ALY_OM_CRITICALITY,
insp.ALY_OM_EST_INTERR,
insp.ALY_OM_APSC_AREA_ID,
insp.ALY_OM_APSC_REGION,
insp.ALY_OM_SPEC_LOC,
insp.ALY_OM_LOC_DESC,
insp.ALY_OM_BUILDING,
insp.ALY_OM_APSC_JURIS_NO,
insp.ALY_OM_STATE_LOCID,
insp.ALY_OM_STATE_CRT_EXP,
insp.ALY_OM_STATE_CRT_INT,
insp.ALY_OM_NDE_INSP_DUE,
insp.ALY_OM_NDE_INSP_LAST,
insp.OBJUNIQUEID,
insp.OBJID,
insp.RSITE_RID,
em.EMDATERAISED,
em.EMDATEPLANDUE,
em.EMDATECOMPLETE,
em.EMTYPECODE,
em.EVENTPRIORITY,
em.EMSTATUS,
em.EVENTNAME,
em.EVENTDESCRIPTION,
em.XEMPLAYER,
em.EVENTCOMMENT,
em.EHINSTRUCTION1,
em.EHINSTRUCTION2,
em.EHOBSERVATION1,
em.EHOBSERVATION2,
em.EHRECOMMENDATION1,
em.EHRECOMMENDATION2,
em.EHACTIONTAKEN01,
em.EHACTIONTAKEN02,
em.EMID,
t.RSTREAM_NAME,
access.ALY_OM_APSC_BOARD_NO,
t.EQSERIALNUM,
t.TM_PV_DATE_REPAIRED,
t.TM_PV_DATE_ALTERED,
t.TM_PV_R_CERT_NO,
t.TM_PV_CERT_HOLDER,
t.EQMANUFACTURER,
t.EQMANUFDATE,
t.OBJCOMMISSION,
prop.ALY_OM_VERT_HORIZ,
om.OBJSYS_SORT_1,
om.OBJSYS_SORT_2

from [C8].[VE_OM_ALY_INSP_ACC_TOP] insp
left join [C8].[VE_OM_TM_GEN_SUB] c on insp.OBJID = c.OBJID
inner join [C8].[OBJ_MASTER] om on insp.OBJID = om.OBJID
inner join [C8].[VE_EM] em on insp.OBJID = em.OBJID
inner join [C8].[VE_OM_TM_GEN_TOP] t on t.OBJID = insp.OBJID
inner join [C8].[VE_OM_ALY_INSP_ACCESS] access on access.OBJID = insp.OBJID
inner join [C8].[VE_OM_ALY_PROPERTIES] prop on prop.OBJID = insp.OBJID
where c.OBJID is null
");

            return inspections;
        }

        /// <summary>
        ///     Get a specific inspection from CredoSoft
        /// </summary>
        /// <param name="objId"></param>
        /// <returns></returns>
        public async Task<Inspection> GetInspectionAsync(long objId)
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            var inspection = await connection.QuerySingleAsync<Inspection>($@"
select

insp.RSITE_NAME_ as RSITE_NAME,
insp.OBJGROUPID_ as OBJGROUPID,
insp.OBJNAME_ as OBJNAME,
insp.OBJDESC_ as OBJDESC,
insp.OBJTYPECODE_ as OBJTYPECODE,
insp.OBJSERVICE_ as OBJSERVICE,
insp.OBJCOMMENT_ as OBJCOMMENT,
insp.EQDESIGNCODE_ as EQDESIGNCODE,
insp.EQINSPCODE_ as EQINSPCODE,
insp.TM_PV_ORIENTATION_ as TM_PV_ORIENTATION,
insp.DIM_DIAMOUTSIDE_ as DIM_DIAMOUTSIDE,
insp.DIM_DIAMUNITS_ as DIM_DIAMUNITS,
insp.DIM_LENGTH_ as DIM_LENGTH,
insp.DIM_LENGTHUNIT_ as DIM_LENGTHUNIT,
insp.PRESSUNITS_ as PRESSUNITS,
insp.PRESSDESMAX_ as PRESSDESMAX,
insp.TM_PV_RLF_DEV_PRESS_ as TM_PV_RLF_DEV_PRESS,
insp.PRESSOPERNORM_ as PRESSOPERNORM,
insp.TEMPUNITS_ as TEMPUNITS,
insp.TEMPDESMAX_ as TEMPDESMAX,
insp.TEMPOPERNORM_ as TEMPOPERNORM,
insp.EQ_IS_PWHT_ as EQ_IS_PWHT,
insp.OBJCORRCIRC_ as OBJCORRCIRC,
insp.EQLININGEXT_ as EQLININGEXT,
insp.EQLININGINT_ as EQLININGINT,
insp.OBJRISKCODE_ as OBJRISKCODE,
insp.OBJSTATUS_ as OBJSTATUS,
insp.ALY_OM_APRX_VOL_FEET,
insp.ALY_OM_PV_CAP,
insp.ALY_OM_BLR_1_CAP,
insp.ALY_OM_BLR_2_CAP,
insp.ALY_OM_BLR_3_CAP,
insp.ALY_OM_RISK_NOTES,
insp.ALY_OM_ACC_TYPE,
insp.ALY_OM_INT_TIER,
insp.ALY_OM_EXT_TIER,
insp.ALY_OM_INT_SCAFF,
insp.ALY_OM_EXT_SCAFF,
insp.ALY_OM_INT_INS_RMV,
insp.ALY_OM_EXT_INS_RMV,
insp.ALY_OM_CONF_ENT,
insp.ALY_OM_MANWAY_DET,
insp.ALY_OM_INT_ACC_NOTES,
insp.ALY_OM_EXT_ACC_NOTES,
insp.TM_PV_ROPE_ACCESS,
insp.TM_PV_RMT_VIS_INSP_TYP,
insp.TM_PV_SPEC_PPE_REQ,
insp.ALY_OM_REDUNANCY,
insp.ALY_OM_EXPOSURE,
insp.ALY_OM_EST_CONS,
insp.ALY_OM_CRITICALITY,
insp.ALY_OM_EST_INTERR,
insp.ALY_OM_APSC_AREA_ID,
insp.ALY_OM_APSC_REGION,
insp.ALY_OM_SPEC_LOC,
insp.ALY_OM_LOC_DESC,
insp.ALY_OM_BUILDING,
insp.ALY_OM_APSC_JURIS_NO,
insp.ALY_OM_STATE_LOCID,
insp.ALY_OM_STATE_CRT_EXP,
insp.ALY_OM_STATE_CRT_INT,
insp.ALY_OM_NDE_INSP_DUE,
insp.ALY_OM_NDE_INSP_LAST,
insp.OBJUNIQUEID,
insp.OBJID,
insp.RSITE_RID,
em.EMDATERAISED,
em.EMDATEPLANDUE,
em.EMDATECOMPLETE,
em.EMTYPECODE,
em.EVENTPRIORITY,
em.EMSTATUS,
em.EVENTNAME,
em.EVENTDESCRIPTION,
em.XEMPLAYER,
em.EVENTCOMMENT,
em.EHINSTRUCTION1,
em.EHINSTRUCTION2,
em.EHOBSERVATION1,
em.EHOBSERVATION2,
em.EHRECOMMENDATION1,
em.EHRECOMMENDATION2,
em.EHACTIONTAKEN01,
em.EHACTIONTAKEN02,
em.EMID,
t.RSTREAM_NAME,
access.ALY_OM_APSC_BOARD_NO,
t.EQSERIALNUM,
t.TM_PV_DATE_REPAIRED,
t.TM_PV_DATE_ALTERED,
t.TM_PV_R_CERT_NO,
t.TM_PV_CERT_HOLDER,
t.EQMANUFACTURER,
t.EQMANUFDATE,
t.OBJCOMMISSION,
prop.ALY_OM_VERT_HORIZ,
om.OBJSYS_SORT_1,
om.OBJSYS_SORT_2

from [C8].[VE_OM_ALY_INSP_ACC_TOP] insp
left join [C8].[VE_OM_TM_GEN_SUB] c on insp.OBJID = c.OBJID
inner join [C8].[OBJ_MASTER] om on insp.OBJID = om.OBJID
inner join [C8].[VE_EM] em on insp.OBJID = em.OBJID
inner join [C8].[VE_OM_TM_GEN_TOP] t on t.OBJID = insp.OBJID
inner join [C8].[VE_OM_ALY_INSP_ACCESS] access on access.OBJID = insp.OBJID
inner join [C8].[VE_OM_ALY_PROPERTIES] prop on prop.OBJID = insp.OBJID
where c.OBJID is null
");

            return inspection;
        }

        /// <summary>
        ///     Get all alarm calculations from CredoSoft
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<AlarmCalculation>> GetAllAlarmCalculations()
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            var calculations = await connection.QueryAsync<AlarmCalculation>(@"
select 
    component.OBJID,
    calc.OBJDESC_,
    component.OBJTYPECODE_,
    component.OBJSERVICE,
    calc.TM_PV_SPEC_GRAVITY_,
    component.EQDESIGNCODE_,
    component.EQMANUFDATE,
    component.OBJCOMMISSION,
    props.ALY_OM_VERT_HORIZ,
    calc.TM_PV_CONFIG_GEOM_,
    calc.DIM_DIAMOUTSIDE_,
    calc.DIM_DIAMINSIDE,
    calc.DIM_DIAMUNITS,
    calc.DIM_RADIUSOUTSIDE,
    calc.DIM_RADIUSINSIDE,
    calc.DIM_RADIUS_CROWN_INSIDE,
    calc.DIM_RADIUS_CROWN_OUTSIDE,
    calc.DIM_RADIUS_UNIT,
    calc.DIM_CONE_APEX_ANGLE,
    calc.DIM_TALARM_K,
    calc.DIM_TALARM_L,
    calc.DIM_TALARM_M,
    calc.DIM_LENGTH_,
    calc.DIM_LENGTHUNIT_,
    dims.DIM_TANK_FILL_HEIGHT_M,
    component.MATSPEC_,
    component.MATGRADE_,
    component.RMAT_DAMAGE_GROUP_,
    -- TODO: stress doesn't exist yet?
    calc.DIM_JOINTQUAL,
    calc.DIM_TNOMINAL,
    calc.DIM_TNOMINAL_UNIT,
    calc.DIM_TCORRALLOW,
    calc.DIM_TCORRALLOW_UNIT,
    calc.PRESSUNITS_,
    calc.PRESSDESMAX_,
    calc.PRSSRELIEF_,
    calc.PRESSOPERNORM_,
    calc.TEMPUNITS_,
    calc.TEMPDESMAX_,
    calc.TEMPOPERNORM_,
    calc.DIM_TALARM1_CALCCODE,
    calc.DIM_TALARM1_VAL,
    calc.DIM_TALARM2_CALCCODE,
    calc.DIM_TALARM2_VAL,
    calc.DIM_TALARM3_CALCCODE,
    calc.DIM_TALARM3_VAL,
    calc.DIM_TALARM4_CALCCODE,
    calc.DIM_TALARM4_VAL,
    calc.DIM_TALARM5_CALCCODE,
    calc.DIM_TALARM5_VAL,
    calc.DIM_TALARM_NOTE01,
    calc.DIM_TALARM1_FACTORX,
    calc.DIM_TALARM2_FACTORX,
    calc.DIM_TALARM3_FACTORX,
    calc.DIM_TALARM5_FACTORX,
    calc.DIM_MAWT,
    calc.DIM_MAWT_UNIT,
    calc.DIM_MAWT_STRUCTURAL,
    calc.DIM_MAWT_STRUCTURAL_UNIT,
    calc.DIM_TTOLERANCEPERCENT,
    calc.DIM_DESIGNFACTOR,
    calc.DIM_TALARM_Y,
om.OBJSYS_SITE_RID as RSITE_RID
    
from [C8].[VE_OM_TM_GEN_SUB] component
inner join [C8].[VE_OM_ALY_PROPERTIES] props on props.OBJID = component.OBJID
inner join [C8].[OBJREL_PROPGEN_DIMENSION] dims on dims.OBJMASTERID = component.OBJID
inner join [C8].[OBJ_MASTER] om on om.OBJID = component.OBJID
cross apply (
    select top 1 *
    from [C8].[VE_OM_TM_ALARM]
    where [OBJID] = component.[OBJID]
) calc
");
            return calculations;
        }

        /// <summary>
        ///     Get alarm calculations associated with a specific component in CredoSoft
        /// </summary>
        /// <param name="componentObjId"></param>
        /// <returns></returns>
        public async Task<AlarmCalculation> GetAlarmCalculationForComponentAsync(long componentObjId)
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            var calculations = await connection.QuerySingleAsync<AlarmCalculation>($@"
select 
    component.OBJID,
    calc.OBJDESC_,
    component.OBJTYPECODE_,
    component.OBJSERVICE,
    calc.TM_PV_SPEC_GRAVITY_,
    component.EQDESIGNCODE_,
    component.EQMANUFDATE,
    component.OBJCOMMISSION,
    props.ALY_OM_VERT_HORIZ,
    calc.TM_PV_CONFIG_GEOM_,
    calc.DIM_DIAMOUTSIDE_,
    calc.DIM_DIAMINSIDE,
    calc.DIM_DIAMUNITS,
    calc.DIM_RADIUSOUTSIDE,
    calc.DIM_RADIUSINSIDE,
    calc.DIM_RADIUS_CROWN_INSIDE,
    calc.DIM_RADIUS_CROWN_OUTSIDE,
    calc.DIM_RADIUS_UNIT,
    calc.DIM_CONE_APEX_ANGLE,
    calc.DIM_TALARM_K,
    calc.DIM_TALARM_L,
    calc.DIM_TALARM_M,
    calc.DIM_LENGTH_,
    calc.DIM_LENGTHUNIT_,
    dims.DIM_TANK_FILL_HEIGHT_M,
    component.MATSPEC_,
    component.MATGRADE_,
    component.RMAT_DAMAGE_GROUP_,
    -- TODO: stress doesn't exist yet?
    calc.DIM_JOINTQUAL,
    calc.DIM_TNOMINAL,
    calc.DIM_TNOMINAL_UNIT,
    calc.DIM_TCORRALLOW,
    calc.DIM_TCORRALLOW_UNIT,
    calc.PRESSUNITS_,
    calc.PRESSDESMAX_,
    calc.PRSSRELIEF_,
    calc.PRESSOPERNORM_,
    calc.TEMPUNITS_,
    calc.TEMPDESMAX_,
    calc.TEMPOPERNORM_,
    calc.DIM_TALARM1_CALCCODE,
    calc.DIM_TALARM1_VAL,
    calc.DIM_TALARM2_CALCCODE,
    calc.DIM_TALARM2_VAL,
    calc.DIM_TALARM3_CALCCODE,
    calc.DIM_TALARM3_VAL,
    calc.DIM_TALARM4_CALCCODE,
    calc.DIM_TALARM4_VAL,
    calc.DIM_TALARM5_CALCCODE,
    calc.DIM_TALARM5_VAL,
    calc.DIM_TALARM_NOTE01,
    calc.DIM_TALARM1_FACTORX,
    calc.DIM_TALARM2_FACTORX,
    calc.DIM_TALARM3_FACTORX,
    calc.DIM_TALARM5_FACTORX,
    calc.DIM_MAWT,
    calc.DIM_MAWT_UNIT,
    calc.DIM_MAWT_STRUCTURAL,
    calc.DIM_MAWT_STRUCTURAL_UNIT,
    calc.DIM_TTOLERANCEPERCENT,
    calc.DIM_DESIGNFACTOR,
    calc.DIM_TALARM_Y,
om.OBJSYS_SITE_RID as RSITE_RID
    
from [C8].[VE_OM_TM_GEN_SUB] component
inner join [C8].[VE_OM_ALY_PROPERTIES] props on props.OBJID = component.OBJID
inner join [C8].[OBJREL_PROPGEN_DIMENSION] dims on dims.OBJMASTERID = component.OBJID
inner join [C8].[OBJ_MASTER] om on om.OBJID = component.OBJID
cross apply (
    select top 1 *
    from [C8].[VE_OM_TM_ALARM]
    where [OBJID] = component.[OBJID]
) calc
where calc.[OBJID] = {componentObjId}
");

            return calculations;
        }

        /// <summary>
        ///     Get all asset attachments
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<AssetAttachment>> GetAllAssetAttachmentsAsync()
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            var attachments = await connection.QueryAsync<AssetAttachment>(@"
select a.*, om.OBJSYS_SITE_RID as RSITE_RID
from [C8].[VL_OM_DOC] a
inner join [C8].[OBJ_MASTER] om on om.OBJID = a.OBJID");

            return attachments;
        }

        /// <summary>
        ///     Get attachments associated with a specific asset
        /// </summary>
        /// <param name="objId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<AssetAttachment>> GetAssetAttachmentsAsync(long objId)
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            var attachments = await connection.QueryAsync<AssetAttachment>(@$"
select a.*, om.OBJSYS_SITE_RID as RSITE_RID
from [C8].[VL_OM_DOC] a
inner join [C8].[OBJ_MASTER] om on om.OBJID = a.OBJID
where OBJID = {objId}");
            return attachments;
        }

        /// <summary>
        ///     Get all attachments associated with inspections
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<InspectionAttachment>> GetAllInspectionAttachmentsAsync()
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            var attachments = await connection.QueryAsync<InspectionAttachment>(@"
select a.*, om.OBJSYS_SITE_RID as RSITE_RID
from [C8].[VL_EM_DOC] a
inner join [C8].[OBJ_MASTER] om on om.OBJID = a.OBJID
");

            return attachments;
        }

        /// <summary>
        ///     Get attachments associated with a specific inspection
        /// </summary>
        /// <param name="emid"></param>
        /// <returns></returns>
        public async Task<IEnumerable<InspectionAttachment>> GetInspectionAttachmentsAsync(long emid)
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            var attachments =
                await connection.QueryAsync<InspectionAttachment>(@$"
select a.*, om.OBJSYS_SITE_RID as RSITE_RID
from [C8].[VL_EM_DOC] a
inner join [C8].[OBJ_MASTER] om on om.OBJID = a.OBJID
where EMID = {emid}");

            return attachments;
        }

        /// <summary>
        ///     Get all asset management sites
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<AssetManagementSite>> GetAllAssetManagementSitesAsync()
        {
            await using var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();

            var assetManagementSites = await connection.QueryAsync<AssetManagementSite>(@"select
       [RSITE_RID]
      ,[RSITE_GROUP]
      ,[RSITE_NAME]
  from [C8].[CDOREF_SITE]");

            return assetManagementSites;
        }
    }
}