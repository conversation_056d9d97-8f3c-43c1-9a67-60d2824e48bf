﻿using System;
using System.ComponentModel;
using System.Globalization;

namespace ClientPortal.Shared.Converters
{
    public class StringListConverter : TypeConverter
    {
        public override bool CanConvertFrom(ITypeDescriptorContext context, Type sourceType) =>
            sourceType == typeof(string) || base.CanConvertFrom(context, sourceType);

        public override object ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, object value)
        {
            switch (value)
            {
                case null:
                case string s when string.IsNullOrEmpty(s):
                    return null;
                case string s:
                    return new StringList(s.Split(','));
                default:
                    return base.ConvertFrom(context, culture, value);
            }
        }
    }
}