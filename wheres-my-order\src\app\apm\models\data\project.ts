import {
    IAttribute,
    PredefinedValueAttribute
} from '../../../shared/models/attributes';
import {
    ChangeLog,
    CommentChangeLog,
    PhotoChangeLog,
    ValueChangeLog
} from './asset';
import { Photo } from './photo';

export interface Project {
    id: string;
    locationId: string;
    assetIds: IAttribute<string[]>;
    name: IAttribute<string>;
    description: IAttribute<string>;
    status: PredefinedValueAttribute;
    plannedStart: IAttribute<Date>;
    plannedEnd: IAttribute<Date>;
    accountingDetails: ProjectAccountingDetails;
    clientDetails: any;
    activities: ProjectActivities;
}

export interface ProjectAccountingDetails {
    apmProjectNumber: IAttribute<string>;
    teamDistrictNumber: IAttribute<string>;
    teamProjectNumber: IAttribute<string>;
    projectNumber: IAttribute<string>;
}

// export interface ChangeLog {
//     entries: Entry[];
// }

export interface WorkOrderNumber {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName?: any;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface ProjectActivityDate {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: any;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName?: any;
    photos: Photo[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment: string;
}

export interface User {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue?: any;
    pendingValue?: any;
    currentPendingOrValue?: any;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName?: any;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface Duration {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit?: any;
    currentValue?: number;
    pendingValue?: any;
    currentPendingOrValue?: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName?: any;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface Count {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit?: any;
    currentValue?: number;
    pendingValue?: any;
    currentPendingOrValue?: number;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName?: any;
    photos: any[];
    photoChangeLog: PhotoChangeLog;
    commentChangeLog: CommentChangeLog;
    comment?: any;
}

export interface Activity {
    name: string;
    duration: Duration;
    count: Count;
}

export interface ProjectActivityCurrentEntry {
    workOrderNumber: WorkOrderNumber;
    date: ProjectActivityDate;
    user: User;
    activities: Activity[];
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface ProjectActivityPendingEntry {
    workOrderNumber: WorkOrderNumber;
    date: Date;
    user: User;
    activities: Activity[];
    databaseId: string;
    createdBy: string;
    createdTime: Date;
}

export interface ProjectActivities {
    name: string;
    changeLog: ChangeLog;
    currentEntries: ProjectActivityCurrentEntry[];
    pendingEntries: ProjectActivityPendingEntry[];
}
