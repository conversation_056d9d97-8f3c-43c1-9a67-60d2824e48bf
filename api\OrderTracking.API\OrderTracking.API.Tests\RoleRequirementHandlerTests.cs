﻿using System.Security.Claims;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Moq;
using NUnit.Framework;
using OrderTracking.API.Authorization.Handlers;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Tests
{
    [TestFixture]
    public class RoleRequirementHandlerTests
    {
        [Test]
        public async Task AuthorizationHandler_Should_Succeed()
        {
            //Arrange    
            var userProfiles = new Mock<IUserProfilesService>(MockBehavior.Loose);
            var currentProfile = new UserProfile();
            currentProfile.Roles.Add("WMO:Edit");
            userProfiles.Setup(service => service.GetAsync("<EMAIL>")).ReturnsAsync(currentProfile);
            var requirements = new[]
            {
                new ClientPortalRoleRequirement(new[]
                    {"App:Admin", "WMO:Admin", "WMO:Edit", "WMO:ManufacturingUser", "WMO:EngineeringUser"})
            };
            const string author = "<EMAIL>";
            var user = new ClaimsPrincipal(
                new ClaimsIdentity(
                    new[]
                    {
                        new Claim(ClaimsIdentity.DefaultNameClaimType, author)
                    },
                    "Basic")
            );

            var context = new AuthorizationHandlerContext(requirements, user, null);
            var subject = new RoleRequirementHandler(userProfiles.Object);

            //Act
            await subject.HandleAsync(context);

            //Assert
            Assert.That(context.HasSucceeded, Is.True);
        }

        [Test]
        public async Task AuthorizationHandler_Should_Not_Succeed()
        {
            //Arrange    
            var userProfiles = new Mock<IUserProfilesService>(MockBehavior.Loose);
            var currentProfile = new UserProfile();
            currentProfile.Roles.Add("WMO:View");
            userProfiles.Setup(service => service.GetAsync("<EMAIL>")).ReturnsAsync(currentProfile);
            var requirements = new[]
            {
                new ClientPortalRoleRequirement(new[]
                    {"App:Admin", "WMO:Admin", "WMO:Edit", "WMO:ManufacturingUser", "WMO:EngineeringUser"})
            };
            const string author = "<EMAIL>";
            var user = new ClaimsPrincipal(
                new ClaimsIdentity(
                    new[]
                    {
                        new Claim(ClaimsIdentity.DefaultNameClaimType, author)
                    },
                    "Basic")
            );

            var context = new AuthorizationHandlerContext(requirements, user, null);
            var subject = new RoleRequirementHandler(userProfiles.Object);

            //Act
            await subject.HandleAsync(context);

            //Assert
            Assert.That(context.HasSucceeded, Is.False);
        }

        [Test]
        public async Task HandleRequirementAsync_UserDoesNotExist_Fails()
        {
            var userProfiles = new Mock<IUserProfilesService>(MockBehavior.Loose);
            userProfiles.Setup(u => u.GetAsync("<EMAIL>")).ReturnsAsync((UserProfile) null);
            var requirements = new[]
            {
                new ClientPortalRoleRequirement(new[]
                    {"App:Admin", "WMO:Admin", "WMO:Edit", "WMO:ManufacturingUser", "WMO:EngineeringUser"})
            };
            const string author = "<EMAIL>";
            var user = new ClaimsPrincipal(
                new ClaimsIdentity(
                    new[]
                    {
                        new Claim(ClaimsIdentity.DefaultNameClaimType, author)
                    },
                    "Basic")
            );
            
            var context = new AuthorizationHandlerContext(requirements, user, null);
            var subject = new RoleRequirementHandler(userProfiles.Object);

            await subject.HandleAsync(context);

            Assert.That(context.HasFailed, Is.True);
        }
    }
}