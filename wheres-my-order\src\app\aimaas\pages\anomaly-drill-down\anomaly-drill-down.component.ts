import { Location } from '@angular/common';
import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { DxDataGridComponent } from 'devextreme-angular/ui/data-grid';
import DataSource from 'devextreme/data/data_source';
import { exportDataGrid } from 'devextreme/excel_exporter';
import dxDataGrid from 'devextreme/ui/data_grid';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver';
import { combineLatest } from 'rxjs';
import { UserProfile } from '../../../profile/models/user-profile';
import { Breadcrumb } from '../../../shared/components';
import { DataGridService, UsersService } from '../../../shared/services';
import {
    AnomaliesRecommendations,
    Asset,
    AssetAttachment,
    AssetComponent,
    AssetInspection,
    AssetManagementSite,
    AssetSubmissions,
    InspectionAttachment,
    RowExpandingHandler
} from '../../models';
import { GeneralAnalysis } from '../../models/general-analysis';
import { SiteLabelPipe } from '../../pipes';
import { CredoSoftService } from '../../services';

@Component({
    selector: 'app-anomaly-drill-down',
    templateUrl: './anomaly-drill-down.component.html',
    styleUrls: ['./anomaly-drill-down.component.scss']
})
export class AnomalyDrillDownComponent implements OnInit, AfterViewInit {
    // constructor() { }

    isLoading: boolean;
    selectedAssetId: any;
    storageKey: string = 'datagrid-state';
    popupVisible = false;
    components: any[];
    inspections: any[];
    anomalyDataSource: DataSource;
    assetComponentMap: AssetComponent[];
    generalAnalysis: any[];
    assetGeneralAnalysisMap: GeneralAnalysis[];
    assetInspectionMap: any;
    currentFilter: any = [];
    filterValue: Array<any>;
    assetAttachments: AssetAttachment[] = [];
    inspectionAttachments: InspectionAttachment[] = [];
    currentAssetDetails: Asset;
    inspectionSchedule: any[];
    submissionsSchedule: any[];
    visibleAssetAttachments: AssetAttachment[];
    componentAttachments: AssetAttachment[] = [];
    submissions: any[];
    visibleInspectionAttachments: InspectionAttachment[] = [];
    anomaliesData: AnomaliesRecommendations[] = [];
    submissionPopupVisible: boolean = false;
    initialAnomaly: AnomaliesRecommendations = null;
    submissionPopupTitle: string = 'Anomaly Update';
    availableSites: AssetManagementSite[];
    selectedSite: AssetManagementSite;
    userId: string;
    breaCrumbLabel: string = 'Recommendation List';
    isDataLoaded: boolean = false;
    currentAssetId: string;
    currentUser: UserProfile;
    assetDetailsPopupVisible: boolean = false;
    equipmentPopupTitle: string = ' ';
    inspectionPopupTitle: string = ' ';
    assets: Asset[] = [];
    visibleInspectionAnamolies: AnomaliesRecommendations[];
    generalInspectionDetails: AssetInspection[];
    selectedOperationId: any;
    addUserPreferences = (
        useri: string,
        storageKey: string,
        values: string
    ) => {
        if (this.credoService) {
            this.credoService
                .addPreference(useri, storageKey, values)
                .subscribe(
                    (response) => {
                        console.log('Preference added successfully:', response);
                    },
                    (error) => {
                        //    console.error('Error adding preference:', error);
                    }
                );
        } else {
            console.error('CredoService is undefined!');
        }
    };
    saveState = (state) => {
        this._users.currentProfile$.subscribe((user: any) => {
            this.userId = user.id;
            //    console.log(this.userId, 'current user email');
        });
        this.addUserPreferences(
            this.userId,
            'equipment',
            JSON.stringify(state)
        );
    };
    loadState = async () => {
        try {
            const response = await this.getUserPreference();
            if (response && response.equipment) {
                const equipmentValue = JSON.parse(response.equipment);
                equipmentValue.filterValue = this.currentFilter;
                return equipmentValue;
            } else {
                console.error('No anomaly preference found in response.');
                return null;
            }
        } catch (error) {
            console.error('Error loading preference:', error);
            return null;
        }
    };
    @ViewChild(DxDataGridComponent)
    grid: DxDataGridComponent;

    constructor(
        private readonly credoService: CredoSoftService,
        private readonly _sitePipe: SiteLabelPipe,
        private readonly _users: UsersService,
        private readonly _grid: DataGridService,
        private location: Location
    ) {}

    crumbs: Breadcrumb[];

    ngAfterViewInit(): void {
    }
    getUserPreference(): Promise<any> {
        this._users.currentProfile$.subscribe((user: any) => {
            this.userId = user.id;
            //    console.log(this.userId, 'current user email');
        });
        return new Promise((resolve, reject) => {
            this.credoService.getPreference(this.userId, 'anomaly').subscribe(
                (response) => {
                    if (response && response.equipment) {
                        resolve(response);
                    } else {
                        reject('No equipment preference found in response.');
                    }
                },
                (error) => {
                    reject('Error fetching preference: ' + error);
                }
            );
        });
    }

    addUserPreference(useri: string, storageKey: string, values: string): void {
        if (this.credoService) {
            this.credoService
                .addPreference(useri, storageKey, values)
                .subscribe(
                    (response) => {
                        // console.log('Preference added successfully:', response);
                    },
                    (error) => {
                        // console.error('Error adding preference:', error);
                    }
                );
        } else {
            console.error('CredoService is undefined!');
        }
    }
    ngOnInit(): void {
        //this.isLoading = true;
        combineLatest([
            this.credoService.getAllAnomaliesAsDataSource(),
            //  this.credoService.components$,
            this.credoService.inspections$,
            //   this.credoService.assetAttachments$,
            //this.credoService.inspectionAttachments$,
            //  this.credoService.assetSubmissions$,
            this.credoService.assetManagementSites$,
            this._users.currentProfile$,
            this.credoService.anomalies$,
            this.credoService.assets$

            // this.credoService.generalAnalysis$
        ])
            // finalize(() => this.grid?.instance?.endCustomLoading())
            .pipe()
            .subscribe(
                async ([
                    ds,
                    // components,
                    inspections,
                    // assetAttachments,
                    // inspectionAttachments,
                    // alarmCalcs,
                    // assetSubmissions,
                    sites,
                    currentUser,
                    anomaliesData,
                    assets
                ]: //generalanalysis
                //
                [
                    DataSource,
                    //    AssetComponent[],
                    AssetInspection[],
                    //AssetAttachment[],
                    // InspectionAttachment[],
                    // AlarmCalc[],
                    //  AssetSubmissions[],
                    AssetManagementSite[],
                    UserProfile,
                    AnomaliesRecommendations[],
                    Asset[]
                    //GeneralAnalysis[]
                ]) => {
                    this.anomalyDataSource = ds;
                    //   this.components = components;
                    this.inspections = inspections;
                    // this.assetAttachments = assetAttachments;
                    // this.inspectionAttachments = inspectionAttachments;
                    // this.alarmCalcs = alarmCalcs;
                    //   console.log(history.state.data);
                    this.currentUser = currentUser;
                    this.anomaliesData = anomaliesData;
                    if (history.state?.data?.currentFilter)
                        this.currentFilter = history.state.data.currentFilter;
                    // this.submissions = assetSubmissions;
                    this.availableSites = sites;
                    this.assets = assets;
                    //this.generalAnalysis = generalanalysis;
                    this.userId = currentUser.email;
                    const roles = currentUser.roles.map((role) =>
                        role.toLowerCase()
                    );
                    if (roles) {
                        if (roles.includes('aimaas:demo')) {
                            this.availableSites = this.availableSites.filter(
                                (site) =>
                                    site.locationid ==
                                    Number('635140707384299520')
                            );
                        } else if (
                            !roles.includes('app:admin') &&
                            !roles.includes('aimaas:admin') &&
                            currentUser.assetManagementSiteIds
                        ) {
                            this.availableSites = this.availableSites.filter(
                                (site) =>
                                    currentUser.assetManagementSiteIds.includes(
                                        site.locationid
                                    )
                            );
                        }
                    }
                    this.selectedSite =
                        this.availableSites.find(
                            (site) =>
                                site.locationid ==
                                Number(localStorage.getItem('selectedSite'))
                        ) ?? this.availableSites[0];

                    if (history?.state?.data?.assetObjIds) {
                        //  console.log('inside asset object idss');
                        if (history.state.data.assetObjIds.length <= 0) {
                            this.currentFilter = [
                                'assetid',
                                'noneof',
                                (
                                    await this.anomalyDataSource.store().load() as any[]
                                )?.map((item) => item.assetid)
                            ];
                        } else {
                            this.currentFilter = [
                                'assetid',
                                'anyof',
                                history.state.data.assetObjIds
                            ];
                        }
                    }
                    this.isLoading = false;
                    this.isDataLoaded = true;
                }
            );
        this.updateBreadcrumbs();
    }
    restoreAssetsDefaultsClicked = async (e) => {
        const result = await this._grid.resetGridState(this.grid);
        if (result) {
            //    console.log(this.userId, 'current user email');
            this.addUserPreferences(this.userId, 'anomaly', JSON.stringify(''));
        }
    };

    clientSubmissionTitleValueChange(e: any) {
        this.submissionPopupTitle = e;
    }
    clientDataFormSubmitted(e: any) {
        this.submissionPopupVisible = false;
    }
    clientSubmitDataOnclick(e: string) {
        if (e === 'frombuttonclick') {
            this.initialAnomaly = null;
        }
        this.submissionPopupVisible = !this.submissionPopupVisible;
    }
    anomalyCellTemplate = (cellElement, cellInfo) => {
        const anomalyNumber = cellInfo.value ? cellInfo.value : '';
        const link = document.createElement('a');
        link.href = '#';
        link.innerText = anomalyNumber;
        link.onclick = (event) => {
            event.preventDefault();
            this.onAnomalyClick(cellInfo.data);
        };
        cellElement.appendChild(link);
    };
    onAnomalyClick(data: AnomaliesRecommendations) {
        
        this.initialAnomaly = data;
        this.clientSubmitDataOnclick('fromanomaly');
    }
    updateBreadcrumbs() {
        if (history.state?.breadCrumbLabel) {
            this.breaCrumbLabel = history.state.breadCrumbLabel
                ? history.state.breadCrumbLabel
                : 'Equipment';
        }
        this.crumbs = [
            { label: 'KPI Dashboards', route: '/aimaas/dashboards' },
            { label: this.breaCrumbLabel, route: '/aimaas/drilldown' }
        ];
    }
    onContentReady(event) {
        this.grid?.instance?.endCustomLoading();
    }
    onCellPrepared(event) {
        if (event.rowType == 'data' || this.isDataLoaded) {
            this.isLoading = false;
        } else {
            this.isLoading = true;
        }
    }
    getComponentsForAsset(id: string): AssetComponent[] {
        if (!this.components) {
            return [];
        }
        return this.components.filter((item) => item.assetid === id);
    }
    getGeneralAnalysisForAsset(id: string): GeneralAnalysis[] {
        if (!this.generalAnalysis) {
            return [];
        }
        return this.generalAnalysis.filter((item) => item.assetid === id);
    }
    getInspectionsForAsset(id: string): AssetInspection[] {
        if (!this.inspections) {
            return [];
        }

        return this.inspections?.filter((item) => item.assetid === id);
    }
    getSubmissionForAsset(id: string): AssetSubmissions[] {
        if (!this.submissions) {
            return [];
        }
        return this.submissions?.filter((item) => item.assetId === id);
    }
    assetSelectionChanged(data) {
        const anomaly = data?.row?.data;

        this.location.replaceState(
            `/aimaas/drilldown?assetid=${anomaly?.assetid}`
        );
        const asset = this.assets?.find(
            (asset) => asset.id == anomaly?.assetid
        );
        this.assetComponentMap = [];
        var comp = this.assetComponentMap;
        this.currentAssetId = String(asset.id);
        this.credoService.getAllComponents(asset.id).subscribe((data) => {
            this.assetComponentMap = data;
            comp = this.assetComponentMap.sort((a, b) => {
                const clientCompare = a.componentname.localeCompare(
                    b.componentname
                );
                if (clientCompare !== 0) {
                    return clientCompare;
                }
            });
        });
        this.currentAssetDetails = asset;
        this.inspectionSchedule = this.getInspectionsForAsset(String(asset.id));
        this.equipmentPopupTitle = `EQUIPMENT - ${asset.assetid}`;
        this.assetDetailsPopupVisible = true;
    }

    closePopup() {
        this.popupVisible = false;
        this.assetDetailsPopupVisible = false;
        this.equipmentPopupTitle = ' ';
        this.inspectionPopupTitle = ' ';
        this.location.replaceState('/aimaas/inspection-drilldown');
    }
    inspectionSelectionChanged(data) {
        const anomaly = data?.row?.data;

        const inspection = this.inspections?.find(
            (ins) => ins.planoperationid == anomaly?.operationid
        );
        if (inspection?.planoperationid !== null) {
            this.location.replaceState(
                `/aimaas/inspection-drilldown?op=${inspection.planoperationid}`
            );

            this.popupVisible = true;
        }
        this.inspectionPopupTitle =
            inspection.operationtype !== null
                ? `INSPECTION - ${inspection?.assetidname} - ${
                      inspection?.operationtype
                  } - ${inspection?.date.replace(/-/g, '/')} `
                : `SCHEDULE - ${inspection?.assetidname} - ${
                      inspection?.scheduletype
                  } - ${
                      inspection?.nextinspectiondue
                          ? inspection.nextinspectiondue.replace(/-/g, '/')
                          : ''
                  }`;
        this.visibleInspectionAnamolies = this.anomaliesData.filter(
            (anomaly) => anomaly.operationid == inspection.planoperationid
        );
        this.generalInspectionDetails = inspection;
        this.selectedOperationId = inspection.planoperationid;
    }
    formatPID(data) {
        if (data.pid == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(data.pid, 'text/html');
        return doc.documentElement.textContent ?? ' ';
    }
    formatLocalJudictional(data) {
        if (data.localjuridictional == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(
            data.localjuridictional,
            'text/html'
        );
        return doc.documentElement.textContent ?? ' ';
    }
    onRowExpanding(e: {
        cancel: boolean;
        component: dxDataGrid;
        element: HTMLElement;
        key: any;
        model: any;
    }) {
        RowExpandingHandler.handle(e);
    }

    customDisplayExpr = (site: any): string => {
        if (site) return this._sitePipe.transform(site);
    };

    changeSite(selectedSite) {
        let _selectedSite = selectedSite.selectedItem;
        if (_selectedSite == null) {
            setTimeout(() => {
                this.selectedSite = this.availableSites[0];
                localStorage.setItem(
                    'selectedSite',
                    String(this.selectedSite.locationid)
                );
            }, 1);
        } else {
            this.selectedSite = selectedSite.selectedItem;
            localStorage.setItem(
                'selectedSite',
                String(this.selectedSite.locationid)
            );
        }
        this.anomalyDataSource.filter(
            (anomaly) => anomaly.locationid === this.selectedSite.locationid
        );
        this.anomalyDataSource.load();
    }
    convertHtmlToText(html: string): string {
        if (html == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return doc.documentElement.textContent ?? ' ';
    }
    formatAnomalyDescription(data) {
        if (data.anomalydescription == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(
            data.anomalydescription,
            'text/html'
        );
        return doc.documentElement.textContent ?? ' ';
    }
    formatAnomalyProposedRecom(data) {
        if (data.proposedrecommemendation == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(
            data.proposedrecommemendation,
            'text/html'
        );
        return doc.documentElement.textContent ?? ' ';
    }
    async onExporting(event) {
        const workbook = new Workbook();
        const worksheet = workbook.addWorksheet('Anomalies');
        await exportDataGrid({
            component: event.component,
            worksheet
        });
        const buffer: BlobPart = await workbook.xlsx.writeBuffer();
        saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'Anomalies.xlsx'
        );
    }
}
