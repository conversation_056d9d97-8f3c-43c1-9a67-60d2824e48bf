﻿using System;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class ProjectTransportObject
    {
        [JsonProperty(PropertyName = "id")]
        public string Id { get; set; }

        [JsonProperty(PropertyName = "clientProjectNumber")]
        public string ClientProjectNumber { get; set; }
        
        [JsonProperty(PropertyName = "contactEmail")]
        public string ContactEmail { get; set; }

        [JsonProperty(PropertyName = "contactName")]
        public string ContactName { get; set; }

        [JsonProperty(PropertyName = "contactPhoneNumber")]
        public string ContactPhoneNumber { get; set; }

        [JsonProperty(PropertyName = "contactTitle")]
        public string ContactTitle { get; set; }

        [JsonProperty(PropertyName = "description")]
        public string Description { get; set; }

        [JsonProperty(PropertyName = "locationId")]
        public string LocationId { get; set; }

        [JsonProperty(PropertyName = "projectCity")]
        public string ProjectCity { get; set; }

        [JsonProperty(PropertyName = "projectState")]
        public string ProjectState { get; set; }

        [JsonProperty(PropertyName = "projectName")]
        public string ProjectName { get; set; }

        [JsonProperty(PropertyName = "teamDistrictNumber")]
        public string TeamDistrictNumber { get; set; }

        [JsonProperty(PropertyName = "teamProjectNumber")]
        public string TeamProjectNumber { get; set; }

        [JsonProperty(PropertyName = "status")]
        public string Status { get; set; }

        [JsonProperty(PropertyName = "plannedEnd")]
        public DateTime? PlannedEnd { get; set; }

        [JsonProperty(PropertyName = "plannedStart")]
        public DateTime? PlannedStart { get; set; }
    }
}
