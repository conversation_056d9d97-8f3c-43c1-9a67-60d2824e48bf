import { Injectable } from '@angular/core';
import {
    ShareClient,
    ShareDirectoryClient,
    ShareServiceClient
} from '@azure/storage-file-share';
import { ToastrService } from 'ngx-toastr';
import { environment } from '../../../environments/environment';
import { LocalBlobService } from '../../shared/services';
import { CredoSoftService } from './credo-soft.service';

@Injectable({
    providedIn: 'root'
})
export class FileShareService {
    private _serviceClient: ShareServiceClient;
    private _shareClient: ShareClient;
    private _directoryClient: ShareDirectoryClient;
    private _initialized = false;

    constructor(
        private readonly _localBlobs: LocalBlobService,
        private readonly _toasts: ToastrService,
        private readonly _credo: CredoSoftService
    ) {}

    async initialize() {
        try {
            const sas = await this.getSAS();
            this.getServiceClient(sas);
            this.getShareClient();
            this.getInitialDirectoryClient();
            this._initialized = true;
        } catch (e) {
            this._initialized = false;
        }
    }

    getSAS(): Promise<string> {
        try {
            return this._credo.getSAS().toPromise();
        } catch (e) {
            console.error(
                'unable to retrieve authentication key for file share'
            );
            throw e;
        }
    }
    isInitialized(): boolean {
        return this._initialized;
    }
    async download(location: string): Promise<void> {
        if (this._initialized === false) {
            throw new Error(
                'This instance has not been initialized yet.  Please invoke `initialize` before anything else.'
            );
        }

        const fileName = this.getFileName(location);
        const pathPart = this.getPathWithoutFileName(location, fileName);

        try {
            const response = await this._directoryClient
                .getDirectoryClient(pathPart)
                .getFileClient(fileName)
                .download(0);

            const blob = await response.blobBody;
            this._localBlobs.saveBlobToDisk(fileName, blob);
        } catch (e) {
            if (e.details) {
                this._toasts.error(e.toString(), e.details.errorCode, {
                    timeOut: 10000,
                    progressBar: true
                });
            } else {
                this._toasts.error(e.message, e.code, {
                    timeOut: 10000,
                    progressBar: true
                });
            }
        }
    }

    private getServiceClient(sas: string) {
        this._serviceClient = new ShareServiceClient(
            `https://${environment.credo.fileShare.account}.file.core.windows.net${sas}`
        );
    }

    private getInitialDirectoryClient() {
        this._directoryClient =
            this._shareClient.getDirectoryClient('Credo/c8new/Files');
    }

    private getShareClient() {
        this._shareClient = this._serviceClient.getShareClient(
            environment.credo.fileShare.shareName
        );
    }

    private getFileName(location: string): any {
        return location.split(/^.*[\\\/]/).pop();
    }

    private getPathWithoutFileName(location: string, fileName: string) {
        return location.replace(fileName, '').slice(0, -1);
    }
}
