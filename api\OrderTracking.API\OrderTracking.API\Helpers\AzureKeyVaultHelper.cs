using Azure.Security.KeyVault.Secrets;
using Azure.Identity;
using System;
using System.Threading.Tasks;

namespace OrderTracking.API.Helpers
{
    /// <summary>
    /// Azure Key Vault Helper
    /// </summary>
    public class AzureKeyVaultHelper
    {
        /// <summary>
        /// Get secret from Azure Key Vault
        /// </summary>
        /// <param name="secretName"></param>
        /// <param name="keyVaultName"></param>
        /// <returns></returns>
        public static async Task<string> GetSecretAsync(string secretName, string keyVaultName)
        {
            try
            {
                var keyVaultUri = new Uri($"https://{keyVaultName}.vault.azure.net/");
                var credential = new DefaultAzureCredential();
                var client = new SecretClient(keyVaultUri, credential);

                KeyVaultSecret secret = await client.GetSecretAsync(secretName);
                return secret.Value;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving secret '{secretName}' from Key Vault '{keyVaultName}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get secret from Azure Key Vault (synchronous version)
        /// </summary>
        /// <param name="secretName"></param>
        /// <param name="keyVaultName"></param>
        /// <returns></returns>
        public static string GetSecret(string secretName, string keyVaultName)
        {
            try
            {
                var keyVaultUri = new Uri($"https://{keyVaultName}.vault.azure.net/");
                var credential = new DefaultAzureCredential();
                var client = new SecretClient(keyVaultUri, credential);

                KeyVaultSecret secret = client.GetSecret(secretName);
                return secret.Value;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving secret '{secretName}' from Key Vault '{keyVaultName}': {ex.Message}", ex);
            }
        }
    }
}
