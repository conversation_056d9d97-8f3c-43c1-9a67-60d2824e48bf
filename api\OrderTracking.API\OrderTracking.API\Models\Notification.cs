﻿#region Copyright Quest Integrity Group, LLC 2020
// www.QuestIntegrity.com
// +1-303-415-1475
// 
// All rights are reserved. Reproduction or transmission in whole or in part, in
// any form or by any means, electronic, mechanical or otherwise, is prohibited
// without the prior written consent of the copyright owner.
// 
// File Created: 2020-02-17 3:15 PM
// Updated:      2020-02-17 3:15 PM
// Created by:   <PERSON><PERSON><PERSON><PERSON>, Sean
#endregion

using System;
using Google.Cloud.Firestore;
using Newtonsoft.Json;

namespace OrderTracking.API.Models
{
    /// <summary>
    ///     Notification meant for a client application
    /// </summary>
    [FirestoreData]
    public class Notification
    {
        /// <summary>
        ///     Unique identifier
        /// </summary>
        
        [JsonProperty(PropertyName = "id")]
        [FirestoreDocumentId]
        public string Id { get; set; }

        /// <summary>
        ///     Intended recipient of notification (email from UserProfile)
        /// </summary>
        [JsonProperty(PropertyName = "recipient")]
        [FirestoreProperty("recipient")]
        public string Recipient { get; set; }

        /// <summary>
        ///     Email (from UserProfile) of the person sending the notification (if applicable)
        /// </summary>
        [JsonProperty(PropertyName = "notifier")]
        [FirestoreProperty("notifier")]
        public string Notifier { get; set; }

        /// <summary>
        ///     Title of the notification
        /// </summary>
        [JsonProperty(PropertyName = "title")]
        [FirestoreProperty("title")]
        public string Title { get; set; }

        /// <summary>
        ///     Summary of the notification
        /// </summary>
        [JsonProperty(PropertyName = "summary")]
        [FirestoreProperty("summary")]
        public string Summary { get; set; }

        /// <summary>
        ///     When the notification was created
        /// </summary>
        [JsonProperty(PropertyName = "dateCreated")]
        [FirestoreProperty("dateCreated")]
        public DateTime DateCreated { get; set; }

        /// <summary>
        ///     Whether the notification has been acknowledged by the recipient or not.
        /// </summary>
        [JsonProperty(PropertyName = "read")]
        [FirestoreProperty("read")]
        public bool Read { get; set; }
    }
}
