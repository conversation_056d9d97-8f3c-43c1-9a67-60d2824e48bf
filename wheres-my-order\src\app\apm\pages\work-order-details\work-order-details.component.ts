import { Component, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { DxDataGridComponent } from 'devextreme-angular';
import { EditorPreparingEvent, RowUpdatedEvent } from 'devextreme/ui/data_grid';
import { OptionChangedEvent } from 'devextreme/ui/select_box';
import { SelectionChangedEvent } from 'devextreme/ui/tab_panel';
import { ToastrService } from 'ngx-toastr';
import { ReplaySubject, combineLatest } from 'rxjs';
import { finalize, map, switchMap, tap } from 'rxjs/operators';
import {
    AssetAccessComponent,
    AssetPpeComponent,
    InspectionInformationComponent,
    InspectionResultsComponent
} from '../../components';
import {
    APMStatus,
    Asset,
    AssetAccess,
    AssetDetailsPhotoTransport,
    AssetPPEViewModel,
    InspectionInfo,
    ProjectVm,
    PublishUnpublishWorkOrderUpdate,
    Task,
    TaskUpdate,
    VisualInspectionPhotoTransport,
    WorkOrder,
    WorkOrderDetail,
    availableStatusesToChangeTo,
    cannotChangeStatusFromCanceledOrCompleted
} from '../../models';
import { ApmService } from '../../services/apm.service';

@Component({
    selector: 'app-work-order-details',
    templateUrl: './work-order-details.component.html',
    styleUrls: ['./work-order-details.component.scss']
})
export class WorkOrderDetailsComponent {
    private _workOrder: WorkOrder;
    private readonly _workOrderSubject = new ReplaySubject<WorkOrder>();

    selectedExternalTask: Task | undefined;
    selectedInternalTask: Task | undefined;

    workOrder$ = this._workOrderSubject.asObservable();
    project$ = this.workOrder$.pipe(
        switchMap((wo) => this._apm.getProject(wo.projectId))
    );
    internalTasks$ = this.workOrder$.pipe(
        map((w) =>
            w.tasks.filter((t) =>
                ['Internal Visual', 'Full'].includes(t.taskType)
            )
        )
    );

    externalTasks$ = this.workOrder$.pipe(
        map((w) =>
            w.tasks.filter((t) =>
                ['External Visual', 'Full'].includes(t.taskType)
            )
        )
    );
    internalInspectionResultsVisible$ = this.internalTasks$.pipe(
        map((tasks) => tasks?.length > 0)
    );
    externalInspectionResultsVisible$ = this.externalTasks$.pipe(
        map((tasks) => tasks?.length > 0)
    );
    users$ = this._apm.getUsers();
    workOrderDetail: WorkOrderDetail = {} as WorkOrderDetail;

    @ViewChildren(InspectionResultsComponent)
    inspectionResultsComponents: QueryList<InspectionResultsComponent>;

    statusOptions: string[];
    editorOptions: object;
    isLoadingWODetail: boolean;
    allowEditing$ = this._apm.allowEditing$;

    @ViewChild(InspectionInformationComponent)
    inspectionInformation: InspectionInformationComponent;
    @ViewChild(AssetPpeComponent) assetPPE: AssetPpeComponent;
    @ViewChild(AssetAccessComponent) assetAccess: AssetAccessComponent;
    @ViewChild(DxDataGridComponent) dataGrid: DxDataGridComponent;

    constructor(
        private readonly _activatedRoute: ActivatedRoute,
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService
    ) {
        this.isLoadingWODetail = true;
        this._activatedRoute.params
            ?.pipe(
                switchMap((params) => this._apm.getWorkOrder(params.id, this.getProjectId())),
                tap((wo) => this._workOrderSubject.next(wo))
            )
            .subscribe();

        combineLatest([this.workOrder$, this.project$]).subscribe(
            ([wo, project]) => {
                this.setupWorkOrderDetail(wo, project);
            }
        );

        this.editorOptions = {
            itemTemplate: 'statusTemplate'
        };
    }

    onTaskSelectorOptionChanged(e: OptionChangedEvent) {
        if (e.name === 'items' && e.value?.length === 1) {
            e.component.option('value', e.value[0]);
        }
    }

    onRowUpdated(e: RowUpdatedEvent) {
        var update = {
            projectId: this._workOrder.projectId,
            workOrderId: this._workOrder.id,
            status: e.data?.status,
            supervisor: e.data?.supervisor,
            taskAssignees: e.data.taskAssignees,
            databaseId: e.data.id
        } as TaskUpdate;
        this.dataGrid.instance.beginCustomLoading('Loading...');
        this._apm
            .updateWorkOrderHeader(update)
            .pipe(
                switchMap((_) => this._apm.getWorkOrder(update.workOrderId, update.projectId)),
                tap((wo) => this._workOrderSubject.next(wo)),
                tap(() =>
                    this._toasts.success(
                        'Work Order successfully updated',
                        'Update successful'
                    )
                ),
                finalize(() => this.dataGrid.instance.endCustomLoading())
            )
            .subscribe();
    }

    onSaveInspectionInfo(inspectionInfo: InspectionInfo) {
        this.inspectionInformation.isSaving = true;
        this._apm
            .updateWorkOrder(inspectionInfo)
            .pipe(
                tap(() => {
                    this.inspectionInformation.isEditing = false;
                    this.inspectionInformation.isSaving = false;
                }),
                tap(() =>
                    this._toasts.success(
                        'Work Order successfully updated',
                        'Update successful'
                    )
                )
            )
            .subscribe();
    }

    onAssetPPESave(e: Partial<AssetPPEViewModel>) {
        this.assetPPE.isSaving = true;
        this._apm
            .updateAssetPPE(e)
            .pipe(
                switchMap((a) => this._apm.getWorkOrder(
                    this._activatedRoute.snapshot.params.id, this.getProjectId()
                )),
                tap((wo) => this._workOrderSubject.next(wo)),
                tap(() => {
                    this.assetPPE.isEditing = false;
                    this.assetPPE.isSaving = false;
                }),
                tap((a) =>
                    this._toasts.success(
                        'Asset PPE successfully updated',
                        'Update successful!'
                    )
                )
            )
            .subscribe();
    }

    assetAccessUpdated(update: AssetAccess) {
        this.assetAccess.isSaving = true;
        const assetUpdate$ = this._apm.updateAssetAccess({
            accessForAerialLiftForAllLocationsAtHeight:
                update.accessForAerialLiftForAllLocationsAtHeight
                    ? 'Yes'
                    : 'No',
            additionalPortsNeeded: update.additionalPortsNeeded ? 'Yes' : 'No',
            additionalPortsNeededComment: update.additionalPortsNeededComment,
            aerialLiftNeeded: update.aerialLiftNeeded ? 'Yes' : 'No',
            aerialLiftNeededComment: update.aerialLiftNeededComment,
            allComponentsUnder4FeetInHeight:
                update.allComponentsUnder4FeetInHeight ? 'Yes' : 'No',
            allComponentsUnder4FeetInHeightComment:
                update.allComponentsUnder4FeetInHeightComment,
            assetOutOfService: update.assetOutOfService ? 'Yes' : 'No',
            batteryPoweredPermitted: update.batteryPoweredPermitted
                ? 'Yes'
                : 'No',
            cleaningRecommendations: update.cleaningRecommendations,
            cleaningRecommendationsComment:
                update.cleaningRecommendationsComment,
            cleaningServiceReview: update.cleaningServiceReview,
            cleaningServiceReviewComment: update.cleaningServiceReviewComment,
            clientProvidedOperator: update.clientProvidedOperator
                ? 'Yes'
                : 'No',
            clientRequiredProofOfTraining: update.clientRequiredProofOfTraining
                ? 'Yes'
                : 'No',
            coatingCondition: update.coatingCondition,
            coatingConditionsObserved: update.coatingConditionsObserved,
            coatingConditionsObservedComment:
                update.coatingConditionsObservedComment,
            coatingLinerConditions: update.coatingLinerConditions,
            coatingLinerConditionsObserved:
                update.coatingLinerConditionsObserved,
            coatingLinerConditionsObservedComment:
                update.coatingLinerConditionsObservedComment,
            coatingLinerType: update.coatingLinerType,
            coatingLinerTypeComment: update.coatingLinerTypeComment,
            coatingRemovalRequired: update.coatingRemovalRequired
                ? 'Yes'
                : 'No',
            coatingRemovalRequiredComment: update.coatingRemovalRequiredComment,
            coatingType: update.coatingType,
            coatingTypeComment: update.coatingTypeComment,
            corrosionIdentified: update.corrosionIdentified,
            corrosionIdentifiedComment: update.corrosionIdentifiedComment,
            corrosionRemovalRecommendation:
                update.corrosionRemovalRecommendation,
            corrosionRemovalRecommendationComment:
                update.corrosionRemovalRecommendationComment,
            estimatedDistanceToAnyLiveElectricalOverheadLines:
                update.estimatedDistanceToAnyLiveElectricalOverheadLines,
            existingInspectionPorts: update.existingInspectionPorts
                ? 'Yes'
                : 'No',
            fixedEquipmentLaddersStairwaysPlatformsInstalled:
                update.fixedEquipmentLaddersStairwaysPlatformsInstalled
                    ? 'Yes'
                    : 'No',
            fixedEquipmentLaddersStairwaysPlatformsInstalledComment:
                update.fixedEquipmentLaddersStairwaysPlatformsInstalledComment,
            gasPoweredPermitted: update.gasPoweredPermitted ? 'Yes' : 'No',
            hasInsulation: update.hasInsulation,
            heatTracing: update.heatTracing,
            heatTracingComment: update.heatTracingComment,
            id: update.id,
            inspectionOpeningTypes: update.inspectionOpeningTypes,
            inspectionOpeningTypesComment: update.inspectionOpeningTypesComment,
            inspectionOpeningsPresent: update.inspectionOpeningsPresent
                ? 'Yes'
                : 'No',
            insulationPlugsMissing: update.insulationPlugsMissing
                ? 'Yes'
                : 'No',
            insulationPlugsMissingComment: update.insulationPlugsMissingComment,
            insulationRemovalRequired: update.insulationRemovalRequired
                ? 'Yes'
                : 'No',
            insulationRemovalRequiredComment:
                update.insulationRemovalRequiredComment,
            insulationType: update.insulationType,
            insulationTypeComment: update.insulationTypeComment,
            jacketingType: update.jacketingType,
            ladderRequirements: update.ladderRequirements,
            ladderRequirementsComment: update.ladderRequirementsComment,
            possibleAsbestos: update.possibleAsbestos ? 'Yes' : 'No',
            possibleAsbestosComment: update.possibleAsbestosComment,
            ropeAccessRequired: update.ropeAccessRequired ? 'Yes' : 'No',
            ropeAccessRequiredComment: update.ropeAccessRequiredComment,
            scaffoldingRequired: update.scaffoldingRequired ? 'Yes' : 'No',
            scaffoldingRequiredComment: update.scaffoldingRequiredComment,
            sizeOfAllAccessOpenings: update.sizeOfAllAccessOpenings,
            ventilationRequirements: update.ventilationRequirements,
            ventilationRequirementsComment:
                update.ventilationRequirementsComment
        });

        assetUpdate$
            .pipe(
                switchMap((a) =>
                    this._apm.getWorkOrder(
                        this._activatedRoute.snapshot.params.id, this.getProjectId()
                    )
                ),
                tap((wo) => this._workOrderSubject.next(wo)),
                tap(() => {
                    this.assetAccess.isSaving = false;
                    this.assetAccess.isEditing = false;
                }),
                tap(() =>
                    this._toasts.success(
                        'Asset Access successfully updated',
                        'Update successful!'
                    )
                )
            )
            .subscribe();
    }

    onAssetDetailsPhotoDelete(e: AssetDetailsPhotoTransport) {
        this._apm
            .deleteAssetDetailsPhoto(e)
            .pipe(
                tap(() =>
                    this._toasts.success(
                        'Photo successfully deleted',
                        'Photo deleted'
                    )
                ),
                switchMap(() =>
                    this._apm.getWorkOrder(
                        this._activatedRoute.snapshot.params.id, this.getProjectId()
                    )
                ),
                tap((wo) => this._workOrderSubject.next(wo))
            )
            .subscribe();
    }

    onPhotoDelete(e: VisualInspectionPhotoTransport) {
        this._apm
            .deleteVisualInspectionPhoto(e)
            .pipe(
                tap(() =>
                    this._toasts.success(
                        'Photo successfully deleted',
                        'Photo deleted'
                    )
                ),
                switchMap((_) =>
                    this._apm.getWorkOrder(
                        this._activatedRoute.snapshot.params.id, this.getProjectId()
                    )
                ),
                tap((wo) => this._workOrderSubject.next(wo))
            )
            .subscribe();
    }

    onAssetDetailsPhotoDescriptionUpdate(e: AssetDetailsPhotoTransport) {
        this._apm
            .updateAssetDetailsPhotoDescription(e)
            .pipe(
                tap(() => {
                    this._toasts.success(
                        'Photo description was updated successfully',
                        'Description Saved'
                    );
                })
            )
            .subscribe();
    }

    onPhotoDescriptionUpdate(e: VisualInspectionPhotoTransport) {
        this._apm
            .updateVisualInspectionPhotoDescription(e)
            .pipe(
                tap(() => {
                    this._toasts.success(
                        'Photo description was updated successfully',
                        'Description Saved'
                    );
                })
            )
            .subscribe();
    }

    onPublishedUnpublished(update: PublishUnpublishWorkOrderUpdate) {
        this._apm
            .publishUnpublishWorkOrder(update)
            .pipe(
                tap(() => {
                    const toastText =
                        update.isPublishing === true
                            ? 'published'
                            : 'unpublished';
                    const toastTextUppercase =
                        update.isPublishing === true
                            ? 'Published'
                            : 'Unpublished';
                    this._toasts.success(
                        'Work Order was successfully ' + toastText,
                        'Successfully ' + toastTextUppercase
                    );
                }),
                tap((wo) => this._workOrderSubject.next(wo))
            )
            .subscribe();
    }

    onTabSelectionChanged(e: SelectionChangedEvent) {
        // We want these two tabs to collapse all but the first section of their
        // accordions because if someone decides to expand all of them for both tabs,
        // and then switch between them, the issue still remains.  So we are going
        // to avoid the bug, for now, by collapsing all but the first section to
        // avoid the rendering bug mentioned here:
        // https://supportcenter.devexpress.com/ticket/details/t1037592/dxtabpanel-rendering-the-template-from-two-items-at-once-on-top-of-each-other
        if (
            e.addedItems.some(
                (i) =>
                    i.template === 'internal-inspection-results' ||
                    i.template === 'external-inspection-results' ||
                    i.title === 'Internal Inspection Results' ||
                    i.title === 'External Inspection Results'
            )
        ) {
            this.inspectionResultsComponents?.forEach((c) =>
                c.restoreDefaultCollapseState()
            );
        }
    }

    onHeaderTaskGridEditorPreparing(e: EditorPreparingEvent) {
        if (e.parentType === 'dataRow' && e.dataField === 'status') {
            e.editorName = 'dxSelectBox';
            e.editorOptions.readOnly =
                cannotChangeStatusFromCanceledOrCompleted(
                    e.row.data,
                    this.workOrderDetail
                );

            if (this._workOrder)
                e.editorOptions.items = availableStatusesToChangeTo(
                    this._workOrder,
                    e.row.data.taskId,
                    e.row.data.taskType,
                    e.row.data.status
                );
        }
    }

    private setupWorkOrderDetail(wo: WorkOrder, project: ProjectVm) {
        this._workOrder = wo;

        const walkdown = this._workOrder?.asset?.walkDown;

        this.workOrderDetail = {
            id: this._workOrder.id,
            projectId: this._workOrder.projectId,
            assetID: Asset.findAssetNumber(
                wo?.asset?.walkDown,
                this._workOrder?.asset?.assetCategory
            ),
            assetDescription:
                walkdown?.sectionIdentification?.attributeEquipment_Description
                    ?.currentValue,
            client: 'Chevron',
            location: wo.facilityName?.currentValue,
            inspectionTypes: [],
            assetCategory: this._workOrder.asset.assetCategory,
            status: this._workOrder.status?.currentValue,
            publishTime: this._workOrder.publishedTime,
            projectName: project.name,
            afeNumber: this.generateAFENumberString(wo.tasks),
            fieldWorkCompleted: this._workOrder.fieldWorkCompleted.currentValue
        };
        wo.tasks?.forEach((task) =>
            this.workOrderDetail.inspectionTypes.push({
                taskType: task.taskType,
                id: task.id,
                taskId: task.taskAPMNumber.currentValue,
                taskAssignees: task.assignedUsers,
                supervisor: task.taskDetails.supervisor?.currentValue,
                status: task.status?.currentValue as APMStatus
            })
        );

        this.isLoadingWODetail = false;
    }

    private generateAFENumberString(tasks: Task[]) {
        let output = '';

        tasks.forEach((task) => {
            if (task.purchaseOrderAFE.currentValue !== null) {
                output += task.purchaseOrderAFE.currentValue + '; ';
            }
        });

        output = output.trim();
        output = output.substring(0, output.length - 1);

        return output;
    }

    private getProjectId(): string | undefined {
        return this._activatedRoute.snapshot.queryParams?.projectId ?? this._workOrder?.projectId
    }
}
