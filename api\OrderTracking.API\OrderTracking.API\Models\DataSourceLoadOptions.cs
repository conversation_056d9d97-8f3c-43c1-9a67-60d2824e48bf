﻿using System;
using System.Linq;
using System.Threading.Tasks;
using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.Helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace OrderTracking.API.Models
{
    /// <summary>
    ///     Descends from class with properties for data processing, specifying a specific ModelBinder.
    ///     This is used for query parameters sent by DevExtreme controls that have built in filtering,
    ///     searching, paging, etc.
    /// </summary>
    [ModelBinder(BinderType = typeof(DataSourceLoadOptionsBinder))]
    public class DataSourceLoadOptions : DataSourceLoadOptionsBase
    {
    }

    /// <summary>
    ///     Class to help bind query parameters
    /// </summary>
    public class DataSourceLoadOptionsBinder : IModelBinder
    {
        /// <summary>
        ///     Method to bind parameters to model
        /// </summary>
        /// <param name="bindingContext"></param>
        /// <returns></returns>
        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            if (bindingContext == null) throw new ArgumentNullException(nameof(bindingContext));

            var loadOptions = new DataSourceLoadOptions();
            DataSourceLoadOptionsParser.Parse(loadOptions,
                key => bindingContext.ValueProvider.GetValue(key).FirstOrDefault());
            bindingContext.Result = ModelBindingResult.Success(loadOptions);
            return Task.CompletedTask;
        }
    }
}