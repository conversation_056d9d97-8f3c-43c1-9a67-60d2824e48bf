using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Azure.Storage.Blobs;
using Azure.Security.KeyVault.Secrets;
using Azure.Identity;
using OrderTracking.API.Services;
using OrderTracking.API.Helpers;
using OrderTracking.API.Models;
using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Moq;

namespace OrderTracking.API.Tests
{
    [TestClass]
    public class AzureIntegrationTests
    {
        private IConfiguration _configuration;
        private ILogger<CloudStorageService> _logger;
        private BlobStorage _blobStorageOptions;

        [TestInitialize]
        public void Setup()
        {
            // Setup configuration
            var configBuilder = new ConfigurationBuilder()
                .AddJsonFile("appsettings.test.json", optional: false)
                .AddEnvironmentVariables();
            _configuration = configBuilder.Build();

            // Setup logger
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            _logger = loggerFactory.CreateLogger<CloudStorageService>();

            // Setup blob storage options
            _blobStorageOptions = new BlobStorage
            {
                APMStorageAccountName = _configuration["BlobStorage:APMStorageAccountName"],
                APMBlobContainerName = _configuration["BlobStorage:APMBlobContainerName"],
                APMWOStorageAccountName = _configuration["BlobStorage:APMWOStorageAccountName"],
                APMWOBlobContainerName = _configuration["BlobStorage:APMWOBlobContainerName"],
                KeyVaultName = _configuration["BlobStorage:KeyVaultName"]
            };
        }

        [TestMethod]
        public async Task TestAzureKeyVaultConnection()
        {
            // Arrange
            var keyVaultName = _configuration["KeyVault:VaultName"];
            var testSecretName = "test-secret";

            // Act & Assert
            try
            {
                var secret = await AzureKeyVaultHelper.GetSecretAsync(testSecretName, keyVaultName);
                Assert.IsNotNull(secret, "Secret should be retrieved from Key Vault");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Key Vault connection failed: {ex.Message}");
            }
        }

        [TestMethod]
        public async Task TestAzureBlobStorageConnection()
        {
            // Arrange
            var storageAccountName = _blobStorageOptions.APMStorageAccountName;
            var containerName = _blobStorageOptions.APMBlobContainerName;

            // Act & Assert
            try
            {
                var credential = new DefaultAzureCredential();
                var blobServiceClient = new BlobServiceClient(
                    new Uri($"https://{storageAccountName}.blob.core.windows.net"), 
                    credential);
                
                var containerClient = blobServiceClient.GetBlobContainerClient(containerName);
                var exists = await containerClient.ExistsAsync();
                
                Assert.IsTrue(exists.Value, "Blob container should exist and be accessible");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Blob Storage connection failed: {ex.Message}");
            }
        }

        [TestMethod]
        public async Task TestBlobUploadAndDownload()
        {
            // Arrange
            var mockFile = CreateMockFile("test-file.txt", "Hello, Azure!");
            var folderName = "test-folder";
            var fileName = "test-file.txt";

            // Create service instance (this would normally be done through DI)
            var options = Microsoft.Extensions.Options.Options.Create(_blobStorageOptions);
            var service = new TestCloudStorageService(options, _logger);

            try
            {
                // Act - Upload
                var uploadResult = await service.UploadAttachmentObjectAsync(folderName, mockFile);
                Assert.IsNotNull(uploadResult, "Upload should return a result");

                // Act - Download
                var downloadResult = await service.DownloadObjectAsync(folderName, fileName);
                Assert.IsNotNull(downloadResult, "Download should return a result");
                Assert.IsNotNull(downloadResult.Stream, "Downloaded stream should not be null");

                // Verify content
                using var reader = new StreamReader(downloadResult.Stream);
                var content = await reader.ReadToEndAsync();
                Assert.AreEqual("Hello, Azure!", content, "Downloaded content should match uploaded content");

                // Cleanup - Delete
                await service.DeleteObjectAsync(folderName, fileName);
            }
            catch (Exception ex)
            {
                Assert.Fail($"Blob upload/download test failed: {ex.Message}");
            }
        }

        [TestMethod]
        public async Task TestSignedUrlGeneration()
        {
            // Arrange
            var options = Microsoft.Extensions.Options.Options.Create(_blobStorageOptions);
            var service = new TestCloudStorageService(options, _logger);

            try
            {
                // Act - Generate container SAS URL
                var containerSasUrl = await service.GetSignedUrl();
                Assert.IsNotNull(containerSasUrl, "Container SAS URL should be generated");
                Assert.IsTrue(containerSasUrl.Contains("sig="), "SAS URL should contain signature");

                // Act - Generate blob SAS URL
                var blobSasUrl = await service.GetSignedUrl("test-blob.txt");
                Assert.IsNotNull(blobSasUrl, "Blob SAS URL should be generated");
                Assert.IsTrue(blobSasUrl.Contains("sig="), "SAS URL should contain signature");
            }
            catch (Exception ex)
            {
                Assert.Fail($"SAS URL generation test failed: {ex.Message}");
            }
        }

        [TestMethod]
        public async Task TestListBlobs()
        {
            // Arrange
            var options = Microsoft.Extensions.Options.Options.Create(_blobStorageOptions);
            var service = new TestCloudStorageService(options, _logger);
            var folderName = "test-list-folder";

            try
            {
                // Setup - Upload a test file
                var mockFile = CreateMockFile("list-test.txt", "Test content");
                await service.UploadAttachmentObjectAsync(folderName, mockFile);

                // Act
                var blobs = await service.ListObjectAsync(folderName);
                Assert.IsNotNull(blobs, "Blob list should not be null");

                // Cleanup
                await service.DeleteObjectAsync(folderName, "list-test.txt");
            }
            catch (Exception ex)
            {
                Assert.Fail($"List blobs test failed: {ex.Message}");
            }
        }

        [TestMethod]
        public void TestManagedIdentityConfiguration()
        {
            // Arrange & Act
            try
            {
                var credential = new DefaultAzureCredential();
                Assert.IsNotNull(credential, "Default Azure Credential should be created");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Managed Identity configuration test failed: {ex.Message}");
            }
        }

        [TestMethod]
        public async Task TestApplicationInsightsConnection()
        {
            // Arrange
            var connectionString = _configuration["ApplicationInsights:ConnectionString"];
            
            // Act & Assert
            try
            {
                Assert.IsNotNull(connectionString, "Application Insights connection string should be configured");
                Assert.IsTrue(connectionString.Contains("InstrumentationKey="), 
                    "Connection string should contain instrumentation key");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Application Insights connection test failed: {ex.Message}");
            }
        }

        private IFormFile CreateMockFile(string fileName, string content)
        {
            var mock = new Mock<IFormFile>();
            var bytes = System.Text.Encoding.UTF8.GetBytes(content);
            var stream = new MemoryStream(bytes);

            mock.Setup(f => f.FileName).Returns(fileName);
            mock.Setup(f => f.Length).Returns(bytes.Length);
            mock.Setup(f => f.OpenReadStream()).Returns(stream);
            mock.Setup(f => f.ContentType).Returns("text/plain");

            return mock.Object;
        }
    }

    // Test implementation of CloudStorageService for testing
    public class TestCloudStorageService : CloudStorageService
    {
        public TestCloudStorageService(
            Microsoft.Extensions.Options.IOptions<BlobStorage> options,
            ILogger<CloudStorageService> logger)
            : base(options, logger)
        {
        }
    }
}
