apiVersion: deploy.cloud.google.com/v1
kind: Target
metadata:
  name: cpa-remote-monitoring-stg
  labels: {}
requireApproval: true
run:
  location: projects/oneinsight-stage-051d/locations/us-central1

---
apiVersion: deploy.cloud.google.com/v1
kind: Target
metadata:
  name: cpa-remote-monitoring-prod
  labels: {}
requireApproval: true
run:
  location: projects/oneinsight-prod-7998/locations/us-central1
