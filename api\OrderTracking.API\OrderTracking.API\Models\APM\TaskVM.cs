using System;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    /// <summary>
    ///     View Model for interacting with the Data Grid from DevExtreme
    /// </summary>
    public class TaskVM
    {
        [JsonProperty(PropertyName = "id")]
        public string ID { get; set; }

        [JsonProperty(PropertyName = "equipmentId")]
        public string EquipmentId { get; set; }

        [JsonProperty(PropertyName = "area")]
        public string Area { get; set; }

        [JsonProperty(PropertyName = "unit")]
        public string Unit { get; set; }

        [JsonProperty(PropertyName = "equipmentType")]
        public string EquipmentType { get; set; }

        [JsonProperty(PropertyName = "equipmentDescription")]
        public string EquipmentDescription { get; set; }

        [JsonProperty(PropertyName = "dueDate")]
        public DateTime? DueDate { get; set; }

        [JsonProperty(PropertyName = "taskType")]
        public string TaskType { get; set; }

        [JsonProperty(PropertyName = "assignedUsers")]
        public string[] AssignedUsers { get; set; }

        [JsonProperty(PropertyName = "taskUpdatedDate")]
        public DateTime? TaskUpdatedDate { get; set; }

        [JsonProperty(PropertyName = "afe")]
        public string AFE { get; set; }

        [JsonProperty(PropertyName = "supervisor")]
        public string Supervisor { get; set; }

        [JsonProperty(PropertyName = "plannedStart")]
        public DateTime? PlannedStart { get; set; }

        [JsonProperty(PropertyName = "plannedEnd")]
        public DateTime? PlannedEnd { get; set; }

        [JsonProperty(PropertyName = "status")]
        public string Status { get; set; }

        [JsonProperty(PropertyName = "apmProjectNumber")]
        public string APMProjectNumber { get; set; }

        [JsonProperty(PropertyName = "apmWorkOrderNumber")]
        public string APMWorkOrderNumber { get; set; }

        [JsonProperty(PropertyName = "projectName")]
        public string ProjectName { get; set; }

        [JsonProperty(PropertyName = "teamProjectNumber")]
        public string TEAMProjectNumber { get; set; }

        [JsonProperty(PropertyName = "apmTaskNumber")]
        public string APMTaskNumber { get; set; }

        [JsonProperty(PropertyName = "client")]
        public string Client { get; set; }

        [JsonProperty(PropertyName = "workOrderId")]
        public string WorkOrderId { get; set; }

        [JsonProperty(PropertyName = "clientWorkOrderNumber")]
        public string ClientWorkOrderNumber { get; set; }

        [JsonProperty(PropertyName = "projectId")]
        public string ProjectId { get; set; }
        [JsonProperty(PropertyName = "businessUnitId")]
        public string BusinessUnitId { get; set; }

        [JsonProperty(PropertyName = "assetDatabaseId")]
        public string AssetDatabaseId { get; set; }
    }
}