import { Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { DxPieChartComponent } from 'devextreme-angular/ui/pie-chart';
import { DrawnEvent } from 'devextreme/viz/pie_chart';
import { createSafeResizeObserver } from '../../../shared/helpers';

import { AssetBreakdown } from '../../models';

@Component({
    selector: 'app-assets-doughnut-chart',
    templateUrl: './assets-doughnut-chart.component.html',
    styleUrls: ['./assets-doughnut-chart.component.scss']
})
export class AssetsDoughnutChartComponent implements OnInit, OnDestroy {
    totalAssets = 0;
    private _chartData: AssetBreakdown[];
    @Input() set chartData(values: AssetBreakdown[]) {
        this.totalAssets = 0;
        if (!values) {
            return;
        }
        this._chartData = values;
        this._chartData.forEach((data) => (this.totalAssets += data.count));
    }
    get chartData() {
        return this._chartData;
    }
    @Input() loading: boolean;
    @ViewChild(DxPieChartComponent) chart: DxPieChartComponent;

    private _observer: ResizeObserver;

    constructor() {}

    ngOnInit(): void {
        this._observer = createSafeResizeObserver(() => {
            this.renderChart();
        });
        const assetsDoughnut = document.getElementById('assetsDoughnut');
        if (assetsDoughnut) this._observer.observe(assetsDoughnut);
    }

    ngOnDestroy(): void {
        this._observer.disconnect();
        this._observer = null;
    }

    renderChart() {
        this.chart?.instance?.render({ force: true });
    }

    customizeLabel(arg) {
        return `${arg.valueText} (${arg.percentText})`;
    }

    onDrawn(e: DrawnEvent) {
        setTimeout(() => e.component.render({ animate: true }));
    }
}
