export class AssetComponent {

        clientid: string;
        clientname: string;
        locationname: string;
        locationid: string;
        assetid: string;
        componentid: string;
        componentname: string;
        description: string;
        material:string;
        outsidediameter: string;
        headtype: string;
        notes: string;
        jointefficiency: string;
        allowablestress: string;
        corrosionallowance: string;
        reassesedlimitthickness: string;
        retirementdate: string;
        remaininglife: string;
        drivingcml: string;
        ltcorrosionrate: string;
        stcorrosionrate: string;
        nominalthickness: string;
        lastdate: string;
        lastvalue: string;
        previousdate: string;
        previousvalue: string;
        firstdate: string;
        firstvalue: string;
        limitThickness:string;
    // diM_DIAMOUTSIDE: number;
    // diM_DIAMUNITS: string;
    // diM_LENGTH: number;
    // diM_LENGTHUNIT: any;
    // eQ_IS_PWHT: number;
    // eqdesigncode: string;
    // eqinspcode: string;
    // eqliningext: any;
    // eqliningint: any;
    // eqmanufacturer: any;
    // eqmanufdate: any;
    // eqserialnum: string;
    // matgrade: any;
    // matspec: any;
    // objcomment: any;
    // objcommission: any;
    // objcorrcirc: any;
    // objdesc: string;
    // objgroupid: string;
    // objid: number;
    // objnamE_PARENT: string;
    // objname: string;
    // objparent: number;
    // objriskcode: any;
    // objservice: any;
    // objstatus: string;
    // objsyS_SORT_1: any;
    // objsyS_SORT_2: any;
    // objtypecode: string;
    // objuniqueid: string;
    // pressdesmax: number;
    // pressopernorm: number;
    // pressunits: string;
    // prssrelief: number;
    // rmaT_DAMAGE_GROUP: any;
    // rsitE_NAME: string;
    // rsitE_RID: number;
    // rstreaM_NAME: any;
    // tM_PV_CERT_HOLDER: any;
    // tM_PV_CONFIG_GEOM: string;
    // tM_PV_DATE_ALTERED: any;
    // tM_PV_DATE_REPAIRED: any;
    // tM_PV_NAT_BOARD_NO: string;
    // tM_PV_ORIENTATION: string;
    // tM_PV_R_CERT_NO: any;
    // tM_PV_SPEC_GRAVITY: any;
    // tempdesmax: number;
    // tempopernorm: number;
    // tempunits: string;
    // xpicK_OBJREF_MATID: any;

    constructor(options?: Partial<AssetComponent>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }
    }
}
