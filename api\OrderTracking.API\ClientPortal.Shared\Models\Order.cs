using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using ClientPortal.Shared.Models.Helpers;
using Dapper.Contrib.Extensions;
using Newtonsoft.Json;
// ReSharper disable InconsistentNaming 

namespace ClientPortal.Shared.Models
{
    [Dapper.Contrib.Extensions.Table("Orders")]
    [System.ComponentModel.DataAnnotations.Schema.Table("Orders")]
    public class Order
    {
        [ExplicitKey]
        [JsonProperty(PropertyName = "id")]
        public string Id { get; set; }

        [JsonProperty(PropertyName = "hash")] public string HASH { get; set; }

        [JsonProperty(PropertyName = "salesLineDataAreaId")]
        public string SALESLINEDATAAREAID { get; set; }

        [JsonProperty(PropertyName = "salesId")]
        public string SALESID { get; set; }

        [JsonProperty(PropertyName = "prodId")]
        public string PRODID { get; set; }

        [JsonProperty(PropertyName = "projId")]
        public string PROJID { get; set; }

        [JsonProperty(PropertyName = "salesName")]
        public string SALESNAME { get; set; }

        [JsonProperty(PropertyName = "externalCustomer")]
        public string EXTERNALCUSTOMER { get; set; }

        [JsonProperty(PropertyName = "externalCustomerName")]
        public string EXTERNALCUSTOMERNAME { get; set; }

        [JsonProperty(PropertyName = "priceUnit")]
        public decimal? PRICEUNIT { get; set; }

        [JsonProperty(PropertyName = "salesLineItemId")]
        public string SALESLINEITEMID { get; set; }

        [Write(false)]
        [JsonProperty(PropertyName = "isCustomOrder")]
        public bool ISCUSTOMORDER
        {
            get
            {
                if (SALESLINEITEMID == null)
                    if (JSSJOBID == null) return false;
                    else return true;
                if (SALESLINEITEMID.StartsWith('4'))
                    return true;

                return false;
            }
        }

        [JsonProperty(PropertyName = "name")]

        public string NAME { get; set; }

        [JsonProperty(PropertyName = "salesQty")]
        public decimal? SALESQTY { get; set; }

        [JsonProperty(PropertyName = "internalCustomerRef")]
        public string INTERNAL_CUSTOMERREF { get; set; }

        [JsonProperty(PropertyName = "internalPurchaseOrderFormNum")]
        public string INTERNAL_PURCHASEORDERFORMNUM { get; set; }

        [JsonProperty(PropertyName = "internalCompanyPurchaseId")]
        public string INTERCOMPANYPURCHID { get; set; }

        [JsonProperty(PropertyName = "jssJobId")]
        public string JSSJOBID { get; set; }

        [JsonProperty(PropertyName = "jssDateReceived")]
        [JsonConverter(typeof(AsUTCDateTimeConverter))]
        public DateTime? JSSDATERECEIVED { get; set; }

        [JsonProperty(PropertyName = "jssDateDesigned")]
        [JsonConverter(typeof(AsUTCDateTimeConverter))]
        public DateTime? JSSDATEDESIGNED { get; set; }

        [JsonProperty(PropertyName = "jssDateConverted")]
        [JsonConverter(typeof(AsUTCDateTimeConverter))]
        public DateTime? JSSDATECONVERTED { get; set; }

        [JsonProperty(PropertyName = "jssDateVerified")]
        [JsonConverter(typeof(AsUTCDateTimeConverter))]
        public DateTime? JSSDATEVERIFIED { get; set; }

        [JsonProperty(PropertyName = "jssDateToMfg")]
        [JsonConverter(typeof(AsUTCDateTimeConverter))]
        public DateTime? JSSDATETOMFG { get; set; }

        [JsonProperty(PropertyName = "collectRefProdId")]
        public string COLLECTREFPRODID { get; set; }

        [JsonProperty(PropertyName = "createdDateTime")]
        [JsonConverter(typeof(AsUTCDateTimeConverter))]
        public DateTime? CREATEDDATETIME { get; set; }

        [JsonProperty(PropertyName = "schedStart")]
        public DateTime? SCHEDSTART { get; set; }

        [JsonProperty(PropertyName = "schedEnd")]
        public DateTime? SCHEDEND { get; set; }

        [JsonProperty(PropertyName = "finishedDate")]
        public DateTime? FINISHEDDATE { get; set; }

        [JsonProperty(PropertyName = "shippingDateRequested")]
        public DateTime? SHIPPINGDATEREQUESTED { get; set; }

        [JsonProperty(PropertyName = "shippingDateConfirmed")]
        public DateTime? SHIPPINGDATECONFIRMED { get; set; }

        [JsonProperty(PropertyName = "deliveryDate")]
        public DateTime? DELIVERYDATE { get; set; }

        [JsonProperty(PropertyName = "packingSlipId")]
        public string PACKINGSLIPID { get; set; }

        [JsonProperty("salesStatus")] public string SALESSTATUS { get; set; }

        [JsonProperty(PropertyName = "lineAmount")]
        public decimal? LINEAMOUNT { get; set; }

        [JsonProperty(PropertyName = "customerAccount")]
        public string CUSTACCOUNT { get; set; }

        [JsonProperty(PropertyName = "tisTrackingNumber")]
        public string TIS_TRACKINGNUMBER { get; set; }

        [JsonProperty(PropertyName = "inventorySiteId")]
        public string INVENTSITEID { get; set; }

        [JsonProperty(PropertyName = "wmsLocationId")]
        public string WMSLOCATIONID { get; set; }

        [JsonProperty(PropertyName = "inventoryLocationId")]
        public string INVENTLOCATIONID { get; set; }

        [JsonProperty(PropertyName = "customerRef")]
        public string CUSTOMERREF { get; set; }

        [JsonProperty(PropertyName = "purchaseOrderFormNum")]
        public string PURCHORDERFORMNUM { get; set; }

        [JsonProperty(PropertyName = "districtNumber")]
        public string DISTRICTNUMBER { get; set; }

        [JsonProperty(PropertyName = "billingCustomerName")]
        public string BILLINGCUSTOMERNAME { get; set; }

        [JsonProperty(PropertyName = "salesLineInventTransId")]
        public string SALESLINE_INVENTTRANSID { get; set; }

        [JsonProperty(PropertyName = "salesLineInventRefId")]
        public string SALESLINE_INVENTREFID { get; set; }

        /// <summary> 
        ///     Seems to be a duplicate of SALESID.  Checked for any that weren't equal, and there weren't any 
        /// </summary> 
        [JsonProperty(PropertyName = "salesTableSalesId")]
        public string SALESTABLE_SALESID { get; set; }

        [JsonProperty(PropertyName = "custPackingSlipTransRecId")]
        public long? CUSTPACKINGSLIPTRANS_RECID { get; set; }

        [JsonProperty(PropertyName = "custPackingSlipJourRecId")]
        public long? CUSTPACKINGSLIPJOUR_RECID { get; set; }

        [JsonProperty(PropertyName = "inventDimId")]
        public string INVENTDIM_INVENTDIMID { get; set; }

        [JsonProperty(PropertyName = "tisSalesProdRecId")]
        public long? TIS_SALESPROD_RECID { get; set; }

        [JsonProperty(PropertyName = "purchTablePurchId")]
        public string PURCHTABLE_PURCHID { get; set; }

        [JsonProperty(PropertyName = "projTableProjId")]
        public string PROJTABLE_PROJID { get; set; }

        [JsonProperty(PropertyName = "projFundingSourceRecId")]
        public long? PROJFUNDINGSOURCE_RECID { get; set; }

        [JsonProperty(PropertyName = "dirPartyTableRecId")]
        public long? DIRPARTYTABLE_RECID { get; set; }

        [JsonProperty(PropertyName = "custTableAccountNum")]
        public string CUSTTABLE_ACCOUNTNUM { get; set; }

        [JsonProperty(PropertyName = "internalDistrictNumber")]
        public string INTERNALDISTRICTNUMBER { get; set; }

        [JsonProperty(PropertyName = "routeId")]
        public string ROUTEID { get; set; }

        [JsonProperty(PropertyName = "ordered")]
        public decimal? ORDERED { get; set; }

        [JsonProperty(PropertyName = "quantityShipped")]
        public decimal? DELIVERED { get; set; }

        [JsonProperty(PropertyName = "quantityRemaining")]
        public decimal? REMAININGQUANTITY { get; set; }

        [JsonProperty(PropertyName = "referenceNumber")]
        public string REFERENCENUMBER { get; set; }

        [JsonProperty(PropertyName = "salesPrice")]
        public decimal? SALESLINE_SALESPRICE { get; set; }

        [JsonProperty(PropertyName = "status")]
        public string STATUS { get; set; }

        [JsonProperty(PropertyName = "deliveryRemarks")]
        public string TRI_JSSDELIVERYREMARKS { get; set; }

        [JsonProperty(PropertyName = "salesOrderShipDate")]
        public DateTime? SHIPDATE { get; set; }

        [JsonProperty(PropertyName = "receiptDateConfirmed")]
        public DateTime? CONFIRMEDRECEIPTDATE { get; set; }

        [JsonProperty(PropertyName = "salesLineRecId")]
        public long? SALESLINE_RECID { get; set; }

        [JsonProperty(PropertyName = "prodServiceLineDetail")]
        public string PRODSERVICELINEDETAIL { get; set; }

        [JsonProperty(PropertyName = "prodGroupId")]
        public string PRODGROUPID { get; set; }

        public string TIS_JSSROUTING { get; set; }
        public DateTime? JSSDATESTAMPED { get; set; }
        public string JSSRAPIDQUOTEREQUESTED { get; set; }
        public string JSSRAPIDQUOTEAPPLICABLE { get; set; }
        public DateTime? JSSRAPIDQUOTEDATE { get; set; }
        public string JSSCOMPONENTID { get; set; }
        public string OLDJSSVERIFIER { get; set; }
        public string JSSPROFESSIONALENGINEER { get; set; }
        public string JSSLANE { get; set; }
        public string JSSPESTAMP { get; set; }
        public string OLDJSSCURRENTHOLDER { get; set; }
        public DateTime? JSSDATECUSTOMERETA { get; set; }
        public string JSSDESCCOMPONENT { get; set; }
        public string JSSJOBPRIORITY { get; set; }
        public DateTime? JSSDATEENTERED { get; set; }
        public DateTime? JSSDATECOMPLETED { get; set; }
        public string JSSENCLOSUREMATERIAL { get; set; }
        public string JSSLINESIZE { get; set; }
        public string JSSLINEMATERIAL { get; set; }
        public string JSSLEAKTYPE { get; set; }
        public decimal? JSSOD { get; set; }
        public decimal? JSSDESIGNPRESSURE { get; set; }
        public decimal? JSSDESIGNTEMPERATURE { get; set; }
        public string JSSENCLOSURESEAL { get; set; }
        public string JSSBRANCH { get; set; }
        public string CUSTNAME { get; set; }
        public string PRODServiceLineGroup { get; set; }
        public string JSSENGINEERINGPRIORITY { get; set; }
        public string JSSJobsTableRepairType { get; set; }
        public string JSSSpecTableRepairType { get; set; }
        public string JSSJOBSTATUS { get; set; }
        public DateTime? JSSMODIFIEDDATETIME { get; set; }
        public string JSSMODIFIEDBY { get; set; }
        public string OldJSSDesigner { get; set; }
        public string DataTaker { get; set; }
        public string JSSTier { get; set; }

        public string JSSSERVICELINE { get; set; }
        public string JSSSERVICELINEGROUP { get; set; }

        [Write(false)]
        [JsonProperty(PropertyName = "canUploadSupportDocuments")]
        public bool CanUploadSupportDocuments =>
            SALESLINE_RECID != null && SALESLINE_RECID > 0;

        [JsonProperty("files")]
        [Write(false)]
        [ForeignKey(nameof(OrderBlobFile.SalesLineRecId))]
        public ICollection<OrderBlobFile> Files { get; set; }
    }
}