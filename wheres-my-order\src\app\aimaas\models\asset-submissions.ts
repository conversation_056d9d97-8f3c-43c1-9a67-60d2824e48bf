export class AssetSubmissions {
    submissiontype: string;
    typecomment: string;
    datetime: string;
    submitteduser: string;
    createddate: string;
    anomalyId: string;
    clientClosedDate: string;
    comment: string;
    assetId: string;
    serviceType: string;

    constructor(options?: Partial<AssetSubmissions>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }
    }

}
