import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import { ApmService } from '../../../apm/services';
import { LeakReportingGridComponent } from './leak-reporting-grid.component';

describe('LeakReportingGridComponent', () => {
    let component: LeakReportingGridComponent;
    let fixture: ComponentFixture<LeakReportingGridComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [HttpClientTestingModule, ToastrModule.forRoot()],
            providers: [
                { provide: ApmService, useValue: { selectedBU$: of('123') } }
            ],
            declarations: [LeakReportingGridComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(LeakReportingGridComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
