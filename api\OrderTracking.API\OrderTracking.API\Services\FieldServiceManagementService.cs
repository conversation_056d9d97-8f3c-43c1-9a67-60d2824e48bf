﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.Extensions.Options;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using OrderTracking.API.Controllers;
using OrderTracking.API.Models;
using Entity = Microsoft.Xrm.Sdk.Entity;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     Service class to interact with FSM in Dynamics 365.
    /// </summary>
    public class FieldServiceManagementService : IFieldServiceManagementService
    {
        private const string WorkOrderEntityName = "msdyn_workorder";
        private const string WorkOrderNameAttributeName = "msdyn_name";
        private const string AnnotationEntityName = "annotation";
        private const string AnnotationObjectIdAttributeName = "objectid";
        private const string WorkOrderTypeEntityName = "msdyn_workordertype";
        private const string TypeNameAttributeName = "msdyn_name";
        private const string TradeType = "Trade";
        
        private readonly ServiceClient _client;
        private readonly IHttpClientFactory _clientFactory;
        private readonly string _updateFlowUrl;

        /// <summary>
        ///     Constructor for service class
        /// </summary>
        /// <param name="config"></param>
        /// <param name="clientFactory"></param>
        public FieldServiceManagementService(IOptions<FSMConfig> config, IHttpClientFactory clientFactory)
        {
            _clientFactory = clientFactory;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            _client = new ServiceClient(
                $"AuthType=OAuth; Username={config.Value.UserName}; Password={config.Value.Password}; Url={config.Value.Url}");
            _updateFlowUrl = config.Value.UpdateFlowUrl;
        }

        /// <summary>
        ///     Get Work Orders.  Pull `msdyn_workorder` where the type is TRADE
        /// </summary>
        /// <returns></returns>
        public async Task<EntityCollection> GetWorkOrders()
        {
            var tradeType = await GetTradeType();
            if (tradeType == null) return null;

            var query = new QueryExpression(WorkOrderEntityName) {ColumnSet = new ColumnSet(true)};
            query.Criteria.AddCondition(WorkOrderTypeEntityName, ConditionOperator.Equal, tradeType.Id);
            var collection = await _client.RetrieveMultipleAsync(query);
            return collection;
        }

        /// <summary>
        ///     Get a `msdyn_workorder` by ID (Guid)
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Entity> GetWorkOrderById(Guid id)
        {
            var workOrder = await _client.RetrieveAsync(WorkOrderEntityName, id, new ColumnSet(true));
            return workOrder;
        }

        /// <summary>
        ///     Find a work order by its `msdyn_name` field.  This should be unique, but will return `FirstOrDefault()` either way.
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public async Task<Entity> GetWorkOrderByName(string name)
        {
            var tradeType = await GetTradeType();
            if (tradeType == null) return null;

            var query = new QueryExpression(WorkOrderEntityName) {ColumnSet = new ColumnSet(true)};
            query.Criteria.AddCondition(WorkOrderNameAttributeName, ConditionOperator.Equal, name);
            query.Criteria.AddCondition(WorkOrderTypeEntityName, ConditionOperator.Equal, tradeType.Id);
            var collection = await _client.RetrieveMultipleAsync(query);
            var workOrder = collection.Entities.FirstOrDefault();
            return workOrder;
        }

        /// <summary>
        ///     Get annotations (notes, files, etc.) for a work order
        /// </summary>
        /// <param name="workOrder"></param>
        /// <returns></returns>
        public async Task<EntityCollection> GetAnnotationsForWorkOrder(Entity workOrder)
        {
            var collection = await GetAnnotationsForWorkOrder(workOrder.Id);
            return collection;
        }

        /// <summary>
        ///     Get annotations (notes, files, etc.) for a work order by the work order's ID (Guid).
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<EntityCollection> GetAnnotationsForWorkOrder(Guid id)
        {
            var query = new QueryExpression(AnnotationEntityName) {ColumnSet = new ColumnSet(true)};
            query.Criteria.AddCondition(AnnotationObjectIdAttributeName, ConditionOperator.Equal, id);
            var collection = await _client.RetrieveMultipleAsync(query);
            return collection;
        }

        /// <summary>
        ///     Search work orders by name using the `Like` condition operator.
        /// </summary>
        /// <param name="searchTerm"></param>
        /// <returns></returns>
        public async Task<EntityCollection> SearchWorkOrderByName(string searchTerm)
        {
            var tradeType = await GetTradeType();
            if (tradeType == null) return null;

            var query = new QueryExpression(WorkOrderEntityName) {ColumnSet = new ColumnSet(true)};
            query.Criteria.AddCondition(WorkOrderNameAttributeName, ConditionOperator.Like, searchTerm);
            query.Criteria.AddCondition(WorkOrderTypeEntityName, ConditionOperator.Equal, tradeType.Id);
            var collection = await _client.RetrieveMultipleAsync(query);
            return collection;
        }

        public async Task<string> MoveToUnscheduled(string workRequestNumber)
        {
            if (string.IsNullOrEmpty(workRequestNumber)) throw new ArgumentNullException(nameof(workRequestNumber));

            var payload = new Payload
            {
                WorkRequestNumber = workRequestNumber,
                Status = "Unscheduled",
                Comments = new List<Comment>()
            };

            var returnMessage = await SubmitPayload(payload);

            return returnMessage;
        }
        
        /// <summary>
        ///     Move Work Request to Scheduled.  workRequestNumber is the msdyn_name attribute
        /// </summary>
        /// <param name="workRequestNumber"></param>
        /// <returns></returns>
        public async Task<string> MoveToScheduled(string workRequestNumber)
        {
            if (string.IsNullOrEmpty(workRequestNumber)) throw new ArgumentNullException(nameof(workRequestNumber));

            var payload = new Payload
            {
                WorkRequestNumber = workRequestNumber,
                Status = "Scheduled",
                Comments = new List<Comment>()
            };

            var returnMessage = await SubmitPayload(payload);

            return returnMessage;
        }

        /// <summary>
        ///     Move Work Request to In Progress.  workRequestNumber is the msdyn_name attribute
        /// </summary>
        /// <param name="statusUpdate"></param>
        /// <returns></returns>
        public async Task<string> MoveToInProgress(StatusUpdate statusUpdate)
        {
            if (string.IsNullOrEmpty(statusUpdate.WorkRequestNumber)) throw new ArgumentNullException(nameof(statusUpdate.WorkRequestNumber));

            var payload = new Payload
            {
                WorkRequestNumber = statusUpdate.WorkRequestNumber,
                Status = "In Progress",
                Resource = new Resource
                {
                    Name = "Brand",
                    EmailAddress = "<EMAIL>",
                    Starttime = statusUpdate.StartTime,
                    Endtime = statusUpdate.EndTime,
                    Arrivingtime = statusUpdate.ArrivalTime
                },
                Comments = new List<Comment>()
            };

            var returnMessage = await SubmitPayload(payload);

            return returnMessage;
        }

        /// <summary>
        ///     Move Work Request to Completed.  workRequestNumber is the msdyn_name attribute
        /// </summary>
        /// <param name="workRequestNumber"></param>
        /// <returns></returns>
        public async Task<string> MoveToComplete(string workRequestNumber)
        {
            if (string.IsNullOrEmpty(workRequestNumber)) throw new ArgumentNullException(nameof(workRequestNumber));

            var payload = new Payload
            {
                WorkRequestNumber = workRequestNumber,
                Status = "Completed",
                Resource = null,
                Comments = new List<Comment>()
            };

            var returnMessage = await SubmitPayload(payload);

            return returnMessage;
        }

        /// <summary>
        ///     Add a comment to the timeline
        /// </summary>
        /// <param name="workRequestNumber"></param>
        /// <param name="comment"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<string> AddComment(string workRequestNumber, string comment, UserProfile user)
        {
            if (string.IsNullOrEmpty(workRequestNumber)) throw new ArgumentNullException(nameof(workRequestNumber));
            if (string.IsNullOrEmpty(comment)) throw new ArgumentNullException(nameof(comment));

            var workRequest = await GetWorkOrderByName(workRequestNumber);

            var payload = new Payload
            {
                WorkRequestNumber = workRequestNumber,
                Status = workRequest.Attributes.FirstOrDefault(a => a.Key == "msdyn_systemstatus").Value.ToString(),
                Comments = new List<Comment>
                {
                    new Comment
                    {
                        Username = user.GivenName,
                        Useremail = user.Id,
                        Timestamp = DateTime.UtcNow,
                        Description = comment
                    }
                }
            };

            var returnMessage = await SubmitPayload(payload);

            return returnMessage;
        }

        private async Task<string> SubmitPayload(Payload payload)
        {
            var client = _clientFactory.CreateClient();

            using var content = SerializePayload(payload);
            var result = await client.PostAsync(_updateFlowUrl, content);
            var returnMessage = await result.Content.ReadAsStringAsync();
            return returnMessage;
        }

        private static StringContent SerializePayload(Payload payload) =>
            new StringContent(JsonConvert.SerializeObject(payload), Encoding.UTF8, "application/json");

        /// <summary>
        ///     Get the Trade type from D365 so that we know we are looking up the correct work orders regardless of environment
        /// </summary>
        /// <returns></returns>
        private async Task<Entity> GetTradeType()
        {
            var tradeQuery = new QueryExpression(WorkOrderTypeEntityName);
            tradeQuery.Criteria.AddCondition(TypeNameAttributeName, ConditionOperator.Equal, TradeType);
            var types = await _client.RetrieveMultipleAsync(tradeQuery);
            var tradeType = types.Entities.FirstOrDefault();
            return tradeType;
        }
    }

    /// <summary>
    ///     Payload for Update Flow POST requests
    /// </summary>
    public class Payload
    {
        /// <summary>
        ///     Work Request number (msdyn_name)
        /// </summary>
        public string WorkRequestNumber { get; set; }

        /// <summary>
        ///     Bookable Resource (required for putting work requests into "In Progress")
        /// </summary>
        public Resource Resource { get; set; }

        /// <summary>
        ///     Comments to be added to a work request
        /// </summary>
        public List<Comment> Comments { get; set; }

        /// <summary>
        ///     Status that a work request is moving to
        /// </summary>
        public string Status { get; set; }
    }

    /// <summary>
    ///     Bookable Resource for an In Progress Work Request
    /// </summary>
    public class Resource
    {
        /// <summary>
        ///     Name of Bookable Resource
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        ///     Email address of Bookable Resource
        /// </summary>
        public string EmailAddress { get; set; }

        public DateTime Starttime { get; set; }
        public DateTime Endtime { get; set; }
        public DateTime Arrivingtime { get; set; }
    }

    /// <summary>
    ///     Timeline Comment
    /// </summary>
    public class Comment
    {
        /// <summary>
        ///     Username of commenter
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        ///     Email of commenter
        /// </summary>
        public string Useremail { get; set; }

        /// <summary>
        ///     Timestamp of comment
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        ///     Comments content
        /// </summary>
        public string Description { get; set; }
    }

    public interface IFieldServiceManagementService
    {
        /// <summary>
        ///     Get Work Orders.  Pull `msdyn_workorder` where the type is TRADE
        /// </summary>
        /// <returns></returns>
        Task<EntityCollection> GetWorkOrders();

        /// <summary>
        ///     Get a `msdyn_workorder` by ID (Guid)
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<Entity> GetWorkOrderById(Guid id);

        /// <summary>
        ///     Find a work order by its `msdyn_name` field.  This should be unique, but will return `FirstOrDefault()` either way.
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        Task<Entity> GetWorkOrderByName(string name);

        /// <summary>
        ///     Get annotations (notes, files, etc.) for a work order
        /// </summary>
        /// <param name="workOrder"></param>
        /// <returns></returns>
        Task<EntityCollection> GetAnnotationsForWorkOrder(Entity workOrder);

        /// <summary>
        ///     Get annotations (notes, files, etc.) for a work order by the work order's ID (Guid).
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<EntityCollection> GetAnnotationsForWorkOrder(Guid id);

        /// <summary>
        ///     Search work orders by name using the `Like` condition operator.
        /// </summary>
        /// <param name="searchTerm"></param>
        /// <returns></returns>
        Task<EntityCollection> SearchWorkOrderByName(string searchTerm);

        /// <summary>
        ///     Move Work Request to In Progress.  workRequestNumber is the msdyn_name attribute
        /// </summary>
        /// <param name="workRequestNumber"></param>
        /// <returns></returns>
        Task<string> MoveToInProgress(StatusUpdate workRequestNumber);

        /// <summary>
        ///     Move Work Request to Completed.  workRequestNumber is the msdyn_name attribute
        /// </summary>
        /// <param name="workRequestNumber"></param>
        /// <returns></returns>
        Task<string> MoveToComplete(string workRequestNumber);

        /// <summary>
        ///     Add a comment to the timeline
        /// </summary>
        /// <param name="workRequestNumber"></param>
        /// <param name="comment"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        Task<string> AddComment(string workRequestNumber, string comment, UserProfile user);

        /// <summary>
        ///     Move Work Request to Scheduled.  workRequestNumber is the msdyn_name attribute
        /// </summary>
        /// <param name="workRequestNumber"></param>
        /// <returns></returns>
        Task<string> MoveToScheduled(string workRequestNumber);

        Task<string> MoveToUnscheduled(string workRequestNumber);
    }
}