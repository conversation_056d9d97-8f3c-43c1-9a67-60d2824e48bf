import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs';

import { pluckAllUnique } from '../../core/operators';
import { UserProfile } from '../../profile/models';
import { Breadcrumb } from '../../shared/components';
import { KeyOfType } from '../../shared/helpers';
import { DataGridService, UsersService } from '../../shared/services';

@Component({
    selector: 'app-user-audit',
    templateUrl: './user-audit.component.html',
    styleUrls: ['./user-audit.component.scss'],
})
export class UserAuditComponent implements OnInit {
    crumbs: Breadcrumb[] = [
        { label: 'Administration', route: '/admin' },
        { label: 'User Audit', route: '/admin/user-audit' },
    ];
    users$: Observable<UserProfile[]>;
    allRoles$: Observable<string[]>;
    allDistrictIds$: Observable<string[]>;
    allCustomerAccounts$: Observable<string[]>;

    constructor(
        private readonly _users: UsersService,
        private readonly _grid: DataGridService
    ) {}

    ngOnInit(): void {
        this.users$ = this._users.getAll();
        this.allRoles$ = this.users$.pipe(pluckAllUnique('roles'));
        this.allDistrictIds$ = this.users$.pipe(pluckAllUnique('districtIds'));
        this.allCustomerAccounts$ = this.users$.pipe(
            pluckAllUnique('customerAccounts')
        );
    }

    onToolbarPreparing(event) {
        const toolbarItems = event.toolbarOptions.items;
        toolbarItems.unshift({
            widget: 'dxButton',
            options: {
                icon: 'fa fa-undo',
                hint: 'Restore Grid Defaults',
                onClick: () => this._grid.resetGridState(event.component),
            },
            location: 'after',
        });
    }

    rolesDisplayValue(rowData: UserProfile): string | null {
        return rowData.roles.join(', ') || null;
    }

    districtIdsDisplayValue(rowData: UserProfile): string | null {
        return rowData.districtIds.join(', ') || null;
    }

    customerAccountsDisplayValue(rowData: UserProfile): string | null {
        return rowData.customerAccounts.join(', ') || null;
    }

    rolesFilterExpression(filterValue: string): (data: UserProfile) => boolean {
        return createFilterFunction(filterValue, 'roles');
    }

    districtFilterExpression(
        filterValue: string
    ): (data: UserProfile) => boolean {
        return createFilterFunction(filterValue, 'districtIds');
    }

    customerAccountsFilterExpression(
        filterValue: string
    ): (data: UserProfile) => boolean {
        return createFilterFunction(filterValue, 'customerAccounts');
    }
}

function createFilterFunction(
    filterValue: string,
    dataField: KeyOfType<UserProfile, string[]>
): (data: UserProfile) => boolean {
    return (data: UserProfile) => {
        const values = data[dataField];
        if (filterValue === null) return values?.length === 0;
        return values?.indexOf(filterValue) >= 0;
    };
}
