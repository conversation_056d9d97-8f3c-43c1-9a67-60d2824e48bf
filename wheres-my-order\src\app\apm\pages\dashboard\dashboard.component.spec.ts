import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import {
    DxChartModule,
    DxPieChartModule,
    DxRangeSelectorModule,
    DxResponsiveBoxModule,
    DxSelectBoxModule,
    DxTagBoxModule
} from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { OverviewBoxComponent } from '../../../shared/components';
import {
    ActivityHoursPieChartComponent,
    AssetCategoriesSelectorComponent,
    AssetsDoughnutChartComponent,
    DailyInspectionCountComponent,
    EquipmentByAreaAndTypeComponent,
    InspectionStatusDoughnutChartComponent,
    InspectionsWithoutDueDatesComponent,
    OverviewBoxesComponent
} from '../../components';
import { ApmService } from '../../services';
import { DashboardService } from '../../services/dashboard.service';
import { DashboardComponent } from './dashboard.component';

describe('DashboardComponent', () => {
    let component: DashboardComponent;
    let fixture: ComponentFixture<DashboardComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                HttpClientTestingModule,
                RouterTestingModule,
                DxPieChartModule,
                DxChartModule,
                DxSelectBoxModule,
                DxRangeSelectorModule,
                DxTagBoxModule,
                ToastrModule.forRoot(),
                DxResponsiveBoxModule
            ],
            declarations: [
                DashboardComponent,
                AssetsDoughnutChartComponent,
                OverviewBoxesComponent,
                InspectionStatusDoughnutChartComponent,
                EquipmentByAreaAndTypeComponent,
                ActivityHoursPieChartComponent,
                OverviewBoxComponent,
                DailyInspectionCountComponent,
                InspectionsWithoutDueDatesComponent,
                AssetCategoriesSelectorComponent
            ],
            providers: [
                { provide: DashboardService, useValue: {} },
                { provide: ApmService, useValue: {} }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(DashboardComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
