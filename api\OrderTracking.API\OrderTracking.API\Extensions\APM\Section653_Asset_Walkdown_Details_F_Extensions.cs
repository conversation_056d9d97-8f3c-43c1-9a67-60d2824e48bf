using System.Linq;
using APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Extensions.APM
{
    /// <summary>
    ///     Extension methods to update 653 Asset Walk downs
    /// </summary>
    public static class Section653_Asset_Walkdown_Details_F_Extensions
    {
        /// <summary>
        ///     Updates a 653 asset walk down from a <see cref="SixFiftyThreeWalkDown" />
        /// </summary>
        /// <param name="details"></param>
        /// <param name="data"></param>
        public static void Update(this Section653_Asset_Walkdown_Details_F details, SixFiftyThreeWalkDown data)
        {
            if (data.Name != null)
                details.sectionIdentification.attributeName.SetValue(data.Name.Value);
            if (data.NumberOrId != null)
                details.sectionIdentification.attributeNumber_or_ID.SetValue(data.NumberOrId.Value);
            if (data.AssetType != null)
                details.sectionIdentification.attributeAsset_Type.SetValue(data.AssetType.Value);
            if (data.EquipmentDescription != null)
                details.sectionIdentification.attributeEquipment_Description.SetValue(data.EquipmentDescription.Value);
            if (data.LastKnownInspectionDate != null)
                details.sectionIdentification.attributeLast_known_inspection_date
                    .SetValue(data.LastKnownInspectionDate.Value);
            if (data.Location != null)
                details.sectionIdentification.attributeLocation.SetValue(data.Location.Value);

            // Potentially throws an InvalidLocationException
            details.sectionIdentification.attributeGIS_Location.SetLocation(data.Latitude, data.Longitude);
            
            if (data.DesignCode != null)
                details.sectionGeneralInformation.sectionDesign.attributeDesign_Code.SetValue(data.DesignCode.Value);
            if (data.CodeYear != null)
                details.sectionGeneralInformation.sectionDesign.attributeCode_Year.SetValue(data.CodeYear.Value);
            if (data.Addendum != null)
                details.sectionGeneralInformation.sectionDesign.attributeAddendum.SetValue(data.Addendum.Value);
            if (data.MaximumFillHeight != null)
                details.sectionGeneralInformation.sectionDesign.attributeMaximum_Fill_Height.SetValue(
                    data.MaximumFillHeight.Value);
            if (data.Diameter != null)
                details.sectionGeneralInformation.sectionDesign.attributeDiameter.SetValue(data.Diameter.Value);
            if (data.Height != null)
                details.sectionGeneralInformation.sectionDesign.attributeHeight.SetValue(data.Height.Value);
            if (data.TankVolumeInBBL != null)
                details.sectionGeneralInformation.sectionDesign.attributeTank_Volume_in_BBL.SetValue(
                    data.TankVolumeInBBL.Value);
            if (data.ConstructionMethod != null)
                details.sectionGeneralInformation.sectionDesign.attributeConstruction_Method.SetValue(
                    data.ConstructionMethod.Value);
            if (data.Orientation != null)
                details.sectionGeneralInformation.sectionDesign.attributeOrientation.SetValue(data.Orientation.Value);
            if (data.RT != null)
                details.sectionGeneralInformation.sectionDesign.attributeRT.SetValue(data.RT.Value);
            if (data.InstallationDate != null)
                details.sectionGeneralInformation.sectionDesign.attributeInstallation_Date.SetValue(
                    data.InstallationDate.Value);
            if (data.InServiceDate != null)
                details.sectionGeneralInformation.sectionDesign.attributeIn_service_Date.SetValue(data.InServiceDate
                    .Value);
            if (data.PAndIdNumber != null)
                details.sectionGeneralInformation.sectionDesign.attributePID_Number.SetValue(data.PAndIdNumber.Value);
            if (data.ConstructionDesignDrawingNumber != null)
                details.sectionGeneralInformation.sectionDesign.attributeConstructionDesign_Drawing_Number.SetValue(
                    data.ConstructionDesignDrawingNumber.Value);
            if (data.LowestFlangeRating != null)
                details.sectionGeneralInformation.sectionDesign.attributeLowest_Flange_Rating.SetValue(
                    data.LowestFlangeRating.Value);
            if (data.ServiceProductContents != null)
                details.sectionGeneralInformation.sectionService.attributeServiceProductContents.SetValue(
                    data.ServiceProductContents.Value);
            if (data.SpecificGravity != null)
                details.sectionGeneralInformation.sectionService.attributeSpecific_Gravity.SetValue(
                    data.SpecificGravity.Value);
            if (data.IntendedService != null)
                details.sectionGeneralInformation.sectionService.attributeIntended_Service.SetValue(
                    data.IntendedService.Value);
            if (data.InspectionOpenings != null && data.InspectionOpenings.Length > 0)
                foreach (var opening in data.InspectionOpenings.Where(i => i != null))
                {
                    var dbOpening = details.sectionGeneralInformation.sectionDesign.sectionInspectionOpenings
                        .CurrentEntries
                        .Find(o => o.DatabaseId == opening.DatabaseId);
                    if (opening.Number != null)
                        dbOpening.attributeOpening_Number.SetValue(opening.Number.Value);
                    if (opening.Size != null)
                        dbOpening.attributeOpening_Size.SetValue(opening.Size.Value);
                    if (opening.Type != null)
                        dbOpening.attributeOpening_Type.SetValue(opening.Type.Value);
                }

            if (data.InspectionCode != null)
                details.sectionGeneralInformation.sectionDesign.sectionInspection.attributeInspection_Code.SetValue(
                    data.InspectionCode.Value);
            if (data.InspectionYear != null)
                details.sectionGeneralInformation.sectionDesign.sectionInspection.attributeYear.SetValue(
                    data.InspectionYear.Value);
            if (data.InspectionAddendum != null)
                details.sectionGeneralInformation.sectionDesign.sectionInspection.attributeAddendum.SetValue(
                    data.InspectionAddendum.Value);
            if (data.DataPlateAttached != null)
                details.sectionGeneralInformation.sectionDesign.sectionDataPlate.attributeAttached.SetValue(
                    data.DataPlateAttached.Value);
            if (data.DataPlateLegible != null)
                details.sectionGeneralInformation.sectionDesign.sectionDataPlate.attributeLegible.SetValue(
                    data.DataPlateLegible.Value);
            if (data.ManufacturerName != null)
                details.sectionGeneralInformation.sectionDesign.sectionManufacturerFabricator.attributeName.SetValue(
                    data.ManufacturerName.Value);
            if (data.ManufacturerDate != null)
                details.sectionGeneralInformation.sectionDesign.sectionManufacturerFabricator.attributeDate.SetValue(
                    data.ManufacturerDate.Value);
            if (data.ManufacturerSerialNumber != null)
                details.sectionGeneralInformation.sectionDesign.sectionManufacturerFabricator.attributeSerial_Number
                    .SetValue(data.ManufacturerSerialNumber.Value);
            if (data.HasRepairOrAlterationPlate != null)
                details.sectionRepairsandAlterations.attributeDoes_the_asset_have_a_repair_or_alteration_plate.SetValue(
                    data.HasRepairOrAlterationPlate.Value);
            if (data.RepairOrAlterationPlateLegible != null)
                details.sectionRepairsandAlterations.attributeRepairAlteration_Plates_Legible.SetValue(
                    data.RepairOrAlterationPlateLegible.Value);

            if (data.Repairs != null && data.Repairs.Length > 0)
                foreach (var repair in data.Repairs.Where(r => r != null))
                {
                    var dbRepair =
                        details.sectionRepairsandAlterations.sectionRepairs.CurrentEntries.Find(r =>
                            r.DatabaseId == repair.DatabaseId);
                    if (repair.DateRepairedOrAltered != null)
                        dbRepair.attributeDate_Repaired_or_Altered.SetValue(repair.DateRepairedOrAltered.Value);
                    if (repair.PurposeOfRepairAlteration != null)
                        dbRepair.attributePurpose_of_repairalteration.SetValue(repair.PurposeOfRepairAlteration.Value);
                    if (repair.RepairAlterationOrganization != null)
                        dbRepair.attributeRepairAlteration_organization.SetValue(repair.RepairAlterationOrganization
                            .Value);
                }

            if (data.CurrentService != null)
                details.sectionOperatingDesignConditions.attributeCurrent_service.SetValue(data.CurrentService.Value);
            if (data.DesignTemperature != null)
                details.sectionOperatingDesignConditions.attributeDesign_Temp.SetValue(data.DesignTemperature.Value);
            if (data.CurrentOperatingTemperature != null)
                details.sectionOperatingDesignConditions.attributeCurrent_Operating_Temperature.SetValue(
                    data.CurrentOperatingTemperature.Value);
            if (data.CurrentFillLevel != null)
                details.sectionOperatingDesignConditions.attributeCurrent_Fill_level_if_available.SetValue(
                    data.CurrentFillLevel.Value);
            if (data.OperationStatus != null)
                details.sectionOperatingDesignConditions.attributeOperation_Status.SetValue(data.OperationStatus.Value);
            if (data.TankEquippedWithVRU != null)
                details.sectionOperatingDesignConditions.attributeIs_the_tank_equipped_with_VRU.SetValue(
                    data.TankEquippedWithVRU.Value);
            if (data.TankEquippedWithLeakDetection != null)
                details.sectionOperatingDesignConditions.attributeTank_equipped_with_Leak_Detection.SetValue(
                    data.TankEquippedWithLeakDetection.Value);
            if (data.TankOutOfService != null)
                details.sectionOperatingDesignConditions.sectionTankOutOfServiceRequirements.attribute653AWQ331
                    .SetValue(data.TankOutOfService.Value);
            if (data.RegulatoryRequirements != null && data.RegulatoryRequirements.Length > 0)
                foreach (var requirement in data.RegulatoryRequirements.Where(r => r != null))
                {
                    var dbRequirement =
                        details.sectionOperatingDesignConditions.sectionRegulatoryRequirements.CurrentEntries
                            .Find(r => r.DatabaseId == requirement.DatabaseId);
                    if (requirement.JurisdictionRegulatoryAgency != null)
                        dbRequirement.attributeJurisdiction_Regulatory_agency.SetValue(requirement
                            .JurisdictionRegulatoryAgency.Value);
                }

            if (data.ShellCourses != null && data.ShellCourses.Length > 0)
                foreach (var course in data.ShellCourses.Where(s => s != null))
                {
                    var dbCourse =
                        details.sectionComponents.sectionShellCourses.CurrentEntries.Find(c =>
                            c.DatabaseId == course.DatabaseId);
                    if (course.AllowableStressAtTemp != null)
                        dbCourse.attributeAllowable_Stress_at_Temperature.SetValue(course.AllowableStressAtTemp.Value);
                    if (course.CorrosionAllowance != null)
                        dbCourse.attributeCorrosion_Allowance.SetValue(course.CorrosionAllowance.Value);
                    if (course.JointEfficiency != null)
                        dbCourse.attributeJoint_Efficiency.SetValue(course.JointEfficiency.Value);
                    if (course.LengthOrHeight != null)
                        dbCourse.attributeLength_or_Height.SetValue(course.LengthOrHeight.Value);
                    if (course.MaterialSpecAndGrade != null)
                        dbCourse.attributeMaterial_Spec_and_Grade.SetValue(course.MaterialSpecAndGrade.Value);
                    if (course.NominalThickness != null)
                        dbCourse.attributeNominal_Thickness.SetValue(course.NominalThickness.Value);
                    if (course.Number != null)
                        dbCourse.attributeNumber.SetValue(course.Number.Value);
                }

            if (data.TankFloorType != null)
                details.sectionComponents.sectionTankBottomFloor.attributeType.SetValue(data.TankFloorType.Value);
            if (data.TankFloorMaterialSpecAndGrade != null)
                details.sectionComponents.sectionTankBottomFloor.attributeMaterial_Spec_and_Grade.SetValue(
                    data.TankFloorMaterialSpecAndGrade.Value);
            if (data.TankFloorAnnularRingNominalThickness != null)
                details.sectionComponents.sectionTankBottomFloor.attributeNominal_thickness_annular_ring.SetValue(
                    data.TankFloorAnnularRingNominalThickness.Value);
            if (data.TankFloorSketchPlatesNominalThickness != null)
                details.sectionComponents.sectionTankBottomFloor.attributeNominal_thickness_sketch_plates.SetValue(
                    data.TankFloorSketchPlatesNominalThickness.Value);
            if (data.TankFloorInnerPlatesNominalThickness != null)
                details.sectionComponents.sectionTankBottomFloor.attributeNominal_thickness_inner_plates.SetValue(
                    data.TankFloorInnerPlatesNominalThickness.Value);
            if (data.TankFloorCorrosionAllowance != null)
                details.sectionComponents.sectionTankBottomFloor.attributeCorrosion_Allowance.SetValue(
                    data.TankFloorCorrosionAllowance.Value);
            if (data.TankRoofs != null && data.TankRoofs.Length > 0)
                foreach (var roof in data.TankRoofs.Where(t => t != null))
                {
                    var dbRoof =
                        details.sectionComponents.sectionTankRoof.CurrentEntries.Find(r =>
                            r.DatabaseId == roof.DatabaseId);
                    if (roof.CorrosionAllowance != null)
                        dbRoof.attributeCorrosion_Allowance.SetValue(roof.CorrosionAllowance.Value);
                    if (roof.MaterialSpecAndGrade != null)
                        dbRoof.attributeMaterial_Spec_and_Grade.SetValue(roof.MaterialSpecAndGrade.Value);
                    if (roof.NominalThickness != null)
                        dbRoof.attributeNominal_thickness_roof.SetValue(roof.NominalThickness.Value);
                    if (roof.Type != null)
                        dbRoof.attributeType.SetValue(roof.Type.Value);
                }

            if (data.Nozzles != null && data.Nozzles.Length > 0)
                foreach (var nozzle in data.Nozzles.Where(n => n != null))
                {
                    var dbNozzle =
                        details.sectionComponents.sectionNozzles.CurrentEntries.Find(n =>
                            n.DatabaseId == nozzle.DatabaseId);
                    if (nozzle.FlangeRating != null)
                        dbNozzle.attributeFlange_Rating.SetValue(nozzle.FlangeRating.Value);
                    if (nozzle.MaterialSpecAndGrade != null)
                        dbNozzle.attributeMaterial_Spec_and_Grade.SetValue(nozzle.MaterialSpecAndGrade.Value);
                    if (nozzle.Number != null)
                        dbNozzle.attributeNumber.SetValue(nozzle.Number.Value);
                    if (nozzle.PipeSchedule != null)
                        dbNozzle.attributePipe_Schedule.SetValue(nozzle.PipeSchedule.Value);
                    if (nozzle.PipeSize != null)
                        dbNozzle.attributePipe_Size.SetValue(nozzle.PipeSize.Value);
                    if (nozzle.ReinforcementPadDimensions != null)
                        dbNozzle.attributeReinforcement_pad_dimensions.SetValue(nozzle.ReinforcementPadDimensions
                            .Value);
                    if (nozzle.ReinforcementPadThickness != null)
                        dbNozzle.attributeReinforcement_pad_thickness.SetValue(nozzle.ReinforcementPadThickness.Value);
                    if (nozzle.ReinforcementPadType != null)
                        dbNozzle.attributeReinforcement_pad_type.SetValue(nozzle.ReinforcementPadType.Value);
                    if (nozzle.Type != null)
                        dbNozzle.attributeType.SetValue(nozzle.Type.Value);
                }
        }
    }
}