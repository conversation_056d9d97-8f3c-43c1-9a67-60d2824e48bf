<div class="responsive-paddings content-block">
    <h4>Dashboard</h4>

    <app-data-selection [projects]="dashboard.selectedBUProjects$ | async"
                        (selectedProjects)="onSelectedProjects($event)"
                        (refresh)="onRefresh()">
    </app-data-selection>

    <div id="layout">
        <dx-responsive-box singleColumnScreen="xs sm md">
            <!-- ROWS -->
            <dxi-row [ratio]="1"></dxi-row>
            <dxi-row [ratio]="2"></dxi-row>
            <dxi-row [ratio]="2"></dxi-row>
            <dxi-row [ratio]="2"></dxi-row>
            <dxi-row [ratio]="1"></dxi-row>

            <!-- COLUMNS -->
            <dxi-col [ratio]="1"></dxi-col>
            <dxi-col [ratio]="1"></dxi-col>

            <!-- ITEMS -->
            <dxi-item>
                <dxi-location [row]="0"
                              [col]="0"></dxi-location>
                <div *dxTemplate>
                    <app-assets-doughnut-chart [loading]="dashboard.loadingAssetsDonutChart$ | async"
                                               [chartData]="dashboard.assetsCategoriesSummary$ | async">
                    </app-assets-doughnut-chart>
                </div>

            </dxi-item>

            <dxi-item>
                <dxi-location [row]="0"
                              [col]="1"></dxi-location>
                <div *dxTemplate>
                    <app-inspection-status-doughnut-chart [loading]="dashboard.loadingInspectionStatuses$ | async"
                                                          [inspectionStatusBreakdown]="dashboard.inspectionStatuses$ | async">
                    </app-inspection-status-doughnut-chart>
                </div>
            </dxi-item>

            <dxi-item>
                <dxi-location [row]="1"
                              [col]="0"
                              [colspan]="3"></dxi-location>

                <div *dxTemplate>
                    <app-timeline [loading]="dashboard.loadingTimeline$ | async"
                                  [tasks]="dashboard.timelineTasks$ | async"
                                  [projects]="dashboard.timelineProjects$ | async">
                    </app-timeline>
                </div>
            </dxi-item>

            <dxi-item>
                <dxi-location [row]="2"
                              [col]="0"
                              [colspan]="3"></dxi-location>
                <div *dxTemplate>
                    <app-daily-inspection-count [loading]="dashboard.loadingStatusesByMonth$ | async"
                                                [inspectionStatusesByMonth]="dashboard.statusesByMonth$ | async">
                    </app-daily-inspection-count>
                </div>
            </dxi-item>

            <dxi-item>
                <dxi-location [row]="3"
                              [col]="0"
                              [colspan]="3"></dxi-location>
                <div *dxTemplate>
                    <app-task-status-by-week [loading]="dashboard.loadingStatusesByWeek$ | async"
                                             [statusesByWeek]="dashboard.statusesByWeek$ | async">
                    </app-task-status-by-week>
                </div>
            </dxi-item>

            <dxi-item>
                <dxi-location [row]="4"
                              [col]="0"
                              [colspan]="3"></dxi-location>
                <div *dxTemplate>
                    <app-equipment-by-area-and-type [chartData]="dashboard.assetsByAreaAndType$ | async"
                                                    [loading]="dashboard.loadingAssetsByAreaAndType$ | async">
                    </app-equipment-by-area-and-type>
                </div>
            </dxi-item>


        </dx-responsive-box>

        <dx-responsive-box>
            <!-- ROWS -->
            <dxi-row [ratio]="1"></dxi-row>

            <!-- COLUMNS -->
            <dxi-col [ratio]="1"></dxi-col>

            <!-- ITEMS -->
            <dxi-item>
                <dxi-location [row]="0"
                              [col]="0"></dxi-location>
                <div *dxTemplate>
                    <app-inspections-without-due-dates [loading]="dashboard.loadingNoDueDatesInspections$ | async"
                                                       [chartData]="dashboard.inspectionsWithoutDueDates$ | async">
                    </app-inspections-without-due-dates>
                </div>
            </dxi-item>
        </dx-responsive-box>

    </div>

</div>