using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Extensions;
using ClientPortal.Shared.Models;
using Dapper;
using Dapper.Contrib.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MoreLinq;
using Z.Dapper.Plus;

namespace ClientPortal.WMO.ETL.Services
{
    public class ETLService : IETLService
    {
        private readonly ILogger<IETLService> _logger;
        private readonly IETLSettingsService _settings;
        private readonly string _source;
        private readonly string _target;

        public ETLService(ILogger<IETLService> logger, IConfiguration config, IETLSettingsService settings)
        {
            _logger = logger;
            _settings = settings;
            _source = config.GetConnectionString("Source");
            _target = config.GetConnectionString("Target");
        }

        public async Task FinishJobAsync(OrdersJob ordersJob, string status)
        {
            ordersJob.EndedUTC = DateTime.UtcNow;
            ordersJob.Status = status;

            await using var targetConnection = new SqlConnection(_target);
            await targetConnection.OpenAsync();

            await targetConnection.InsertAsync(ordersJob);
        }

        public async Task BulkCopySourceToTargetAsync()
        {
            _logger.LogInformation("Bulk copying AX dump to OrdersDump table");

            var stopwatch = Stopwatch.StartNew();

            await using var sourceConnection = new SqlConnection(_source);
            await sourceConnection.OpenAsync();

            var source = await GetSourceReader(sourceConnection);

            await using var targetConnection = new SqlConnection(_target);
            await targetConnection.OpenAsync();

            using var bulkCopy = targetConnection.CreateSqlBulkCopy("OrdersDump", _logger);
            await bulkCopy.WriteToServerAsync(source);

            _logger.LogInformation($"Bulk copy took {stopwatch.Elapsed}");
        }

        public OrdersJob StartJob() => OrdersJob.CreateNewJob();

        public async Task InsertNewOrdersAsync(OrdersJob ordersJob)
        {
            _logger.LogInformation("Finding records to insert from OrdersDump table");

            await using var targetConnection = new SqlConnection(_target);
            await targetConnection.OpenAsync();

            var idsOfNewRecords = (await targetConnection.QueryAsync<string>(@"
SELECT Id FROM OrdersDump
EXCEPT
SELECT Id FROM Orders
")).ToList();

            _logger.LogInformation($"Found {idsOfNewRecords.Count} new Ids");

            var newOrders = new List<Order>();

            foreach (var batch in idsOfNewRecords.Batch(2098))
            {
                const string sql = "SELECT * FROM OrdersDump WHERE id IN @ids";
                newOrders.AddRange(await targetConnection.QueryAsync<Order>(sql, new {ids = batch}));
            }

            _logger.LogInformation("Beginning bulk insert of new records into Orders table");

            DapperPlusManager.Entity<Order>().Table("Orders");

            await targetConnection.BulkActionAsync(x => x.BulkInsert(newOrders));

            ordersJob.Inserted = newOrders.Count;
        }

        public async Task UpdateChangedOrdersAsync(OrdersJob ordersJob)
        {
            _logger.LogInformation("Finding records that have changed from OrdersDump table");

            await using var targetConnection = new SqlConnection(_target);
            await targetConnection.OpenAsync();

            var idsAndHashesOfChangedRecords = (await targetConnection.QueryAsync<Entity>(@"
SELECT Id, HASH FROM OrdersDump
EXCEPT
SELECT Id, HASH FROM Orders
")).ToList();

            _logger.LogInformation($"Found {idsAndHashesOfChangedRecords.Count} records that need an update");

            var changedOrders = new List<Order>();

            const int size = 2098; // TODO: Play with
            foreach (var batch in idsAndHashesOfChangedRecords.Batch(size))
            {
                var entities = batch.ToList();
                const string sql = "SELECT * FROM OrdersDump WHERE Id IN @ids AND HASH IN @hashes";
                var queryParameters = new
                {
                    ids = entities.Select(idAndHash => idAndHash.Id),
                    hashes = entities.Select(idAndHash => idAndHash.HASH)
                };
                var ordersBatch = await targetConnection.QueryAsync<Order>(sql, queryParameters);
                changedOrders.AddRange(ordersBatch);
            }

            _logger.LogInformation("Beginning bulk update of changed records on Orders table");

            DapperPlusManager.Entity<Order>().Table("Orders");

            await targetConnection.BulkActionAsync(x => x.BulkUpdate(changedOrders));

            ordersJob.Updated = changedOrders.Count;
        }

        public async Task DeleteRemovedOrdersAsync(OrdersJob ordersJob)
        {
            _logger.LogInformation("Finding records that need to be removed from Orders table");

            await using var targetConnection = new SqlConnection(_target);
            await targetConnection.OpenAsync();

            var idsOfRecordsToDelete = (await targetConnection.QueryAsync<string>(@"
SELECT Id FROM Orders
EXCEPT
SELECT Id FROM OrdersDump
")).ToList();

            _logger.LogInformation($"Found {idsOfRecordsToDelete.Count} records to remove from Orders table");

            var ordersToDelete = new List<Order>();

            foreach (var batch in idsOfRecordsToDelete.Batch(2098))
            {
                const string sql = "SELECT * FROM Orders WHERE Id IN @ids";
                var ordersBatch = await targetConnection.QueryAsync<Order>(sql, new {ids = batch});
                ordersToDelete.AddRange(ordersBatch);
            }

            _logger.LogInformation("Beginning bulk delete of records to remove from Orders table");

            DapperPlusManager.Entity<Order>().Table("Orders");

            await targetConnection.BulkActionAsync(x => x.BulkDelete(ordersToDelete));

            ordersJob.Deleted = ordersToDelete.Count;
        }

        public async Task SkipJobAsync()
        {
            _logger.LogInformation("Skipping execution of OrdersJob while previous OrdersJob is still in progress");

            await using var targetConnection = new SqlConnection(_target);
            await targetConnection.OpenAsync();

            var skippedJob = OrdersJob.CreateSkippedJob();

            await targetConnection.InsertAsync(skippedJob);
        }

        public async Task EnsureTargetDatabaseReadyAsync()
        {
            await using var targetConnection = new SqlConnection(_target);
            await targetConnection.OpenAsync();

            // Make sure we have an Orders table
            var ordersTableExists = await targetConnection.TableExistsAsync("Orders");
            if (!ordersTableExists)
                await targetConnection.CreateOrdersTableAsync();

            // Make sure we have an empty OrdersDump table
            if (await targetConnection.TableExistsAsync("OrdersDump"))
                await targetConnection.TruncateTableAsync("OrdersDump");
            else
                await targetConnection.CreateOrdersTableAsync("OrdersDump");

            // Make sure we have an OrdersJobs table
            var ordersJobsTableExists = await targetConnection.TableExistsAsync("OrdersJobs");
            if (!ordersJobsTableExists)
                await targetConnection.CreateOrdersJobsTableAsync();

            // Make sure we have a ETLSettings table
            var etlSettingsTableExists = await targetConnection.TableExistsAsync("ETLSettings");
            if (!etlSettingsTableExists)
                await _settings.CreateETLSettingsTableAsync();
        }

        public async Task CheckSourceData()
        {
            var checkForDataLoss = await _settings.GetCheckForDataLoss();
            if (!checkForDataLoss) return;

            var dataLossThreshold = await _settings.GetDataLossThreshold();

            await using var targetConnection = new SqlConnection(_target);
            await targetConnection.OpenAsync();

            var oldDataCount = await targetConnection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM Orders");
            var newDataCount = await targetConnection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM OrdersDump");

            if (newDataCount < dataLossThreshold * oldDataCount)
                throw new UndesirableDataException(oldDataCount, newDataCount);
        }

        private static async Task<DbDataReader> GetSourceReader(DbConnection sourceConnection)
        {
            DbDataReader source;
            try
            {
                source = await sourceConnection.GetSourceReader();
            }
            catch (SqlException e)
            {
                if (e.Message.Contains("Invalid object name 'orders-dump.dbo.TIS_WMOSPQUERYRESULTSTEMP'"))
                    throw new SourceTableUnavailableException(e);

                throw;
            }

            return source;
        }
    }
}