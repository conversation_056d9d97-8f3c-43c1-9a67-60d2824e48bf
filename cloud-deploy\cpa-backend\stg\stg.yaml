---
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: run-clientportal-api-stg-usc1
  annotations:
    run.googleapis.com/description: clientportal-api-backend-stg
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "2"
        run.googleapis.com/cloudsql-instances: oneinsight-stage-051d:us-central1:mssql-clientportal-stage-usc1
        run.googleapis.com/vpc-access-connector: projects/vpc-host-nonprod-pj334-cv215/locations/us-central1/connectors/slcon-run-dev-usc1
        run.googleapis.com/vpc-access-egress: private-ranges-only
        run.googleapis.com/startup-cpu-boost: "true"
    spec:
      serviceAccountName: <EMAIL>
      containers:
        - name: run-clientportal-api-stg-usc1
          image: us-docker.pkg.dev/oneinsight-stage-051d/ar-dotnet-us/cpa-backend:latest
          ports:
            - name: http1
              containerPort: 80
          env:
            - name: GOOGLE_CLOUD_PROJECT
              value: oneinsight-stage-051d
            - name: AnteaDb__ConnectionString
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: AnteaDb__ConnectionString
            - name: APM__Environment
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: APM__Environment
            - name: APM__Firebase
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: APM__Firebase
            - name: AzureAd__ClientSecret
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: AzureAd__ClientSecret
            - name: AzureAdB2C__ClientId
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: AzureAdB2C__ClientId
            - name: BlobStorage__APMKey
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: BlobStorage__APMKey
            - name: Clients__ClientPortal
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: Clients__ClientPortal
            - name: Connections__AuthKey
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: Connections__AuthKey
            - name: Connections__AnteaAttachmentsBucketName
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: Connections__AnteaAttachmentsBucketName
            - name: Connections__AnteaSubmissionsBucketName
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: Connections__AnteaSubmissionsBucketName
            - name: ConnectionStrings__RemoteMonitoring
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: ConnectionStrings__RemoteMonitoring
            - name: DeploymentEnvironment
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: DeploymentEnvironment
            - name: FSMConfig__Password
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: FSMConfig__Password
            - name: FSMConfig__UpdateFlowUrl
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: FSMConfig__UpdateFlowUrl
            - name: FSMConfig__Url
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: FSMConfig__Url
            - name: FSMConfig__UserName
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: FSMConfig__UserName
            - name: SendGrid__APIKey
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: SendGrid__APIKey
            - name: Swagger__ClientId
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: Swagger__ClientId
            - name: Swagger__ClientSecret
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: Swagger__ClientSecret
            - name: ZDapperPlus__LicenseKey
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: ZDapperPlus__LicenseKey
            - name: BlobStorage__APMCloudStorageProjectId
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: BlobStorage__APMCloudStorageProjectId
            - name: BlobStorage__APMCloudStorageBucketName
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: BlobStorage__APMCloudStorageBucketName
            - name: BlobStorage__APMServiceCredentialsSecretId
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: BlobStorage__APMServiceCredentialsSecretId
            - name: Connections_SecretManagerProjectId
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: Connections_SecretManagerProjectId
            - name: Connections__DatabaseName
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: Connections__DatabaseName
            - name: Connections__ProjectID
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: Connections__ProjectID
            - name: BlobStorage__APMWOCloudStorageBucketName
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: BlobStorage__APMWOCloudStorageBucketName
          resources:
            limits:
              cpu: 2000m
              memory: 8Gi
          startupProbe:
            timeoutSeconds: 240
            periodSeconds: 240
            failureThreshold: 1
            tcpSocket:
              port: 80
