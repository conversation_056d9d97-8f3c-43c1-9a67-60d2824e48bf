﻿using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class FieldWorkCompletedTransportObject
    {
        [JsonProperty("id")] public string Id { get; set; }
        [JsonProperty("projectId")] public string ProjectId { get; set; }
        [Required]
        [JsonProperty("fieldWorkCompletedDate")] public DateTime? FieldWorkCompletedDate { get; set; }

    }
}
