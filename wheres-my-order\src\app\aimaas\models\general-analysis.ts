export class GeneralAnalysis {
    clientid: string;
    clientname: string;
    locationname: string;
    locationid: string;
    assetid: string;
    componentid: string;
    drivingcomponent: string;
    retirementdate: string;
    remaininglife: string;
    drivingcml: string;
    ltcorrosionrate: string;
    stcorrosionrate: string;
    lastdate: string;
    lastvalue: string;
    previousdate: string;
    previousvalue: string;
    firstdate: string;
    firstvalue: string;
    nominalthickness: string;
    retirementthickness: string;

    constructor(options?: Partial<GeneralAnalysis>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }
    }
}
