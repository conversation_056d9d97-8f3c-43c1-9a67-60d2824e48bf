using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class AssetVM
    {
        [JsonProperty(PropertyName = "assetCategory")]
        public string AssetCategory { get; set; }

        [JsonProperty(PropertyName = "equipmentId")]
        public string EquipmentId { get; set; }

        [JsonProperty(PropertyName = "assetName")]
        public string AssetName { get; set; }

        [JsonProperty(PropertyName = "id")]
        public string Id { get; set; }

        [JsonProperty(PropertyName = "area")]
        public string Area { get; set; }

        [JsonProperty(PropertyName = "assetType")]
        public string AssetType { get; set; }

        [JsonProperty(PropertyName = "assetDescription")]
        public string AssetDescription { get; set; }

        [JsonProperty(PropertyName = "gisLocation")]
        public string GISLocation { get; set; }
        
        [JsonProperty(PropertyName = "unit")]
        public string Unit { get; set; }

        [JsonProperty(PropertyName = "workOrderIds")]
        public string[] WorkOrderIds { get; set; }

        [JsonProperty(PropertyName = "businessUnitId")]
        public string BusinessUnitId { get; set; }

        [JsonProperty(PropertyName = "locationId")]
        public string LocationId { get; set; }
    }
}