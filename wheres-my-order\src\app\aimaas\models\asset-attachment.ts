export class AssetAttachment {
    CLIENTID: string;
    CLIENTNAME: string;
    LOCATIONNAME: string;
    LOCATIONID: string;
    assetid: string;
    DOCUMENTID: string;
    FILENAME: string;
    FILETYPE: string;
    DOCUMENTTYPE: string;
    CREATEDDATE: string;
    FILELOCATION: string;
    DESCRIPTION: String;
    /* objid: number;
    objname: string;
    urlR_CREATETIME: Date;
    urlR_FILE_EXT: string;
    urlR_FILE_NAME: string;
    urlR_PROP_TYPE_DOC: string;
    urlR_RID: number;
    urlR_URL: string;
    urlaO_RID: number;
    rsitE_RID: number;*/

    constructor(options?: Partial<AssetAttachment>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }
    }
}
