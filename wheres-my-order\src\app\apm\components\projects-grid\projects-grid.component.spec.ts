import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { DxDataGridModule, DxPopupModule } from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import { ApmService } from '../../services';
import { ProjectsGridComponent } from './projects-grid.component';

describe('ProjectsGridComponent', () => {
    let component: ProjectsGridComponent;
    let fixture: ComponentFixture<ProjectsGridComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxDataGridModule,
                DxPopupModule,
                HttpClientTestingModule,
                ToastrModule.forRoot(),
                RouterTestingModule
            ],
            declarations: [ProjectsGridComponent],
            providers: [
                {
                    provide: ApmService,
                    useValue: { selectedBU$: of('123'), buSelected$: of(false) }
                }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ProjectsGridComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
