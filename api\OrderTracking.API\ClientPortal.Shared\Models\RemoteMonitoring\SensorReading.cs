﻿using System;
using ClientPortal.Shared.Models.Helpers;
using Dapper.Contrib.Extensions;
using Newtonsoft.Json;

namespace ClientPortal.Shared.Models.RemoteMonitoring
{
    [Table("SensorReadings")]
    public class SensorReading
    {
        /// <summary>
        ///     Date and Time of reading in UTC
        /// </summary>
        [JsonProperty("dateTimeUTC")]
        [ExplicitKey]
        [JsonConverter(typeof(AsUTCDateTimeConverter))]
        public DateTime DateTimeUTC { get; set; }

        /// <summary>
        ///     ?
        /// </summary>
        public string ThicknessAlarmState { get; set; }

        /// <summary>
        ///     ?
        /// </summary>
        public string CorrosionAlarmState { get; set; }

        /// <summary>
        ///     Thickness in inches
        /// </summary>
        public double? Thickness { get; set; }

        /// <summary>
        ///     Temperature compensated thickness in inches
        /// </summary>
        public double? TempCompensatedThickness { get; set; }

        /// <summary>
        ///     Material temperature in degrees Fahrenheit
        /// </summary>
        public double? MaterialTemperature { get; set; }

        /// <summary>
        ///     DSI temperature in degrees Fahrenheit
        /// </summary>
        [JsonProperty("dsiTemperature")]
        public double? DSITemperature { get; set; }

        /// <summary>
        ///     Reference velocity in in/usec
        /// </summary>
        public double? ReferenceVelocity { get; set; }

        /// <summary>
        ///     Temperature compensated velocity in in/usec
        /// </summary>
        public double? TempCompensatedVelocity { get; set; }

        /// <summary>
        ///     Corrosion rate (short term) in in/yr
        /// </summary>
        public double? CorrosionRateShortTerm { get; set; }

        /// <summary>
        ///     End of life (short term)
        /// </summary>
        public DateTime? EndOfLifeShortTerm { get; set; }

        /// <summary>
        ///     Corrosion rate (short term) in in/yr
        /// </summary>
        public double? CorrosionRateLongTerm { get; set; }

        /// <summary>
        ///     End of life (long term)
        /// </summary>
        public DateTime? EndOfLifeLongTerm { get; set; }

        /// <summary>
        ///     Thickness alarm threshold
        /// </summary>
        public double? ThicknessAlarmThreshold { get; set; }

        /// <summary>
        ///     Thickness warning threshold
        /// </summary>
        public double? ThicknessWarningThreshold { get; set; }

        /// <summary>
        ///     Corrosion alarm threshold
        /// </summary>
        public double? CorrosionAlarmThreshold { get; set; }

        /// <summary>
        ///     Corrosion warning threshold
        /// </summary>
        public double? CorrosionWarningThreshold { get; set; }

        /// <summary>
        ///     Name of company
        /// </summary>
        public string Company { get; set; }

        /// <summary>
        ///     Name of site
        /// </summary>
        public string Site { get; set; }

        /// <summary>
        ///     Name of plant
        /// </summary>
        public string Plant { get; set; }

        /// <summary>
        ///     Name of asset
        /// </summary>
        public string Asset { get; set; }

        /// <summary>
        ///     Name of collection point
        /// </summary>
        public string CollectionPoint { get; set; }

        /// <summary>
        ///     Name of TML
        /// </summary>
        [JsonProperty("tml")]
        [ExplicitKey]
        public string TML { get; set; }
    }
}