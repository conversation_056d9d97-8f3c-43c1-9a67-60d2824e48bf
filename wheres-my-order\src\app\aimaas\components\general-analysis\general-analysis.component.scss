// * {
//     font-family: Arial, sans-serif;
//     margin: 0;
//     padding: 0;
// }

.container {
    padding: 20px;
    border: 1px solid #ddd;
    margin-bottom: 20px;
}

// .header {
//     margin-bottom: 20px;
//     margin-top: 20px;
//     font-size: 20px;
//     font: roboto;
// }

// .header h3 {
//     font-size: 24px;
//     font-weight: bold;
//     display: inline-block;
// }

.analysis {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 20px;
}

.left,
.right {
    flex: 0 0 48%;
    box-sizing: border-box;
}

.left label,
.right label {
    display: block;
    font-weight: bold;
}

.left span,
.right span {
    display: block;
    margin-top: 5px;
    margin-bottom: 15px;
}

.data {
    display: flex;
    flex-direction: column;
    margin: 0px 500px 0px 42px;
}

.row {
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;
}

.row label {
    flex: 1;
    font-weight: bold;
}

.row span {
    flex: 1;
    text-align: center;
}
