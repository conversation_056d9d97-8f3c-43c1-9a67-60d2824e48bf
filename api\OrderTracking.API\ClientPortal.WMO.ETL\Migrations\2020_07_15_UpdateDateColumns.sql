UPDATE Orders
SET 
	JSSDATERECEIVED = CASE WHEN JSSDATERECEIVED = '01/01/1900' THEN NULL ELSE JSSDATERECEIVED END
	,JSSDATEDESIGNED = CASE WHEN JSSDATEDESIGNED = '01/01/1900' THEN NULL ELSE JSSDATEDESIGNED END
	,JSSDATECONVERTED = CASE WHEN JSSDATECONVERTED = '01/01/1900' THEN NULL ELSE JSSDATECONVERTED END
	,JSSDATEVERIFIED = CASE WHEN JSSDATEVERIFIED = '01/01/1900' THEN NULL ELSE JSSDATEVERIFIED END
	,JSSDATETOMFG = CASE WHEN JSSDATETOMFG = '01/01/1900' THEN NULL ELSE JSSDATETOMFG END
	,CREATEDDATETIME = CASE WHEN CREATEDDATETIME = '01/01/1900' THEN NULL ELSE CREATEDDATETIME END
	,SCHEDSTART = CASE WHEN SCHEDSTART = '01/01/1900' THEN NULL ELSE SCHEDSTART END
	,SCHEDEND = CASE WHEN SCHEDEND = '01/01/1900' THEN NULL ELSE SCHEDEND END
	,FINISHEDDATE = CASE WHEN FINISHEDDATE = '01/01/1900' THEN NULL ELSE FINISHEDDATE END
	,SHIPPINGDATECONFIRMED = CASE WHEN SHIPPINGDATECONFIRMED = '01/01/1900' THEN NULL ELSE SHIPPINGDATECONFIRMED END
	,DELIVERYDATE = CASE WHEN DELIVERYDATE = '01/01/1900' THEN NULL ELSE DELIVERYDATE END
	,SHIPDATE = CASE WHEN SHIPDATE = '01/01/1900' THEN NULL ELSE SHIPDATE END
	,CONFIRMEDRECEIPTDATE = CASE WHEN CONFIRMEDRECEIPTDATE = '01/01/1900' THEN NULL ELSE CONFIRMEDRECEIPTDATE END



	