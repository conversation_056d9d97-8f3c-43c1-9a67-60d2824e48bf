import { Component, EventEmitter, Input, Output } from '@angular/core';
import cloneDeep from 'clone-deep';
import { DxGalleryComponent, DxTextAreaComponent } from 'devextreme-angular';
import dxForm from 'devextreme/ui/form';
import { saveAs } from 'file-saver';
import { firstValueFrom } from 'rxjs';
import { IMediaEntry } from '../../../shared/models/attributes';
import {
    AssetPath,
    LeakReportWorkDetails,
    PhotoDelete,
    PhotoDescriptionUpdate
} from '../../models';
import { ApmService } from '../../services';

@Component({
    selector: 'app-work-details-tab',
    templateUrl: './work-details-tab.component.html',
    styleUrls: ['./work-details-tab.component.scss']
})
export class WorkDetailsTabComponent {
    private _originalPhotoDescription: string | undefined;
    private _workDetails: LeakReportWorkDetails;
    private _original: LeakReportWorkDetails;
    private _changes: Partial<LeakReportWorkDetails> = {};

    assetPathLoadingCompleted = false;
    assetPathsArray: AssetPath = {
        allPhotos: {},
        workDetailPhotos: {
            client: undefined,
            state: undefined,
            city: undefined,
            area: undefined,
            facility: undefined,
            lease: undefined,
            clientContact: undefined,
            clientWorkOrder: undefined,
            purchaseOrder: undefined,
            teamProjectNumber: undefined,
            referenceEdition: undefined,
            inspectionTypes: undefined,
            inspectedBy: undefined,
            postalCode: undefined,
            jobDescription: undefined,
            clientContactNumber: undefined,
            teamDistrict: undefined,
            clientCostCode: undefined,
            inspectionReference: undefined,
            inspectionDate: undefined,
            inspectorCertificateNumber: undefined,
            reviewedBy: undefined,
            reviewerEmail: undefined,
            reviewerCertificateNumber: undefined
        }
    };

    @Output() saving = new EventEmitter<Partial<LeakReportWorkDetails>>();
    @Output() photoDescriptionUpdate =
        new EventEmitter<PhotoDescriptionUpdate>();
    @Output() photoDelete = new EventEmitter<PhotoDelete>();

    @Input() allowEditing: boolean;
    @Input() set workDetails(value: LeakReportWorkDetails) {
        this._workDetails = value;
        this._original = cloneDeep(this._workDetails);
        this._changes = {};
        this.allPhotos = [
            ...value.client.photos,
            ...value.state.photos,
            ...value.city.photos,
            ...value.area.photos,
            ...value.facility.photos,
            ...value.lease.photos,
            ...value.clientContact.photos,
            ...value.clientWorkOrder.photos,
            ...value.purchaseOrder.photos,
            ...value.teamProjectNumber.photos,
            ...value.referenceEdition.photos,
            ...value.inspectionTypes.photos,
            ...value.inspectedBy.photos,
            ...value.postalCode.photos,
            ...value.jobDescription.photos,
            ...value.clientContactNumber.photos,
            ...value.teamDistrict.photos,
            ...value.clientCostCode.photos,
            ...value.inspectionReference.photos,
            ...value.inspectionDate.photos,
            ...value.inspectorCertificateNumber.photos,
            ...value.reviewedBy.photos,
            ...value.reviewerEmail.photos,
            ...value.reviewerCertificateNumber.photos
        ];
        this.updateAssetPaths();
    }
    get workDetails(): LeakReportWorkDetails {
        return this._workDetails;
    }

    isEditing: boolean;
    showPhotoPopup: boolean;
    popupGallerySelectedIndex = 0;
    isEditingPhotoDescription = false;
    allPhotos: IMediaEntry[] | undefined;

    constructor(private readonly _apm: ApmService) {}

    onEditClicked(e) {
        this.isEditing = true;
    }

    onCancelClicked(e) {
        this.isEditing = false;
        this.workDetails = this._original;
    }

    onSaveClicked(e) {
        this.isEditing = false;
        this.saving.next(this._changes);
    }

    thumbnailDoubleClicked(e: MouseEvent, gallery: DxGalleryComponent) {
        this.showPhotoPopup = !this.showPhotoPopup;
        const index = this.allPhotos.findIndex(
            (p) => p.databaseId === gallery.selectedItem.databaseId
        );
        this.popupGallerySelectedIndex = index;
    }

    onFieldDataChanged(e: {
        dataField: string;
        value: any;
        component: dxForm;
    }) {
        if (!e.dataField.includes('.')) return;
        const [field, valueOrComment] = e.dataField.split('.');
        this._changes = {
            ...this._changes,
            [field]: { ...this._changes[field], [valueOrComment]: e.value }
        };
    }

    onEditDescriptionClicked(e, currentValue: string) {
        this.isEditingPhotoDescription = true;
        this._originalPhotoDescription = currentValue;
    }

    onDescriptionSave(e, photoInfo: IMediaEntry, description: string) {
        const update: PhotoDescriptionUpdate = {
            reportId: this._workDetails.id,
            photoDatabaseId: photoInfo.databaseId,
            description
        };
        this.photoDescriptionUpdate.next(update);
        this.isEditingPhotoDescription = false;
    }

    onDescriptionCancel(e, editor: DxTextAreaComponent) {
        this.isEditingPhotoDescription = false;
        editor.instance.option('value', this._originalPhotoDescription);
    }

    onDeletePhotoClicked(e, photoInfo: IMediaEntry) {
        const photoTransport: PhotoDelete = {
            reportId: this._workDetails.id,
            photoDatabaseId: photoInfo.databaseId
        };
        this.photoDelete.next(photoTransport);
    }

    async onDownloadClicked(fileName: string) {
        const url = await firstValueFrom(this._apm.getSignedUrl(fileName));
        const blob = await this._apm.downloadFileFromUrl(url).toPromise();
        saveAs(blob, fileName);
    }

    getAssetImage(type: string, blobPath: string) {
        if (type) {
            return this.assetPathsArray.workDetailPhotos[type][blobPath]
                ? this.assetPathsArray.workDetailPhotos[type][blobPath]
                : '';
        } else {
            return this.assetPathsArray.allPhotos[blobPath]
                ? this.assetPathsArray.allPhotos[blobPath]
                : '';
        }
    }

    async updateAssetPaths() {
        const subTypes = [
            'client',
            'state',
            'city',
            'area',
            'facility',
            'lease',
            'clientContact',
            'clientWorkOrder',
            'purchaseOrder',
            'teamProjectNumber',
            'referenceEdition',
            'inspectionTypes',
            'inspectedBy',
            'postalCode',
            'jobDescription',
            'clientContactNumber',
            'teamDistrict',
            'clientCostCode',
            'inspectionReference',
            'inspectionDate',
            'inspectorCertificateNumber',
            'reviewedBy',
            'reviewerEmail',
            'reviewerCertificateNumber'
        ];
        subTypes.forEach((subType) => {
            this._workDetails[subType].photos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.workDetailPhotos[subType][photo.blobName] =
                    assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        });
        this.assetPathLoadingCompleted = true;
    }
}
