<!-- TREE VIEW AND NAVIGATION -->
<div class="dx-card  content-block responsive-paddings selection-tree">
    <h4>APM Clients</h4>
    <hr>
    <dx-toolbar>
        <dxi-item widget="dxDropDownButton"
                  location="before"
                  [options]="{text: 'Add', items: ['New Client', 'New Business Unit'], dropDownOptions: { width: 150 }, onItemClick: onAddNewClientOrSite}">
        </dxi-item>
        <dxi-item style="display: none;"
                  widget="dxButton"
                  location="after"
                  [disabled]="true"
                  [options]="{icon: 'trash', onClick: removeCurrentNode}">
        </dxi-item>
        <dxi-item widget="dxButton"
                  location="after"
                  [options]="{icon: 'refresh', onClick: refresh}"></dxi-item>
    </dx-toolbar>

    <dx-tree-view #treeview
                  [dataSource]="clients$ | async"
                  [searchEnabled]="true"
                  [searchExpr]="['text', 'parentText']"
                  [selectionMode]="'single'"
                  [showCheckBoxesMode]="'none'"
                  [selectByClick]="true"
                  (onSelectionChanged)="onSelectionChanged($event)">
    </dx-tree-view>

</div>

<!-- FORM + SELECTION DETAILS/EDITING -->
<div *ngIf="(selectedNode || currentCardAction)"
     class="dx-card content-block responsive-paddings"
     style="display: flex; flex-direction: column;"
     [ngSwitch]="currentCardAction">

    <!-- NEW CLIENT FORM -->
    <div *ngSwitchCase="'NEW-CLIENT'"
         class="form">
        <div class="form-body">
            <h4>
                <span class="fa-stack fa-lg">
                    <i class="fa fa-circle fa-stack-2x"></i>
                    <i class="fa fa-cogs fa-stack-1x fa-inverse"></i>
                </span>
                Create New Client
            </h4>

            <hr>

            <form (submit)="newClientSubmit($event, newClientForm)"
                  style="display: flex; flex-direction: column">
                <dx-form #newClientForm
                         [showValidationSummary]="true">
                    <dxi-item [dataField]="'clientName'">
                        <dxi-validation-rule type="required"
                                             message="Client name is required">
                        </dxi-validation-rule>
                    </dxi-item>
                    <dxi-item itemType="button"
                              [buttonOptions]="newClientButtonOptions$ | async">
                    </dxi-item>
                </dx-form>
            </form>
        </div>
    </div>

    <!-- NEW BUSINESS UNIT FORM -->
    <div *ngSwitchCase="'NEW-BU'"
         class="form">
        <div class="form-body">
            <h4>
                <span class="fa-stack fa-lg">
                    <i class="fa fa-circle fa-stack-2x"></i>
                    <i class="fa fa-cogs fa-stack-1x fa-inverse"></i>
                </span>
                Create New
                {{form.instance?.getEditor('client')?.option('value')?.text}}
                Business Unit
            </h4>

            <hr>

            <form #form
                  (submit)="newBUSubmit($event, newBUForm)"
                  style="display: flex; flex-direction: column">
                <dx-form #newBUForm
                         [showValidationSummary]="true">
                    <dxi-item dataField="client"
                              editorType="dxSelectBox"
                              [editorOptions]="{items: treeview.items, displayExpr: 'text'}">
                        <dxi-validation-rule type="required"
                                             message="Client is required">
                        </dxi-validation-rule>
                    </dxi-item>
                    <dxi-item [dataField]="'siteName'">
                        <dxi-validation-rule type="required"
                                             message="Site name is required">
                        </dxi-validation-rule>
                        <dxi-validation-rule type="async"
                                             [validationCallback]="uniqueBUNameNewBU"
                                             message="Business Unit name is already in use">
                        </dxi-validation-rule>
                    </dxi-item>
                </dx-form>

                <h5>Users</h5>
                <dx-tag-box #businessUnitNewUsers
                            [dataSource]="availableUsers$ | async"
                            [searchEnabled]="true"
                            [displayExpr]="apmUserDisplayExpr"
                            [acceptCustomValue]="false"
                            [applyValueMode]="'useButtons'"></dx-tag-box>

                <h5>Districts <small><em>Coming soon</em></small></h5>
                <dx-tag-box #businessUnitNewDistricts
                            [disabled]="true"
                            [dataSource]="availableDistricts$ | async"
                            [searchEnabled]="true"
                            [displayExpr]="districtDisplayExpr"
                            [acceptCustomValue]="false"
                            [applyValueMode]="'useButtons'"></dx-tag-box>

                <div class="actions"
                     style="flex-shrink: 0; justify-self: flex-end; align-self: flex-end">

                    <dx-button text="Cancel"
                               type="normal"
                               stylingMode="outlined"
                               [disabled]="isSaving$ | async"
                               (onClick)="newBusinessUnitCancelClicked($event)">
                    </dx-button>
                    <dx-button text="Save"
                               type="success"
                               stylingMode="contained"
                               [disabled]="isSaving$ | async"
                               [useSubmitBehavior]="true">
                    </dx-button>
                </div>
            </form>
        </div>
    </div>

    <!-- EXISTING CLIENT FORM -->
    <div *ngSwitchCase="'EDIT-CLIENT'"
         class="form">
        <div class="form-body">
            <h4>
                <span class="fa-stack fa-lg">
                    <i class="fa fa-circle fa-stack-2x"></i>
                    <i
                       class="fa fa-{{(isEditing$ | async) ? 'cogs' : 'users'}} fa-stack-1x fa-inverse"></i>
                </span>
                <dx-form #existingClientForm
                         [formData]="selectedClient"
                         *ngIf="isEditing$ | async; else notEditingClient"
                         style="display: inline-block">
                    <dxi-item dataField="text">
                        <dxo-label [visible]="false"></dxo-label>
                        <dxi-validation-rule type="required"
                                             message="Client name is required">
                        </dxi-validation-rule>
                    </dxi-item>
                </dx-form>
                <ng-template #notEditingClient>
                    {{selectedClient.text}}
                </ng-template>
                <br>
                <small><em>Id: {{selectedClient.id}}</em></small>
            </h4>

            <hr>

            <h5>Business Units</h5>

            <dx-list [dataSource]="selectedClient.items"
                     [selectionMode]="'none'"></dx-list>

            <hr>

            <div class="actions"
                 style="flex-shrink: 0; justify-self: flex-end; align-self: flex-end">
                <dx-button *ngIf="(isEditing$ | async) === false"
                           text="Edit"
                           type="default"
                           stylingMode="contained"
                           (onClick)="editClientClicked($event)">
                </dx-button>

                <dx-button *ngIf="isEditing$ | async"
                           text="Cancel"
                           type="normal"
                           stylingMode="outlined"
                           [disabled]="isSaving$ | async"
                           (onClick)="editClientCancelClicked($event)">
                </dx-button>
                <dx-button *ngIf="isEditing$ | async"
                           text="Save"
                           type="success"
                           stylingMode="contained"
                           [disabled]="isSaving$ | async"
                           (onClick)="saveExistingClientClicked($event)">
                </dx-button>
            </div>
        </div>
    </div>

    <!-- EXISTING BUSINESS UNIT FORM -->
    <div *ngSwitchCase="'EDIT-BU'"
         class="form">
        <div class="form-body">
            <h4>
                <span class="fa-stack fa-lg">
                    <i class="fa fa-circle fa-stack-2x"></i>
                    <i
                       class="fa fa-{{(isEditing$ | async) ? 'cogs' : 'users'}} fa-stack-1x fa-inverse"></i>
                </span>
                {{selectedBusinessUnit.parentText}}
                <dx-form #existingBUForm
                         [formData]="selectedBusinessUnit"
                         *ngIf="isEditing$ | async; else notEditingBusinessUnit"
                         style="display: inline-block">
                    <dxi-item dataField="text">
                        <dxo-label [visible]="false"></dxo-label>
                        <dxi-validation-rule type="required"
                                             message="Site name is required">
                        </dxi-validation-rule>
                        <dxi-validation-rule type="async"
                                             [validationCallback]="uniqueBUNameExistingBU"
                                             message="Business Unit name is already in use">
                        </dxi-validation-rule>
                    </dxi-item>

                </dx-form>
                <ng-template #notEditingBusinessUnit>
                    {{selectedBusinessUnit.text}}
                </ng-template>
                <br>
                <small><em>Id: {{selectedBusinessUnit.id}}</em></small>
            </h4>

            <hr>

            <h5>Users</h5>
            <dx-tag-box #businessUnitNewUsers
                        *ngIf="isEditing$ |async"
                        [dataSource]="availableUsers$ | async"
                        [searchEnabled]="true"
                        [readOnly]="(isEditing$ | async) === false"
                        [displayExpr]="apmUserDisplayExpr"
                        [acceptCustomValue]="false"
                        [applyValueMode]="'useButtons'"></dx-tag-box>
            <dx-list #businessUnitUsers
                     [style.flexBasis]="selectedBusinessUnit.users?.length > 0 ? '200px' : 'auto'"
                     [dataSource]="selectedBusinessUnit.users"
                     [displayExpr]="apmUserDisplayExpr"
                     [activeStateEnabled]="isEditing$ | async"
                     [allowItemDeleting]="isEditing$ | async"
                     (onContentReady)="onBUUsersContentReady($event)"
                     (onItemDeleting)="onBUUserRemoving($event)">
            </dx-list>

            <!-- TODO: Districts are not being implemented at this time. -->
            <h5>Districts <small><em>Coming soon</em></small></h5>
            <dx-tag-box #businessUnitNewDistricts
                        [disabled]="true"
                        *ngIf="isEditing$ | async"
                        [dataSource]="availableDistricts$ | async"
                        [searchEnabled]="true"
                        [readOnly]="(isEditing$ | async) === false"
                        [displayExpr]="districtDisplayExpr"
                        [acceptCustomValue]="false"
                        [applyValueMode]="'useButtons'"></dx-tag-box>
            <dx-list #businessUnitDistricts
                     [disabled]="true"
                     [style.flexBasis]="selectedBusinessUnit.districts?.length > 0 ? '200px' : 'auto'"
                     [dataSource]="selectedBusinessUnit.districts"
                     [displayExpr]="districtDisplayExpr"
                     [activeStateEnabled]="isEditing$ | async"
                     [allowItemDeleting]="isEditing$ | async"
                     (onContentReady)="onBUDistrictsContentReady($event)"
                     (onItemDeleting)="onBUDistrictRemoving($event)">
            </dx-list>
            <h5>Groups <small><em>Coming soon</em></small></h5>
            <!-- TODO: Groups are not being implemented at this time. -->
            <dx-tag-box *ngIf="isEditing$ | async"
                        [disabled]="true"></dx-tag-box>
            <dx-list [disabled]="true"
                     [style.flexBasis]="selectedBusinessUnit.groups?.length > 0 ? '200px' : 'auto'"
                     [dataSource]="selectedBusinessUnit.groups"
                     [activeStateEnabled]="isEditing$ | async"
                     [allowItemDeleting]="isEditing$ | async"></dx-list>

            <div class="actions"
                 style="flex-shrink: 0; justify-self: flex-end; align-self: flex-end">
                <dx-button *ngIf="(isEditing$ | async) === false"
                           text="Edit"
                           type="default"
                           stylingMode="contained"
                           (onClick)="editBUClicked($event)">
                </dx-button>

                <dx-button *ngIf="isEditing$ | async"
                           text="Cancel"
                           type="normal"
                           stylingMode="outlined"
                           [disabled]="isSaving$ | async"
                           (onClick)="editBusinessUnitCancelClicked($event)">
                </dx-button>
                <dx-button *ngIf="isEditing$ | async"
                           text="Save"
                           type="success"
                           stylingMode="contained"
                           [disabled]="isSaving$ | async"
                           (onClick)="saveExistingBusinessUnitClicked($event)">
                </dx-button>
            </div>
        </div>
    </div>
</div>