﻿//using OrderTracking.API.Interfaces.Services;

//namespace OrderTracking.API.Interfaces
//{
//    /// <summary>
//    ///     Placeholder interface for EDR BlobStorageService.  This way we can
//    ///     have multiple IBlobStorageService instances available in dependency
//    ///     injection.
//    /// </summary>
//    public interface IEDRBlobStorageService : IBlobStorageService
//    {

//    }
//}