﻿using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using OrderTracking.API.Repositories;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     Service class to provide access to user profiles
    /// </summary>
    public interface IUserProfilesService : IUserProfileRepository
    {
        #region Public Methods

        /// <summary>
        ///     Update user profile, including the id (WHY?)
        /// </summary>
        /// <param name="userProfile"></param>
        /// <param name="originalId"></param>
        /// <returns></returns>
        Task<UserProfile> UpdateItemIncludingIdAsync(UserProfile userProfile, string originalId);

        /// <summary>
        ///     Add a role to a user profile
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        Task<UserProfile> AddRoleToUser(string userId, string roleId);

        /// <summary>
        ///     Delete multiple users
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task DeleteUsersAsync(string[] ids);

        #endregion
    }
}