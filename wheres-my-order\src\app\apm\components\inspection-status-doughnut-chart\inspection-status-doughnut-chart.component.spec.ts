import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxLoadIndicatorModule } from 'devextreme-angular';
import { DxPieChartModule } from 'devextreme-angular/ui/pie-chart';
import { DashboardService } from '../../services';
import { InspectionStatusDoughnutChartComponent } from './inspection-status-doughnut-chart.component';

describe('InspectionStatusDoughnutChartComponent', () => {
    let component: InspectionStatusDoughnutChartComponent;
    let fixture: ComponentFixture<InspectionStatusDoughnutChartComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DxPieChartModule, DxLoadIndicatorModule],
            declarations: [InspectionStatusDoughnutChartComponent],
            providers: [{ provide: DashboardService, useValue: {} }]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(
            InspectionStatusDoughnutChartComponent
        );
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
