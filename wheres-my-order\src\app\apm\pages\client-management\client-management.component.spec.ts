import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
    DxButtonModule,
    DxFormModule,
    DxListModule,
    DxTagBoxModule,
    DxTextBoxModule,
    DxToolbarModule,
    DxTreeViewModule
} from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import { DistrictService } from '../../../shared/services';
import { ApmService, ClientManagementService } from '../../services';
import { ClientManagementComponent } from './client-management.component';

describe('ClientManagementComponent', () => {
    let component: ClientManagementComponent;
    let fixture: ComponentFixture<ClientManagementComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxToolbarModule,
                DxTreeViewModule,
                DxFormModule,
                DxTextBoxModule,
                DxButtonModule,
                DxTagBoxModule,
                DxListModule,
                ToastrModule.forRoot(),
                HttpClientTestingModule
            ],
            declarations: [ClientManagementComponent],
            providers: [
                ClientManagementService,
                { provide: ApmService, useValue: { getUsers: () => of([]) } },
                {
                    provide: DistrictService,
                    useValue: { getDistricts: () => of([]) }
                }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ClientManagementComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
