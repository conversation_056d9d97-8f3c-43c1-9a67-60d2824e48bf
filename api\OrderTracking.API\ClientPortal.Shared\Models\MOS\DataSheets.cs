﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using Newtonsoft.Json;

namespace ClientPortal.Shared.Models.MOS
{
    [Table("DataSheetPackages")]
    public class DataSheetPackage
    {
        public int Id { get; set; }
        public string JssNumber { get; set; }
        public string ClientName { get; set; }
        public DateTime Date { get; set; }
        public string TEAMClientNumber { get; set; }
        [ForeignKey(nameof(DataSheet.PackageId))]
        public List<DataSheet> DataSheets { get; set; } = new List<DataSheet>();
    }

    public abstract class DataSheet
    {
        public int Id { get; set; }
        [ForeignKey(nameof(Package))]
        public int PackageId { get; set; }
        [JsonIgnore]
        public DataSheetPackage Package { get; set; }

        public string Type => GetType().Name;
    }

    [Table("DimensionalSheets")]
    public abstract class DimensionalSheet : DataSheet
    {
        public string GivenBy { get; set; }
        public string Plant { get; set; }
        public string Unit { get; set; }
        public string CheckedBy { get; set; }
        public DateTime CheckedByDate { get; set; }
        public string SurfaceCondition { get; set; }
        public string LineSize { get; set; }
        public int SeverityOfLeak { get; set; }
        public string ShipTo { get; set; }
        public SealType SealType { get; set; }
        public string LineSkinTemp { get; set; }
        public string OtherSkinTemp { get; set; }
    }

    [Flags]
    public enum SealType
    {
        Crunch = 1,
        Packing = 2,
        VoidFill = 4,
        Tongue = 8,
        Tubing = 16,
        PerimeterSeal = 32,
        Other = 64
    }

    [Flags]
    public enum FlgApp
    {
        Line = 1,
        ValveEnd = 2,
        Bonnet = 4,
        Exchanger = 8,
        SafetyValve = 16,
        Other = 32
    }

    [Table("LeakRepairHardwareSpecificationSheets")]
    public class LeakRepairHardwareSpecificationSheet : DataSheet
    {
        // Location
        public string Site { get; set; }
        public string LineNo { get; set; }
        public string DistrictWorkOrderNo { get; set; }
        public string TeamCjNo { get; set; }
        public string Technician1 { get; set; }
        public string Technician2 { get; set; }


        // Service and Piping Details
        public bool LineMaterial { get; set; }
        public int MainLineSize { get; set; }
        public string BranchSize { get; set; }
        public int LineSchedule { get; set; }
        public string LineContent { get; set; }
        public int LineContentPercentage { get; set; }
        public int SystemDesignTemp { get; set; }
        public int MaxSystemDesignTemp { get; set; }
        public int SystemOpTemp { get; set; }
        public int MaxSystemOpTemp { get; set; }
        public int SysDesignPressure { get; set; }
        public int SysOpPressure { get; set; }
        public int MaxSysOpPressure { get; set; }
        public int MDMT { get; set; }
        public int Grade { get; set; }
        public int Frequency { get; set; }
        public bool Vibration { get; set; }
        public bool SourService { get; set; }

        // Defect
        public bool DefectCause { get; set; }
        public bool DefectType { get; set; }
        public string OtherDefect { get; set; }
        public int RemainingWallThickness { get; set; }
        public bool NdtDataProvided { get; set; }

        // Repair Requirements
        public DateTime ExpectedRemovalDate { get; set; }
        public int ReqRepairCorrosionAllowance { get; set; }
        public bool IfDosed { get; set; }
        public bool SuperheatedSteam { get; set; }
        public bool SprayTypeDesuperheater { get; set; }
        public bool Strongback { get; set; }
        public int EnclosureQuantity { get; set; }
        public string SealantSelection { get; set; }
        public string EnclosureMaterial { get; set; }
        public string ClientSpecificRequirements { get; set; }

        // Job Processing and Client Sign-Off
        public bool ProcessingRequirements { get; set; }
        public DateTime ApprovalInformationDeadline { get; set; }
        public DateTime RequestedInstallationDate { get; set; }
        public string ApprovalEmails { get; set; }
        public bool QualityRequirements { get; set; }
        public bool EngineeringPriorityLevel { get; set; }
        public string PrintName { get; set; }
        public string Email { get; set; }
        public int Telephone { get; set; }
        public string OrderNo { get; set; }
    }

    [Table("HtsEngineeringDataCoverSheets")]
    public class HtsEngineeringDataCoverSheet : DataSheet
    {
        // General Information
        public string DistrictProjectId { get; set; }
        public string SafetyReviewNo { get; set; }
        public string Location { get; set; }
        public string HtsTechSupportRep { get; set; }
        public string BallParkNo { get; set; }
        public string ContactInfo { get; set; }
        public DateTime RequestedDeliveryDate { get; set; }


        // Service and Piping Details
        public bool ForImmediateManufacture { get; set; }
        public bool PriceQuote { get; set; }
        public bool CalculationPackage { get; set; }
        public bool Routine { get; set; }
        public bool WaitforClientApproval { get; set; }
        public bool BallPark { get; set; }
        public bool PeStampRequired { get; set; }
        public bool Priority { get; set; }

        // Existing Line Conditions
        public int LineSize { get; set; }
        public int Pressure { get; set; }
        public string Material { get; set; }
        public int LineSchedule { get; set; }
        public int Temperature { get; set; }
        public string Service { get; set; }
        public string FlangeRating { get; set; }
        public bool NuclearNaceRequired { get; set; }
        public bool LineLocation { get; set; }

        // Design Criteria
        public int DesignPressure { get; set; }
        public string FittingMaterial { get; set; }
        public int Quantity { get; set; }
        public int DesignTemperature { get; set; }
        public bool FlangeRatingList { get; set; } //has a list of options
        public int MDMT { get; set; }
        public string Other { get; set; }
        public int CorrosionAllowance { get; set; }
        public bool CutterDiameter { get; set; }
        public bool DesignCode { get; set; } //multiple selections
        public bool DesignFactor { get; set; } // multiple selections
        public bool RunLengthType { get; set; }
        public bool BranchHeightType { get; set; }
        public int RunLength { get; set; }
        public int BranchHeight { get; set; }
        public string HotTap { get; set; }
        public string StockFitting { get; set; }
        public string PartNumber { get; set; }

        // Weld on Fittings - Section 1

        //
        public bool NotApplicable { get; set; }
        public bool PressureRatainingSplitTee { get; set; }
        public bool NozzleWithRepad { get; set; }
        public bool SphericalTee { get; set; }
        public bool NozzleWithFullEncirclementSaddle { get; set; }
        public bool NozzleOnly { get; set; }
        public bool RepadOnly { get; set; }
        public bool WeldOLet { get; set; }
        public bool FullEncorclementSaddleOnly { get; set; }
        public bool FullEncirclementSaddle { get; set; }

        //
        public bool Part1 { get; set; }
        public bool Part2 { get; set; }
        public bool Part3 { get; set; }
        public bool Part4 { get; set; }
        public bool Part5 { get; set; }

        //
        public bool WeepHole { get; set; }
        public bool NptPortSize { get; set; }

        // Bolt on Fitttings - Section 2//
        public bool NotApplicable2 { get; set; }
        public bool PerimeterSeal { get; set; }
        public bool SelfSeal { get; set; }
        public bool StrongbacksRequired { get; set; }
        public bool SealantType { get; set; }
        public bool ClientSpecified { get; set; }

        //Branch Information - Section 3
        public string Size { get; set; }
        public bool BranchEndOption { get; set; }
        public int WallThickness { get; set; }
        public bool Standard { get; set; }
        public bool ClientSpecified3 { get; set; }

        //Branch Information - Section 4
        public string Size4 { get; set; }
        public bool FlangeRF { get; set; }
        public bool FlangeRTJ { get; set; }
        public bool ButtWeld { get; set; }
        public bool Threaded { get; set; }
        public int WallThickness4 { get; set; }
        public bool Standard4 { get; set; }
        public bool ClientSpecified4 { get; set; }
        public bool ParallelToRun { get; set; }
        public bool PerpendicularToRun { get; set; }

        //Completion Plug - Section 5
        public bool NotRequired5 { get; set; }
        public bool StandardCompletionPlugWithoutScarfedNipple { get; set; }
        public bool StandardCompletionPlugWithScarfedNipple { get; set; }
        public int FlowThroughPlugWithPigBars { get; set; }
        public bool SolidCompletionPlugWithPigBars { get; set; }
        public int MinValveBore { get; set; }
        public bool Buna { get; set; }
        public bool Viton { get; set; }
        public bool Aflas { get; set; }
        public bool EPDM { get; set; }
        public bool MetalToMetalSeal { get; set; }
        public bool HSS2Seal { get; set; }

        //Hi Stop,Sure Stop, and HTP - Section 6
        public bool HiStop { get; set; }
        public bool WeldOn { get; set; }
        public bool FlangeWithPlug { get; set; }
        public bool SureStop { get; set; }
        public bool BoltOn { get; set; }
        public bool PermanentValve { get; set; }
        public bool HTP { get; set; }
        public bool DrawWorksNotRequired { get; set; }
        public bool DrawWorksforSettingCompletion { get; set; }
        public bool PermanentDrawWorksHold { get; set; }

        //Hi Stop,Sure Stop, and HTP - Section 7
        public bool WeldOn7 { get; set; }
        public bool BoltOn7 { get; set; }
        public bool StandardFlange { get; set; }
        public bool LineStopFlange { get; set; }
        public bool BranchWithInternalThreads { get; set; }

        //Angle or Elbow Tap - Section 8
        public bool ElbowLet { get; set; }
        public bool NozzleOnly7 { get; set; }
        public bool ShortRadius { get; set; }
        public bool LongRadius { get; set; }
        public bool ElbowOnHeel { get; set; }
        public bool ElbowStraight { get; set; }
        public bool Angle { get; set; }
        public int AngleDegree { get; set; }


        //Across-the-line-Stop - Section 9
        public bool WeldOn9 { get; set; }
        public bool BoltOn9 { get; set; }
        public string Size9 { get; set; }
        public string Other9 { get; set; }
        public int WallThickness9 { get; set; }
        public bool RubberSeal { get; set; }
        public bool HSS2Seal9 { get; set; }
        public bool DrawWorksNotRequired9 { get; set; }
        public int DrawWorksforSettingPlug9 { get; set; }
        public int PermanentMachineAndValveRemoved { get; set; }

        //Material Request
        public int CEPercentage { get; set; }
        public int YieldStrength { get; set; }
        public string MaterialOrigin { get; set; }
        public string MaterialToughness { get; set; }
        public int MaximumHardness { get; set; }
        public string AdditionalNotes { get; set; }

        //Welding Request 
        public string PWHT { get; set; }
        public string BackingStrips { get; set; }
        public string Additional { get; set; }

        //QA Request
        public string ThirdPartyInspection { get; set; }
        public bool NoPressureTest { get; set; }
        public bool FieldTest { get; set; }
        public bool ShopTest { get; set; }
        public int HoldTime { get; set; }
        public int TestPressure { get; set; }
        public bool Primer { get; set; }
        public bool None { get; set; }
        public string Other10 { get; set; }
        public bool BlindFlange { get; set; }
        public bool PortInFlange { get; set; }
        public bool Studs { get; set; }
        public bool Gasket { get; set; }
        public string PortSize { get; set; }
        public string Grade { get; set; }
        public string GasketType { get; set; }
        public string Notes10 { get; set; }
    }

    [Table("DS100s")]
    public class DS100 : DimensionalSheet
    {
        public FlgApp FlgApp { get; set; }

        public double Flange1Width { get; set; }
        public double Flange2Width { get; set; }
        public int FirstDesiredEarLocationPoint { get; set; }
        public int SecondDesiredEarLocationPoint { get; set; }

        public double Flange1OuterDiameter12To6 { get; set; }
        public double Flange1OuterDiameter1To7 { get; set; }
        public double Flange1OuterDiameter2To8 { get; set; }
        public double Flange1OuterDiameter3To9 { get; set; }
        public double Flange1OuterDiameter4To10 { get; set; }
        public double Flange1OuterDiameter5To11 { get; set; }

        public double Flange2OuterDiameter12To6 { get; set; }
        public double Flange2OuterDiameter1To7 { get; set; }
        public double Flange2OuterDiameter2To8 { get; set; }
        public double Flange2OuterDiameter3To9 { get; set; }
        public double Flange2OuterDiameter4To10 { get; set; }
        public double Flange2OuterDiameter5To11 { get; set; }

        public int FirstStudDepthPoint { get; set; }
        public int SecondStudDepthPoint { get; set; }
        public int ThirdStudDepthPoint { get; set; }
        public int FourthStudDepthPoint { get; set; }
        public double FirstStudDepthForFlange1 { get; set; }
        public double SecondStudDepthForFlange1 { get; set; }
        public double ThirdStudDepthForFlange1 { get; set; }
        public double FourthStudDepthForFlange1 { get; set; }
        public double FirstStudDepthForFlange2 { get; set; }
        public double SecondStudDepthForFlange2 { get; set; }
        public double ThirdStudDepthForFlange2 { get; set; }
        public double FourthStudDepthForFlange2 { get; set; }

        public int DepthToGasket { get; set; }
        public int NumberOfStuds { get; set; }
        public int StudSize { get; set; }
        public string StudMaterial { get; set; }
        public int FromFlange { get; set; }

        // Mismatch

        //Flange
        public int Point1FlangeOver { get; set; }
        public int Point2FlangeOver { get; set; }
        public int Point3FlangeOver { get; set; }
        public int Point4FlangeOver { get; set; }
        public int Point5FlangeOver { get; set; }
        public int Point6FlangeOver { get; set; }
        public int Point7FlangeOver { get; set; }
        public int Point8FlangeOver { get; set; }
        public int Point9FlangeOver { get; set; }
        public int Point10FlangeOver { get; set; }
        public int Point11FlangeOver { get; set; }
        public int Point12FlangeOver { get; set; }

        //Flange Under

        public int Point1FlangeUnder { get; set; }
        public int Point2FlangeUnder { get; set; }
        public int Point3FlangeUnder { get; set; }
        public int Point4FlangeUnder { get; set; }
        public int Point5FlangeUnder { get; set; }
        public int Point6FlangeUnder { get; set; }
        public int Point7FlangeUnder { get; set; }
        public int Point8FlangeUnder { get; set; }
        public int Point9FlangeUnder { get; set; }
        public int Point10FlangeUnder { get; set; }
        public int Point11FlangeUnder { get; set; }
        public int Point12FlangeUnder { get; set; }

        //Amount
        public int Point1Amount { get; set; }
        public int Point2Amount { get; set; }
        public int Point3Amount { get; set; }
        public int Point4Amount { get; set; }
        public int Point5Amount { get; set; }
        public int Point6Amount { get; set; }
        public int Point7Amount { get; set; }
        public int Point8Amount { get; set; }
        public int Point9Amount { get; set; }
        public int Point10Amount { get; set; }
        public int Point11Amount { get; set; }
        public int Point12Amount { get; set; }

        //Gap
        public int Point1Gap { get; set; }
        public int Point2Gap { get; set; }
        public int Point3Gap { get; set; }
        public int Point4Gap { get; set; }
        public int Point5Gap { get; set; }
        public int Point6Gap { get; set; }
        public int Point7Gap { get; set; }
        public int Point8Gap { get; set; }
        public int Point9Gap { get; set; }
        public int Point10Gap { get; set; }
        public int Point11Gap { get; set; }
        public int Point12Gap { get; set; }

        //Overall
        public int Point1Overall { get; set; }
        public int Point2Overall { get; set; }
        public int Point3Overall { get; set; }
        public int Point4Overall { get; set; }
        public int Point5Overall { get; set; }
        public int Point6Overall { get; set; }
        public int Point7Overall { get; set; }
        public int Point8Overall { get; set; }
        public int Point9Overall { get; set; }
        public int Point10Overall { get; set; }
        public int Point11Overall { get; set; }
        public int Point12Overall { get; set; }

        //
        public bool SS016 { get; set; }
        public bool SS035 { get; set; }
        public bool CS035 { get; set; }

        //
        public string Capnuts { get; set; }
        public int CapnutsNumbersRequired { get; set; }
        public string SlottedStuds { get; set; }
        public int SlottedStudsNumbersRequired { get; set; }
        public string InjectionRings { get; set; }
        public int InjectionRingsNumbersRequired { get; set; }
        public bool DTP { get; set; }
        public bool Elbows { get; set; }
        public string OtherWireWrap { get; set; }
        public string Notes { get; set; }

        public string NumberAndSizeRequired { get; set; }
    }

    [Table("DS101s")]
    public class DS101 : DimensionalSheet
    {
        // General Information
        public string Flg { get; set; }

        // Flange Data
        public int Flange1PointA { get; set; }
        public int Flange1PointB { get; set; }
        public int Flange1PointC { get; set; }
        public int Flange1PointD { get; set; }
        public int Flange1PointE { get; set; }
        public int Flange1PointF { get; set; }
        public int Flange1PointG { get; set; }
        public int Flange1PointH { get; set; }
        public int Flange1PointJ { get; set; }
        public int Flange1PointK { get; set; }
        public int Flange1PointL { get; set; }
        public int Flange1PointM { get; set; }
        public int Flange1PointN { get; set; }
        public int Flange1PointP { get; set; }
        public int Flange1PointQ { get; set; }
        public int Flange1PointR { get; set; }
        public int Flange1PointS { get; set; }
        public int Flange1PointT { get; set; }
        public int Flange1PointU { get; set; }
        public int Flange1PointW { get; set; }
        public int Flange1PointX { get; set; }

        // Flange Data
        public int Flange2PointA { get; set; }
        public int Flange2PointB { get; set; }
        public int Flange2PointC { get; set; }
        public int Flange2PointD { get; set; }
        public int Flange2PointE { get; set; }
        public int Flange2PointF { get; set; }
        public int Flange2PointG { get; set; }
        public int Flange2PointH { get; set; }
        public int Flange2PointJ { get; set; }
        public int Flange2PointK { get; set; }
        public int Flange2PointL { get; set; }
        public int Flange2PointM { get; set; }
        public int Flange2PointN { get; set; }
        public int Flange2PointP { get; set; }
        public int Flange2PointQ { get; set; }
        public int Flange2PointR { get; set; }
        public int Flange2PointS { get; set; }
        public int Flange2PointT { get; set; }
        public int Flange2PointU { get; set; }
        public int Flange2PointW { get; set; }
        public int Flange2PointX { get; set; }

        //
        public int NoOfStuds { get; set; }
        public int StudSize { get; set; }
        public string StudMaterial { get; set; }
        public int DTG { get; set; }

        // Mismatch

        //Flange
        public string Point1FlangeOver { get; set; }
        public string Point2FlangeOver { get; set; }
        public string Point3FlangeOver { get; set; }
        public string Point4FlangeOver { get; set; }
        public string Point5FlangeOver { get; set; }
        public string Point6FlangeOver { get; set; }
        public string Point7FlangeOver { get; set; }
        public string Point8FlangeOver { get; set; }
        public string Point9FlangeOver { get; set; }
        public string Point10FlangeOver { get; set; }
        public string Point11FlangeOver { get; set; }
        public string Point12FlangeOver { get; set; }

        //Flange Under

        public string Point1FlangeUnder { get; set; }
        public string Point2FlangeUnder { get; set; }
        public string Point3FlangeUnder { get; set; }
        public string Point4FlangeUnder { get; set; }
        public string Point5FlangeUnder { get; set; }
        public string Point6FlangeUnder { get; set; }
        public string Point7FlangeUnder { get; set; }
        public string Point8FlangeUnder { get; set; }
        public string Point9FlangeUnder { get; set; }
        public string Point10FlangeUnder { get; set; }
        public string Point11FlangeUnder { get; set; }
        public string Point12FlangeUnder { get; set; }

        //Amount
        public int Point1Amount { get; set; }
        public int Point2Amount { get; set; }
        public int Point3Amount { get; set; }
        public int Point4Amount { get; set; }
        public int Point5Amount { get; set; }
        public int Point6Amount { get; set; }
        public int Point7Amount { get; set; }
        public int Point8Amount { get; set; }
        public int Point9Amount { get; set; }
        public int Point10Amount { get; set; }
        public int Point11Amount { get; set; }
        public int Point12Amount { get; set; }

        //
        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS102s")]
    public class DS102 : DimensionalSheet
    {
        // General Information
        public string Flg { get; set; }

        // Flange Data
        public int Flange1PointA { get; set; }
        public int Flange1PointB { get; set; }
        public int Flange1PointC { get; set; }
        public int Flange1PointD { get; set; }
        public int Flange1PointE { get; set; }
        public int Flange1PointF { get; set; }
        public int Flange1PointG { get; set; }
        public int Flange1PointH { get; set; }
        public int Flange1PointJ { get; set; }
        public int Flange1PointW { get; set; }

        //Section I
        public int Flange1Point1X { get; set; }
        public int Flange1Point2X { get; set; }
        public int Flange1Point3X { get; set; }
        public int Flange1Point4X { get; set; }
        public int Flange1Point5X { get; set; }
        public int Flange1Point6X { get; set; }
        public int Flange1Point7X { get; set; }
        public int Flange1Point8X { get; set; }
        public int Flange1Point9X { get; set; }
        public int Flange1Point10X { get; set; }
        public int Flange1PointWX { get; set; }
        public int Flange1Point1Y { get; set; }
        public int Flange1Point2Y { get; set; }
        public int Flange1Point3Y { get; set; }
        public int Flange1Point4Y { get; set; }
        public int Flange1Point5Y { get; set; }
        public int Flange1PointWY { get; set; }

        // Flange Data -flange 2
        public int Flange2PointA { get; set; }
        public int Flange2PointB { get; set; }
        public int Flange2PointC { get; set; }
        public int Flange2PointD { get; set; }
        public int Flange2PointE { get; set; }
        public int Flange2PointF { get; set; }
        public int Flange2PointG { get; set; }
        public int Flange2PointH { get; set; }
        public int Flange2PointJ { get; set; }
        public int Flange2PointW { get; set; }

        //Section I
        public int Flange2Point1X { get; set; }
        public int Flange2Point2X { get; set; }
        public int Flange2Point3X { get; set; }
        public int Flange2Point4X { get; set; }
        public int Flange2Point5X { get; set; }
        public int Flange2Point6X { get; set; }
        public int Flange2Point7X { get; set; }
        public int Flange2Point8X { get; set; }
        public int Flange2Point9X { get; set; }
        public int Flange2Point10X { get; set; }
        public int Flange2PointWX { get; set; }
        public int Flange2Point1Y { get; set; }
        public int Flange2Point2Y { get; set; }
        public int Flange2Point3Y { get; set; }
        public int Flange2Point4Y { get; set; }
        public int Flange2Point5Y { get; set; }
        public int Flange2PointWY { get; set; }

        //
        public int NoOfStuds { get; set; }
        public int StudSize { get; set; }
        public string StudMaterial { get; set; }
        public int DTG { get; set; }

        //Note (For a and b) 
        public bool IncrOHalf { get; set; }
        public bool IncrOne { get; set; }
        public bool IncrOneAndHalf { get; set; }

        // LTR - Section II Flange 1
        public int Point1xLtrFlange1Section2 { get; set; }
        public int Point2xLtrFlange1Section2 { get; set; }
        public int Point3xLtrFlange1Section2 { get; set; }
        public int Point4xLtrFlange1Section2 { get; set; }
        public int Point5xLtrFlange1Section2 { get; set; }
        public int Point6xLtrFlange1Section2 { get; set; }
        public int Point7xLtrFlange1Section2 { get; set; }
        public int Point8xLtrFlange1Section2 { get; set; }
        public int Point9xLtrFlange1Section2 { get; set; }
        public int Point10xLtrFlange1Section2 { get; set; }
        public int PointWxLtrFlange1Section2 { get; set; }
        public int Point1yLtrFlange1Section2 { get; set; }
        public int Point2yLtrFlange1Section2 { get; set; }
        public int Point3yLtrFlange1Section2 { get; set; }
        public int Point4yLtrFlange1Section2 { get; set; }
        public int Point5yLtrFlange1Section2 { get; set; }
        public int PointWyLtrFlange1Section2 { get; set; }

        // LTR - Section II Flange 2
        public int Point1xLtrFlange2Section2 { get; set; }
        public int Point2xLtrFlange2Section2 { get; set; }
        public int Point3xLtrFlange2Section2 { get; set; }
        public int Point4xLtrFlange2Section2 { get; set; }
        public int Point5xLtrFlange2Section2 { get; set; }
        public int Point6xLtrFlange2Section2 { get; set; }
        public int Point7xLtrFlange2Section2 { get; set; }
        public int Point8xLtrFlange2Section2 { get; set; }
        public int Point9xLtrFlange2Section2 { get; set; }
        public int Point10xLtrFlange2Section2 { get; set; }
        public int PointWxLtrFlange2Section2 { get; set; }
        public int Point1yLtrFlange2Section2 { get; set; }
        public int Point2yLtrFlange2Section2 { get; set; }
        public int Point3yLtrFlange2Section2 { get; set; }
        public int Point4yLtrFlange2Section2 { get; set; }
        public int Point5yLtrFlange2Section2 { get; set; }
        public int PointWyLtrFlange2Section2 { get; set; }


        // LTR - Section III Flange 1
        public int Point1xLtrFlange1Section3 { get; set; }
        public int Point2xLtrFlange1Section3 { get; set; }
        public int Point3xLtrFlange1Section3 { get; set; }
        public int Point4xLtrFlange1Section3 { get; set; }
        public int Point5xLtrFlange1Section3 { get; set; }
        public int Point6xLtrFlange1Section3 { get; set; }
        public int Point7xLtrFlange1Section3 { get; set; }
        public int Point8xLtrFlange1Section3 { get; set; }
        public int Point9xLtrFlange1Section3 { get; set; }
        public int Point10xLtrFlange1Section3 { get; set; }
        public int PointWxLtrFlange1Section3 { get; set; }
        public int Point1yLtrFlange1Section3 { get; set; }
        public int Point2yLtrFlange1Section3 { get; set; }
        public int Point3yLtrFlange1Section3 { get; set; }
        public int Point4yLtrFlange1Section3 { get; set; }
        public int Point5yLtrFlange1Section3 { get; set; }
        public int PointWyLtrFlange1Section3 { get; set; }

        // LTR - Section III Flange 2
        public int Point1xLtrFlange2Section3 { get; set; }
        public int Point2xLtrFlange2Section3 { get; set; }
        public int Point3xLtrFlange2Section3 { get; set; }
        public int Point4xLtrFlange2Section3 { get; set; }
        public int Point5xLtrFlange2Section3 { get; set; }
        public int Point6xLtrFlange2Section3 { get; set; }
        public int Point7xLtrFlange2Section3 { get; set; }
        public int Point8xLtrFlange2Section3 { get; set; }
        public int Point9xLtrFlange2Section3 { get; set; }
        public int Point10xLtrFlange2Section3 { get; set; }
        public int PointWxLtrFlange2Section3 { get; set; }
        public int Point1yLtrFlange2Section3 { get; set; }
        public int Point2yLtrFlange2Section3 { get; set; }
        public int Point3yLtrFlange2Section3 { get; set; }
        public int Point4yLtrFlange2Section3 { get; set; }
        public int Point5yLtrFlange2Section3 { get; set; }
        public int PointWyLtrFlange2Section3 { get; set; }

        // LTR - Section IV Flange 1
        public int Point1xLtrFlange1Section4 { get; set; }
        public int Point2xLtrFlange1Section4 { get; set; }
        public int Point3xLtrFlange1Section4 { get; set; }
        public int Point4xLtrFlange1Section4 { get; set; }
        public int Point5xLtrFlange1Section4 { get; set; }
        public int Point6xLtrFlange1Section4 { get; set; }
        public int Point7xLtrFlange1Section4 { get; set; }
        public int Point8xLtrFlange1Section4 { get; set; }
        public int Point9xLtrFlange1Section4 { get; set; }
        public int Point10xLtrFlange1Section4 { get; set; }
        public int PointWxLtrFlange1Section4 { get; set; }
        public int Point1yLtrFlange1Section4 { get; set; }
        public int Point2yLtrFlange1Section4 { get; set; }
        public int Point3yLtrFlange1Section4 { get; set; }
        public int Point4yLtrFlange1Section4 { get; set; }
        public int Point5yLtrFlange1Section4 { get; set; }
        public int PointWyLtrFlange1Section4 { get; set; }

        // LTR - Section IV Flange 2
        public int Point1xLtrFlange2Section4 { get; set; }
        public int Point2xLtrFlange2Section4 { get; set; }
        public int Point3xLtrFlange2Section4 { get; set; }
        public int Point4xLtrFlange2Section4 { get; set; }
        public int Point5xLtrFlange2Section4 { get; set; }
        public int Point6xLtrFlange2Section4 { get; set; }
        public int Point7xLtrFlange2Section4 { get; set; }
        public int Point8xLtrFlange2Section4 { get; set; }
        public int Point9xLtrFlange2Section4 { get; set; }
        public int Point10xLtrFlange2Section4 { get; set; }
        public int PointWxLtrFlange2Section4 { get; set; }
        public int Point1yLtrFlange2Section4 { get; set; }
        public int Point2yLtrFlange2Section4 { get; set; }
        public int Point3yLtrFlange2Section4 { get; set; }
        public int Point4yLtrFlange2Section4 { get; set; }
        public int Point5yLtrFlange2Section4 { get; set; }
        public int PointWyLtrFlange2Section4 { get; set; }


        // Mismatch

        //Flange Over - Section 1 
        public string Point2xFlangeOverSection1 { get; set; }
        public string Point4xFlangeOverSection1 { get; set; }
        public string Point6xFlangeOverSection1 { get; set; }
        public string Point8xFlangeOverSection1 { get; set; }
        public string Point10xFlangeOverSection1 { get; set; }
        public string Point1yFlangeOverSection1 { get; set; }
        public string Point3yFlangeOverSection1 { get; set; }
        public string Point5yFlangeOverSection1 { get; set; }

        //Flange Under - Section 1 
        public string Point2xFlangeUnderSection1 { get; set; }
        public string Point4xFlangeUnderSection1 { get; set; }
        public string Point6xFlangeUnderSection1 { get; set; }
        public string Point8xFlangeUnderSection1 { get; set; }
        public string Point10xFlangeUnderSection1 { get; set; }
        public string Point1yFlangeUnderSection1 { get; set; }
        public string Point3yFlangeUnderSection1 { get; set; }
        public string Point5yFlangeUnderSection1 { get; set; }

        //Amount - Section 1 
        public string Point2xAmountSection1 { get; set; }
        public string Point4xAmountSection1 { get; set; }
        public string Point6xAmountSection1 { get; set; }
        public string Point8xAmountSection1 { get; set; }
        public string Point10xAmountSection1 { get; set; }
        public string Point1yAmountSection1 { get; set; }
        public string Point3yAmountSection1 { get; set; }
        public string Point5yAmountSection1 { get; set; }

        //Flange Over - Section 2
        public string Point2xFlangeOverSection2 { get; set; }
        public string Point4xFlangeOverSection2 { get; set; }
        public string Point6xFlangeOverSection2 { get; set; }
        public string Point8xFlangeOverSection2 { get; set; }
        public string Point10xFlangeOverSection2 { get; set; }
        public string Point1yFlangeOverSection2 { get; set; }
        public string Point3yFlangeOverSection2 { get; set; }
        public string Point5yFlangeOverSection2 { get; set; }

        //Flange Under - Section 2
        public string Point2xFlangeUnderSection2 { get; set; }
        public string Point4xFlangeUnderSection2 { get; set; }
        public string Point6xFlangeUnderSection2 { get; set; }
        public string Point8xFlangeUnderSection2 { get; set; }
        public string Point10xFlangeUnderSection2 { get; set; }
        public string Point1yFlangeUnderSection2 { get; set; }
        public string Point3yFlangeUnderSection2 { get; set; }
        public string Point5yFlangeUnderSection2 { get; set; }

        //Amount - Section 2
        public string Point2xAmountSection2 { get; set; }
        public string Point4xAmountSection2 { get; set; }
        public string Point6xAmountSection2 { get; set; }
        public string Point8xAmountSection2 { get; set; }
        public string Point10xAmountSection2 { get; set; }
        public string Point1yAmountSection2 { get; set; }
        public string Point3yAmountSection2 { get; set; }
        public string Point5yAmountSection2 { get; set; }

        //Flange Over - Section 3
        public string Point2xFlangeOverSection3 { get; set; }
        public string Point4xFlangeOverSection3 { get; set; }
        public string Point6xFlangeOverSection3 { get; set; }
        public string Point8xFlangeOverSection3 { get; set; }
        public string Point10xFlangeOverSection3 { get; set; }
        public string Point1yFlangeOverSection3 { get; set; }
        public string Point3yFlangeOverSection3 { get; set; }
        public string Point5yFlangeOverSection3 { get; set; }

        //Flange Under - Section 3
        public string Point2xFlangeUnderSection3 { get; set; }
        public string Point4xFlangeUnderSection3 { get; set; }
        public string Point6xFlangeUnderSection3 { get; set; }
        public string Point8xFlangeUnderSection3 { get; set; }
        public string Point10xFlangeUnderSection3 { get; set; }
        public string Point1yFlangeUnderSection3 { get; set; }
        public string Point3yFlangeUnderSection3 { get; set; }
        public string Point5yFlangeUnderSection3 { get; set; }

        //Amount - Section 3
        public string Point2xAmountSection3 { get; set; }
        public string Point4xAmountSection3 { get; set; }
        public string Point6xAmountSection3 { get; set; }
        public string Point8xAmountSection3 { get; set; }
        public string Point10xAmountSection3 { get; set; }
        public string Point1yAmountSection3 { get; set; }
        public string Point3yAmountSection3 { get; set; }
        public string Point5yAmountSection3 { get; set; }

        //Flange Over - Section 4
        public string Point2xFlangeOverSection4 { get; set; }
        public string Point4xFlangeOverSection4 { get; set; }
        public string Point6xFlangeOverSection4 { get; set; }
        public string Point8xFlangeOverSection4 { get; set; }
        public string Point10xFlangeOverSection4 { get; set; }
        public string Point1yFlangeOverSection4 { get; set; }
        public string Point3yFlangeOverSection4 { get; set; }
        public string Point5yFlangeOverSection4 { get; set; }

        //Flange Under - Section 4
        public string Point2xFlangeUnderSection4 { get; set; }
        public string Point4xFlangeUnderSection4 { get; set; }
        public string Point6xFlangeUnderSection4 { get; set; }
        public string Point8xFlangeUnderSection4 { get; set; }
        public string Point10xFlangeUnderSection4 { get; set; }
        public string Point1yFlangeUnderSection4 { get; set; }
        public string Point3yFlangeUnderSection4 { get; set; }
        public string Point5yFlangeUnderSection4 { get; set; }

        //Amount - Section 4
        public string Point2xAmountSection4 { get; set; }
        public string Point4xAmountSection4 { get; set; }
        public string Point6xAmountSection4 { get; set; }
        public string Point8xAmountSection4 { get; set; }
        public string Point10xAmountSection4 { get; set; }
        public string Point1yAmountSection4 { get; set; }
        public string Point3yAmountSection4 { get; set; }
        public string Point5yAmountSection4 { get; set; }

        //
        //public int Obstructions { get; set; } // supposed to be an image

        //public string Notes { get; set; }
    }

    [Table("DS103s")]
    public class DS103 : DimensionalSheet
    {
        public string Flg { get; set; }
        public int A { get; set; }
        public int H { get; set; }
        public int G { get; set; }
        public int P { get; set; }
        public int R { get; set; }
        public int Q { get; set; }
        public int T { get; set; }
        public int U { get; set; }
        public int V { get; set; }
        public int DTG { get; set; }
        public int MinClearanceBetweenNuts { get; set; }
        public int NoOfStudsBetweenStud2and3 { get; set; }
        public int StudSize { get; set; }
        public string StudMaterial { get; set; }
        public int Stud1Flg1 { get; set; }
        public int Stud2Flg1 { get; set; }
        public int Stud3Flg1 { get; set; }
        public int Stud1Flg2 { get; set; }
        public int Stud2Flg2 { get; set; }
        public int Stud3Flg3 { get; set; }
        public int Flange1Width { get; set; }
        public int Flange2Width { get; set; }
        public bool Holland { get; set; }
        public bool DandT { get; set; }
        public bool Other { get; set; }
        public int Flange1OD { get; set; }
        public int Flange2OD { get; set; }
        public string FlgOverStud1 { get; set; }
        public string FlgOverStud2 { get; set; }
        public string FlgOverStud3 { get; set; }
        public string FlgOverStud4 { get; set; }
        public string FlgOverStud5 { get; set; }
        public string FlgOverStud6 { get; set; }
        public string FlgOverStud7 { get; set; }
        public string FlgOverStud8 { get; set; }

        public string FlgUnderStud1 { get; set; }
        public string FlgUnderStud2 { get; set; }
        public string FlgUnderStud3 { get; set; }
        public string FlgUnderStud4 { get; set; }
        public string FlgUnderStud5 { get; set; }
        public string FlgUnderStud6 { get; set; }
        public string FlgUnderStud7 { get; set; }
        public string FlgUnderStud8 { get; set; }
        public int Amount1 { get; set; }
        public int Amount2 { get; set; }
        public int Amount3 { get; set; }
        public int Amount4 { get; set; }
        public int Amount5 { get; set; }
        public int Amount6 { get; set; }
        public int Amount7 { get; set; }
        public int Amount8 { get; set; }
        public int FlgGapAmount1 { get; set; }
        public int FlgGapAmount2 { get; set; }
        public int FlgGapAmount3 { get; set; }
        public int FlgGapAmount4 { get; set; }
        public int Obstructions { get; set; } // supposed to be an image
        public string Notes { get; set; }
    }

    [Table("DS104s")]
    public class DS104 : DimensionalSheet
    {
        public string Flg { get; set; }
        public int A { get; set; }
        public int B { get; set; }
        public int C { get; set; }
        public int D { get; set; }
        public int E { get; set; }
        public int F { get; set; }
        public int G { get; set; }
        public int H { get; set; }
        public int J { get; set; }
        public int K { get; set; }
        public int L { get; set; }
        public int M { get; set; }
        public int N { get; set; }
        public int P { get; set; }
        public int Q { get; set; }
        public int R { get; set; }
        public int S { get; set; }
        public int T { get; set; }
        public int U { get; set; }
        public int V { get; set; }
        public int W { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public int Z { get; set; }
        public int AA { get; set; }
        public int BB { get; set; }
        public int CC { get; set; }
        public int DD { get; set; }
        public int EE { get; set; }
        public int FF { get; set; }
        public int GG { get; set; }
        public int HH { get; set; }
        public int JJ { get; set; }
        public int KK { get; set; }

        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int F2 { get; set; }
        public int G2 { get; set; }
        public int H2 { get; set; }
        public int J2 { get; set; }
        public int K2 { get; set; }
        public int L2 { get; set; }
        public int M2 { get; set; }
        public int N2 { get; set; }
        public int P2 { get; set; }
        public int Q2 { get; set; }
        public int R2 { get; set; }
        public int S2 { get; set; }
        public int T2 { get; set; }
        public int U2 { get; set; }
        public int V2 { get; set; }
        public int W2 { get; set; }
        public int X2 { get; set; }
        public int Y2 { get; set; }
        public int Z2 { get; set; }
        public int AA2 { get; set; }
        public int BB2 { get; set; }
        public int CC2 { get; set; }
        public int DD2 { get; set; }
        public int EE2 { get; set; }
        public int FF2 { get; set; }
        public int GG2 { get; set; }
        public int HH2 { get; set; }
        public int JJ2 { get; set; }
        public int KK2 { get; set; }
        public bool Threaded { get; set; }
        public bool Welded { get; set; }

        public bool IdTab { get; set; }
        public string LocationOfBlows { get; set; }

        public int Flg1ValvePos { get; set; }
        public int Flg2ValvePos { get; set; }

        public int ValveClkPt { get; set; }
        public int TabClkPt { get; set; }


        public int Obstructions { get; set; } // supposed to be an image
        public string Notes { get; set; }
    }

    [Table("DS105s")]
    public class DS105 : DimensionalSheet
    {
        public string Flg { get; set; }
        public int Temp1 { get; set; }
        public int Temp2 { get; set; }
        public int Temp3 { get; set; }
        public int Temp4 { get; set; }
        public int Pres1 { get; set; }
        public int Pres2 { get; set; }
        public int Pres3 { get; set; }
        public int Pres4 { get; set; }

        public int Dim1 { get; set; }
        public int Dim2 { get; set; }
        public int Dim3 { get; set; }
        public int Dim4 { get; set; }

        public int Flg1ODPt12to6F { get; set; }
        public int Flg1ODPt1to7 { get; set; }
        public int Flg1ODPt2to8 { get; set; }
        public int Flg1ODPt3to9 { get; set; }
        public int Flg1ODPt4to10 { get; set; }

        public int Flg1ODPt5to11 { get; set; }

        public int Flg1Width { get; set; }

        public int Flg2ODPt12to6F { get; set; }
        public int Flg2ODPt1to7 { get; set; }
        public int Flg2ODPt2to8 { get; set; }
        public int Flg2ODPt3to9 { get; set; }
        public int Flg2ODPt4to10 { get; set; }

        public int Flg2ODPt5to11 { get; set; }

        public int Flg2Width { get; set; }

        public int Flg3ODPt12to6F { get; set; }
        public int Flg3ODPt1to7 { get; set; }
        public int Flg3ODPt2to8 { get; set; }
        public int Flg3ODPt3to9 { get; set; }
        public int Flg3ODPt4to10 { get; set; }

        public int Flg3ODPt5to11 { get; set; }

        public int Flg3Width { get; set; }

        public string StudDepth1 { get; set; }
        public string StudDepth2 { get; set; }
        public string StudDepth3 { get; set; }
        public string StudDepth4 { get; set; }

        public int Flg1StudDepth1 { get; set; }
        public int Flg1StudDepth2 { get; set; }
        public int Flg1StudDepth3 { get; set; }
        public int Flg1StudDepth4 { get; set; }

        public int Flg2StudDepth1 { get; set; }
        public int Flg2StudDepth2 { get; set; }
        public int Flg2StudDepth3 { get; set; }
        public int Flg2StudDepth4 { get; set; }

        public int Flg3StudDepth1 { get; set; }
        public int Flg3StudDepth2 { get; set; }
        public int Flg3StudDepth3 { get; set; }
        public int Flg3StudDepth4 { get; set; }

        public int NoOfStuds { get; set; }
        public int StudSize { get; set; }
        public string StudMaterial { get; set; }

        public int DTG { get; set; }

        // Mismatch

        //Flange
        public int Point1FlangeOver { get; set; }
        public int Point2FlangeOver { get; set; }
        public int Point3FlangeOver { get; set; }
        public int Point4FlangeOver { get; set; }
        public int Point5FlangeOver { get; set; }
        public int Point6FlangeOver { get; set; }
        public int Point7FlangeOver { get; set; }
        public int Point8FlangeOver { get; set; }
        public int Point9FlangeOver { get; set; }
        public int Point10FlangeOver { get; set; }
        public int Point11FlangeOver { get; set; }
        public int Point12FlangeOver { get; set; }

        //Flange Under

        public int Point1FlangeUnder { get; set; }
        public int Point2FlangeUnder { get; set; }
        public int Point3FlangeUnder { get; set; }
        public int Point4FlangeUnder { get; set; }
        public int Point5FlangeUnder { get; set; }
        public int Point6FlangeUnder { get; set; }
        public int Point7FlangeUnder { get; set; }
        public int Point8FlangeUnder { get; set; }
        public int Point9FlangeUnder { get; set; }
        public int Point10FlangeUnder { get; set; }
        public int Point11FlangeUnder { get; set; }
        public int Point12FlangeUnder { get; set; }

        //Amount
        public int Point1Amount { get; set; }
        public int Point2Amount { get; set; }
        public int Point3Amount { get; set; }
        public int Point4Amount { get; set; }
        public int Point5Amount { get; set; }
        public int Point6Amount { get; set; }
        public int Point7Amount { get; set; }
        public int Point8Amount { get; set; }
        public int Point9Amount { get; set; }
        public int Point10Amount { get; set; }
        public int Point11Amount { get; set; }
        public int Point12Amount { get; set; }


        // Mismatch

        //Flange
        public int If3Point1FlangeOver { get; set; }
        public int If3Point2FlangeOver { get; set; }
        public int If3Point3FlangeOver { get; set; }
        public int If3Point4FlangeOver { get; set; }
        public int If3Point5FlangeOver { get; set; }
        public int If3Point6FlangeOver { get; set; }
        public int If3Point7FlangeOver { get; set; }
        public int If3Point8FlangeOver { get; set; }
        public int If3Point9FlangeOver { get; set; }
        public int If3Point10FlangeOver { get; set; }
        public int If3Point11FlangeOver { get; set; }
        public int If3Point12FlangeOver { get; set; }

        //Flange Under

        public int If3Point1FlangeUnder { get; set; }
        public int If3Point2FlangeUnder { get; set; }
        public int If3Point3FlangeUnder { get; set; }
        public int If3Point4FlangeUnder { get; set; }
        public int If3Point5FlangeUnder { get; set; }
        public int If3Point6FlangeUnder { get; set; }
        public int If3Point7FlangeUnder { get; set; }
        public int If3Point8FlangeUnder { get; set; }
        public int If3Point9FlangeUnder { get; set; }
        public int If3Point10FlangeUnder { get; set; }
        public int If3Point11FlangeUnder { get; set; }
        public int If3Point12FlangeUnder { get; set; }

        //Amount
        public int If3Point1Amount { get; set; }
        public int If3Point2Amount { get; set; }
        public int If3Point3Amount { get; set; }
        public int If3Point4Amount { get; set; }
        public int If3Point5Amount { get; set; }
        public int If3Point6Amount { get; set; }
        public int If3Point7Amount { get; set; }
        public int If3Point8Amount { get; set; }
        public int If3Point9Amount { get; set; }
        public int If3Point10Amount { get; set; }
        public int If3Point11Amount { get; set; }
        public int If3Point12Amount { get; set; }

        public int MinNutToNut { get; set; }
        public int NutPointToPoint { get; set; }
        public int NutFlatToFlat { get; set; }

        //flange gap
        //if three flanges 
        public int Point1Flg1MinusFlg3 { get; set; }
        public int Point2Flg1MinusFlg3 { get; set; }
        public int Point3Flg1MinusFlg3 { get; set; }
        public int Point4Flg1MinusFlg3 { get; set; }
        public int Point5Flg1MinusFlg3 { get; set; }
        public int Point6Flg1MinusFlg3 { get; set; }
        public int Point7Flg1MinusFlg3 { get; set; }
        public int Point8Flg1MinusFlg3 { get; set; }

        public int Point10Flg1MinusFlg3 { get; set; }
        public int Point11Flg1MinusFlg3 { get; set; }
        public int Point12Flg1MinusFlg3 { get; set; }

        public int Point1Flg3MinusFlg2 { get; set; }
        public int Point2Flg3MinusFlg2 { get; set; }
        public int Point3Flg3MinusFlg2 { get; set; }
        public int Point4Flg3MinusFlg2 { get; set; }
        public int Point5Flg3MinusFlg2 { get; set; }
        public int Point6Flg3MinusFlg2 { get; set; }
        public int Point7Flg3MinusFlg2 { get; set; }
        public int Point8Flg3MinusFlg2 { get; set; }

        public int Point10Flg3MinusFlg2 { get; set; }
        public int Point11Flg3MinusFlg2 { get; set; }
        public int Point12Flg3MinusFlg2 { get; set; }

        // if two flanges

        public int Point1Flg1MinusFlg2 { get; set; }
        public int Point2Flg1MinusFlg2 { get; set; }
        public int Point3Flg1MinusFlg2 { get; set; }
        public int Point4Flg1MinusFlg2 { get; set; }
        public int Point5Flg1MinusFlg2 { get; set; }
        public int Point6Flg1MinusFlg2 { get; set; }
        public int Point7Flg1MinusFlg2 { get; set; }
        public int Point8Flg1MinusFlg2 { get; set; }

        public int Point10Flg1MinusFlg2 { get; set; }
        public int Point11Flg1MinusFlg2 { get; set; }
        public int Point12Flg1MinusFlg2 { get; set; }

        public int Obstructions { get; set; }
        public string Notes { get; set; }
    }

    [Table("DS110s")]
    public class DS110 : DimensionalSheet
    {
        public string Flg { get; set; }
        public int A1 { get; set; }
        public int B1 { get; set; }

        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int H1 { get; set; }
        public int J1 { get; set; }
        public int K1 { get; set; }
        public int W1 { get; set; }
        public int WH1 { get; set; }

        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int H2 { get; set; }
        public int J2 { get; set; }
        public int K2 { get; set; }
        public int W2 { get; set; }
        public int WH2 { get; set; }

        public int F1a { get; set; }
        public int F1b { get; set; }
        public int F2a { get; set; }
        public int F2b { get; set; }
        public int G1a { get; set; }
        public int G1b { get; set; }
        public int G2a { get; set; }
        public int G2b { get; set; }
        public int L13 { get; set; }
        public int L16 { get; set; }
        public int L19 { get; set; }
        public int L112 { get; set; }
        public int L23 { get; set; }
        public int L26 { get; set; }
        public int L29 { get; set; }
        public int L212 { get; set; }

        //Gap
        public int Point1Gap { get; set; }
        public int Point2Gap { get; set; }
        public int Point3Gap { get; set; }
        public int Point4Gap { get; set; }
        public int Point5Gap { get; set; }
        public int Point6Gap { get; set; }
        public int Point7Gap { get; set; }
        public int Point8Gap { get; set; }
        public int Point9Gap { get; set; }
        public int Point10Gap { get; set; }
        public int Point11Gap { get; set; }
        public int Point12Gap { get; set; }

        //Overall
        public int Point1Overall { get; set; }
        public int Point2Overall { get; set; }
        public int Point3Overall { get; set; }
        public int Point4Overall { get; set; }
        public int Point5Overall { get; set; }
        public int Point6Overall { get; set; }
        public int Point7Overall { get; set; }
        public int Point8Overall { get; set; }
        public int Point9Overall { get; set; }
        public int Point10Overall { get; set; }
        public int Point11Overall { get; set; }
        public int Point12Overall { get; set; }

        // Outside Diameter
        public string OdFlangeOne12to6 { get; set; }
        public DateTime OdFlangeOne1to7 { get; set; }
        public string OdFlangeOne2to8 { get; set; }
        public string OdFlangeOne3to9 { get; set; }
        public string OdFlangeOne4to10 { get; set; }
        public string OdFlangeOne5to11 { get; set; }

        //Flange 2
        public string OdFlangeTwo12to6 { get; set; }
        public DateTime OdFlangeTwo1to7 { get; set; }
        public string OdFlangeTwo2to8 { get; set; }
        public string OdFlangeTwo3to9 { get; set; }
        public string OdFlangeTwo4to10 { get; set; }
        public string OdFlangeTwo5to11 { get; set; }

        //Stud Depths
        public string StudDepthPoint1 { get; set; }
        public string StudDepthPoint2 { get; set; }
        public string StudDepthPoint3 { get; set; }
        public int StudDepthPoint1Flange1 { get; set; }
        public int StudDepthPoint2Flange1 { get; set; }
        public int StudDepthPoint3Flange1 { get; set; }
        public int StudDepthPoint1Flange2 { get; set; }
        public int StudDepthPoint2Flange2 { get; set; }
        public int StudDepthPoint3Flange2 { get; set; }

        //
        public int DepthToGasket { get; set; }
        public int NoOfStuds { get; set; }
        public int StudSize { get; set; }
        public string StudMaterial { get; set; }
        public int FromFlange { get; set; }

        public bool CanStudsBeCut { get; set; }
        // Mismatch

        //Flange
        public int Point1FlangeOver { get; set; }
        public int Point2FlangeOver { get; set; }
        public int Point3FlangeOver { get; set; }
        public int Point4FlangeOver { get; set; }
        public int Point5FlangeOver { get; set; }
        public int Point6FlangeOver { get; set; }
        public int Point7FlangeOver { get; set; }
        public int Point8FlangeOver { get; set; }
        public int Point9FlangeOver { get; set; }
        public int Point10FlangeOver { get; set; }
        public int Point11FlangeOver { get; set; }
        public int Point12FlangeOver { get; set; }

        //Flange Under

        public int Point1FlangeUnder { get; set; }
        public int Point2FlangeUnder { get; set; }
        public int Point3FlangeUnder { get; set; }
        public int Point4FlangeUnder { get; set; }
        public int Point5FlangeUnder { get; set; }
        public int Point6FlangeUnder { get; set; }
        public int Point7FlangeUnder { get; set; }
        public int Point8FlangeUnder { get; set; }
        public int Point9FlangeUnder { get; set; }
        public int Point10FlangeUnder { get; set; }
        public int Point11FlangeUnder { get; set; }
        public int Point12FlangeUnder { get; set; }

        //Amount
        public int Point1Amount { get; set; }
        public int Point2Amount { get; set; }
        public int Point3Amount { get; set; }
        public int Point4Amount { get; set; }
        public int Point5Amount { get; set; }
        public int Point6Amount { get; set; }
        public int Point7Amount { get; set; }
        public int Point8Amount { get; set; }
        public int Point9Amount { get; set; }
        public int Point10Amount { get; set; }
        public int Point11Amount { get; set; }
        public int Point12Amount { get; set; }

        public int MinNutToNut { get; set; }
        public int NutPointToPoint { get; set; }
        public int NutFlatToFlat { get; set; }

        public string EarLocations { get; set; }
        public int Flange1Width { get; set; }
        public int Flange2Width { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS111s")]
    public class DS111 : DimensionalSheet
    {
        public string Flg { get; set; }

        public bool SlipOn { get; set; }
        public bool LapJoint { get; set; }
        public bool ThreadedAndSocket { get; set; }
        public int A1 { get; set; }
        public int B1 { get; set; }

        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int H1 { get; set; }
        public int J1 { get; set; }
        public int K1 { get; set; }
        public int W1 { get; set; }
        public int WH1 { get; set; }

        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int H2 { get; set; }
        public int J2 { get; set; }
        public int K2 { get; set; }
        public int W2 { get; set; }
        public int WH2 { get; set; }

        public int F1a { get; set; }
        public int F1b { get; set; }
        public int F2a { get; set; }
        public int F2b { get; set; }
        public int G1a { get; set; }
        public int G1b { get; set; }
        public int G2a { get; set; }
        public int G2b { get; set; }
        public int L13 { get; set; }
        public int L16 { get; set; }
        public int L19 { get; set; }
        public int L112 { get; set; }
        public int L23 { get; set; }
        public int L26 { get; set; }
        public int L29 { get; set; }
        public int L212 { get; set; }

        //Gap
        public int Point1Gap { get; set; }
        public int Point2Gap { get; set; }
        public int Point3Gap { get; set; }
        public int Point4Gap { get; set; }
        public int Point5Gap { get; set; }
        public int Point6Gap { get; set; }
        public int Point7Gap { get; set; }
        public int Point8Gap { get; set; }
        public int Point9Gap { get; set; }
        public int Point10Gap { get; set; }
        public int Point11Gap { get; set; }
        public int Point12Gap { get; set; }

        //Overall
        public int Point1Overall { get; set; }
        public int Point2Overall { get; set; }
        public int Point3Overall { get; set; }
        public int Point4Overall { get; set; }
        public int Point5Overall { get; set; }
        public int Point6Overall { get; set; }
        public int Point7Overall { get; set; }
        public int Point8Overall { get; set; }
        public int Point9Overall { get; set; }
        public int Point10Overall { get; set; }
        public int Point11Overall { get; set; }
        public int Point12Overall { get; set; }

        // Outside Diameter
        public string OdFlangeOne12to6 { get; set; }
        public DateTime OdFlangeOne1to7 { get; set; }
        public string OdFlangeOne2to8 { get; set; }
        public string OdFlangeOne3to9 { get; set; }
        public string OdFlangeOne4to10 { get; set; }
        public string OdFlangeOne5to11 { get; set; }

        //Flange 2
        public string OdFlangeTwo12to6 { get; set; }
        public DateTime OdFlangeTwo1to7 { get; set; }
        public string OdFlangeTwo2to8 { get; set; }
        public string OdFlangeTwo3to9 { get; set; }
        public string OdFlangeTwo4to10 { get; set; }
        public string OdFlangeTwo5to11 { get; set; }

        //Stud Depths
        public string StudDepthPoint1 { get; set; }
        public string StudDepthPoint2 { get; set; }
        public string StudDepthPoint3 { get; set; }
        public int StudDepthPoint1Flange1 { get; set; }
        public int StudDepthPoint2Flange1 { get; set; }
        public int StudDepthPoint3Flange1 { get; set; }
        public int StudDepthPoint1Flange2 { get; set; }
        public int StudDepthPoint2Flange2 { get; set; }
        public int StudDepthPoint3Flange2 { get; set; }

        //
        public int DepthToGasket { get; set; }
        public int NoOfStuds { get; set; }
        public int StudSize { get; set; }
        public string StudMaterial { get; set; }
        public int FromFlange { get; set; }

        public bool CanStudsBeCut { get; set; }
        // Mismatch

        //Flange
        public int Point1FlangeOver { get; set; }
        public int Point2FlangeOver { get; set; }
        public int Point3FlangeOver { get; set; }
        public int Point4FlangeOver { get; set; }
        public int Point5FlangeOver { get; set; }
        public int Point6FlangeOver { get; set; }
        public int Point7FlangeOver { get; set; }
        public int Point8FlangeOver { get; set; }
        public int Point9FlangeOver { get; set; }
        public int Point10FlangeOver { get; set; }
        public int Point11FlangeOver { get; set; }
        public int Point12FlangeOver { get; set; }

        //Flange Under

        public int Point1FlangeUnder { get; set; }
        public int Point2FlangeUnder { get; set; }
        public int Point3FlangeUnder { get; set; }
        public int Point4FlangeUnder { get; set; }
        public int Point5FlangeUnder { get; set; }
        public int Point6FlangeUnder { get; set; }
        public int Point7FlangeUnder { get; set; }
        public int Point8FlangeUnder { get; set; }
        public int Point9FlangeUnder { get; set; }
        public int Point10FlangeUnder { get; set; }
        public int Point11FlangeUnder { get; set; }
        public int Point12FlangeUnder { get; set; }

        //Amount
        public int Point1Amount { get; set; }
        public int Point2Amount { get; set; }
        public int Point3Amount { get; set; }
        public int Point4Amount { get; set; }
        public int Point5Amount { get; set; }
        public int Point6Amount { get; set; }
        public int Point7Amount { get; set; }
        public int Point8Amount { get; set; }
        public int Point9Amount { get; set; }
        public int Point10Amount { get; set; }
        public int Point11Amount { get; set; }
        public int Point12Amount { get; set; }

        public int MinNutToNut { get; set; }
        public int NutPointToPoint { get; set; }
        public int NutFlatToFlat { get; set; }

        public string EarLocations { get; set; }
        public int Flange1Width { get; set; }
        public int Flange2Width { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS113s")]
    public class DS113 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int L1 { get; set; }
        public int L2 { get; set; }
        public int W1 { get; set; }
        public int W2 { get; set; }
        public int W3 { get; set; }
        public int M { get; set; }
        public int N { get; set; }
        public int P { get; set; }
        public int Q1 { get; set; }
        public int Q2 { get; set; }
        public int R1 { get; set; }
        public int R2 { get; set; }
        public int S1 { get; set; }
        public int S2 { get; set; }

        public bool CanStudsBeCut { get; set; }
        public string LocationOfLeak { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS120s")]
    public class DS120 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int W1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int W2 { get; set; }
        public int G { get; set; }
        public int J { get; set; }

        //Union 
        public int E1 { get; set; }
        public int E2 { get; set; }
        public int F1 { get; set; }
        public int F2 { get; set; }
        public int H { get; set; }

        //Hex 
        public int PttoPt { get; set; }
        public int FtoF { get; set; }

        public bool Coupling { get; set; }
        public bool Union { get; set; }

        public string LocationOfBlow { get; set; }
        public bool Threaded { get; set; }
        public bool Welded { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS124s")]
    public class DS124 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int F1 { get; set; }
        public int G1 { get; set; }
        public int W1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int F2 { get; set; }
        public int G2 { get; set; }
        public int W2 { get; set; }
        public int H1 { get; set; }
        public int H2 { get; set; }
        public int J1 { get; set; }
        public int J2 { get; set; }
        public int K2 { get; set; }
        public int L { get; set; }

        public bool Round { get; set; }
        public bool Square { get; set; }

        public string LocationOfBlow { get; set; }
        public bool Threaded { get; set; }
        public bool Welded { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS126s")]
    public class DS126 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int W1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int W2 { get; set; }
        public int E { get; set; }
        public int F { get; set; }
        public int G1 { get; set; }
        public int G2 { get; set; }
        public int H1 { get; set; }
        public int H2 { get; set; }
        public int J1 { get; set; }
        public int J2 { get; set; }

        public bool Welded { get; set; }
        public bool Bent { get; set; }

        public string LocationOfBlow { get; set; }

        public string Notes { get; set; }
    }

    [Table("DS128s")]
    public class DS128 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int W1 { get; set; }
        public int WH1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int W2 { get; set; }
        public int WH2 { get; set; }
        public int E { get; set; }
        public int F1 { get; set; }
        public int F2 { get; set; }
        public int G1 { get; set; }
        public int G2 { get; set; }
        public int H1 { get; set; }
        public int H2 { get; set; }

        public string LocationOfBlow { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS129s")]
    public class DS129 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int F1 { get; set; }
        public int G1 { get; set; }
        public int W1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int F2 { get; set; }
        public int G2 { get; set; }
        public int W2 { get; set; }
        public int H1 { get; set; }
        public int H2 { get; set; }
        public int J1 { get; set; }
        public int J2 { get; set; }
        public int K1 { get; set; }
        public int K2 { get; set; }
        public int L { get; set; }

        public string LocationOfBlow { get; set; }

        public bool Screwed { get; set; }
        public bool Welded { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS133s")]
    public class DS133 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int F1 { get; set; }
        public int G1 { get; set; }
        public int W1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int F2 { get; set; }
        public int G2 { get; set; }
        public int W2 { get; set; }
        public int H1 { get; set; }
        public int H2 { get; set; }
        public int J1 { get; set; }
        public int J2 { get; set; }
        public int K1 { get; set; }
        public int K2 { get; set; }
        public int L { get; set; }
        public int M { get; set; }

        public string LocationOfBlow { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS134s")]
    public class DS134 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int F1 { get; set; }
        public int G1 { get; set; }
        public int W1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int F2 { get; set; }
        public int G2 { get; set; }
        public int W2 { get; set; }
        public int A3 { get; set; }
        public int B3 { get; set; }
        public int C3 { get; set; }
        public int D3 { get; set; }
        public int F3 { get; set; }
        public int G3 { get; set; }
        public int W3 { get; set; }
        public int H { get; set; }
        public int J1 { get; set; }
        public int J2 { get; set; }
        public int K1 { get; set; }
        public int K2 { get; set; }
        public int L1 { get; set; }
        public int L2 { get; set; }
        public int M { get; set; }


        public string LocationOfBlow { get; set; }

        public bool Screwed { get; set; }
        public bool Welded { get; set; }

        public bool C1D1 { get; set; }
        public bool C2D2 { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS136s")]
    public class DS136 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int A3 { get; set; }
        public int B3 { get; set; }
        public int C3 { get; set; }
        public int D3 { get; set; }
        public int E1 { get; set; }
        public int E2 { get; set; }
        public int E3 { get; set; }
        public int W3 { get; set; }
        public int W4 { get; set; }
        public int F { get; set; }
        public int G1 { get; set; }
        public int G2 { get; set; }
        public int H1 { get; set; }
        public int H2 { get; set; }
        public int J1 { get; set; }
        public int J2 { get; set; }
        public int K { get; set; }


        public string LocationOfBlow { get; set; }

        public bool Screwed { get; set; }
        public bool Welded { get; set; }

        public bool ScrewedSocket { get; set; }
        public bool SmlsButtWeldFitting { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS137s")]
    public class DS137 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int W1 { get; set; }
        public int WH1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }

        public int OepLength { get; set; }
        public int IepLength { get; set; }

        public string LocationOfBlow { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS138s")]
    public class DS138 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int W1 { get; set; }
        public int WH1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int W2 { get; set; }
        public int WH2 { get; set; }
        public int E { get; set; }
        public int F { get; set; }
        public int G { get; set; }
        public int L3 { get; set; }
        public int L6 { get; set; }
        public int L9 { get; set; }
        public int L12 { get; set; }


        public string LocationOfBlow { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS139s")]
    public class DS139 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int W1 { get; set; }
        public int WH1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int W2 { get; set; }
        public int WH2 { get; set; }
        public int E { get; set; }
        public int F { get; set; }
        public int G { get; set; }
        public int L3 { get; set; }
        public int L6 { get; set; }
        public int L9 { get; set; }
        public int L12 { get; set; }


        public string LocationOfBlow { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS140s")]
    public class DS140 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int W1 { get; set; }
        public int WH1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int W2 { get; set; }
        public int WH2 { get; set; }
        public int A3 { get; set; }
        public int B3 { get; set; }
        public int C3 { get; set; }
        public int D3 { get; set; }
        public int E3 { get; set; }
        public int W3 { get; set; }
        public int WH3 { get; set; }
        public int W4 { get; set; }
        public int F { get; set; }
        public int W5 { get; set; }
        public int G { get; set; }
        public int H { get; set; }
        public int J { get; set; }
        public int K { get; set; }
        public int L1 { get; set; }
        public int L2 { get; set; }
        public int M1 { get; set; }
        public int M2 { get; set; }
        public int N1 { get; set; }
        public int N2 { get; set; }

        public string LocationOfBlow { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS141s")]
    public class DS141 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int F1 { get; set; }
        public int W1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int F2 { get; set; }
        public int G2 { get; set; }
        public int W2 { get; set; }
        public int A3 { get; set; }
        public int B3 { get; set; }
        public int C3 { get; set; }
        public int D3 { get; set; }
        public int E3 { get; set; }
        public int F3 { get; set; }
        public int G3 { get; set; }
        public int W3 { get; set; }
        public int H { get; set; }
        public int J { get; set; }
        public int K { get; set; }
        public int L { get; set; }
        public int M1 { get; set; }
        public int M2 { get; set; }
        public int N1 { get; set; }
        public int N2 { get; set; }
        public int P1 { get; set; }
        public int P2 { get; set; }
        public int Q { get; set; }


        public string LocationOfBlow { get; set; }

        public bool Screwed { get; set; }
        public bool Welded { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS150s")]
    public class DS150 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A { get; set; }
        public int B3 { get; set; }
        public int B6 { get; set; }
        public int B9 { get; set; }
        public int B12 { get; set; }
        public int W1 { get; set; }
        public int C1 { get; set; }
        public int C2 { get; set; }
        public int C3 { get; set; }
        public int C4 { get; set; }
        public int C5 { get; set; }
        public int C6 { get; set; }
        public int C7 { get; set; }
        public int C8 { get; set; }
        public int C9 { get; set; }
        public int C10 { get; set; }
        public int C11 { get; set; }
        public int C12 { get; set; }

        public int VesselDiameter { get; set; }
        public int VesselCirc { get; set; }


        public string LocationOfBlow { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS151s")]
    public class DS151 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A { get; set; }
        public int B3 { get; set; }
        public int B6 { get; set; }
        public int B9 { get; set; }
        public int B12 { get; set; }
        public int W1 { get; set; }
        public int C1 { get; set; }
        public int C2 { get; set; }
        public int C3 { get; set; }
        public int C4 { get; set; }
        public int C5 { get; set; }
        public int C6 { get; set; }
        public int C7 { get; set; }
        public int C8 { get; set; }
        public int C9 { get; set; }
        public int C10 { get; set; }
        public int C11 { get; set; }
        public int C12 { get; set; }

        public int VesselDiameter { get; set; }
        public int VesselCirc { get; set; }


        public string LocationOfBlow { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS194s")]
    public class DS194 : DimensionalSheet
    {
        public int A { get; set; }
        public int B { get; set; }
        public int C { get; set; }
        public int D { get; set; }
        public int NoOfStuds { get; set; }
        public int SizeOfStuds { get; set; }
        public int InjPortSize { get; set; }
        public int WallThicknessBtoDby2 { get; set; }
        public int NoOfInjectionPorts { get; set; }

        public string LocationOfBlow { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS195s")]
    public class DS195 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int A2 { get; set; }
        public int B1 { get; set; }
        public int B2 { get; set; }
        public int C1 { get; set; }
        public int C2 { get; set; }
        public int D { get; set; }
        public int E { get; set; }
        public int F1 { get; set; }
        public int F2 { get; set; }
        public int G1 { get; set; }
        public int G2 { get; set; }
        public int H1 { get; set; }
        public int H2 { get; set; }
        public int J1 { get; set; }
        public int J2 { get; set; }
        public int K { get; set; }
        public int L { get; set; }
        public int M { get; set; }
        public int N { get; set; }
        public int P { get; set; }
        public int Q { get; set; }
        public int R { get; set; }
        public int S { get; set; }
        public int T { get; set; }

        public int BoltDia { get; set; }
        public int ThreadSize { get; set; }
        public int ThrdSize1 { get; set; }
        public int HoleDia { get; set; }
        public int StudDia { get; set; }
        public int ThreadDepth { get; set; }
        public int SquareWidth { get; set; }
        public int SquareDepth { get; set; }
        public int HeadDia { get; set; }
        public int FaceWidth { get; set; }

        public bool EyeBolt { get; set; }
        public bool StudThrdVlvBody { get; set; }
        public bool StudFlgVlvBody { get; set; }
        public bool CarriageOrStepBolt { get; set; }


        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS196s")]
    public class DS196 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A { get; set; }
        public int B { get; set; }
        public int C { get; set; }
        public int D { get; set; }
        public int E { get; set; }
        public int F { get; set; }
        public int G { get; set; }
        public int H { get; set; }
        public int J { get; set; }
        public int K { get; set; }

        public bool RisingStem { get; set; }
        public bool Rising { get; set; }
        public bool Handle { get; set; }
        public bool Slotted { get; set; }
        public bool OvalFlats { get; set; }
        public bool OneFlat { get; set; }
        public bool Tapered { get; set; }
        public bool SqFlats { get; set; }
        public bool SqTaper { get; set; }
    }

    // TODO: This one didn't seem to have the same properties for SealType.  Does the actual form not have the same fields as the rest?
    [Table("DS197s")]
    public class DS197 : DimensionalSheet
    {
        public bool Threaded { get; set; }
        public bool Smooth { get; set; }
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int A2 { get; set; }
        public int B1 { get; set; }
        public int B2 { get; set; }
        public int C1 { get; set; }
        public int C2 { get; set; }
        public int D { get; set; }
        public int E { get; set; }
        public int F1 { get; set; }
        public int F2 { get; set; }
        public int G1 { get; set; }
        public int G2 { get; set; }
        public int H1 { get; set; }
        public int H2 { get; set; }
        public int J1 { get; set; }
        public int J2 { get; set; }
        public int K { get; set; }
        public int L { get; set; }
        public int M { get; set; }
        public int N { get; set; }
        public int P { get; set; }
        public int Q { get; set; }
        public int R { get; set; }
        public int S { get; set; }

        public int NoOfStuds { get; set; }
        public int SizeOfStuds { get; set; }
        public int ThreadOD { get; set; }
        public int NoOfThreadsPerInch { get; set; }
        public int Lead { get; set; }

        public string OtherValveStemData { get; set; }
    }

    [Table("DS200s")]
    public class DS200 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int F1 { get; set; }
        public int H1 { get; set; }
        public int J1 { get; set; }
        public int W1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int F2 { get; set; }
        public int H2 { get; set; }
        public int J2 { get; set; }
        public int W2 { get; set; }
        public int He { get; set; }
        public int KbE { get; set; }
        public int LbF { get; set; }
        public int KcE { get; set; }
        public int LcF { get; set; }
        public int M { get; set; }
        public int N { get; set; }
        public int P { get; set; }
        public int Q { get; set; }
        public int R { get; set; }
        public int T { get; set; }
        public int U { get; set; }
        public int V { get; set; }
        public int Y { get; set; }
        public int Aa { get; set; }
        public int Bb { get; set; }
        public int Cc { get; set; }
        public int Dd { get; set; }
        public int Ee { get; set; }
        public int Ff { get; set; }
        public int Gg { get; set; }
        public int X1At3 { get; set; }
        public int X1At6 { get; set; }
        public int X1At9 { get; set; }
        public int X1At12 { get; set; }
        public int X2At3 { get; set; }
        public int X2At6 { get; set; }
        public int X2At9 { get; set; }
        public int X2At12 { get; set; }

        public bool RemoveHandle { get; set; }

        public bool CutStem { get; set; }
        public int StemClockPt { get; set; }

        public int Obstructions { get; set; }
        public int Notes { get; set; }

        public bool CircularBonnet { get; set; }
        public bool SquareBonnet { get; set; }
        public bool OvalBonnet { get; set; }
        public string LocationOfBlow { get; set; }

        public bool XX { get; set; }
        public bool YY { get; set; }
        public bool XY { get; set; }
        public bool YX { get; set; }
    }

    [Table("DS201s")]
    public class DS201 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int F1 { get; set; }
        public int H1 { get; set; }
        public int J1 { get; set; }
        public int W1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int F2 { get; set; }
        public int H2 { get; set; }
        public int J2 { get; set; }
        public int W2 { get; set; }
        public int He { get; set; }
        public int KbE { get; set; }
        public int LbF { get; set; }
        public int KcE { get; set; }
        public int LcF { get; set; }
        public int M { get; set; }
        public int N { get; set; }
        public int P { get; set; }
        public int Q { get; set; }
        public int R { get; set; }
        public int T { get; set; }
        public int U { get; set; }
        public int V { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public int Z { get; set; }
        public int Aa { get; set; }
        public int Bb { get; set; }
        public int Cc { get; set; }
        public int Xa3 { get; set; }
        public int Xa6 { get; set; }
        public int Xa9 { get; set; }
        public int Xa12 { get; set; }
        public int Xd3 { get; set; }
        public int Xd6 { get; set; }
        public int Xd9 { get; set; }
        public int Xd12 { get; set; }

        public bool RemoveHandle { get; set; }

        public bool CutStem { get; set; }
        public int StemClockPt { get; set; }

        public int Obstructions { get; set; }
        public int Notes { get; set; }

        public bool CircularBonnet { get; set; }
        public bool SquareBonnet { get; set; }
        public bool OvalBonnet { get; set; }
        public string LocationOfBlow { get; set; }

        public bool XX { get; set; }
        public bool YY { get; set; }
        public bool XY { get; set; }
        public bool YX { get; set; }
    }

    [Table("DS205s")]
    public class DS205 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int F1 { get; set; }
        public int H1 { get; set; }
        public int J1 { get; set; }
        public int W1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int F2 { get; set; }
        public int H2 { get; set; }
        public int J2 { get; set; }
        public int W2 { get; set; }
        public int L { get; set; }
        public int M { get; set; }
        public int N { get; set; }
        public int P { get; set; }
        public int Q { get; set; }
        public int R { get; set; }
        public int S { get; set; }
        public int T { get; set; }
        public int U { get; set; }
        public int V { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public int Z { get; set; }
        public int Aa { get; set; }
        public int Bb { get; set; }
        public int Cc { get; set; }
        public int X13 { get; set; }
        public int X16 { get; set; }
        public int X19 { get; set; }
        public int X112 { get; set; }
        public int X23 { get; set; }
        public int X26 { get; set; }
        public int X29 { get; set; }
        public int X212 { get; set; }

        public bool AlongTheLine { get; set; }
        public bool AgainstTheLine { get; set; }

        public string Notes { get; set; }
    }

    [Table("DS206s")]
    public class DS206 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int F1 { get; set; }
        public int J1 { get; set; }
        public int W1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int F2 { get; set; }
        public int J2 { get; set; }
        public int W2 { get; set; }
        public int Ha { get; set; }
        public int K1A { get; set; }
        public int K2A { get; set; }
        public int L1B { get; set; }
        public int L2B { get; set; }
        public int Ma { get; set; }
        public int Mb { get; set; }
        public int N { get; set; }
        public int P { get; set; }
        public int T { get; set; }
        public int U { get; set; }
        public int V { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public int Aa { get; set; }
        public int Bb { get; set; }
        public int Cc { get; set; }


        public bool RemoveHandle { get; set; }

        public bool CutStem { get; set; }
        public int StemClockPt { get; set; }

        public bool Screwed { get; set; }
        public bool Welded { get; set; }
        public bool Hex12at6 { get; set; }

        public bool Hex3at9 { get; set; }
        public string OtherHexOrientation { get; set; }

        public int Obstructions { get; set; }
        public int Notes { get; set; }

        public bool CircularBonnet { get; set; }
        public bool SquareBonnet { get; set; }
        public bool OvalBonnet { get; set; }
        public string LocationOfBlow { get; set; }

        public bool XX { get; set; }
        public bool YY { get; set; }
        public bool XY { get; set; }
        public bool YX { get; set; }
    }

    [Table("DS209s")]
    public class DS209 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int F1 { get; set; }
        public int G1 { get; set; }
        public int H1 { get; set; }
        public int J1 { get; set; }
        public int K1 { get; set; }
        public int W1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int F2 { get; set; }
        public int G2 { get; set; }
        public int H2 { get; set; }
        public int J2 { get; set; }
        public int K2 { get; set; }
        public int W2 { get; set; }
        public int E3 { get; set; }
        public int F3 { get; set; }
        public int L1 { get; set; }
        public int L2 { get; set; }
        public int M { get; set; }
        public int N { get; set; }
        public int P { get; set; }
        public int Q { get; set; }
        public int R { get; set; }
        public int S { get; set; }
        public int T { get; set; }
        public int U { get; set; }
        public int V { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public int Z { get; set; }
        public int Aa { get; set; }
        public int Bb { get; set; }


        public bool RemoveHandle { get; set; }

        public bool CutStem { get; set; }
        public int StemClockPt { get; set; }

        public bool Screwed { get; set; }
        public bool Welded { get; set; }
        public bool Hex12at6 { get; set; }

        public bool Hex3at9 { get; set; }
        public string OtherHexOrientation { get; set; }

        public int Obstructions { get; set; }
        public int Notes { get; set; }

        public string LocationOfBlow { get; set; }
    }

    [Table("DS210s")]
    public class DS210 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int F1 { get; set; }
        public int G1 { get; set; }
        public int H1 { get; set; }
        public int J1 { get; set; }
        public int K1 { get; set; }
        public int W1 { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int F2 { get; set; }
        public int G2 { get; set; }
        public int H2 { get; set; }
        public int J2 { get; set; }
        public int K2 { get; set; }
        public int W2 { get; set; }
        public int W3 { get; set; }
        public int W4 { get; set; }
        public int M { get; set; }
        public int N { get; set; }
        public int P { get; set; }
        public int R { get; set; }
        public int T { get; set; }
        public int U { get; set; }
        public int V { get; set; }
        public int Х { get; set; }
        public int Y { get; set; }
        public int Y1 { get; set; }
        public int Z { get; set; }


        public bool RemoveHandle { get; set; }

        public bool CutStem { get; set; }
        public int StemClockPt { get; set; }

        public bool Screwed { get; set; }
        public bool Welded { get; set; }
        public bool Hex12at6 { get; set; }

        public bool Hex3at9 { get; set; }
        public string OtherHexOrientation { get; set; }

        public int Obstructions { get; set; }
        public int Notes { get; set; }

        public string LocationOfBlow { get; set; }
    }

    [Table("DS230s")]
    public class DS230 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A1 { get; set; }
        public int B1 { get; set; }
        public int C1 { get; set; }
        public int D1 { get; set; }
        public int E1 { get; set; }
        public int F1 { get; set; }
        public int J1 { get; set; }
        public int W1 { get; set; }
        public int K1A { get; set; }
        public int K2A { get; set; }
        public int A2 { get; set; }
        public int B2 { get; set; }
        public int C2 { get; set; }
        public int D2 { get; set; }
        public int E2 { get; set; }
        public int F2 { get; set; }
        public int J2 { get; set; }
        public int W2 { get; set; }
        public int L1A { get; set; }
        public int L2A { get; set; }
        public int Ha { get; set; }
        public int Ja { get; set; }
        public int Ma { get; set; }
        public int Mb { get; set; }
        public int N { get; set; }
        public int P { get; set; }
        public int Q { get; set; }
        public int R { get; set; }
        public int S { get; set; }
        public int T { get; set; }
        public int U { get; set; }
        public int V1 { get; set; }
        public int V2 { get; set; }
        public int X1 { get; set; }
        public int X2 { get; set; }
        public int T1 { get; set; }
        public int Y2 { get; set; }
        public int Z { get; set; }
        public int Aa { get; set; }
        public int Yk { get; set; }

        public bool Screwed { get; set; }
        public bool Welded { get; set; }
        public bool Hex12at6 { get; set; }

        public bool Hex3at9 { get; set; }
        public string OtherHexOrientation { get; set; }


        public bool RemoveHandle { get; set; }

        public bool CutStem { get; set; }
        public int StemClockPt { get; set; }

        public int Obstructions { get; set; }
        public int Notes { get; set; }

        public bool CircularBonnet { get; set; }
        public bool SquareBonnet { get; set; }
        public string LocationOfBlow { get; set; }

        public bool XX { get; set; }
        public bool YY { get; set; }
        public bool XY { get; set; }
        public bool YX { get; set; }
    }

    [Table("DS300s")]
    public class DS300 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A3 { get; set; }
        public int B3 { get; set; }
        public int C3 { get; set; }
        public int D3 { get; set; }
        public int J1 { get; set; }
        public int J2 { get; set; }
        public int K1 { get; set; }
        public int K2 { get; set; }
        public int L1 { get; set; }
        public int L2 { get; set; }
        public int M { get; set; }
        public int W1 { get; set; }
        public int W2 { get; set; }
        public int W3 { get; set; }
        public int W4 { get; set; }


        public string LocationOfBlow { get; set; }

        public int Obstructions { get; set; } // supposed to be an image

        public string Notes { get; set; }
    }

    [Table("DS901s")]
    public class DS901 : DimensionalSheet
    {
        public string Flg { get; set; }

        public int A { get; set; }
        public int B { get; set; }
        public int C { get; set; }
        public int D { get; set; }
        public int E { get; set; }
        public int F { get; set; }
        public int G { get; set; }
        public int H { get; set; }
        public int J { get; set; }
        public int K { get; set; }
        public int L { get; set; }
        public int M { get; set; }
        public int N { get; set; }
        public int P { get; set; }
        public int Q { get; set; }
        public int R { get; set; }
        public int S { get; set; }
        public int T { get; set; }
        public int U { get; set; }
        public int V { get; set; }


        public string LocationOfLeakingTubeColumnNo { get; set; }

        public string LocationOfLeakingTubeRowNo { get; set; }

        public int FlatToFlat { get; set; }

        public int PointToPoint { get; set; }
    }
}