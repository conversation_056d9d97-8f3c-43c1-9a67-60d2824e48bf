using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using ClientPortal.Shared.Extensions;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.EDR
{
    /// <summary>
    ///     Additional equipment submission which includes a list of additional equipment and a date/time when the equipment is
    ///     needed by
    /// </summary>
    public class AdditionalEquipmentSubmission
    {
        /// <summary>
        ///     The list of additional equipment needed
        /// </summary>
        [JsonProperty(PropertyName = "equipment")]
        public ICollection<AdditionalEquipment> Equipment { get; set; }

        /// <summary>
        ///     The user that added the equipment to the request
        /// </summary>
        [JsonProperty(PropertyName = "addedBy")]
        public string AddedBy { get; set; }

        /// <summary>
        ///     The date the equipment was added
        /// </summary>
        [JsonProperty(PropertyName = "dateAdded")]
        public DateTime DateAdded { get; set; }

        /// <summary>
        ///     The date/time the additional equipment is needed by
        /// </summary>
        [JsonProperty(PropertyName = "neededBy")]
        public DateTime NeededBy { get; set; }

        public override bool Equals(object obj)
        {
            if (obj is not AdditionalEquipmentSubmission b) return false;

            if (Equipment.Count != b.Equipment.Count) return false;

            return Equipment.All(e => b.Equipment.Any(ee => ee.Equals(e))) && AddedBy == b.AddedBy &&
                   DateAdded == b.DateAdded && NeededBy == b.NeededBy;
        }
    }

    /// <summary>
    ///     Additional equipment needed on a job type
    /// </summary>
    public class AdditionalEquipment
    {
        /// <summary>
        ///     Name or description of the equipment needed
        /// </summary>
        [JsonProperty(PropertyName = "equipmentNeeded")]
        public string EquipmentNeeded { get; set; }

        /// <summary>
        ///     The quantity for the equipment needed
        /// </summary>
        [JsonProperty(PropertyName = "quantity")]
        public double Quantity { get; set; }

        /// <summary>
        ///     Notes for the equipment needed
        /// </summary>
        [JsonProperty(PropertyName = "notes")]
        public string Notes { get; set; }

        public override bool Equals(object obj)
        {
            if (obj is not AdditionalEquipment b) return false;

            var arbitrarilyChosenTolerance = 0.00001;
            return EquipmentNeeded == b.EquipmentNeeded && Math.Abs(Quantity - b.Quantity) < arbitrarilyChosenTolerance && Notes == b.Notes;
        }
    }

    /// <summary>
    ///     Job Type for equipment requests
    /// </summary>
    public class JobType
    {
        private static readonly Dictionary<string, string> JobTypes = new()
        {
            {"HS", "High Stop"},
            {"HST", "Hot Stop"},
            {"LS", "Line Stop"},
            {"HT", "Hot Tap"},
            {"PLLS", "Pipeline Line Stop"},
            {"PLHT", "Pipeline Hot Tap"},
            {"ILS", "Inflatable Line Stop"},
            {"LFRZ", "Line Freeze"},
            {"SS", "Short Stop"},
            {"SUST", "Sure Stop"},
            {"HTP", "High Temp Plug"},
            {"FLS", "Folding Head Line Stop"},
            // reverse lookup too
            {"High Stop", "HS"},
            {"Hot Stop", "HST"},
            {"Line Stop", "LS"},
            {"Hot Tap", "HT"},
            {"Pipeline Line Stop", "PLLS"},
            {"Pipeline Hot Tap", "PLHT"},
            {"Inflatable Line Stop", "ILS"},
            {"Line Freeze", "LFRZ"},
            {"Short Stop", "SS"},
            {"Sure Stop", "SUST"},
            {"High Temp Plug", "HTP"},
            {"Folding Head Line Stop", "FLS"}
        };

        private ICollection<RequestNote> _notes = new List<RequestNote>();

        /// <summary>
        ///     Unique identifier
        /// </summary>
        [JsonProperty(PropertyName = "id")]
        public int Id { get; set; }

        /// <summary>
        ///     Type of job
        /// </summary>
        [JsonProperty(PropertyName = "type")]
        [DisplayName("Type")]
        public string Type { get; set; }

        /// <summary>
        ///     Pipe size
        /// </summary>
        [JsonProperty(PropertyName = "pipeSize")]
        [DisplayName("Pipe Size")]
        public string PipeSize { get; set; }

        /// <summary>
        ///     Flange rating
        /// </summary>
        [JsonProperty(PropertyName = "flangeRating")]
        [DisplayName("Flange Rating")]
        public string FlangeRating { get; set; }

        /// <summary>
        ///     Operating pressure
        /// </summary>
        [JsonProperty(PropertyName = "operatingPressure")]
        [DisplayName("Operating Pressure")]
        public string OperatingPressure { get; set; }

        /// <summary>
        ///     Operating temperature
        /// </summary>
        [JsonProperty(PropertyName = "operatingTemperature")]
        [DisplayName("Operating Temperature")]
        public string OperatingTemperature { get; set; }

        /// <summary>
        ///     Status of the request
        /// </summary>
        [Required]
        [JsonProperty(PropertyName = "requestStatus")]
        public string RequestStatus { get; set; }

        /// <summary>
        ///     Whether the request is in a delay state or not
        /// </summary>
        [JsonProperty("delayed")]
        public bool Delayed { get; set; }

        /// <summary>
        ///     Description of the job
        /// </summary>
        [JsonProperty("description")]
        public string Description { get; set; }

        /// <summary>
        ///     QA Technician
        /// </summary>
        [JsonProperty("qualityControlTechnician")]
        public string QualityControlTechnician { get; set; }

        /// <summary>
        ///     Date the equipment was returned
        /// </summary>
        [JsonProperty(PropertyName = "dateReturned")]
        public DateTime? DateReturned { get; set; }

        /// <summary>
        ///     Where the equipment was returned
        /// </summary>
        [JsonProperty(PropertyName = "locationReturned")]
        public string LocationReturned { get; set; }

        /// <summary>
        ///     Date the equipment was shipped
        /// </summary>
        [JsonProperty(PropertyName = "dateShipped")]
        public DateTime? DateShipped { get; set; }

        /// <summary>
        ///     Reviewer of the package
        /// </summary>
        [JsonProperty(PropertyName = "packageReviewer")]
        public string PackageReviewer { get; set; }

        /// <summary>
        ///     Travel distance to the top of the pipe
        /// </summary>
        [JsonProperty("travelDistanceToTopPipe")]
        [DisplayName("Travel Distance to Top of Pipe")]
        public string TravelDistanceToTopPipe { get; set; }

        /// <summary>
        ///     Target ship date for equipment
        /// </summary>
        [JsonProperty("targetShipDate")]
        public DateTime? TargetShipDate { get; set; }

        /// <summary>
        ///     JSS Number
        /// </summary>
        [JsonProperty("jssNumber")]
        [DisplayName("JSS Number")]
        public string JSSNumber { get; set; }

        /// <summary>
        ///     Tap Size
        /// </summary>
        [JsonProperty("tapSize")]
        [DisplayName("Tap Size")]
        public string TapSize { get; set; }

        /// <summary>
        ///     Date received
        /// </summary>
        [JsonProperty("receivedDate")]
        public DateTime? ReceivedDate { get; set; }

        /// <summary>
        ///     Shipping Method
        /// </summary>
        [JsonProperty(PropertyName = "shippingMethod")]
        public string ShippingMethod { get; set; }

        /// <summary>
        ///     Notes collected on the equipment request
        /// </summary>
        [JsonProperty("notes")]
        public ICollection<RequestNote> Notes
        {
            get => _notes;
            set { _notes = value?.OrderBy(note => note.Created).ToList() ?? new List<RequestNote>(); }
        }

        /// <summary>
        ///     New Expected Date
        /// </summary>
        [JsonProperty(PropertyName = "newExpectedDate")]
        public DateTime? NewExpectedDate { get; set; }

        /// <summary>
        ///     Reason For Delay
        /// </summary>
        [JsonProperty(PropertyName = "reasonForDelay")]
        public string ReasonForDelay { get; set; }

        /// <summary>
        ///     Whether the job has been delayed ever.
        /// </summary>
        [JsonProperty(PropertyName = "beenDelayed")]
        public bool BeenDelayed { get; set; }

        /// <summary>
        ///     Latest comment on the delay
        /// </summary>
        [JsonProperty(PropertyName = "delayComment")]
        public string DelayComment { get; set; }

        /// <summary>
        ///     Additional equipment that was added to the job
        /// </summary>
        [JsonProperty(PropertyName = "additionalEquipment")]
        public ICollection<AdditionalEquipmentSubmission> AdditionalEquipment { get; set; }

        /// <summary>
        ///     Whether or not the job type is canceled or not.
        /// </summary>
        [JsonProperty(PropertyName = "canceled")]
        public bool Canceled { get; set; }

        /// <summary>
        ///     Whether or not this job type was added as additional hot tapping equipment to a request after progressed from the New status
        /// </summary>
        [JsonProperty(PropertyName = "addedToOriginalJob")]
        public bool? AddedToOriginalJob { get; set; }
        
        /// <summary>
        ///     Builds a default description based on <see cref="PipeSize" />, <see cref="FlangeRating" />, and <see cref="Type" />
        ///     of the job
        /// </summary>
        /// <param name="jobType"></param>
        /// <returns></returns>
        public static string InitialDescription(JobType jobType)
        {
            if (jobType == null) throw new ArgumentNullException(nameof(jobType));
            return jobType.TapSize != null && jobType.Type is "Hot Tap" or "Pipeline Hot Tap"
                ? $"{jobType.TapSize} x {jobType.PipeSize} {jobType.FlangeRating} {JobTypes[jobType.Type]}"
                : $"{jobType.PipeSize} {jobType.FlangeRating} {JobTypes[jobType.Type]}";
        }

        /// <summary>
        ///     This method should be used on the original request
        ///     Checks to see if the request has been acknowledged
        /// </summary>
        /// <param name="request"></param>
        /// <returns>true/false</returns>
        public bool CheckForAcknowledged(EquipmentRequest request) => request != null &&
                                                                      RequestStatus?.ToLower() == "new" &&
                                                                      request.JobTypes.First().RequestStatus
                                                                          ?.ToLower() == "request acknowledged";

        /// <summary>
        ///     This method should be used on the original request
        ///     Checks to see if the request has been delayed
        /// </summary>
        /// <param name="request"></param>
        /// <returns>true/false</returns>
        public bool CheckForDelay(EquipmentRequest request)
        {
            return request != null && !Delayed && request.JobTypes.First(j => j.Id == Id).Delayed;
        }

        /// <summary>
        ///     This method should be used on the original request
        ///     Checks to see if the request is ready for pickup
        /// </summary>
        /// <param name="request"></param>
        /// <returns>true/false</returns>
        public bool CheckForReadyForPickup(EquipmentRequest request)
        {
            return request != null && RequestStatus?.ToLower() == "building equipment" &&
                   request.JobTypes.First(j => j.Id == Id).RequestStatus?.ToLower() == "ready for pickup";
        }

        /// <summary>
        ///     This method should be used on the original request
        ///     Checks to see if the request has been updated while it is delayed
        /// </summary>
        /// <param name="request"></param>
        /// <returns>true/false</returns>
        public bool CheckForDelayUpdate(EquipmentRequest request)
        {
            return request != null && Delayed && request.JobTypes.First(j => j.Id == Id).Delayed &&
                   Notes.Count < request.JobTypes.First(j => j.Id == Id).Notes.Count;
        }

        /// <summary>
        ///     Check to see if status on any jobType has changed
        /// </summary>
        /// <param name="jobType"></param>
        /// <returns>true/false</returns>
        public bool CheckForStatusChange(JobType jobType) => jobType != null && RequestStatus != jobType.RequestStatus;

        /// <summary>
        ///     Check to see the status has been reverted
        /// </summary>
        /// <param name="originalJobType"></param>
        /// <returns>true/false</returns>
        public bool CheckRevertedStatus(JobType originalJobType)
        {
            if (originalJobType?.RequestStatus == null) return false;
            if (RequestStatus == null) return false;
            var jobStatus = new[]
            {
                "new",
                "request acknowledged",
                "building equipment",
                "ready for pickup",
                "shipped",
                "partially returned",
                "returned",
                "hold"
            };
            var newIndex = Array.IndexOf(jobStatus, RequestStatus.ToLower());
            var oldIndex = Array.IndexOf(jobStatus, originalJobType.RequestStatus.ToLower());
            return oldIndex > newIndex;
        }

        /// <summary>
        ///     Check to see the status has been reverted
        /// </summary>
        /// <param name="updatedJobType"></param>
        /// <returns>true/false</returns>
        public bool HasAnyChanges(JobType updatedJobType)
        {
            var allProperties = typeof(JobType).GetProperties().Select(property => property.Name)
                .Where(propertyName => !string.Equals(propertyName, "notes", StringComparison.InvariantCultureIgnoreCase) && !string.Equals(propertyName, "recieveddate", StringComparison.InvariantCultureIgnoreCase)).ToArray();
            var jobTypeChanges = this
                .DetailedPropertyCompare(updatedJobType)
                .Where(change => allProperties.Contains(change.PropertyName)).ToList();

            return jobTypeChanges.Any();
        }

        /// <summary>
        ///     Returns true if there are differences between the original job types collection and the new job types collection
        /// </summary>
        /// <param name="originalJobTypes"></param>
        /// <param name="newJobTypes"></param>
        /// <returns></returns>
        public static JobTypeFieldChangeSummary DetectEquipmentChanges(List<JobType> originalJobTypes,
            List<JobType> newJobTypes)
        {
            // Throw an exception if we don't have enough information
            if (originalJobTypes == null) throw new ArgumentNullException(nameof(originalJobTypes));
            if (newJobTypes == null) throw new ArgumentNullException(nameof(newJobTypes));

            // Prepare the field change summary
            var changes = new JobTypeFieldChangeSummary
            {
                OriginalJobTypes = originalJobTypes,
                NewJobTypes = newJobTypes
            };

            // Summarize field changes for all original job types
            foreach (var jobType in originalJobTypes)
            {
                var matchingJobType = newJobTypes.Find(j => j.Id == jobType.Id);

                // If we don't find a matching "new" job type, that means a job type was deleted, no diff to show.
                if (matchingJobType == null) continue;

                if (matchingJobType.JSSNumber == null)
                    throw new InvalidOperationException(
                        "Cannot determine field change summary for a job type without a JSSNumber");

                var fieldsWeCareAbout = new List<string>
                {
                    nameof(JSSNumber),
                    nameof(Type),
                    nameof(PipeSize),
                    nameof(TapSize),
                    nameof(FlangeRating),
                    nameof(OperatingPressure),
                    nameof(OperatingTemperature),
                    nameof(TravelDistanceToTopPipe),
                    nameof(TargetShipDate),
                    nameof(Description)
                };
                
                // This allows us to use the same method for the equipment grid email and
                // the job info changes email, in new this will always be null/empty and it is not in
                // the grid but is a field on the JobInfo tab
                if (newJobTypes.FirstOrDefault()?.RequestStatus?.ToLower() != "new")
                    fieldsWeCareAbout.Add(nameof(Description));
                var jobTypeChanges = jobType
                    .DetailedPropertyCompare(matchingJobType)
                    .Where(change => fieldsWeCareAbout.Contains(change.PropertyName)).ToList();

                if (jobTypeChanges.Any())
                    changes[matchingJobType.JSSNumber] = jobTypeChanges.ToList();
            }

            return changes;
        }

        
    }
}