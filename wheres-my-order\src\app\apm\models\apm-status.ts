import { APMTaskType } from '.';
import { WorkOrder } from './data';
import { WorkOrderDetail } from './view-models';
import { InspectionType } from './view-models/inspection-type';

export type APMStatus =
    | 'Scheduled'
    | 'In Progress'
    | 'Completed'
    | 'Canceled'
    | 'Not Started'
    | 'Published'
    | 'On Hold'
    | 'Overdue'
    | 'Unknown';

export const allStatuses: ReadonlyArray<APMStatus> = [
    'Canceled',
    'Completed',
    'In Progress',
    'Not Started',
    'On Hold',
    'Overdue',
    'Published',
    'Scheduled'
];

export function statusColor(status: APMStatus) {
    switch (status) {
        case 'Scheduled':
            return '#6682bb';
        case 'In Progress':
            return '#eddb7e';
        case 'Completed':
            return '#60a69f';
        case 'Canceled':
            return '#705370';
        case 'Not Started':
            return '#acacac';
        case 'Published':
            return '#1d7570';
        case 'On Hold':
            return '#eca75b';
        case 'Overdue':
            return '#a37182';
        case 'Unknown':
            return '#5c5c5c';
    }
}

/**
 * Gives the tasks available to change to for the task based on other
 * tasks on the work order and what the current status is of those
 * tasks and the one that the user wants to change the status of.
 * @param workOrder work order associated with the task in question
 * @param taskId id of the task whose status could be changed
 * @param taskType type of task associated with the id
 * @param taskStatus current status of the task associated with the id
 * @returns available statuses that the task could change to
 */
export function availableStatusesToChangeTo(
    workOrder: WorkOrder,
    taskId: string,
    taskType: APMTaskType,
    taskStatus: APMStatus
) {
    const otherTasksOfSameTaskType = workOrder.tasks.filter(
        (t) => t.id !== taskId && t.taskType === taskType
    );
    let conflictingTaskTypesInProgress = false;
    if (
        taskType === 'Full' &&
        workOrder.tasks.some(
            (t) =>
                (t.id !== taskId &&
                    t.taskType === 'External Visual' &&
                    inProgressStatus(t.status.currentValue as APMStatus)) ||
                workOrder.tasks.some(
                    (t) =>
                        t.id !== taskId &&
                        t.taskType === 'Internal Visual' &&
                        inProgressStatus(t.status.currentValue as APMStatus)
                )
        )
    ) {
        conflictingTaskTypesInProgress = true;
    }
    if (
        (taskType === 'External Visual' || taskType === 'Internal Visual') &&
        workOrder.tasks.some(
            (t) =>
                t.id !== taskId &&
                t.taskType === 'Full' &&
                inProgressStatus(t.status.currentValue as APMStatus)
        )
    ) {
        conflictingTaskTypesInProgress = true;
    }
    const statusIsComplete =
        taskStatus === 'Completed' || taskStatus === 'Canceled';
    if (
        statusIsComplete &&
        (otherTasksOfSameTaskType.some(
            (t) =>
                t.status.currentValue !== 'Completed' &&
                t.status.currentValue !== 'Canceled'
        ) ||
            conflictingTaskTypesInProgress)
    ) {
        return workOrder.status.options
            ?.map((o) => o.value)
            .filter(
                (s) =>
                    s !== 'Scheduled' &&
                    s !== 'In Progress' &&
                    s !== 'On Hold' &&
                    s !== 'Published'
            );
    }

    return (
        workOrder.status.options
            ?.map((o) => o.value)
            .filter((value) => value.toLowerCase() !== 'published') ?? [
            'Scheduled',
            'In Progress',
            'Completed',
            'Canceled',
            'On Hold'
        ]
    );
}

export function cannotChangeStatusFromCanceledOrCompleted(
    task: InspectionType,
    workOrderDetail: WorkOrderDetail
) {
    if (workOrderDetail.status === 'Published') return true;
    const currentTaskClosed =
        task.status === 'Completed' || task.status === 'Canceled';
    const otherTasksInProgress = workOrderDetail.inspectionTypes.some(
        (t) =>
            t.id !== task.id &&
            t.taskType === task.taskType &&
            !['Completed', 'Canceled'].includes(t.status)
    );

    let conflictingTaskTypesInProgress =
        (task.taskType === 'Full' &&
            workOrderDetail.inspectionTypes.some(
                (t) =>
                    t.id !== task.id &&
                    t.taskType === 'External Visual' &&
                    inProgressStatus(t.status)
            )) ||
        (task.taskType === 'Internal Visual' &&
            workOrderDetail.inspectionTypes.some(
                (t) =>
                    t.id !== task.id &&
                    t.taskType === 'Internal Visual' &&
                    inProgressStatus(t.status)
            )) ||
        ((task.taskType === 'External Visual' ||
            task.taskType === 'Internal Visual') &&
            workOrderDetail.inspectionTypes.some(
                (t) =>
                    t.id !== task.id &&
                    t.taskType === 'Full' &&
                    inProgressStatus(t.status)
            ));

    return (
        currentTaskClosed &&
        (otherTasksInProgress || conflictingTaskTypesInProgress)
    );
}

export function inProgressStatus(status: APMStatus) {
    return status !== 'Completed' && status !== 'Canceled';
}
