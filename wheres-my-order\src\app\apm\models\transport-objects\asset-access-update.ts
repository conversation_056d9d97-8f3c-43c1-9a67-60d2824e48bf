export interface AssetAccessUpdate {
    id: string;
    hasInsulation: string;
    possibleAsbestos: string;
    possibleAsbestosComment: string;
    jacketingType: string[];
    insulationType: string[];
    insulationTypeComment: string;
    insulationRemovalRequired: string;
    insulationRemovalRequiredComment: string;
    heatTracing: string[];
    heatTracingComment: string;
    existingInspectionPorts: string;
    insulationPlugsMissing: string;
    insulationPlugsMissingComment: string;
    additionalPortsNeeded: string;
    additionalPortsNeededComment: string;
    coatingType: string[];
    coatingTypeComment: string;
    coatingCondition: string[];
    coatingConditionsObserved: string[];
    coatingConditionsObservedComment: string;
    coatingRemovalRequired: string;
    coatingRemovalRequiredComment: string;
    corrosionIdentified: string[];
    corrosionIdentifiedComment: string;
    corrosionRemovalRecommendation: string[];
    corrosionRemovalRecommendationComment: string;
    fixedEquipmentLaddersStairwaysPlatformsInstalled: string;
    fixedEquipmentLaddersStairwaysPlatformsInstalledComment: string;
    allComponentsUnder4FeetInHeight: string;
    allComponentsUnder4FeetInHeightComment: string;
    ladderRequirements: string[];
    ladderRequirementsComment: string;
    aerialLiftNeeded: string;
    aerialLiftNeededComment: string;
    accessForAerialLiftForAllLocationsAtHeight: string;
    gasPoweredPermitted: string;
    batteryPoweredPermitted: string;
    clientRequiredProofOfTraining: string;
    clientProvidedOperator: string;
    estimatedDistanceToAnyLiveElectricalOverheadLines: number;
    scaffoldingRequired: string;
    scaffoldingRequiredComment: string;
    ropeAccessRequired: string;
    ropeAccessRequiredComment: string;
    assetOutOfService: string;
    inspectionOpeningsPresent: string;
    inspectionOpeningTypes: string[];
    inspectionOpeningTypesComment: string;
    sizeOfAllAccessOpenings: string;
    ventilationRequirements: string[];
    ventilationRequirementsComment: string;
    cleaningRecommendations: string[];
    cleaningRecommendationsComment: string;
    cleaningServiceReview: string;
    cleaningServiceReviewComment: string;
    coatingLinerType: string[];
    coatingLinerTypeComment: string;
    coatingLinerConditions: string[];
    coatingLinerConditionsObserved: string[];
    coatingLinerConditionsObservedComment: string;
}
