import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxButtonModule } from 'devextreme-angular';
import { DxFormModule } from 'devextreme-angular/ui/form';
import { DxTabPanelModule } from 'devextreme-angular/ui/tab-panel';
import { ToastrModule } from 'ngx-toastr';
import { ProjectDetailPipe } from '../../pipes';
import { ApmService } from '../../services';
import { DetailsTabComponent } from '../details-tab/details-tab.component';
import { AssetsDetailsComponent } from './assets-details.component';

describe('AssetsDetailsComponent', () => {
    let component: AssetsDetailsComponent;
    let fixture: ComponentFixture<AssetsDetailsComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxTabPanelModule,
                DxFormModule,
                DxButtonModule,
                HttpClientTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [
                AssetsDetailsComponent,
                DetailsTabComponent,
                ProjectDetailPipe
            ],
            providers: [
                {
                    provide: ApmService,
                    useValue: {}
                }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(AssetsDetailsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
