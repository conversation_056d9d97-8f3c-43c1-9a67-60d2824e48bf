﻿using System;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class TaskUpdate
    {
        [JsonProperty("clientWorkOrderDescription")]
        public string ClientWorkOrderDescription { get; set; }

        [<PERSON>sonProperty("clientWorkOrderNumber")]
        public string ClientWorkOrderNumber { get; set; }

        [<PERSON><PERSON><PERSON>roper<PERSON>("leadTechnician")] public string LeadTechnician { get; set; }

        [JsonProperty("plannedEnd")] public DateTime? PlannedEnd { get; set; }

        [JsonProperty("taskAssignees")] public string[] TaskAssignees { get; set; }

        [JsonProperty("plannedStart")] public DateTime? PlannedStart { get; set; }

        [JsonProperty("status")] public string Status { get; set; }

        [Json<PERSON>roperty("supervisor")] public string Supervisor { get; set; }

        [JsonProperty("workOrderId")] public string WorkOrderID { get; set; }

        [<PERSON>son<PERSON>roperty("projectId")] public string ProjectID { get; set; }
        
        [JsonProperty("clientCostCode")] public string ClientCostCode { get; set; }

        [Json<PERSON>roperty("purchaseOrderAFE")] public string PurchaseOrderAFE { get; set; }
        [JsonProperty("dueDate")] public DateTime? DueDate { get; set; }
    }
}