﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Authorization.Handlers
{
    /// <summary>
    ///     Handles the IsTeamEmployeeRequirement
    /// </summary>
    public class IsTeamEmployeeHandler : AuthorizationHandler<IsTeamEmployeeRequirement>
    {
        #region Fields and Constants

        private readonly IUserProfilesService _userProfiles;

        #endregion Fields and Constants

        #region Constructors

        /// <summary>
        ///     Constructs an IsTeamEmployeeHandler, injecting a UserProfilesService
        /// </summary>
        /// <param name="userProfiles"></param>
        public IsTeamEmployeeHandler(IUserProfilesService userProfiles)
        {
            _userProfiles = userProfiles;
        }

        #endregion Constructors

        /// <summary>
        ///     Handle the <see cref="IsTeamEmployeeRequirement"/>
        /// </summary>
        /// <param name="context"></param>
        /// <param name="requirement"></param>
        /// <returns></returns>
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context,
            IsTeamEmployeeRequirement requirement)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));

            if (!context.User.Identity.IsAuthenticated)
            {
                context.Fail();
                return;
            }

            var email = context.User.Identity.Name.ToLower();

            var user = await _userProfiles.GetAsync(email);
            if (user == null) return;

            if (user.IsTeamEmployee) context.Succeed(requirement);
        }
    }
}