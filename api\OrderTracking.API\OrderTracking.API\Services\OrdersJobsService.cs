﻿//using System;
//using System.Collections.Generic;
//using System.Data.SqlClient;
//using System.Linq;
//using System.Threading.Tasks;
//using ClientPortal.Shared.Models;
//using Dapper;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.Logging;
//using OrderTracking.API.Interfaces.Services;
//using OrderTracking.API.Models;

//namespace OrderTracking.API.Services
//{
//    /// <summary>
//    ///     Service class to provide access to Order ETL Jobs (WMO)
//    /// </summary>
//    public class OrdersJobsService : IOrdersJobsService
//    {
//        private readonly string _connectionString;
//        private readonly OrderContext _context;
//        private readonly ILogger<IOrdersJobsService> _logger;

//        /// <summary>
//        ///     Constructs an OrdersJobsService, injecting a logger, configuration, and EntityFramework-based <see cref="OrderContext"/>
//        /// </summary>
//        /// <param name="logger"></param>
//        /// <param name="config"></param>
//        /// <param name="context"></param>
//        public OrdersJobsService(ILogger<IOrdersJobsService> logger, IConfiguration config, OrderContext context)
//        {
//            if (config == null) throw new ArgumentNullException(nameof(config));

//            _logger = logger;
//            _connectionString = config.GetSection("SQLConnections")["Orders"];
//            _context = context;
//        }

//        /// <summary>
//        ///     Get existing Order jobs (ETL) with Entity Framework
//        /// </summary>
//        /// <returns></returns>
//        public IQueryable<OrdersJob> GetJobs()
//        {
//            return Queryable.OrderByDescending(_context.OrdersJobs, job => job.StartedUTC);
//            //return _context.OrdersJobs.OrderByDescending(job => job.StartedUTC);
//        }

//        /// <summary>
//        ///     Get existing Order jobs (ETL) with dapper
//        /// </summary>
//        /// <param name="queryString"></param>
//        /// <returns></returns>
//        public async Task<IEnumerable<OrdersJob>> GetItemsAsync(string queryString)
//        {
//            await using var connection = new SqlConnection(_connectionString);
//            await connection.OpenAsync();

//            return await connection.QueryAsync<OrdersJob>("SELECT * FROM OrdersJobs");
//        }
//    }
//}