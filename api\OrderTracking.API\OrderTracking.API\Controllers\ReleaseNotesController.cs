﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderTracking.API.Interfaces.Services;
using OrderTracking.API.Models;

namespace OrderTracking.API.Controllers
{
    /// <summary>
    ///     HTTP Controller for release notes
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ReleaseNotesController : ControllerBase
    {
        private readonly IReleaseNotesService _releaseNotes;

        /// <summary>
        ///     Constructor for controller
        /// </summary>
        /// <param name="releaseNotes"></param>
        public ReleaseNotesController(IReleaseNotesService releaseNotes)
        {
            _releaseNotes = releaseNotes;
        }

        /// <summary>
        ///     Get endpoint for release notes
        ///     GET: api/ReleaseNotes
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IEnumerable<ReleaseNotes>> Get()
        {
            return await _releaseNotes.GetItemsAsync();
        }

        /// <summary>
        ///     Get endpoint for single release notes document
        ///     GET api/ReleaseNotes/5
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<ReleaseNotes>> Get(string id)
        {
            var releaseNotes = await _releaseNotes.GetItemAsync(id);
            if (releaseNotes == null) return NotFound();
            return releaseNotes;
        }

        /// <summary>
        ///     Post endpoint for release notes
        ///     POST api/ReleaseNotes
        /// </summary>
        /// <param name="releaseNotes"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(Policy = "App:Admin")]
        public async Task<ActionResult<ReleaseNotes>> Post([FromBody] ReleaseNotes releaseNotes)
        {
            if (releaseNotes == null) throw new ArgumentNullException(nameof(releaseNotes));
            releaseNotes = await _releaseNotes.AddItemAsync(releaseNotes);
            return CreatedAtAction(nameof(Get), new { releaseNotes.Id}, releaseNotes);
        }

        /// <summary>
        ///     Put endpoint for release notes
        ///     PUT api/ReleaseNotes/5
        /// </summary>
        /// <param name="id"></param>
        /// <param name="releaseNotes"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        [Authorize(Policy = "App:Admin")]
        public async Task<IActionResult> Put(string id, [FromBody] ReleaseNotes releaseNotes)
        {
            if (releaseNotes == null) throw new ArgumentNullException(nameof(releaseNotes));

            if (id != releaseNotes.Id) return BadRequest();
            var original = await _releaseNotes.GetItemAsync(id);
            if (original == null) return NotFound();
            await _releaseNotes.UpdateItemAsync(id, releaseNotes);
            return NoContent();
        }

        /// <summary>
        ///     Delete endpoint for release notes
        ///     DELETE api/ReleaseNotes/5
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [Authorize(Policy = "App:Admin")]
        public async Task<ActionResult<ReleaseNotes>> Delete(string id)
        {
            if (id == null) throw new ArgumentNullException(nameof(id));

            var original = await _releaseNotes.GetItemAsync(id);
            if (original == null) return NotFound();
            await _releaseNotes.DeleteItemAsync(id);
            return original;
        }
    }
}