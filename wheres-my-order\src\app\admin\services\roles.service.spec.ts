import {
    HttpClientTestingModule,
    HttpTestingController,
} from '@angular/common/http/testing';
import { inject, TestBed } from '@angular/core/testing';

import { Role } from '../models';
import { RolesService } from './roles.service';

describe('RolesService', () => {
    const roles: Role[] = [{ id: 'abc:def', roleName: 'ABC def' }];

    beforeEach(() =>
        TestBed.configureTestingModule({
            providers: [RolesService],
            imports: [HttpClientTestingModule],
        })
    );

    afterEach(inject(
        [HttpTestingController],
        (httpMock: HttpTestingController) => {
            httpMock.verify();
        }
    ));

    it('should be created', () => {
        const service: RolesService = TestBed.inject(RolesService);

        expect(service).toBeTruthy();
    });

    it('call getRoles() should return Observable<Roles[]>', inject(
        [HttpTestingController, RolesService],
        (httpMock: HttpTestingController, service: RolesService) => {
            // We call the service
            service.getRoles().subscribe((data) => {
                expect(data.length).toBe(1);
                expect(data[0].id).toBe('abc:def');
                expect(data[0].roleName).toBe('ABC def');
            });
            // We set the expectations for the HttpClient mock
            const req = httpMock.expectOne('https://localhost:5001/api/roles');

            expect(req.request.method).toEqual('GET');
            // Then we set the fake data to be returned by the mock
            req.flush(roles);
        }
    ));

    it('call delete() should return with no body', inject(
        [HttpTestingController, RolesService],
        (httpMock: HttpTestingController, service: RolesService) => {
            // We call the service
            service.delete('abc:def').subscribe((data) => {
                expect(data).toBeFalsy();
            });
            // We set the expectations for the HttpClient mock
            const req = httpMock.expectOne(
                `https://localhost:5001/api/roles/${roles[0].id}`
            );

            expect(req.request.method).toEqual('DELETE');
            // Then we set the fake data to be returned by the mock
            req.flush(null);
        }
    ));
});
