import { IMediaEntry } from '../../../shared/models/attributes';

export interface LeakReportWorkDetails {
    id: string;
    client: LeakReportWorkDetailsField<string>;
    state: LeakReportWorkDetailsField<string>;
    city: LeakReportWorkDetailsField<string>;
    postalCode: LeakReportWorkDetailsField<string>;
    facility: LeakReportWorkDetailsField<string>;
    area: LeakReportWorkDetailsField<string>;
    clientContact: LeakReportWorkDetailsField<string>;
    clientWorkOrder: LeakReportWorkDetailsField<string>;
    purchaseOrder: LeakReportWorkDetailsField<string>;
    teamProjectNumber: LeakReportWorkDetailsField<string>;
    referenceEdition: LeakReportWorkDetailsField<string>;
    inspectedBy: LeakReportWorkDetailsField<string>;
    jobDescription: LeakReportWorkDetailsField<string>;
    lease: LeakReportWorkDetailsField<string>;
    clientContactNumber: LeakReportWorkDetailsField<string>;
    teamDistrict: LeakReportWorkDetailsField<string>;
    inspectionTypes: LeakReportWorkDetailsField<string[]>;
    clientCostCode: LeakReportWorkDetailsField<string>;
    inspectionReference: LeakReportWorkDetailsField<string>;
    inspectionDate: LeakReportWorkDetailsField<string>;
    inspectorCertificateNumber: LeakReportWorkDetailsField<string>;
    reviewedBy: LeakReportWorkDetailsField<string>;
    reviewerEmail: LeakReportWorkDetailsField<string>;
    reviewerCertificateNumber: LeakReportWorkDetailsField<string>;
}

export interface LeakReportWorkDetailsField<T> {
    value: T;
    comment: string;
    photos: IMediaEntry[]; // TODO: type better
    options?: string[];
}
