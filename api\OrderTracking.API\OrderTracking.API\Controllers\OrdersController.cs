﻿//using System;
//using System.Linq;
//using System.Threading.Tasks;
//using ClientPortal.Shared.Extensions;
//using ClientPortal.Shared.Models;
//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Http;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.Extensions.Logging;
//using OrderTracking.API.Interfaces;
//using OrderTracking.API.Models;
//using OrderTracking.API.Services;

//namespace OrderTracking.API.Controllers
//{
//    /// <summary>
//    ///     API Controller to access WMO order records
//    /// </summary>
//    [Authorize]
//    [Route("api/[controller]")]
//    [ApiController]
//    [Authorize(Policy = "UserIsActive")]
//    public class OrdersController : ControllerBase
//    {
//        #region Constructors

//        /// <summary>
//        ///     Constructor for controller that injects dependencies for user profiles, orders, and a results loader, as well as
//        ///     logging
//        /// </summary>
//        /// <param name="logger"></param>
//        /// <param name="userProfiles"></param>
//        /// <param name="orders"></param>
//        /// <param name="resultsLoader"></param>
//        public OrdersController(
//            ILogger<OrdersController> logger,
//            IUserProfilesService userProfiles,
//            IOrdersService orders,
//            IClientPortalResultsLoader resultsLoader
//        )
//        {
//            _logger = logger;
//            _userProfiles = userProfiles;
//            _orders = orders;
//            _resultsLoader = resultsLoader;
//        }

//        #endregion

//        private static bool ShouldForbid(UserProfile user) =>
//            !user.DistrictIds.Any() &&
//            !user.CustomerAccounts.Any() &&
//            !user.HasRole("wmo:manufacturinguser") &&
//            !user.HasRole("wmo:engineeringuser") &&
//            !user.HasRole("app:admin");

//        #region Fields and Constants

//        private readonly IOrdersService _orders;
//        private readonly IClientPortalResultsLoader _resultsLoader;
//        private readonly ILogger<OrdersController> _logger;
//        private readonly IUserProfilesService _userProfiles;

//        #endregion

//        #region Public Methods

//        /// <summary>
//        ///     Get Orders according to the query parameters defined by the loadOptions.
//        ///     This object is built by the DevExtreme DxDataGrid that is used in the
//        ///     Client Portal's front end application.
//        ///     Returns orders within the last 4 months.
//        /// </summary>
//        /// <param name="loadOptions"></param>
//        /// <returns></returns>
//        [HttpGet]
//        public async Task<IActionResult> Get(DataSourceLoadOptions loadOptions)
//        {
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);
//            if (user == null) return Unauthorized();

//            if (ShouldForbid(user)) return Forbid();

//            var ordersQueryable = _orders.GetOrdersForUser(user);
//            var loadResult = await _resultsLoader.LoadResult(loadOptions, ordersQueryable);
//            if (!user.IsTeamEmployee)
//                loadResult.data = _orders.TrimOrderForClient(loadResult);
//            return Ok(loadResult);
//        }

//        /// <summary>
//        ///     Get Orders according to the query parameters defined by the loadOptions.
//        ///     Returns orders, regardless of age.
//        /// </summary>
//        /// <param name="loadOptions"></param>
//        /// <returns></returns>
//        [HttpGet("Archived")]
//        public async Task<IActionResult> Archived(DataSourceLoadOptions loadOptions)
//        {
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);
//            if (user == null) return Unauthorized();

//            if (ShouldForbid(user)) return Forbid();

//            var ordersQueryable = _orders.GetOrdersForUser(user, true);

//            var loadResult = await _resultsLoader.LoadResult(loadOptions, ordersQueryable);

//            if (!user.IsTeamEmployee)
//                loadResult.data = _orders.TrimOrderForClient(loadResult);

//            return Ok(loadResult);
//        }

//        /// <summary>
//        ///     Get a specific order.
//        /// </summary>
//        /// <param name="id"></param>
//        /// <returns></returns>
//        [HttpGet("{id}")]
//        public async Task<IActionResult> Get(string id)
//        {
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);
//            if (user == null) return Unauthorized();

//            if (ShouldForbid(user)) return Forbid();

//            var order = await _orders.GetItemAsync(id, user);

//            if (order == null)
//                return NotFound();
//            return user.IsTeamEmployee ? Ok(order) : Ok(ClientOrder.CreateClientOrder(order));
//        }

//        /// <summary>
//        ///     Upload 1 or many files to an order.
//        /// </summary>
//        /// <param name="form"></param>
//        /// <returns></returns>
//        [RequestFormLimits(ValueLengthLimit = int.MaxValue, MultipartBodyLengthLimit = int.MaxValue)]
//        [DisableRequestSizeLimit]
//        [Authorize(Policy = "WMO:Edit")]
//        [HttpPost("UploadFiles")]
//        public async Task<IActionResult> UploadFiles(IFormCollection form)
//        {
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);
//            if (user == null) return Unauthorized();

//            if (form == null) throw new ArgumentNullException(nameof(form));
//            var parsed = long.TryParse(form["salesLineRecId"], out var salesLineRecId);
//            if (!parsed || salesLineRecId <= 0)
//                return BadRequest("This order cannot have files uploaded yet.");

//            var orders = _orders.GetBySalesLineRecIdAsync(salesLineRecId);
//            // `CanUploadSupportDocuments` is not mapped to a database column, so entity framework is
//            // failing to generate this query.  We don't actually want this to be a SQL query, so
//            // `ToList` is used here to get the SQL query over with before we use `CanUploadSupportDocuments`
//            // getter property
//            if (orders.ToList().Any(order => !order.CanUploadSupportDocuments))
//                return BadRequest("This order cannot have files uploaded yet.");

//            await _orders.UploadFiles(salesLineRecId, form.Files, user.Id);
//            return NoContent();
//        }

//        /// <summary>
//        ///     Delete a file associated with an order and remove that association.
//        /// </summary>
//        /// <param name="fileId"></param>
//        /// <returns></returns>
//        [Authorize(Policy = "WMO:Edit")]
//        [HttpDelete("DeleteFile/{fileId}")]
//        public async Task<IActionResult> DeleteFile(string fileId)
//        {
//            await _orders.DeleteFile(fileId);
//            return NoContent();
//        }

//        /// <summary>
//        ///     Download a file associated with an order.
//        /// </summary>
//        /// <param name="routeId"></param>
//        /// <param name="blobName"></param>
//        /// <returns></returns>
//        [HttpGet("DownloadFile/{routeId}/{blobName}")]
//        public async Task<IActionResult> Get(string routeId, string blobName)
//        {
//            var downloadInfo = await _orders.DownloadBlobAsync(routeId, blobName);
//            return File(downloadInfo.Stream, "application/octet-stream");
//        }

//        /// <summary>
//        ///     Get a distinct list of the client account numbers found in the orders.
//        /// </summary>
//        /// <param name="loadOptions"></param>
//        /// <returns></returns>
//        [HttpGet("customerAccounts")]
//        public async Task<IActionResult> GetExternalCustomers(DataSourceLoadOptions loadOptions)
//        {
//            var customerAccounts = _orders.GetExternalCustomers();
//            var loadResult = await _resultsLoader.LoadResult(loadOptions, customerAccounts);

//            loadResult.data = loadResult.data
//                .Cast<dynamic>()
//                .Select(customer => new
//                {
//                    externalCustomer = customer.EXTERNALCUSTOMER, externalCustomerName = customer.EXTERNALCUSTOMERNAME
//                });

//            return Ok(loadResult);
//        }

//        #endregion

//        #region Private Methods

//        #endregion
//    }
//}