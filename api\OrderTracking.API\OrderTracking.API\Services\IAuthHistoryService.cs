﻿using System.Collections.Generic;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Mvc;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     AuthHistoryService interface
    /// </summary>
    public interface IAuthHistoryService
    {
        /// <summary>
        ///     Get auth history items
        /// </summary>
        /// <param name="queryString"></param>
        /// <returns></returns>
        Task<IEnumerable<ChangeEvent>> GetItemsAsync();

        /// <summary>
        ///     Get a specific auth history item
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<ActionResult<ChangeEvent>> GetItemAsync(string id);
        
        /// <summary>
        ///     Add an auth history item
        /// </summary>
        /// <param name="changeEvent"></param>
        /// <returns></returns>
        Task<string> AddItemAsync(ChangeEvent changeEvent);
        
        /// <summary>
        ///     Remove auth history item
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task RemoveAsync(string id);
    }
}