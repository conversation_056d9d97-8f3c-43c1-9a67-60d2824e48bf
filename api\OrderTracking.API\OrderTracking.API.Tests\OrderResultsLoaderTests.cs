﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;
//using ClientPortal.Shared.Models;
//using NUnit.Framework;
//using OrderTracking.API.Models;
//using OrderTracking.API.Services;

//namespace OrderTracking.API.Tests
//{
//    [TestFixture]
//    public class OrderResultsLoaderTests
//    {
//        [SetUp]
//        public void SetUp()
//        {
//            _resultsLoader = new ClientPortalResultsLoader();
//        }

//        private ClientPortalResultsLoader _resultsLoader;

//        [Test]
//        public void LoadResult_NullLoadOptions_Throws()
//        {
//            // Arrange
//            DataSourceLoadOptions dataSourceLoadOptions = null;
//            var ordersQueryable = new EnumerableQuery<Order>(new List<Order>());

//            // Act
//            Task AsyncTestDelegate()
//            {
//                // ReSharper disable once ExpressionIsAlwaysNull
//                return _resultsLoader.LoadResult(dataSourceLoadOptions, ordersQueryable);
//            }

//            // Assert
//            Assert.ThrowsAsync<ArgumentNullException>(AsyncTestDelegate);
//        }

//        [Test]
//        public void LoadResult_NullOrdersQueryable_Throws()
//        {
//            // Arrange
//            var dataSourceLoadOptions = new DataSourceLoadOptions();
//            IQueryable<Order> ordersQueryable = null;

//            // Act
//            Task AsyncTestDelegate()
//            {
//                // ReSharper disable once ExpressionIsAlwaysNull
//                return _resultsLoader.LoadResult(dataSourceLoadOptions, ordersQueryable);
//            }

//            // Assert
//            Assert.ThrowsAsync<ArgumentNullException>(AsyncTestDelegate);
//        }

//        // TODO: We aren't able to test this all the way through yet because we need to setup
//        // a testing DbContext with EF Core.
//        // NOTE: Potential good starting place: https://github.com/DevExpress/DevExtreme.AspNet.Data/tree/master/net
//        //[Test]
//        //public async Task TEst()
//        //{
//        //    var dataSourceLoadOptions = new DataSourceLoadOptions();
//        //    var ordersQueryable = new EnumerableQuery<Order>(new List<Order>());

//        //    await _resultsLoader.LoadResult(dataSourceLoadOptions, ordersQueryable);
//        //}
//    }
//}