﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32126.317
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OrderTracking.API", "OrderTracking.API\OrderTracking.API.csproj", "{D0D2CD63-73E1-45DC-BABD-75D25465B1CD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OrderTracking.API.Tests", "OrderTracking.API.Tests\OrderTracking.API.Tests.csproj", "{34C96B51-B657-44A8-B654-4C806D3AD53F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ClientPortal.WMO.ETL", "ClientPortal.WMO.ETL\ClientPortal.WMO.ETL.csproj", "{1DE4BF13-808D-407D-87BF-2C583C0BB6AB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ClientPortal.WMO.ETL.Tests", "ClientPortal.WMO.ETL.Tests\ClientPortal.WMO.ETL.Tests.csproj", "{D29E0BD0-22E2-4613-AC38-1F451397BC35}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ClientPortal.Shared", "ClientPortal.Shared\ClientPortal.Shared.csproj", "{2152B0C7-FD83-4D1B-B855-0D50C0C08B3C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ClientPortal.Shared.Test", "ClientPortal.Shared.Test\ClientPortal.Shared.Test.csproj", "{5FEE3389-069C-43AB-A073-A1C973DE6B50}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RemoteMonitoringJob", "RemoteMonitoringJob\RemoteMonitoringJob.csproj", "{89CED0F4-E69C-4115-83AE-8AF54AEB32D1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MechanicalAndOnStreamServices", "MechanicalAndOperationalServices\MechanicalAndOnStreamServices.csproj", "{47B11F10-6711-489B-A8A8-B4F43E443745}"
	ProjectSection(ProjectDependencies) = postProject
		{2152B0C7-FD83-4D1B-B855-0D50C0C08B3C} = {2152B0C7-FD83-4D1B-B855-0D50C0C08B3C}
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AIMaaS", "AIMaaS\AIMaaS.csproj", "{6B766E76-183D-4435-8E4E-48279540A5E9}"
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D0D2CD63-73E1-45DC-BABD-75D25465B1CD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0D2CD63-73E1-45DC-BABD-75D25465B1CD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0D2CD63-73E1-45DC-BABD-75D25465B1CD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0D2CD63-73E1-45DC-BABD-75D25465B1CD}.Release|Any CPU.Build.0 = Release|Any CPU
		{34C96B51-B657-44A8-B654-4C806D3AD53F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34C96B51-B657-44A8-B654-4C806D3AD53F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{34C96B51-B657-44A8-B654-4C806D3AD53F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{34C96B51-B657-44A8-B654-4C806D3AD53F}.Release|Any CPU.Build.0 = Release|Any CPU
		{1DE4BF13-808D-407D-87BF-2C583C0BB6AB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1DE4BF13-808D-407D-87BF-2C583C0BB6AB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1DE4BF13-808D-407D-87BF-2C583C0BB6AB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1DE4BF13-808D-407D-87BF-2C583C0BB6AB}.Release|Any CPU.Build.0 = Release|Any CPU
		{D29E0BD0-22E2-4613-AC38-1F451397BC35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D29E0BD0-22E2-4613-AC38-1F451397BC35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D29E0BD0-22E2-4613-AC38-1F451397BC35}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D29E0BD0-22E2-4613-AC38-1F451397BC35}.Release|Any CPU.Build.0 = Release|Any CPU
		{2152B0C7-FD83-4D1B-B855-0D50C0C08B3C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2152B0C7-FD83-4D1B-B855-0D50C0C08B3C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2152B0C7-FD83-4D1B-B855-0D50C0C08B3C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2152B0C7-FD83-4D1B-B855-0D50C0C08B3C}.Release|Any CPU.Build.0 = Release|Any CPU
		{5FEE3389-069C-43AB-A073-A1C973DE6B50}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5FEE3389-069C-43AB-A073-A1C973DE6B50}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5FEE3389-069C-43AB-A073-A1C973DE6B50}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5FEE3389-069C-43AB-A073-A1C973DE6B50}.Release|Any CPU.Build.0 = Release|Any CPU
		{89CED0F4-E69C-4115-83AE-8AF54AEB32D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{89CED0F4-E69C-4115-83AE-8AF54AEB32D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{89CED0F4-E69C-4115-83AE-8AF54AEB32D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{89CED0F4-E69C-4115-83AE-8AF54AEB32D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{47B11F10-6711-489B-A8A8-B4F43E443745}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47B11F10-6711-489B-A8A8-B4F43E443745}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47B11F10-6711-489B-A8A8-B4F43E443745}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47B11F10-6711-489B-A8A8-B4F43E443745}.Release|Any CPU.Build.0 = Release|Any CPU
		{6B766E76-183D-4435-8E4E-48279540A5E9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6B766E76-183D-4435-8E4E-48279540A5E9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6B766E76-183D-4435-8E4E-48279540A5E9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6B766E76-183D-4435-8E4E-48279540A5E9}.Release|Any CPU.Build.0 = Release|Any CPU
		{0E87C393-CC89-4A84-3D9D-685CE2376CFF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0E87C393-CC89-4A84-3D9D-685CE2376CFF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0E87C393-CC89-4A84-3D9D-685CE2376CFF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0E87C393-CC89-4A84-3D9D-685CE2376CFF}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A4D7EEF7-A9AC-41A5-9EBE-ED9C5431AB8C}
	EndGlobalSection
EndGlobal
