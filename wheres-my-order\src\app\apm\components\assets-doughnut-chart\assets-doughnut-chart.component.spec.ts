import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxLoadIndicatorModule } from 'devextreme-angular';
import { DxPieChartModule } from 'devextreme-angular/ui/pie-chart';
import { AssetsDoughnutChartComponent } from './assets-doughnut-chart.component';

describe('AssetsDoughnutChartComponent', () => {
    let component: AssetsDoughnutChartComponent;
    let fixture: ComponentFixture<AssetsDoughnutChartComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DxPieChartModule, DxLoadIndicatorModule],
            declarations: [AssetsDoughnutChartComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(AssetsDoughnutChartComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
