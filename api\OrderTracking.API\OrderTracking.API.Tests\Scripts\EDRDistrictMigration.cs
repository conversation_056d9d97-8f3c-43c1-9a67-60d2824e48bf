﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using NUnit.Framework;
using OrderTracking.API.Models.EDR;

namespace OrderTracking.API.Tests.Scripts;

//[TestFixture]
public class EDRDistrictMigration
{
    #region Setup/Teardown

    [OneTimeSetUp]
    public void OneTimeSetUp()
    {
        GetContainerRef();
    }

    #endregion

    // ADD THIS PART TO YOUR CODE

    // The Azure Cosmos DB endpoint for running this sample.
    private const string EndpointUri = "https://client-portal.documents.azure.com:443/";

    // The primary key for the Azure Cosmos account.
    private const string PrimaryKey = "<super-secret>";

    // The Cosmos client instance
    private CosmosClient client;

    private Database _database;

    private Container _container;

    private const string DatabaseId = "ClientPortal";
    private const string ContainerId = "equipment-requests";

    private void GetContainerRef()
    {
        client = new CosmosClient(EndpointUri, PrimaryKey);
        _database = client.GetDatabase(DatabaseId);
        _container = _database.GetContainer(ContainerId);
    }

    private static async Task<IEnumerable<ValidDistrict>> GetValidDistricts()
    {
        var path = Path.Combine(Environment.CurrentDirectory,
            @"..\..\..\..\..\..\wheres-my-order\src\assets\data\districts.json");
        var text = await File.ReadAllTextAsync(path);
        var validDistricts = JsonConvert.DeserializeObject<ValidDistrict[]>(text);
        return validDistricts;
    }

    private class ValidDistrict
    {
        public string Number { get; set; }
        public string Name { get; set; }
    }

    private async Task<List<EquipmentRequest>> GetEquipmentRequests()
    {
        var requests = new List<EquipmentRequest>();

        const string query = "SELECT * FROM c";
        var iterator = _container.GetItemQueryIterator<EquipmentRequest>(query);
        while (iterator.HasMoreResults)
        {
            var resultSet = await iterator.ReadNextAsync();
            requests.AddRange(resultSet);
        }

        return requests;
    }

    //[Test]
    public async Task CanFindDistrictsJson()
    {
        var districts = await GetValidDistricts();
        Assert.That(districts, Is.Not.Empty);
    }

    //[Test]
    [Obsolete]
    public async Task MigrateEquipmentRequestsToNewDistrictStructure()
    {
        var requests = await GetEquipmentRequests();

        var validDistricts = await GetValidDistricts();

        var districtNumbersInUse = requests
            .Where(r => !string.IsNullOrEmpty(r.DistrictName))
            .Select(r => r.DistrictName.Split(' ')[0])
            .ToArray();
        var validDistrictIds = validDistricts.Select(d => d.Number);
        
        Assert.That(districtNumbersInUse.All(num => validDistrictIds.Contains(num)));
        Assert.That(districtNumbersInUse.Except(validDistrictIds), Is.Empty);

        foreach (var request in requests.Where(request => !string.IsNullOrEmpty(request.DistrictName)))
        {
            request.DistrictId = request.DistrictName.Split(' ')[0];
            var response = await _container.ReplaceItemAsync(request, request.Id);
        }
    }
}