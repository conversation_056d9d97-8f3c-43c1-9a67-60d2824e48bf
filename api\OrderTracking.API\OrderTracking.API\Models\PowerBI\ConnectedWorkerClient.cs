﻿using System;
namespace OrderTracking.API.Models.PowerBI
{
    public class ConnectedW<PERSON>kerClient
    {
        private ConnectedWorkerClient() { }

        public static class Chevron
        {
            public const String name = "chevron";

            public class ActivityTracker
            {
                public const String name = "activitytracker";
                public const String workspaceId = "c970f2a8-20df-4746-bc4a-b60e5f7f7aac";
                public const String dashboardId = "c9f710d7-71fa-4b23-98b0-09ef6bd31652";
            }

            public class CESOSI
            {
                public const String name = "cesosi";
                public const String workspaceId = "f41e97a3-6f9f-43a5-993c-0caeeacbdc73";
                public const String dashboardId = "05a80050-2764-46ce-8156-f6fb06767c72";
            }
        }

        public static class GPC
        {
            public const String name = "gpc";

            public class SupplementSections
            {
                public const String name = "supplementsections";
                public const String workspaceId = "79808d09-a467-4464-8248-8f1571a9ceb0";
                public const String dashboardId = "9d733e0e-4486-4601-9968-d8bdf640d413";
            }
        }
    }
}

