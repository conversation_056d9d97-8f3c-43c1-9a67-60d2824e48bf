section {
    padding: 1rem;
    border-bottom: groove;
    .fields {
        padding-left: 1rem;
        display: flex;
        flex-wrap: wrap;

        .field {
            padding-bottom: 1rem;
            width: 33%;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            label {
                font-weight: bold;
            }
        }
        .stackedField {
            padding-bottom: 1rem;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            label {
                font-weight: bold;
            }
        }
    }
}

.buttons {
    display: flex;
    justify-content: flex-end;
    dx-button {
        margin-left: 1rem;
    }
}

.image {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    height: 100%;
    width: 100%;
}

::ng-deep .dx-gallery .dx-gallery-nav-button-prev,
::ng-deep .dx-gallery .dx-gallery-nav-button-next {
    width: 0% !important;
}

.popup-button-div {
    display: flex;
    flex-direction: row-reverse;
    position: absolute;
    bottom: 2rem;
    right: 1rem;
}

.popup-outer-div {
    display: flex;
    align-items: center;
    height: 100%;
}

.gallery-item-outer-div {
    width: 100%;
    display: flex;
    flex-direction: row;
}

.gallery-image {
    max-height: 100%;
    max-width: 100%;
    display: block;
    margin: auto;
}

.popup-right-div {
    text-align: left;
    display: flex;
    flex-direction: column;
    width: 45%;
    margin-right: 5%;
}

::ng-deep .popup-template {
    width: 100%;
    height: 100%;
    max-height: 100%;
    display: flex;
    flex-direction: row;
    white-space: pre-wrap;
    .image-container {
        flex-grow: 1;
        display: flex;
        height: 100%;
        dx-button {
            justify-self: flex-end;
        }
        img {
            height: 100%;
            width: 100%;
            object-fit: contain;
            justify-self: center;
            margin: auto;
        }
    }
    .info {
        margin-left: 1em;
        justify-content: space-between;
        height: 100%;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        .text-content {
            text-align: left;
            .description-buttons {
                display: flex;
                margin-bottom: 1em;
                margin-right: 1em;
                dx-button {
                    margin-top: 1em;
                    margin-left: 1em;
                    &:first-child {
                        margin-left: auto;
                    }
                }
            }
        }
        .buttons {
            margin-bottom: 1em;
            margin-right: 1em;
            align-self: flex-end;
            dx-button {
                margin-left: 1em;
            }
        }
    }
}

.image-tile {
    height: 100%;
    display: flex;
    justify-content: center;
}
.tile-image {
    max-width: 100%;
    max-height: 100%;
}
