{"AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "teamincdev.onmicrosoft.com", "ClientId": "***REMOVED***"}, "BlobStorage": {"BlobEndpoint": "https://tdclientportalfiles.blob.core.windows.net/", "AccountName": "tdclientportalfiles", "ConnectionString": "***REMOVED***", "Key": "***REMOVED***"}, "Connections": {"Database": "ClientPortalDevTestStaging", "UserProfiles": "user-profiles", "Roles": "roles", "Orders": "order-status", "Questionnaires": "questionnaires", "EquipmentRequests": "equipment-requests", "Notifications": "notifications", "OrdersJobs": "orders-jobs", "Endpoint": "https://client-portal.documents.azure.com:443/", "AuthKey": "***REMOVED***"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*"}