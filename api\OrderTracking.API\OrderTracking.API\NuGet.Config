﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
 <packageSources>
     <clear />
     <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
     <!--<add key="DevExpress" value="https://nuget.devexpress.com/XHyuzNND9afihxCrbhh3XCFHY2b5lqhOmKdXkj2vhWtdb3sYpm/api" protocolVersion="23.2.5" />-->
      <!--<add key="gitlab" value="https://gitlab.com/api/v4/projects/55854683/packages/nuget/index.json" />-->
	 <add key="MySource" value="https://pkgs.dev.azure.com/teaminc/debad0dc-be5c-449f-9cbd-c546461d5b75/_packaging/packageManager/nuget/v3/index.json" />

 </packageSources>
	<packageSourceCredentials>
		<MySource>
			<add key="Username" value="dkhande1" />
			<add key="ClearTextPassword" value="1bfO0Jfb7TxGJ1QiUPOzOInlSmp8bvLa2u1EHgHYFn0friT821EOJQQJ99BGACAAAAANtmU4AAASAZDO3gLT="></add>
		</MySource>
	</packageSourceCredentials>
 <!--<packageSourceCredentials>
     <gitlab>
         <add key="Username" value="deepikakhandelwal" />
         <add key="ClearTextPassword" value="**************************" />
     </gitlab>
 </packageSourceCredentials>-->
</configuration>
