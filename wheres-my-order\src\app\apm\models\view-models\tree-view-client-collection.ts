import { TreeViewUser } from '..';
import { TreeViewBusinessUnit } from './tree-view-business-unit';
import { TreeViewClient } from './tree-view-client';

export class TreeViewClientCollection {
    static build(clients: any[], businessUnits: any[], users: any[]) {
        const clientsVM = [];

        const buUsers = new Map<string, Set<TreeViewUser>>();
        users.forEach((user) => {
            (user.businessUnitIds.currentValue ?? []).forEach((id) => {
                const u = new TreeViewUser(user.id, user.name.currentValue);
                if (buUsers.has(id)) buUsers.get(id).add(u);
                else buUsers.set(id, new Set([u]));
            });
        });

        for (let client of clients) {
            const clientBUs = businessUnits.filter(
                (bu) => client.id == bu.clientId
            );
            clientsVM.push(
                new TreeViewClient(
                    client.id,
                    client.name.currentValue,
                    clientBUs.map(
                        (bu) =>
                            new TreeViewBusinessUnit(
                                bu.id,
                                bu.name.currentValue,
                                Array.from(buUsers.get(bu.id) ?? [])
                            )
                    )
                )
            );
        }
        console.log(clientsVM);
        return clientsVM;
    }
}
