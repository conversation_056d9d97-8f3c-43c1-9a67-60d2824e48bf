import {
    Component,
    EventEmitter,
    Input,
    Output,
    ViewChild
} from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { distinctUntilChanged, filter, switchMap } from 'rxjs/operators';
import { UserProfile } from '../../../profile/models';
import { Asset, Location, ProjectUpdate, ProjectVm } from '../../models';
import { ApmService } from '../../services';
import { DetailsTabComponent } from '../details-tab/details-tab.component';

@Component({
    selector: 'app-project-details',
    templateUrl: './project-details.component.html',
    styleUrls: ['./project-details.component.scss']
})
export class ProjectDetailsComponent {
    private _selectedProject: ProjectVm | undefined;
    private readonly _project = new BehaviorSubject<ProjectVm | undefined>(
        undefined
    );
    tabs = [
        { title: 'Details', template: 'details' },
        { title: 'Contacts', template: 'contacts' },
        { title: 'Location', template: 'location' },
        { title: 'Assets', template: 'assets' },
        { title: 'Activity Tracker', template: 'activity-tracker' }
    ];
    @Input() set selectedProject(value: ProjectVm | undefined) {
        this._selectedProject = value;
        this._project.next(this._selectedProject);
    }
    get selectedProject(): ProjectVm | undefined {
        return this._selectedProject;
    }
    @Input() selectedLocation: Location;
    @Input() locations: Location[];
    @Input() selectedProjectAssets: Asset[];
    @Input() assetsForLocation: Asset[];
    @Input() users: UserProfile[];
    @Input() allowEditing: boolean;
    @Output() projectSave = new EventEmitter();
    @ViewChild(DetailsTabComponent) projectDetailsTab: DetailsTabComponent;

    onProjectUpdated(update: Partial<ProjectUpdate>) {
        this.projectSave.next(update);
    }

    activities$ = this._project.asObservable().pipe(
        distinctUntilChanged(),
        filter((project) => project !== null && project !== undefined),
        switchMap((project) => this._apm.getAllActivity(project.id))
    );

    constructor(private readonly _apm: ApmService) {}
}
