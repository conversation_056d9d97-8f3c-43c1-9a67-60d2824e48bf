import { Component, Input } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, combineLatest, firstValueFrom, of } from 'rxjs';
import { catchError, finalize, map, tap } from 'rxjs/operators';
import { userHasSomesRoles } from '../../../core/operators';
import { LeakReportModel } from '../../../report/models/leak-report.model';
import {
    PhotoObject,
    ReportTypes
} from '../../../report/models/report-data.model';
import { ReportRendererFactory } from '../../../report/models/report-renderers.model';
import { LocalBlobService, UsersService } from '../../../shared/services';
import { LeakReport, LeakReportPackage } from '../../models';
import { LeakReportPhotosPipe } from '../../pipes/leak-report-photos.pipe';
import { ApmService } from '../../services';

@Component({
    selector: 'app-report-tab-button',
    templateUrl: './report-tab-button.component.html',
    styleUrls: ['./report-tab-button.component.scss']
})
export class ReportTabButtonComponent {
    private _isGenerating = new BehaviorSubject<boolean>(false);
    private readonly _leakReportPhotosPipe: LeakReportPhotosPipe =
        new LeakReportPhotosPipe();

    @Input() report: LeakReport;

    icon$ = this._isGenerating.pipe(
        map((isGenerating) =>
            isGenerating ? 'fa fa-spinner fa-pulse fa-2x' : ''
        )
    );
    disabled$ = combineLatest([
        this._isGenerating.asObservable(),
        this._users.currentProfile$.pipe(
            userHasSomesRoles(['apm:edit', 'app:admin'])
        )
    ]).pipe(map(([isGenerating, canEdit]) => isGenerating || !canEdit));

    constructor(
        private readonly _apm: ApmService,
        private readonly _localBlob: LocalBlobService,
        private readonly _toasts: ToastrService,
        private readonly _users: UsersService
    ) {}

    onGenerateReportClicked(e) {
        this._isGenerating.next(true);
        this._apm
            .generateReport(this.report.id)
            .pipe(
                tap(async (leakReport: LeakReportPackage) => {
                    const sourceReportRenderer = new LeakReportModel();
                    const photosGroups = this._leakReportPhotosPipe
                        .transform(leakReport?.leakReport)
                        .flatMap((p) => p.photos.photos);
                    const photoBlobsPromises = photosGroups.map(
                        async (photo) => {
                            try {
                                const url = await firstValueFrom(
                                    this._apm.getSignedUrl(photo.blobName)
                                );

                                const blob = await this._apm
                                    .downloadFileFromUrl(url)
                                    .toPromise();

                                return new PhotoObject(
                                    window.URL.createObjectURL(blob),
                                    photo?.description?.currentValue
                                );
                            } catch (error) {
                                return new PhotoObject('', '');
                            }
                        }
                    );

                    leakReport.photos = await Promise.all(photoBlobsPromises);
                    const reportData = sourceReportRenderer.handle(
                        leakReport as unknown as LeakReportPackage
                    );
                    const model = {
                        type: ReportTypes.Report,
                        models: reportData
                    };
                    const renderer = ReportRendererFactory.getRenderer(model);
                    const encodedHtml = renderer.render(model);
                    const windowReference = window.open();
                    windowReference.document.title = `${this.report.apmNumber.currentValue}`;
                    windowReference.document.body.innerHTML = encodedHtml;
                    window.addEventListener('beforeunload', () => {
                        windowReference.close();
                    });
                    window.setTimeout(() => {
                        windowReference.print();
                    }, 1000);
                    this._toasts.success('Generated Report', 'Success');
                }),
                catchError(async (e) => {
                    const error = JSON.parse(await e.error.text());
                    const message = error.message;
                    this._toasts.error('Error', message);
                    return of(e);
                }),
                finalize(() => this._isGenerating.next(false))
            )
            .subscribe();
    }
}
