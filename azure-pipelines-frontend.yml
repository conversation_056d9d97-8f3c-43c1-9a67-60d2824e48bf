# Azure DevOps Pipeline for Frontend (Where's My Order)
# Replaces Google Cloud Build for frontend deployment

trigger:
  branches:
    include:
      - main
      - develop
  paths:
    include:
      - wheres-my-order/*

pool:
  vmImage: 'ubuntu-latest'

variables:
  buildConfiguration: 'Release'
  imageRepository: 'cpa-frontend'
  containerRegistry: 'krakenacr.azurecr.io'
  dockerfilePath: 'wheres-my-order/Dockerfile'
  tag: '$(Build.BuildId)'
  azureSubscription: 'Kraken-Azure-Connection'
  resourceGroupName: 'rg-kraken-$(Environment.Name)'
  containerAppName: 'ca-cpa-frontend-$(Environment.Name)'

stages:
- stage: Build
  displayName: 'Build and Push Docker Image'
  jobs:
  - job: Build
    displayName: 'Build Angular Application'
    steps:
    - task: NodeTool@0
      displayName: 'Install Node.js'
      inputs:
        versionSpec: '18.x'

    - script: |
        cd wheres-my-order
        npm ci
        npm run build-prod
      displayName: 'Install dependencies and build'

    - task: Docker@2
      displayName: 'Build and push Docker image'
      inputs:
        command: 'buildAndPush'
        repository: '$(imageRepository)'
        dockerfile: '$(dockerfilePath)'
        containerRegistry: '$(containerRegistry)'
        tags: |
          $(tag)
          latest

- stage: DeployDev
  displayName: 'Deploy to Development'
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
  variables:
    Environment.Name: 'dev'
  jobs:
  - deployment: DeployToDev
    displayName: 'Deploy to Development Environment'
    environment: 'kraken-dev'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureCLI@2
            displayName: 'Deploy to Azure Container Apps'
            inputs:
              azureSubscription: '$(azureSubscription)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az containerapp update \
                  --name $(containerAppName) \
                  --resource-group $(resourceGroupName) \
                  --image $(containerRegistry)/$(imageRepository):$(tag)

- stage: DeployStaging
  displayName: 'Deploy to Staging'
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  variables:
    Environment.Name: 'stg'
  jobs:
  - deployment: DeployToStaging
    displayName: 'Deploy to Staging Environment'
    environment: 'kraken-staging'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureCLI@2
            displayName: 'Deploy to Azure Container Apps'
            inputs:
              azureSubscription: '$(azureSubscription)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az containerapp update \
                  --name $(containerAppName) \
                  --resource-group $(resourceGroupName) \
                  --image $(containerRegistry)/$(imageRepository):$(tag)

- stage: DeployProduction
  displayName: 'Deploy to Production'
  dependsOn: DeployStaging
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  variables:
    Environment.Name: 'prod'
  jobs:
  - deployment: DeployToProduction
    displayName: 'Deploy to Production Environment'
    environment: 'kraken-production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureCLI@2
            displayName: 'Deploy to Azure Container Apps'
            inputs:
              azureSubscription: '$(azureSubscription)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az containerapp update \
                  --name $(containerAppName) \
                  --resource-group $(resourceGroupName) \
                  --image $(containerRegistry)/$(imageRepository):$(tag)
