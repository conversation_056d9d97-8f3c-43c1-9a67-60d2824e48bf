<section *ngIf="!componentInfo">
    <h4>No calculation information exists for this component.</h4>
</section>
<section *ngIf="componentInfo"
         class="info">
    <div class="fields">
        <div class="field">
            <label>Asset Name</label>
            <span>{{componentInfo.objdesC_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Asset Type</label>
            <span>{{componentInfo.objtypecodE_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Service/Product/Contents</label>
            <span>{{componentInfo.objservice | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Specific Gravity</label>
            <span>{{componentInfo.tM_PV_SPEC_GRAVITY_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Design Code</label>
            <span>{{componentInfo.eqdesigncodE_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Manufacture Date</label>
            <span>{{componentInfo.eqmanufdate  | date:'MMMM d, y'}}</span>
        </div>
        <div class="field">
            <label>Installation/In-service Date</label>
            <span>{{componentInfo.objcommission  | date:'MMMM d, y'}}</span>
        </div>
        <div class="field">
            <label>Orientation (vertical/horizontal)</label>
            <span>{{componentInfo.alY_OM_VERT_HORIZ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Configuration (geometry)</label>
            <span>{{componentInfo.tM_PV_CONFIG_GEOM_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Outside Diameter</label>
            <span>{{componentInfo.diM_DIAMOUTSIDE_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Internal Diameter</label>
            <span>{{componentInfo.diM_DIAMINSIDE | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Diameter Unit </label>
            <span>{{componentInfo.diM_DIAMUNITS | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Radius Outside</label>
            <span>{{componentInfo.diM_RADIUSOUTSIDE | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Radius Inside</label>
            <span>{{componentInfo.diM_RADIUSINSIDE | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Crown Radius Inside</label>
            <span>{{componentInfo.diM_RADIUS_CROWN_INSIDE | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Crown Radius Outside</label>
            <span>{{componentInfo.diM_RADIUS_CROWN_OUTSIDE | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Radius Unit </label>
            <span>{{componentInfo.diM_RADIUS_UNIT | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Cone Apex Angle</label>
            <span>{{componentInfo.diM_CONE_APEX_ANGLE | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>K</label>
            <span>{{componentInfo.diM_TALARM_K | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>L</label>
            <span>{{componentInfo.diM_TALARM_L | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>M</label>
            <span>{{componentInfo.diM_TALARM_M | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Length/height</label>
            <span>{{componentInfo.diM_LENGTH_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Length/height Unit</label>
            <span>{{componentInfo.diM_LENGTHUNIT_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Static Head Pressure Height </label>
            <span>{{componentInfo.diM_TANK_FILL_HEIGHT_M | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Material Spec. No.</label>
            <span>{{componentInfo.matspeC_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Material Type / Grade</label>
            <span>{{componentInfo.matgradE_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Material Damage Group</label>
            <span>{{componentInfo.rmaT_DAMAGE_GROUP_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Joint Efficiency</label>
            <span>{{componentInfo.diM_JOINTQUAL | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Nominal Wall Thickness </label>
            <span>{{componentInfo.diM_TNOMINAL | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Nominal Unit </label>
            <span>{{componentInfo.diM_TNOMINAL_UNIT}}</span>
        </div>
        <div class="field">
            <label>Corrosion Allowance </label>
            <span>{{componentInfo.diM_TCORRALLOW | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Corrosion Allowance Unit </label>
            <span>{{componentInfo.diM_TCORRALLOW_UNIT | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Pressure Unit </label>
            <span>{{componentInfo.pressunitS_}}</span>
        </div>
        <div class="field">
            <label>Maximum Design Pressure / MAWP</label>
            <span>{{componentInfo.pressdesmaX_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Relief Device Set Pressure</label>
            <span>{{componentInfo.prssrelieF_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Normal Operating Pressure</label>
            <span>{{componentInfo.pressopernorM_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Temperature Unit</label>
            <span>{{componentInfo.tempunitS_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Maximum Design Temperature</label>
            <span>{{componentInfo.tempdesmaX_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Normal Operating Temperature</label>
            <span>{{componentInfo.tempopernorM_ | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Alarm1 Calc</label>
            <span>{{componentInfo.diM_TALARM1_CALCCODE | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Alarm2 Calc</label>
            <span>{{componentInfo.diM_TALARM2_CALCCODE | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Alarm3 Calc</label>
            <span>{{componentInfo.diM_TALARM3_CALCCODE | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Alarm1</label>
            <span>{{componentInfo.diM_TALARM1_VAL | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Alarm2</label>
            <span>{{componentInfo.diM_TALARM2_VAL | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Alarm3</label>
            <span>{{componentInfo.diM_TALARM3_VAL | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Alarm4 Calc</label>
            <span>{{componentInfo.diM_TALARM4_CALCCODE | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Tmin Calc</label>
            <span>{{componentInfo.diM_TALARM5_CALCCODE | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Alarm Note</label>
            <span>{{componentInfo.diM_TALARM_NOTE01 | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Alarm4</label>
            <span>{{componentInfo.diM_TALARM4_VAL | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Tmin Value</label>
            <span>{{componentInfo.diM_TALARM5_VAL | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Alarm1 Factor X</label>
            <span>{{componentInfo.diM_TALARM1_FACTORX | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Alarm2 Factor X</label>
            <span>{{componentInfo.diM_TALARM2_FACTORX | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Alarm3 Factor X</label>
            <span>{{componentInfo.diM_TALARM3_FACTORX | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Alarm5 Factor X</label>
            <span>{{componentInfo.diM_TALARM5_FACTORX | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>MAWT</label>
            <span>{{componentInfo.diM_MAWT | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>MAWT Unit</label>
            <span>{{componentInfo.diM_MAWT_UNIT | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>MAWT Structural</label>
            <span>{{componentInfo.diM_MAWT_STRUCTURAL | calcDisplayValue }}</span>
        </div>
        <div class="field">
            <label>Structural Unit</label>
            <span>{{componentInfo.diM_MAWT_STRUCTURAL_UNIT | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Manuf Tolerance %</label>
            <span>{{componentInfo.diM_TTOLERANCEPERCENT | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>Design Factor</label>
            <span>{{componentInfo.diM_DESIGNFACTOR | calcDisplayValue}}</span>
        </div>
        <div class="field">
            <label>T Alarm Y</label>
            <span>{{componentInfo.diM_TALARM_Y | calcDisplayValue}}</span>
        </div>

    </div>
</section>
