﻿// Migrated from Google Cloud Firestore to Azure Cosmos DB
// using Google.Cloud.Firestore;
using Microsoft.Azure.Cosmos;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    ///     Container Factory interface - migrated from Firestore to Azure Cosmos DB
    /// </summary>
    public interface IContainerFactory
    {
        #region Public Methods

        /// <summary>
        ///     Create Azure Cosmos DB container instance (migrated from Firestore collection)
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="partitionKeyPath"></param>
        /// <returns></returns>
        Container CreateCollection<T>(out string partitionKeyPath) where T : class;

        #endregion
    }
}