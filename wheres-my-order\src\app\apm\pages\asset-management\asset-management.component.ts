import { Component, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DxDataGridComponent } from 'devextreme-angular';
import CustomStore from 'devextreme/data/custom_store';
import DataSource from 'devextreme/data/data_source';
import { ClickEvent } from 'devextreme/ui/button';
import {
    ColumnButtonClickEvent,
    RowUpdatedEvent,
    RowUpdatingEvent,
    SelectionChangedEvent
} from 'devextreme/ui/data_grid';
import { ToastrService } from 'ngx-toastr';
import { lastValueFrom } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { DataGridService } from '../../../shared/services';
import { CreationWorkflowComponent } from '../../components';
import { AssetVm, WorkOrderDetailTabInfo, WorkOrderVm } from '../../models';
import { ApmService } from '../../services';
@Component({
    selector: 'app-asset-management',
    templateUrl: './asset-management.component.html',
    styleUrls: ['./asset-management.component.scss']
})
export class AssetManagementComponent {
    /**
     * DATASOURCES
     */
    allowEditing$ = this._apm.allowEditing$;
    users$ = this._apm.getUsers();

    @ViewChild('assetsGrid') assetsGrid: DxDataGridComponent;
    @ViewChild('woGrid') woGrid: DxDataGridComponent;
    @ViewChild(CreationWorkflowComponent)
    creationWorkflow: CreationWorkflowComponent;

    assetsDataSource = new DataSource({
        store: new CustomStore({
            key: 'id',
            byKey: (key: string) => lastValueFrom(this._apm.getAsset(key)),
            load: (options) => lastValueFrom(this._apm.loadAllAssets(options)),
            update: (key: string, values: any) =>
                lastValueFrom(this._apm.updateAsset(values)),
            useDefaultSearch: true
        })
    });
    workOrdersDataSource = new DataSource({
        store: new CustomStore({
            key: 'id',
            byKey: (key: string) => lastValueFrom(this._apm.loadWorkOrder(key)),
            load: async (options) => {
                if (!this.selectedAssetId) return Promise.resolve({ data: [], totalCount: 0 })
                let res = lastValueFrom(this._apm.loadWorkOrders(options, this.selectedAssetId))
                return res.then((value) => {
                    return value ?? Promise.resolve({ data: [], totalCount: 0 })
                })
            },
            update: (key: string, values: WorkOrderVm) =>
                lastValueFrom(
                    this._apm.updateWorkOrder(
                        WorkOrderDetailTabInfo.build(values)
                    )
                )
        })
    });

    // used for lookup column, which can only take a datasource store configuration object,
    // not a datasource itself.
    projectDataSource = this._apm.projectDataSource.store();

    disableAddButton$ = this._apm.selectedBU$.pipe(
        startWith(true),
        map((bu) => bu === null || bu === undefined)
    );

    selectedAssetId: string | undefined;
    selectedBU: string = "";
    shouldCallInizialization = true
    shouldCallAssetSelected = true

    /**
     * CONSTRUCTOR
     */
    constructor(
        private readonly _router: Router,
        private readonly _apm: ApmService,
        private readonly _grid: DataGridService,
        private readonly _toasts: ToastrService
    ) {
        this._apm.selectedBU$.subscribe((selected: string) => {
            if (!this.selectedBU) {
                this.selectedBU = selected;
                return;
            }
            if (selected != this.selectedBU) {
                this.selectedBU = selected;
                this.selectedAssetId = undefined;
                this.assetsDataSource.reload();
                this.assetsGrid.instance.clearSelection();
            }
        });
    }

    /** EVENT HANDLERS */

    customAssetAddClicked = (e: ClickEvent) => {
        this.creationWorkflow.start();
    };

    customAddWorkOrderClicked = (e: ClickEvent) => {
        this.creationWorkflow.startWorkOrderCreation(this.selectedAssetId);
    };

    restoreAssetsDefaultsClicked = async (e: ClickEvent) => {
        const result = await this._grid.resetGridState(this.assetsGrid);
        if (result) {
            this.selectedAssetId = undefined;
            localStorage.removeItem('assetManagementAssetsGrid');
        }
    };

    restoreWorkOrderDefaultsClicked = async (e: ClickEvent) => {
        const result = await this._grid.resetGridState(this.assetsGrid);
        if (result) {
            localStorage.removeItem('assetManagementWorkOrdersGrid');
        }
    };

    onWOGridInitialized(_: any) {
        if (this.shouldCallInizialization) {
            this.shouldCallAssetSelected = false
            this.selectedAssetId = this.assetsGrid.selectedRowKeys[0]
        }
    }

    onAssetSelected(e: SelectionChangedEvent) {
        if (!this.shouldCallAssetSelected) {
            this.shouldCallAssetSelected = true
            return
        }

        this.shouldCallInizialization = false
        let selectedIds = e.selectedRowsData as AssetVm[];

        if (selectedIds.length == 0) return;

        if (this.selectedAssetId) {
            this.selectedAssetId = selectedIds[0].id;
            this.workOrdersDataSource.reload();
        } else {
            this.selectedAssetId = selectedIds[0].id;
        }
    }

    onRowUpdating(e: RowUpdatingEvent) {
        // I'm merging the whole data row (oldData) with the newData because newData
        // only includes values that were changed during editing.  This allows us
        // to have the full object, including changed values, in our update method of
        // our CustomStores
        e.newData = Object.assign(e.oldData, e.newData);
    }

    onAssetUpdated(e: RowUpdatedEvent) {
        this._toasts.success(
            `Asset with Asset ID ${e.data.equipmentId} was successfully updated`,
            'Success'
        );
    }

    onWorkOrderUpdated(e: RowUpdatedEvent) {
        // Handling for if and when we return a WorkOrderVm instead of a full blown Work Order
        // on successfully work order update.
        const apmNumber =
            typeof e.data.apmWorkOrderNumber === 'object' &&
                e.data.apmWorkOrderNumber.hasOwnProperty('currentValue')
                ? e.data.apmWorkOrderNumber.currentValue
                : e.data.apmWorkOrderNumber;
        this._toasts.success(
            `Work Order with APM Work Order Number ${apmNumber} was successfully updated`,
            'Success'
        );
    }

    onAssetCreated() {
        this.assetsGrid.instance.refresh();
    }

    onWorkOrderCreated() {
        this.woGrid.instance.refresh();
    }

    workOrderDetailsClicked = (e: ColumnButtonClickEvent) => {
        this._router.navigate(['apm/work-order-details', e.row.data.id], {
            queryParams: { projectId: e.row.data.projectId }
        });
    };
}
