﻿using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class NewAsset
    {

        [JsonProperty(PropertyName = "id")]
        public string Id { get; set; }

        [JsonProperty(PropertyName = "assetName")]
        public string AssetName { get; set; }

        [JsonProperty(PropertyName = "assetId")]
        public string AsssetId { get; set; }

        [JsonProperty(PropertyName = "locationId")]
        public string LocationId { get; set; }

        [JsonProperty(PropertyName = "projectId")]
        public string ProjectId { get; set; }

        [JsonProperty(PropertyName = "category")]
        public string Category { get; set; }

        [JsonProperty(PropertyName = "startLat")]
        public double? StartLat { get; set; }

        [JsonProperty(PropertyName = "startLong")]
        public double? StartLong { get; set; }

        [JsonProperty(PropertyName = "endLat")]
        public double? EndLat { get; set; }

        [JsonProperty(PropertyName = "endLong")]
        public double? EndLong { get; set; }

        [JsonProperty(PropertyName = "shouldUpdateProject")]
        public bool ShouldUpdateProject { get; set; }

    }
}
