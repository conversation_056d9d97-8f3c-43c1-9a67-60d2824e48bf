#region Copyright Quest Integrity Group, LLC 2020

// www.QuestIntegrity.com
// +1-303-415-1475
// 
// All rights are reserved. Reproduction or transmission in whole or in part, in
// any form or by any means, electronic, mechanical or otherwise, is prohibited
// without the prior written consent of the copyright owner.
// 
// File Created: -- 
// Updated:      2020-02-24 1:59 PM
// Created by:   M<PERSON><PERSON><PERSON>, Sean

#endregion

using System.Collections.Generic;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderTracking.API.Interfaces;
using OrderTracking.API.Models;
using OrderTracking.API.Services;

namespace OrderTracking.API.Controllers
{
    /// <summary>
    ///     API Controller to access notifications
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Policy = "UserIsActive")]
    public class NotificationsController : ControllerBase
    {
        #region Fields and Constants

        private readonly INotificationsService _notifications;
        private readonly IUserProfilesService _userProfiles;

        #endregion

        #region Constructors

        /// <summary>
        ///     Constructor that injects an IUserProfilesService and an INotificationsService
        /// </summary>
        /// <param name="userProfiles"></param>
        /// <param name="notifications"></param>
        public NotificationsController(IUserProfilesService userProfiles, INotificationsService notifications)
        {
            _userProfiles = userProfiles;
            _notifications = notifications;
        }

        #endregion

        #region Public Methods

        /// <summary>
        ///     Get notifications where the recipient is the user making the request.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            string email = User.Identity.Name.ToLower();
            UserProfile user = await _userProfiles.GetAsync(email, email);
            if (user == null)
            {
                return Unauthorized();
            }

            IEnumerable<Notification> notifications = await _notifications.GetItemByUserAsync(user.Id);

            return Ok(notifications);
        }

        /// <summary>
        ///     Updates a notification.  This endpoint's primary purpose is to be able to mark notifications as read.
        /// </summary>
        /// <param name="notification"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<IActionResult> Put(Notification notification)
        {
            if (notification == null) return BadRequest("Notification is missing");
            await _notifications.UpdateItemAsync(notification.Id, notification);
            return NoContent();
        }

        #endregion
    }
}
