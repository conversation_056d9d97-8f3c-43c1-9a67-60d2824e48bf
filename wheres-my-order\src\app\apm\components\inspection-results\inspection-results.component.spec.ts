import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxLoadIndicatorModule } from 'devextreme-angular';
import { DxAccordionModule } from 'devextreme-angular/ui/accordion';
import { DxButtonModule } from 'devextreme-angular/ui/button';
import { DxGalleryModule } from 'devextreme-angular/ui/gallery';
import { DxPopupModule } from 'devextreme-angular/ui/popup';
import { DxTextAreaModule } from 'devextreme-angular/ui/text-area';
import { ToastrModule } from 'ngx-toastr';
import { PhotoSasPipe } from '../../pipes';
import { ApmService } from '../../services';
import { InspectionResultsComponent } from './inspection-results.component';

describe('InspectionResultsComponent', () => {
    let component: InspectionResultsComponent;
    let fixture: ComponentFixture<InspectionResultsComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxAccordionModule,
                HttpClientTestingModule,
                DxTextAreaModule,
                DxButtonModule,
                DxPopupModule,
                DxGalleryModule,
                DxLoadIndicatorModule,
                ToastrModule.forRoot()
            ],
            declarations: [InspectionResultsComponent],
            providers: [{ provide: ApmService, useValue: {} }, PhotoSasPipe]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(InspectionResultsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
