import { AssetDetailsPhoto } from '.';
import { Option, Photo } from '../data';
export interface FiveTenAssetDetails {
    workOrderId: string;
    assetId: string;
    projectId: string;
    assetCategory: string;
    identificationName: string;
    identificationNumber: string;
    assetType: string;
    equipmentDescription: string;
    lastKnownInspectionDate: Date;
    location: string;
    gisLocationLat: number;
    gisLocationLong: number;
    backPhotos: Photo[];
    frontPhotos: Photo[];
    leftPhotos: Photo[];
    rightPhotos: Photo[];
    designCode: string;
    designYear: string;
    designAddendum: string;
    inspectionCode: string;
    inspectionYear: string;
    inspectionAddendum: string;
    dataPlateAttached: boolean;
    dataPlateLegible: boolean;
    manufacturerName: string;
    manufacturerDate: Date;
    manufacturerSerialNumber: string;
    nationalBoardNumber: string;
    rtNumber: string;
    serviceProductContents: string;
    specificGravity: number;
    orientation: string;
    rt: string;
    installationDate: Date;
    inServiceDate: Date;
    pIDNumber: string;
    designDrawingNumber: string;
    lowestFlangeRating: string;
    hydroTestPressure: number;
    typeOfConstruction: string;
    pwht: string;
    ratingChanged: string;
    isFiredPressureVessel: string;
    hasRepairOrAlterationPlate: boolean;
    repairOrAlterationPlateLegible: boolean;
    designConditionsOperatingTemp: number;
    shellSideDesignMAWP: number;
    shellSideDesignTemp: number;
    shellSideOperatingTemp: number;
    shellSideOperatingPressure: number;
    shellSideSetPressure: number;
    tubeSideDesignMAWP: number;
    tubeSideDesignTemp: number;
    tubeSideOperatingTemp: number;
    tubeSideOperatingPressure: number;
    tubeSideSetPressure: number;
    diameterMeasurement: string;
    diameter: number;
    hasMultipleDiameters: boolean;
    diameterComments: string;
    overallLengthHeight: number;
    hasToriconicalSections: boolean;
    operationStatus: string;
    shellCourses: ShellCourse[];
    channels: Channel[];
    heads: Head[];
    nozzles: Nozzle[];
    inspectionOpenings: InspectionOpening[];
    repairs: Repair[];
    allPhotos?: AssetDetailsPhoto[];
    geometryOptions: Option[];
}

export interface Repair {
    databaseId: string;
    dateRepairedOrAltered: string;
    repairAlterationOrganization: string;
    purposeOfRepairAlteration: string;
    isNBFormR1Available: boolean;
    isNBFormR2Available: boolean;
    nbRCertificateNumber: string;
}

export interface InspectionOpening {
    type: string;
    number: number;
    size: string;
    displayName: string;
    databaseId: string;
}

export interface ShellCourse {
    databaseId: string;
    number: string;
    materialSpecAndGrade: string;
    allowableStressAtTemp: number;
    nominalThickness: number;
    corrosionAllowance: number;
    lengthOrHeight: number;
    jointEfficiency: number;
}

export interface Channel {
    databaseId: string;
    number: string;
    location: string;
    materialSpecAndGrade: string;
    allowableStressAtTemp: number;
    nominalThickness: number;
    corrosionAllowance: number;
    length: number;
    jointEfficiency: number;
}

export interface Head {
    databaseId: string;
    number: string;
    location: string;
    geometry: string;
    geometryComments: string;
    materialSpecAndGrade: string;
    allowableStressAtTemp: number;
    nominalThickness: number;
    corrosionAllowance: number;
    jointEfficiency: number;
}

export interface Nozzle {
    databaseId: string;
    number: string;
    type: string;
    materialSpecAndGrade: string;
    pipeSize: string[];
    pipeSchedule: string[];
    flangeRating: string[];
    reinforcementPadType: string;
    reinforcementPadDimensions: string;
    reinforcementPadThickness: number;
}
