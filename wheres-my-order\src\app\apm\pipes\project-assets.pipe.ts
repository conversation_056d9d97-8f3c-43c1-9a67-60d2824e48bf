import { Pipe, PipeTransform } from '@angular/core';
import { CommaSeparatedStringToNumbers } from '../../shared/helpers/comma-separated-string-to-numbers';
import { Asset, ProjectAsset } from '../models';

@Pipe({
    name: 'projectAssets'
})
export class ProjectAssetsPipe implements PipeTransform {
    transform(assets: Asset[]): ProjectAsset[] {
        return assets?.map((asset) => {
            let startLat: number = null;
            let startLong: number = null;
            let endLat: number = null;
            let endLong: number = null;

            if (asset?.assetCategory === 'Piping') {
                const startGis = CommaSeparatedStringToNumbers(
                    asset?.walkDown?.sectionIdentification
                        ?.attributeStart_GIS_Location?.currentValue
                );
                const endGis = CommaSeparatedStringToNumbers(
                    asset?.walkDown?.sectionIdentification
                        ?.attributeEnd_GIS_Location?.currentValue
                );

                if (startGis?.length >= 1) {
                    startLat = startGis[0];

                    if (startGis.length >= 2) {
                        startLong = startGis[1];
                    }
                }

                if (endGis?.length >= 1) {
                    endLat = endGis[0];

                    if (endGis.length >= 2) {
                        endLong = endGis[1];
                    }
                }

                return new ProjectAsset({
                    id: asset?.id,
                    assetId:
                        asset?.walkDown?.sectionIdentification
                            ?.attributeNumber_or_Circuit_ID?.currentValue,
                    assetName:
                        asset?.walkDown?.sectionIdentification?.attributeName
                            ?.currentValue,
                    category: asset?.assetCategory,
                    startLatitude: startLat,
                    startLongitude: startLong,
                    endLatitude: endLat,
                    endLongitude: endLong
                });
            } else {
                const startGis = CommaSeparatedStringToNumbers(
                    asset?.walkDown?.sectionIdentification
                        ?.attributeGIS_Location?.currentValue
                );

                if (startGis?.length >= 1) {
                    startLat = startGis[0];

                    if (startGis.length >= 2) {
                        startLong = startGis[1];
                    }
                }

                return new ProjectAsset({
                    id: asset?.id,
                    assetId:
                        asset?.walkDown?.sectionIdentification
                            ?.attributeNumber_or_ID?.currentValue,
                    assetName:
                        asset?.walkDown?.sectionIdentification?.attributeName
                            ?.currentValue,
                    category: asset?.assetCategory,
                    startLatitude: startLat,
                    startLongitude: startLong
                });
            }
        });
    }
}
