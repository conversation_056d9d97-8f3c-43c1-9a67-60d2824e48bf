.header {
    width: 100%;
    text-align: center;
    font-size: 25px;
}

.container {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 10px;
    border: solid #ffffff;
    text-align: center;
}
.container1 {
    display: flex;
    flex-wrap: nowrap;
    padding-bottom: 10px;
    border: solid #ffffff;
    text-align: center;
}
.item1{
      border: 1px solid #ccc;
      margin: 5px;
}
.item2{
    border: 1px solid #ccc;
    margin: 5px;
    padding-top: 29px;
    width: 350px;
}
.item3{
    padding: 10px;
    flex-wrap: wrap;
    border: 1px solid #ccc;
    margin: 5px;
    box-sizing: border-box; 
    flex: 1 1 30%;  
}
.item4{
    padding: 10px;
    border: 1px solid #ccc;
    margin: 5px;
    box-sizing: border-box;  
    flex: 1 1 30%; 
}
.item5{
    padding: 10px;
    border: 1px solid #ccc;
    margin: 5px;
    box-sizing: border-box; 
    flex: 1 1 30%; 
}
 
.item3 dx-chart,
.item4 dx-pie-chart,
.item5 dx-chart {
    height: 400px;
}

.prioritylevel ul {
    padding: 30px;
    list-style-type: none;
}

.anomaly-heading {
    font-size: 16px;
    margin-bottom: 10px;
}

#diagonalHatchYellow {
    background: repeating-linear-gradient(
        45deg,
        #ffea00,
        #ffea00 10px,
        #ffffff 10px,
        #ffffff 20px
    );
}

#diagonalHatchOrange {
    background: repeating-linear-gradient(
        45deg,
        #ffcc00,
        #ffcc00 10px,
        #ffffff 10px,
        #ffffff 20px
    );
}

#diagonalHatchRed {
    background: repeating-linear-gradient(
        45deg,
        #ff4d4d,
        #ff4d4d 10px,
        #ffffff 10px,
        #ffffff 20px
    );
}

.container2 {
    border: solid #ffffff;
}

.container2 .data-grid {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
}

.level {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    margin-top: 15px;
    width: 100%;
    font-stretch: expanded;
    padding-right: 10px;
    padding-top: 5px;
}

.circle {
    aspect-ratio: 1 / 1;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 10px;
}

.square {
    width: 30px;
    height: 30px;
    background-color: #ccc; 
    text-align: center;
    line-height: 20px; 
    font-size: 15px; 
}

.level-0 {
    background-color: #ffffff;
    border: 1px solid #000000;
}

.level-1 {
    background-color: #f0f0f0;
}

.level-2 {
    background-color: #b0b0b0;
}

.level-3 {
    background-color: #808080;
}

.level-4 {
    background-color: #ffff99;
}

.level-5 {
    background-color: #ff9900;
}

.level-6 {
    background-color: #ff0000;
}

.priority {
    display: flex;
    align-items: center;
    margin-bottom: 5px;  
}

.priority .priority-level {
    
    width: 20px;
    height: 20px;
    border-radius: 50%;  
    background-color: #ccc;
}

.priority .priority-text {
    margin-left: 10px;  
}

.prioritylevel ul li {  
    display: flex;
    align-items: center;
    margin-bottom: 5px; 
    margin-top: 20px; 
}

.prioritylevel ul li span {
    text-align: start;
    margin-left: 10px;  
}
