import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { DxButtonModule } from 'devextreme-angular';
import { DxDataGridModule } from 'devextreme-angular/ui/data-grid';
import { DxFormModule } from 'devextreme-angular/ui/form';
import { DxTabPanelModule } from 'devextreme-angular/ui/tab-panel';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import {
    AssetsDetailsComponent,
    DetailsTabComponent,
    WorkOrdersGridComponent
} from '../../components';
import { ProjectDetailPipe, WorkOrderGridRowsPipe } from '../../pipes';
import { AssetNumberPipe } from '../../pipes/asset-number.pipe';
import { ApmService } from '../../services';
import { WorkOrdersComponent } from './work-orders.component';

describe('WorkOrdersComponent', () => {
    let component: WorkOrdersComponent;
    let fixture: ComponentFixture<WorkOrdersComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                HttpClientTestingModule,
                DxDataGridModule,
                DxTabPanelModule,
                DxFormModule,
                DxButtonModule,
                HttpClientTestingModule,
                ToastrModule.forRoot(),
                RouterTestingModule
            ],
            declarations: [
                WorkOrdersComponent,
                WorkOrdersGridComponent,
                AssetsDetailsComponent,
                DetailsTabComponent,
                WorkOrderGridRowsPipe,
                ProjectDetailPipe
            ],
            providers: [
                AssetNumberPipe,
                {
                    provide: ApmService,
                    useValue: {
                        getUsers: () => {},
                        selectedBU$: of('123'),
                        buSelected$: of(false)
                    }
                }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(WorkOrdersComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
