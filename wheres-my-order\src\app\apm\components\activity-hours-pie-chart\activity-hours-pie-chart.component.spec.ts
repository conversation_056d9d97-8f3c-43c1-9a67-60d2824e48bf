import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxLoadIndicatorModule } from 'devextreme-angular';
import { DxPieChartModule } from 'devextreme-angular/ui/pie-chart';
import { ToastrModule } from 'ngx-toastr';
import { ActivityHoursPieChartComponent } from './activity-hours-pie-chart.component';

describe('ActivityHoursPieChartComponent', () => {
    let component: ActivityHoursPieChartComponent;
    let fixture: ComponentFixture<ActivityHoursPieChartComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxPieChartModule,
                DxLoadIndicatorModule,
                HttpClientTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [ActivityHoursPieChartComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ActivityHoursPieChartComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
