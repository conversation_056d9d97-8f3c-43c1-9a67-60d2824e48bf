<dx-data-grid [dataSource]="assetinspection"
              [wordWrapEnabled]="true">
    <dxo-header-filter [visible]="true"></dxo-header-filter>
    <dxo-filter-panel [visible]="true"></dxo-filter-panel>

    <dxi-column dataField="scheduletype"
                caption="Schedule Type"></dxi-column>
    <dxi-column dataField="frequency"
                caption="Current Schedule Frequency"></dxi-column>
    <dxi-column dataField="inspectiondate"
                caption="Last Date"
                dataType="date"></dxi-column>
    <dxi-column dataField="nextinspectiondue"
                caption="Next Inspection Due"
                dataType="date"></dxi-column>
    <dxi-column dataField="nextduedatenotes"
                caption="Next Due Date Notes"
                [calculateCellValue]="formatNotes"></dxi-column>

    <dxo-toolbar>
        <dxi-item location="after">
            <dx-button text="Complete History"
                       [(disabled)]="completeHistoryButtonDisable"
                       (onClick)="AllDatesClicked()"></dx-button>
        </dxi-item>
    </dxo-toolbar>
</dx-data-grid>