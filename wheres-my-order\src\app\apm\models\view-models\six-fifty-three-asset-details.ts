import { Photo } from '../data';
import { AssetDetailsPhoto } from './asset-details-photo';
import {
    InspectionOpening,
    Nozzle,
    ShellCourse
} from './five-ten-asset-details';

export interface SixFiftyThreeAssetDetails {
    workOrderId: string;
    projectId: string;
    name: string;
    numberOrId: string;
    assetType: string;
    equipmentDescription: string;
    lastKnownInspectionDate: string;
    location: string; // TODO:
    latitude: number;
    longitude: number;
    designCode: string;
    codeYear: string;
    addendum: string;
    maximumFillHeight: number;
    diameter: number;
    height: number;
    tankVolumeInBBL: number;
    constructionMethod: string[];
    orientation: string;
    rt: string;
    installationDate: string;
    inServiceDate: string;
    pAndIdNumber: string;
    constructionDesignDrawingNumber: string;
    lowestFlangeRating: string;
    serviceProductContents: string;
    specificGravity: number;
    intendedService: string;
    inspectionOpenings: InspectionOpening[];
    inspectionCode: string;
    inspectionYear: string;
    inspectionAddendum: string;
    dataPlateAttached: boolean;
    dataPlateLegible: boolean;
    manufacturerName: string;
    manufacturerDate: string;
    manufacturerSerialNumber: string;
    hasRepairOrAlterationPlate: boolean;
    repairOrAlterationPlateLegible: boolean;
    repairs: {
        databaseId: string;
        dateRepairedOrAltered: string;
        repairAlterationOrganization: string;
        purposeOfRepairAlteration: string;
    }[];
    currentService: string;
    designTemperature: number;
    currentOperatingTemperature: number;
    currentFillLevel: number;
    operationStatus: string;
    tankEquippedWithVRU: string;
    tankEquippedWithLeakDetection: string;
    tankOutOfService: string;
    regulatoryRequirements: {
        databaseId: string;
        jurisdictionRegulatoryAgency: string;
    }[];
    shellCourses: ShellCourse[];
    tankFloorType: string;
    tankFloorMaterialSpecAndGrade: string;
    tankFloorAnnularRingNominalThickness: number;
    tankFloorSketchPlatesNominalThickness: number;
    tankFloorInnerPlatesNominalThickness: number;
    tankFloorCorrosionAllowance: number;
    tankRoofs: {
        type: string[];
        materialSpecAndGrade: string;
        nominalThickness: number;
        corrosionAllowance: number;
        databaseId: string;
    }[];
    nozzles: Nozzle[];
    backPhotos: Photo[];
    frontPhotos: Photo[];
    leftPhotos: Photo[];
    rightPhotos: Photo[];
    allPhotos?: AssetDetailsPhoto[];
}
