﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ClientPortal.Shared.Helpers;
using NUnit.Framework;
using OrderTracking.API.Helpers;
using OrderTracking.API.Models.EDR;

namespace OrderTracking.API.Tests
{
    [TestFixture]
    public class EquipmentRequestEmailSetupTests
    {
        
        [Test]
        public void EdrEmailSetup_CreateEquipmentHtml_HasValuesFromJobType()
        {
            // Arrange
            var jobType = new JobType
            {
                JSSNumber = "*********",
                Type = "TestType",
                PipeSize = "TestSize",
                TapSize = "TestTap",
                FlangeRating = "testFlange",
                OperatingTemperature = "testTemp",
                TravelDistanceToTopPipe = "testTravelDistance",
                TargetShipDate = new DateTime()
            };

            // Act
            var equipmentHtml = EquipmentRequestEmailSetup.CreateEquipmentTableHtml(new List<JobType> {jobType});

            // Assert
            Assert.That(equipmentHtml.Contains("*********"));
            Assert.That(equipmentHtml.Contains("TestType"));
            Assert.That(equipmentHtml.Contains("TestSize"));
            Assert.That(equipmentHtml.Contains("TestTap"));
            Assert.That(equipmentHtml.Contains("testFlange"));
            Assert.That(equipmentHtml.Contains("testTemp"));
            Assert.That(equipmentHtml.Contains("testTravelDistance"));
        }

        [Test]
        public void EdrEmailSetup_CreateAttachmentHtml_HasValuesFromAttachments()
        {
            // Arrange
            var request = new EquipmentRequest
            {
                Files = new List<EquipmentRequestBlobFile>
                {
                    new EquipmentRequestBlobFile
                    {
                        Name = "TestFileOne"
                    },
                    new EquipmentRequestBlobFile
                    {
                        Name = "TestFileTwo"
                    }
                }
            };

            // Act
            var attachmentHtml = EquipmentRequestEmailSetup.CreateAttachmentHtml(request);

            //Assert
            Assert.That(attachmentHtml.Contains("TestFileOne"));
            Assert.That(attachmentHtml.Contains("TestFileTwo"));
        }

        [Test]
        public void EdrEmailSetup_CreateNoteHtml_HasValuesFromNotes()
        {
            // Arrange 
            var jobType = new JobType
            {
                Notes = new List<RequestNote>
                {
                    new RequestNote
                    {
                        Note = "TestNote"
                    },
                    new RequestNote
                    {
                        Note = "AnotherTestNote"
                    }
                }
            };

            // Act
            var notesHtml = EquipmentRequestEmailSetup.CreateNoteHtml(jobType);

            // Assert
            Assert.That(notesHtml.Contains("TestNote"));
            Assert.That(notesHtml.Contains("AnotherTestNote"));
        }

        [Test]
        public void EdrEmailSetup_CreateRequestHtml_HasValuesFromEquipmentRequest()
        {
            // Arrange
            var request = new EquipmentRequest
            {
                Client = "TestClient",
                JobDate = new DateTime(),
                JobNumber = "TestJobNumber",
                ShippingAddress = new Address
                {
                    Line1 = "TestShippingLineOne",
                    Line2 = "TestShippingLineTwo",
                    City = "TestShippingCity",
                    State = "TestShippingState",
                    ZipCode = "TestShippingZipCode"
                },
                ShippingPointOfContact = new ShippingPointOfContact
                {
                    Name = "TestShippingPointOfContactName"
                },
                DistrictName = "TestDistrictName"
            };

            // Act
            var requestHtml = EquipmentRequestEmailSetup.CreateRequestHtml(request);

            // Assert
            Assert.That(requestHtml.Contains("TestClient"));
            Assert.That(requestHtml.Contains("TestJobNumber"));
            Assert.That(requestHtml.Contains("TestShippingLineOne"));
            Assert.That(requestHtml.Contains("TestShippingLineTwo"));
            Assert.That(requestHtml.Contains("TestShippingCity"));
            Assert.That(requestHtml.Contains("TestShippingState"));
            Assert.That(requestHtml.Contains("TestShippingZipCode"));
            Assert.That(requestHtml.Contains("TestShippingPointOfContactName"));
            Assert.That(requestHtml.Contains("TestDistrictName"));
        }

        [Test]
        public void EdrEmailSetup_CreateJobInfoHtml_HasValuesFromJobtype()
        {
            // Arrange
            var request = new EquipmentRequest
            {
                EquipmentCenter = "TestEquipmentCenter"
            };
            var jobType = new JobType
            {
                JSSNumber = "*********",
                Description = "TestDescription",
                Type = "TestType",
                PipeSize = "TestSize",
                FlangeRating = "testFlange",
                OperatingPressure = "testPressure",
                TravelDistanceToTopPipe = "testTravelDistance",
                TargetShipDate = new DateTime(),
                ReceivedDate = new DateTime()
            };

            // Act
            var jobInfoHtml = EquipmentRequestEmailSetup.CreateJobInfoHtml(request, jobType);

            // Assert
            Assert.That(jobInfoHtml.Contains("*********"));
            Assert.That(jobInfoHtml.Contains("TestDescription"));
            Assert.That(jobInfoHtml.Contains("TestType"));
            Assert.That(jobInfoHtml.Contains("TestSize"));
            Assert.That(jobInfoHtml.Contains("testFlange"));
            Assert.That(jobInfoHtml.Contains("testPressure"));
            Assert.That(jobInfoHtml.Contains("testTravelDistance"));
            Assert.That(jobInfoHtml.Contains("TestEquipmentCenter"));
        }

        [Test]
        public void EdrEmailSetup_CreateDelayedHtmlContent_HasValuesFromJobType()
        {
            // Arrange
            var request = new EquipmentRequest
            {
                JobNumber = "testJobNumber",
                Id = "TestId"
            };
            var jobType = new JobType
            {
                Description = "testDescription",
                DelayComment = "TestDelayComment",
                ReasonForDelay = "testReasonForDelay",
                NewExpectedDate = new DateTime()
            };
            var clientPortalAddress = "https://www.digitaldev.teaminc.com";


            // Act
            var delayEmailContent =
                EquipmentRequestEmailSetup.CreateDelayedHtmlContent(request, jobType, clientPortalAddress);

            // Assert
            Assert.That(delayEmailContent.Contains("testJobNumber"));
            Assert.That(delayEmailContent.Contains("TestId"));
            Assert.That(delayEmailContent.Contains("testDescription"));
            Assert.That(delayEmailContent.Contains("TestDelayComment"));
            Assert.That(delayEmailContent.Contains("testReasonForDelay"));
            Assert.That(delayEmailContent.Contains("digitaldev.teaminc.com"));
        }

        [Test]
        public async Task EdrEmailSetup_CreateHeaderHtmlContent_HasClientPortalValue()
        {
            // Arrange
            var clientPortalAddress = "digitaldev.teaminc.com";

            // Act
            var headerHtml =await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);
            // Assert
            Assert.That(headerHtml.Contains(clientPortalAddress));
        }
    }
}