using Newtonsoft.Json;

namespace OrderTracking.API.Models.LeakReporting
{
    public class LeakReportPhotoGroupTransport
    {
        [JsonProperty("leakReportID")]
        public string LeakReportID { get; set; }
        [JsonProperty("groupDatabaseID")] 
        public string GroupDatabaseID { get; set; }
        [JsonProperty("description")]
        public string Description { get; set; }
        [JsonProperty("descriptionComment")]
        public string DescriptionComment { get; set; }
        [JsonProperty("photoComment")]
        public string PhotoComment { get; set; }
        [JsonProperty("areaOfInterestLatitude")]
        public double? AreaOfInterestLatitude { get; set; }
        [JsonProperty("areaOfInterestLongitude")]
        public double? AreaOfInterestLongitude { get; set; }
        [JsonProperty("areaOfInterestComment")]
        public string AreaOfInterestComment { get; set; }
        [JsonProperty("upstreamTieInLatitude")]
        public double? UpstreamTieInLatitude { get; set; }
        [JsonProperty("upstreamTieInLongitude")]
        public double? UpstreamTieInLongitude { get; set; }
        [JsonProperty("upstreamTieInComment")]
        public string UpstreamTieInComment { get; set; }
        [JsonProperty("downstreamTieInLatitude")]
        public double? DownstreamTieInLatitude { get; set; }
        [JsonProperty("downstreamTieInLongitude")]
        public double? DownstreamTieInLongitude { get; set; }
        [JsonProperty("downstreamTieInComment")]
        public string DownstreamTieInComment { get; set; }
        [JsonProperty("utHighMeasurement")]
        public double? UTHighMeasurement { get; set; }
        [JsonProperty("utHighMeasurementComment")]
        public string UTHighMeasurementComment { get; set; }
        [JsonProperty("utLowMeasurement")]
        public double? UTLowMeasurement { get; set; }
        [JsonProperty("utLowMeasurementComment")]
        public string UTLowMeasurementComment { get; set; }
    }
}