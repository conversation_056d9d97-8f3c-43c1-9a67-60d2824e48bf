<div class="dx-card responsive-paddings content-block">
    <dx-tab-panel [items]="tabs"
                  [disabled]="!selectedProject">

        <div *dxTemplate="let data of 'details'">
            <app-details-tab [project]="selectedProject | projectDetail : selectedLocation"
                             [allowEditing]="allowEditing"
                             (projectUpdated)="onProjectUpdated($event)">
            </app-details-tab>
        </div>
        <div *dxTemplate="let data of 'contacts'">
            <app-contacts-tab [contacts]="selectedProject | contacts"
                              [selectedProject]="selectedProject"
                              [allowEditing]="allowEditing">
            </app-contacts-tab>
        </div>
        <div class="responsive-paddings"
             *dxTemplate="let data of 'location'">
            <app-location-tab [location]="selectedProject | assignedProjectLocation : locations "
                              [selectedProject]="selectedProject"
                              [allowEditing]="allowEditing">
            </app-location-tab>
        </div>
        <div *dxTemplate="let data of 'assets'">
            <app-assets-tab [availableAssets]="assetsForLocation | projectAssets"
                            [projectAssets]="selectedProjectAssets | projectAssets"
                            [selectedProject]="selectedProject"
                            [locations]="locations"
                            [allowEditing]="allowEditing">
            </app-assets-tab>
        </div>
        <div *dxTemplate="let data of 'activity-tracker'">
            <app-activity-tracker [activities]="activities$ | async"
                                  [selectedProject]="selectedProject"
                                  [availableUsers]="users | userEmails"
                                  [allowEditing]="allowEditing">
            </app-activity-tracker>
        </div>
    </dx-tab-panel>
</div>
