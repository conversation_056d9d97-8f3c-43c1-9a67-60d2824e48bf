﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AngleSharp;
using AngleSharp.Dom;
using AngleSharp.Html.Dom;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using IConfiguration = Microsoft.Extensions.Configuration.IConfiguration;

namespace RemoteMonitoringJob.Services
{
    /// <summary>
    ///     Object containing the sensor's Id that we parse from the page.
    /// </summary>
    public class SensorInfo
    {
        [JsonProperty("id")] public string Id { get; set; }
    }

    public class SmartpimsScraperService : ISmartpimsScraperService
    {
        private const string SmartpimsUrl = "https://smartpims.com";
        private const string SensorDownloadUrl = "https://smartpims.com/smartpims/downloadTmls/sensor";
        private readonly ILogger<SmartpimsScraperService> _logger;
        private readonly IConfiguration _config;
        private IBrowsingContext _context;

        public SmartpimsScraperService(ILogger<SmartpimsScraperService> logger, IConfiguration config)
        {
            _logger = logger;
            _config = config;
        }

        public async Task<IEnumerable<string>> GetSensorIdsAsync()
        {
            // Make sure the browsing context exists
            if (_context == null) InitializeContext();

            // Login to the site, and get the resulting html page on successful login
            var document = await LoginAsync();

            // Get the html of the document
            var docHtml = document.DocumentElement.OuterHtml;

            // Beginning of javascript array used to generate navigation links
            const string indexString = "facilityHierarchyData = ";

            // Index of the variable declaring the navigation links in the html
            var startIndex = docHtml.IndexOf(indexString, StringComparison.Ordinal);

            // Index of the end of the navigation links javascript array
            var endIndex = docHtml.IndexOf(";", startIndex, StringComparison.Ordinal);

            // Javascript array of the 
            var jsonArray =
                JsonConvert.DeserializeObject<List<SensorInfo>>(docHtml[(startIndex + indexString.Length)..endIndex]);

            // Filter to only sensors
            var sensorIds = jsonArray
                .Where(s => s.Id.StartsWith("sensor"))
                .Select(s => s.Id["sensor-thick-".Length..]);
            return sensorIds;
        }

        public async Task<string> GetCSVTextAsync(string id)
        {
            // Make sure the browsing context exists
            if (_context == null) InitializeContext();

            // Download the file
            var file = await _context.OpenAsync($"{SensorDownloadUrl}/{id}");

            // Return the text content
            return file.Body.TextContent;
        }

        private void InitializeContext()
        {
            var config = Configuration.Default.WithDefaultLoader().WithDefaultCookies();
            _context = BrowsingContext.New(config);
        }

        private async Task<IDocument> LoginAsync()
        {
            _logger.LogInformation($"Visiting {SmartpimsUrl} and logging in");

            // Visit the home page (https://smartpims.com) which will show us the login form initially
            var document = await _context.OpenAsync(SmartpimsUrl);

            // Get a reference to the form element
            var loginForm = document.QuerySelector("form") as IHtmlFormElement;

            // Submit a login request
            var username = _config.GetValue<string>("Smartpims:Username");
            var password = _config.GetValue<string>("Smartpims:Password");
            var authenticatedDocument = await loginForm.SubmitAsync(new {username, password});

            // Return the html response from the website on successful login
            return authenticatedDocument;
        }
    }
}