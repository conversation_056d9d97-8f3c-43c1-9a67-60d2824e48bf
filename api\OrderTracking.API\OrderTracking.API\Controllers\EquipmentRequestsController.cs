//using System;
//using System.Collections.Generic;
//using System.Diagnostics;
//using System.IO;
//using System.Linq;
//using System.Threading.Tasks;
//using ClientPortal.Shared.Converters;
//using ClientPortal.Shared.Models;
//using ClientPortal.Shared.Services;
//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Http;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.AspNetCore.Mvc.ModelBinding;
//using Microsoft.AspNetCore.SignalR;
//using Microsoft.Extensions.Configuration;
//using Microsoft.IdentityModel.Tokens;
//using OrderTracking.API.Extensions.EDR;
//using OrderTracking.API.Helpers;
//using OrderTracking.API.Hubs;
//using OrderTracking.API.Interfaces;
//using OrderTracking.API.Models.EDR;
//using OrderTracking.API.Services;

//namespace OrderTracking.API.Controllers
//{
//    /// <summary>
//    ///     API Controller for managing equipment requests, facilitating the EDR feature module
//    /// </summary>
//    [Authorize]
//    [Route("api/[controller]")]
//    [ApiController]
//    [Authorize(Policy = "UserIsActive")]
//    public class EquipmentRequestsController : ControllerBase
//    {
//        private readonly IConfiguration _config;
//        private readonly IEmailService _emails;
//        private readonly DeploymentEnvironment _env;
//        private readonly IEquipmentRequestsService _equipmentRequests;
//        private readonly IHubContext<EDRHub> _hub;
//        private readonly IUserProfilesService _userProfiles;

//        /// <summary>
//        ///     Constructor that injects an IUserProfilesService and an IEquipmentRequestsService.
//        /// </summary>
//        /// <param name="userProfiles"></param>
//        /// <param name="equipmentRequests"></param>
//        /// <param name="hub"></param>
//        /// <param name="emails"></param>
//        /// <param name="config"></param>
//        /// <param name="env"></param>
//        public EquipmentRequestsController(
//            IUserProfilesService userProfiles,
//            IEquipmentRequestsService equipmentRequests,
//            IHubContext<EDRHub> hub,
//            IEmailService emails,
//            IConfiguration config,
//            DeploymentEnvironment env
//        )
//        {
//            _userProfiles = userProfiles;
//            _equipmentRequests = equipmentRequests;
//            _hub = hub;
//            _emails = emails;
//            _config = config;
//            _env = env;
//        }

//        /// <summary>
//        ///     Get all Equipment Requests
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet]
//        public async Task<IActionResult> Get()
//        {
//            var email = User.Identity.Name.ToLower();
//            var user = await _userProfiles.GetAsync(email, email);
//            if (user == null) return Unauthorized();
//            const string queryString = "SELECT * FROM o";
//            var equipmentRequests = await _equipmentRequests.GetItemsAsync(queryString);
//            return Ok(equipmentRequests);
//        }

//        /// <summary>
//        ///     Get a specific Equipment Request
//        /// </summary>
//        /// <param name="id"></param>
//        /// <returns></returns>
//        [HttpGet("{id}")]
//        public async Task<IActionResult> Get(string id)
//        {
//            var email = User.Identity.Name.ToLower();
//            var user = await _userProfiles.GetAsync(email);
//            if (user == null) return Unauthorized();
//            var equipmentRequest = await _equipmentRequests.GetItemAsync(id);
//            return Ok(equipmentRequest);
//        }

//        /// <summary>
//        ///     Create a new Equipment Request
//        /// </summary>
//        /// <param name="form"></param>
//        /// <returns></returns>
//        [HttpPost]
//        public async Task<IActionResult> Post(IFormCollection form)
//        {
//            if (form == null) throw new ArgumentNullException(nameof(form));
//            if (!form.ContainsKey("request")) return BadRequest("Invalid submission");
//            // Get current user profile
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);
//            if (user == null) return Unauthorized();
//            // Get request 
//            var request = EquipmentRequest.From(form["request"]);
//            // Get recipient emails
//            var recipients = new List<IEmailRecipient>();
//            foreach (var recipient in request.SendTo)
//            {
//                var requester = await _userProfiles.GetAsync(recipient);
//                // Make sure email recipients exist before sending email
//                if (requester == null)
//                    return UnprocessableEntity("Send to emails must be users in the OneInsight System");
//                recipients.Add(requester);
//            }

//            var equipmentCenterRecipients = await GetEquipmentCenterEmailRecipients(request, user);
//            recipients.AddRange(equipmentCenterRecipients);

//            // Put a new id on it.
//            request.Id = Guid.NewGuid().ToString();
//            // Initialize job types
//            var counter = 0;
//            foreach (var jobType in request.JobTypes)
//            {
//                counter += 1;
//                jobType.Id = counter;
//                jobType.RequestStatus = "New";
//                jobType.Description = JobType.InitialDescription(jobType);
//            }

//            // Touch ReceivedDate
//            request.ReceivedDate = DateTime.UtcNow;
//            // Add new request to database
//            await _equipmentRequests.AddItemAsync(request);
//            // Upload any files
//            await _equipmentRequests.UploadFilesAsync(request, form.Files, user.Id);
//            // Send Email
//            var attachments = SetupFileAttachments(form.Files);
//            var clientPortalAddress = _config.GetSection("Clients:ClientPortal").Value;
//            var createdEmail =
//                await EquipmentRequestEmailSetup.CreateCreatedEmail(recipients, request, attachments,
//                    clientPortalAddress);
//            await _emails.SendEmail(createdEmail);
//            // Make sure that we close the streams when we are done with them. 
//            foreach (var attachment in attachments) attachment.Stream?.Close();
//            return CreatedAtAction(nameof(Get), new {id = request.Id},
//                await _equipmentRequests.GetItemAsync(request.Id));
//        }

//        /// <summary>
//        ///     Update an existing Equipment Request
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="form"></param>
//        /// <returns></returns>
//        [HttpPut("{id}")]
//        public async Task<IActionResult> Put(string id, IFormCollection form)
//        {
//            UserProfile requester = null;
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);
//            if (user == null) return Unauthorized();
//            if (form == null) throw new ArgumentNullException(nameof(form));
//            if (id == null) throw new ArgumentNullException(nameof(id));
//            var clientPortalAddress = _config.GetSection("Clients:ClientPortal").Value;
//            var request = EquipmentRequest.From(form["request"]);
//            var valid = TryValidateModel(request);
//            if (valid == false)
//                return UnprocessableEntity(ModelState.Values
//                    .Where(v => v.ValidationState == ModelValidationState.Invalid).Select(v => v.Errors));
//            var count = 0;
//            foreach (var jobType in request.JobTypes)
//            {
//                count++;
//                jobType.Id = count;
//                if (string.IsNullOrEmpty(jobType.Description) && jobType.IsNew())
//                    jobType.Description = JobType.InitialDescription(jobType);
//            }

//            var originalRequest = await _equipmentRequests.GetItemAsync(request.Id);

//            var missingShippingInfo = request.GetMissingShippingInfo().ToList();
//            if (originalRequest.Requester != null)
//            {
//                requester = await _userProfiles.GetAsync(originalRequest.Requester);

//                if (requester != null)
//                {
//                    // Only looking at the first request since when they are in the "new" state they are all one request
//                    // and all jobtypes get moved to "request acknowledged" at the same time. 
//                    // Request Acknowledged emails
//                    if (originalRequest.JobTypes.First().CheckForAcknowledged(request))
//                    {
//                        // request acknowledged email
//                        await _emails.SendEmail(
//                            await EquipmentRequestEmailSetup.CreateRequestAcknowledgedEmail(requester, request,
//                                clientPortalAddress));
//                        if (missingShippingInfo.Any() && !request.WillCallOrder)
//                        {
//                            var recipients = new List<IEmailRecipient> {requester};
//                            foreach (var recipientEmail in request.SendTo)
//                            {
//                                var recipient = await _userProfiles.GetAsync(recipientEmail);
//                                recipients.Add(recipient);
//                            }

//                            // missing shipping info email
//                            await _emails.SendEmail(
//                                await EquipmentRequestEmailSetup.CreateMissingShippingEmail(
//                                    recipients,
//                                    request,
//                                    missingShippingInfo,
//                                    request.JobTypes.FirstOrDefault(), originalRequest.JobTypes.FirstOrDefault(),
//                                    clientPortalAddress));
//                        }
//                    }
//                    else
//                    {
//                        // Missing shipping email
//                        foreach (var jobType in request.JobTypes)
//                        {
//                            var revertingStatus =
//                                jobType.CheckRevertedStatus(
//                                    originalRequest.JobTypes.FirstOrDefault(j => j.Id == jobType.Id));
//                            if (missingShippingInfo.Any() &&
//                                !revertingStatus &&
//                                jobType.CheckForStatusChange(
//                                    originalRequest.JobTypes.FirstOrDefault(j => j.Id == jobType.Id)) &&
//                                !request.WillCallOrder)
//                            {
//                                var recipients = new List<IEmailRecipient> {requester};
//                                foreach (var recipientEmail in request.SendTo)
//                                {
//                                    var recipient = await _userProfiles.GetAsync(recipientEmail);
//                                    recipients.Add(recipient);
//                                }

//                                await _emails.SendEmail(await EquipmentRequestEmailSetup.CreateMissingShippingEmail(
//                                    recipients, request, missingShippingInfo,
//                                    request.JobTypes.First(j => j.Id == jobType.Id),
//                                    originalRequest.JobTypes.First(j => j.Id == jobType.Id), clientPortalAddress));
//                            }
//                        }
//                    }

//                    // Delay emails
//                    foreach (var jobType in originalRequest.JobTypes)
//                        if (jobType.CheckForDelay(request))
//                        {
//                            var recipients = new List<IEmailRecipient> {requester};
//                            foreach (var recipientEmail in request.SendTo)
//                            {
//                                var recipient = await _userProfiles.GetAsync(recipientEmail);
//                                recipients.Add(recipient);
//                            }

//                            recipients.AddRange(await GetEquipmentCenterEmailRecipients(request, user));

//                            await _emails.SendEmail(await EquipmentRequestEmailSetup.CreateDelayedEmail(recipients,
//                                originalRequest,
//                                request.JobTypes.First(j => j.Id == jobType.Id), clientPortalAddress));
//                        }

//                    // Ready for pickup emails
//                    foreach (var jobType in originalRequest.JobTypes)
//                        if (jobType.CheckForReadyForPickup(request))
//                        {
//                            var equipmentRequestBlobFiles = request.Files.Where(f => f.HasPhoto);
//                            var downloadTasks = equipmentRequestBlobFiles.Select(async f =>
//                            {
//                                var download = await _equipmentRequests.DownloadBlobAsync(request.Id, f.Name);
//                                var ms = new MemoryStream();
//                                await download.Content.CopyToAsync(ms);
//                                return new EmailAttachment {FileName = f.Name, Stream = ms};
//                            });
//                            var recipients = new List<IEmailRecipient> {requester};
//                            foreach (var recipientEmail in request.SendTo)
//                            {
//                                var recipient = await _userProfiles.GetAsync(recipientEmail);
//                                recipients.Add(recipient);
//                            }

//                            var attachments = await Task.WhenAll(downloadTasks);
//                            await _emails.SendEmail(await EquipmentRequestEmailSetup.CreateReadyForPickupEmail(
//                                recipients, request,
//                                request.JobTypes.First(j => j.Id == jobType.Id), clientPortalAddress, attachments));
//                        }
//                }
//            }

//            // Additional Hot Tapping Equipment Added email:
//            var originalJobTypeIds = originalRequest.JobTypes.Select(j => j.Id);
//            // If there are new job types that were added, and are in the New status
//            var jobTypesWereAdded = originalRequest.JobTypes.Count < request.JobTypes.Count;
//            var someNewJobTypesHaveStatusAsNew = request.JobTypes.Any(j => !originalJobTypeIds.Contains(j.Id) && j.IsNew());
//            if (jobTypesWereAdded && someNewJobTypesHaveStatusAsNew)
//            {
//                var equipmentCenterRecipients = await GetEquipmentCenterEmailRecipients(request, user);
//                if (equipmentCenterRecipients == null || equipmentCenterRecipients.Count == 0)
//                {
//                    equipmentCenterRecipients.Add(user);
//                }
//                var addedHotTappingEquipmentEmail =
//                    await EquipmentRequestEmailSetup.CreateAddedHotTappingEquipmentEmail(equipmentCenterRecipients,
//                        request,
//                        clientPortalAddress);
//                await _emails.SendEmail(addedHotTappingEquipmentEmail);
//            }

//            if (originalRequest.JobTypes.First().IsNew() &&
//                request.JobTypes.First().IsNew())
//            {
//                if (originalRequest.JobTypes.First().CheckForDelayUpdate(request))
//                {
//                    var delayUpdateEmail = await EquipmentRequestEmailSetup.CreateDelayUpdateEmail(
//                        originalRequest,
//                        request,
//                        request.JobTypes.First(j => j.Id == originalRequest.JobTypes.First().Id),
//                        clientPortalAddress,
//                        await SetupDelayEmailRecipients(request));
//                    await _emails.SendEmail(delayUpdateEmail);
//                }
//            }
//            else
//            {
//                var changes = EquipmentRequest.DetectJobInfoChanges(originalRequest, request);
//                if (changes.Changes.Any() || changes.JobTypeChanges.Any())
//                {
//                    var recipients = await SetupRequestChangeSummaryRecipientsAsync(request);
//                    recipients.Add(user);
//                    await _emails.SendEmail(await EquipmentRequestEmailSetup.CreateJobInfoChangeEmail(recipients,
//                        originalRequest, changes, clientPortalAddress));
//                }

//                foreach (var jobType in originalRequest.JobTypes)
//                    if (jobType.CheckForDelayUpdate(request))
//                    {
//                        var delayUpdateEmail = await EquipmentRequestEmailSetup.CreateDelayUpdateEmail(
//                            originalRequest,
//                            request,
//                            request.JobTypes.First(j => j.Id == jobType.Id),
//                            clientPortalAddress,
//                            await SetupDelayEmailRecipients(request));
//                        await _emails.SendEmail(delayUpdateEmail);
//                    }
//            }

//            // Only send job type/equipment update email if the request is in the new state, and not when it is delayed
//            if (originalRequest.JobTypes.First().IsNew() &&
//                request.JobTypes.First().IsNew() &&
//                request.JobTypes.All(j => j.Delayed == false))
//            {
//                // Detect if there are any changes to the job types equipment grid fields
//                var jobTypeDiff =
//                    JobType.DetectEquipmentChanges(originalRequest.JobTypes.ToList(), request.JobTypes.ToList());


//                if (jobTypeDiff.HasChanges && jobTypeDiff.Values.Any(variances =>
//                        variances.Any(variance => variance.PropertyName != nameof(JobType.TargetShipDate))))
//                {
//                    // Get the recipients
//                    var recipients = await SetupRequestChangeSummaryRecipientsAsync(request);

//                    // Create the email
//                    var equipmentChangedEmail =
//                        await EquipmentRequestEmailSetup.CreateJobEquipmentUpdatedEmailAsync(
//                            jobTypeDiff,
//                            recipients,
//                            request,
//                            clientPortalAddress);

//                    // Send the email
//                    await _emails.SendEmail(equipmentChangedEmail);
//                }

//                if (jobTypeDiff.HasChanges) CreateEquipmentUpdateNote(originalRequest, request, user);
//            }

//            // logic for custom notes
//            if (!originalRequest.HasFileChanges(request))
//            {
//                if (originalRequest.JobTypes.First().IsNew() &&
//                    request.JobTypes.First().IsNew() &&
//                    request.JobTypes.All(j => j.Delayed == false))
//                {
//                    if (!originalRequest.HasFieldChanges(request) && !originalRequest.JobTypes.Any(j =>
//                            j.HasAnyChanges(request.JobTypes.FirstOrDefault(i => i.Id == j.Id))))
//                    {
//                        var recipients = new List<IEmailRecipient>();

//                        if (requester != null) recipients.Add(requester);

//                        foreach (var recipientEmail in request.SendTo)
//                        {
//                            var recipient = await _userProfiles.GetAsync(recipientEmail);
//                            recipients.Add(recipient);
//                        }

//                        recipients.AddRange(await GetEquipmentCenterEmailRecipients(request, user));

//                        await _emails.SendEmail(await EquipmentRequestEmailSetup.CreateCustomNoteEmail(recipients,
//                            request, request.JobTypes.LastOrDefault(), clientPortalAddress, true));
//                    }
//                }
//                else
//                {
//                    foreach (var jobType in request.JobTypes)
//                    {
//                        var originalJobType =
//                            originalRequest.JobTypes.FirstOrDefault(originalJobType =>
//                                originalJobType.Id == jobType.Id);
//                        if (originalJobType != null)
//                            if (!originalJobType.CheckForStatusChange(jobType) &&
//                                originalJobType.Delayed == jobType.Delayed)
//                                if (!originalJobType.HasAnyChanges(jobType) &&
//                                    !originalRequest.HasFieldChanges(request))
//                                {
//                                    if (jobType == null) Debugger.Break();
//                                    var notesCount = jobType.Notes.Count;
//                                    var oldNotesCount = originalJobType.Notes?.Count ?? 0;
//                                    if (notesCount > oldNotesCount)
//                                    {
//                                        var recipients = new List<IEmailRecipient>();

//                                        if (requester != null) recipients.Add(requester);

//                                        foreach (var recipientEmail in request.SendTo)
//                                        {
//                                            var recipient = await _userProfiles.GetAsync(recipientEmail);
//                                            recipients.Add(recipient);
//                                        }

//                                        recipients.AddRange(await GetEquipmentCenterEmailRecipients(request, user));

//                                        await _emails.SendEmail(
//                                            await EquipmentRequestEmailSetup.CreateCustomNoteEmail(recipients, request,
//                                                jobType, clientPortalAddress));
//                                    }
//                                }
//                    }
//                }
//            }

//            if (originalRequest.JobNumber == request.JobNumber)
//            {
//                await _equipmentRequests.UpdateItemAsync(request.Id, request);
//            }
//            else
//            {
//                await _equipmentRequests.DeleteItemAsync(originalRequest.Id, originalRequest.JobNumber);
//                await _equipmentRequests.AddItemAsync(request);
//            }

//            if (form.Files.Count > 0) await _equipmentRequests.UploadFilesAsync(request, form.Files, email);
//            request = await _equipmentRequests.GetItemAsync(request.Id);

//            if (form.Files.Count > 0 && request.JobTypes.All(jobType => jobType.Delayed == false))
//            {
//                var recipients = new List<IEmailRecipient> {requester};
//                foreach (var recipientEmail in request.SendTo)
//                {
//                    var recipient = await _userProfiles.GetAsync(recipientEmail);
//                    recipients.Add(recipient);
//                }

//                if (_env.IsProduction() || _env.IsStaging())
//                {
//                    var profile = EquipmentCenter.CreateRecipient(request.EquipmentCenter);
//                    recipients.Add(profile);
//                }
//                else if (string.Equals(request.EquipmentCenter, "Test Equipment Center",
//                             StringComparison.CurrentCultureIgnoreCase))
//                {
//                    await CreateTestEqcRecipients(recipients);
//                }

//                var newAttachments = SetupFileAttachments(form.Files);
//                await _emails.SendEmail(await EquipmentRequestEmailSetup.CreateAttachmentAddedEmail(recipients,
//                    request, newAttachments, clientPortalAddress));
//                foreach (var attachment in newAttachments) attachment.Stream?.Close();
//            }

//            await _hub.Clients.All.SendAsync("requestUpdated", request);
//            return NoContent();
//        }

//        /// <summary>
//        ///     Delete a file that is associated with an Equipment Request and remove that association.
//        /// </summary>
//        /// <param name="equipmentRequestId"></param>
//        /// <param name="fileId"></param>
//        /// <returns></returns>
//        [HttpDelete("{equipmentRequestId}/DeleteFile/{fileId}")]
//        public async Task<IActionResult> DeleteFile(string equipmentRequestId, string fileId)
//        {
//            var request = await _equipmentRequests.GetItemAsync(equipmentRequestId);
//            await _equipmentRequests.DeleteFile(equipmentRequestId, fileId);
//            var deletedFiles = request.Files.Where(file => file.Id == fileId);
//            if (request.Requester == null) return NoContent();

//            var requester = await _userProfiles.GetAsync(request.Requester);
//            var recipients = new List<IEmailRecipient> {requester};
//            foreach (var recipientEmail in request.SendTo)
//            {
//                var recipient = await _userProfiles.GetAsync(recipientEmail);
//                recipients.Add(recipient);
//            }

//            if (_env.IsProduction() || _env.IsStaging())
//            {
//                var profile = EquipmentCenter.CreateRecipient(request.EquipmentCenter);
//                recipients.Add(profile);
//            }
//            else if (string.Equals(request.EquipmentCenter, "Test Equipment Center",
//                         StringComparison.CurrentCultureIgnoreCase))
//            {
//                await CreateTestEqcRecipients(recipients);
//            }

//            var clientPortalAddress = _config.GetSection("Clients:ClientPortal").Value;
//            if (request.JobTypes.All(j => j.Delayed == false))
//                await _emails.SendEmail(
//                    await EquipmentRequestEmailSetup.CreateAttachmentRemovedEmail(recipients, request, deletedFiles,
//                        clientPortalAddress));
//            return NoContent();
//        }

//        /// <summary>
//        ///     Download a file that is associated with an Equipment Request.
//        /// </summary>
//        /// <param name="routeId"></param>
//        /// <param name="blobName"></param>
//        /// <returns></returns>
//        [HttpGet("DownloadFile/{routeId}/{blobName}")]
//        public async Task<IActionResult> Get(string routeId, string blobName)
//        {
//            var downloadInfo = await _equipmentRequests.DownloadBlobAsync(routeId, blobName);
//            return File(downloadInfo.Content, "application/octet-stream");
//        }

//        /// <summary>
//        ///     Delete 1 or many Equipment Demand requests
//        /// </summary>
//        /// <param name="ids"></param>
//        /// <returns></returns>
//        [Authorize(Policy = "App:QA")]
//        [HttpDelete]
//        public async Task<IActionResult> DeleteRequests(StringList ids)
//        {
//            if (ids == null) return BadRequest("Must provide at least one id");
//            await _equipmentRequests.DeleteItemsAsync(ids.ToArray());
//            return NoContent();
//        }

//        /// <summary>
//        ///     Add additional equipment to an equipment request job type line item.
//        ///     Will create a new job type line item within the request if the status
//        ///     of the job type is shipping or past that stage.
//        /// </summary>
//        /// <param name="requestId"></param>
//        /// <param name="jobTypeId"></param>
//        /// <param name="submission"></param>
//        /// <returns></returns>
//        [HttpPost("AdditionalEquipment/{requestId}/JobTypes/{jobTypeId:int}")]
//        public async Task<IActionResult> AddAdditionalEquipment(string requestId, int jobTypeId,
//            AdditionalEquipmentSubmission submission)
//        {
//            var request = await _equipmentRequests.GetItemAsync(requestId);
//            if (request == null) return NotFound();
//            var jobType = request.JobTypes.SingleOrDefault(j => j.Id == jobTypeId);
//            if (jobType == null) return NotFound();
//            if (request.EquipmentCenter.IsNullOrEmpty())
//                return BadRequest("Equipment Center must be set before additional equipment can be added.");

//            // Get current user profile
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);

//            submission.AddedBy = user.Id;
//            submission.DateAdded = DateTime.UtcNow;

//            if (new[] {"Shipped", "Partially Returned", "Returned"}.Contains(jobType.RequestStatus))
//            {
//                // Create a new job type, copying the properties and changing description to have "ADDED EQUIPMENT - " prepended.
//                var newJobType = new JobType
//                {
//                    BeenDelayed = jobType.BeenDelayed,
//                    DateReturned = jobType.DateReturned,
//                    DateShipped = jobType.DateShipped,
//                    DelayComment = jobType.DelayComment,
//                    Delayed = jobType.Delayed,
//                    Description = jobType.Description.StartsWith("ADDED EQUIPMENT - ")
//                        ? jobType.Description
//                        : $"ADDED EQUIPMENT - {jobType.Description}",
//                    FlangeRating = jobType.FlangeRating,
//                    Id = request.JobTypes.Select(j => j.Id).Max() + 1,
//                    RequestStatus = "Building Equipment",
//                    JSSNumber = jobType.JSSNumber,
//                    LocationReturned = jobType.LocationReturned,
//                    NewExpectedDate = jobType.NewExpectedDate,
//                    Notes = jobType.Notes,
//                    OperatingPressure = jobType.OperatingPressure,
//                    OperatingTemperature = jobType.OperatingTemperature,
//                    PackageReviewer = jobType.PackageReviewer,
//                    PipeSize = jobType.PipeSize,
//                    QualityControlTechnician = jobType.QualityControlTechnician,
//                    ReasonForDelay = jobType.ReasonForDelay,
//                    ReceivedDate = jobType.ReceivedDate,
//                    ShippingMethod = jobType.ShippingMethod,
//                    TapSize = jobType.TapSize,
//                    TargetShipDate = jobType.TargetShipDate,
//                    TravelDistanceToTopPipe = jobType.TravelDistanceToTopPipe,
//                    Type = jobType.Type,
//                    AdditionalEquipment = new List<AdditionalEquipmentSubmission> {submission}
//                };
//                newJobType.Notes.Add(new RequestNote
//                {
//                    Created = DateTime.UtcNow,
//                    Note =
//                        "Additional equipment added after Ready for Pickup.  New line item created to track additional equipment request.  All previous job note entries refer to original request.",
//                    UserName = user.Name,
//                    UserEmail = user.Id
//                });
//                request.JobTypes.Add(newJobType);
//                jobTypeId = newJobType.Id;
//            }
//            else
//            {
//                jobType.AdditionalEquipment ??= new List<AdditionalEquipmentSubmission>();
//                jobType.AdditionalEquipment.Add(submission);
//                jobType.Notes.Add(new RequestNote
//                {
//                    Created = DateTime.UtcNow, UserEmail = user.Id, UserName = user.Name,
//                    Note = "Additional equipment added"
//                });
//            }

//            await _equipmentRequests.UpdateItemAsync(requestId, request);

//            // Send email
//            var recipients = await GetEquipmentCenterEmailRecipients(request, user);
//            if (recipients == null)
//                return BadRequest(
//                    "Equipment Center must be provided before this request can add additional equipment.");

//            if (recipients.Count <= 0) return Ok();

//            var clientPortalAddress = _config.GetSection("Clients:ClientPortal").Value;
//            await _emails.SendEmail(
//                await EquipmentRequestEmailSetup.CreateAdditionalEquipmentEmail(recipients, request, jobTypeId,
//                    clientPortalAddress));

//            return Ok();
//        }

//        /// <summary>
//        ///     Get email for Equipment Center (depending on environment).  Provide user for use in a local environment.
//        /// </summary>
//        /// <param name="request"></param>
//        /// <param name="user"></param>
//        /// <returns></returns>
//        private async Task<List<IEmailRecipient>> GetEquipmentCenterEmailRecipients(EquipmentRequest request,
//            IEmailRecipient user)
//        {
//            var recipients = new List<IEmailRecipient>();
//            if (request.EquipmentCenter == null) return null;

//            if (_env.IsProduction())
//            {
//                var profile = EquipmentCenter.CreateRecipient(request.EquipmentCenter);
//                recipients.Add(profile);
//            }
//            else if (_env.IsLocal())
//            {
//                recipients.Add(user);
//            }
//            else if (string.Equals(request.EquipmentCenter, "Test Equipment Center",
//                         StringComparison.CurrentCultureIgnoreCase))
//            {
//                await CreateTestEqcRecipients(recipients);
//            }

//            return recipients;
//        }

//        private async Task<List<IEmailRecipient>> SetupDelayEmailRecipients(EquipmentRequest request)
//        {
//            var recipients = new List<IEmailRecipient>();
//            if (request.EquipmentCenter == null) return recipients;
//            if (_env.IsStaging() || _env.IsProduction())
//            {
//                var profile = EquipmentCenter.CreateRecipient(request.EquipmentCenter);
//                recipients.Add(profile);
//            }
//            else if (_env.IsLocal())
//            {
//                var localEmail = User?.Identity?.Name?.ToLower();
//                var localUser = await _userProfiles.GetAsync(localEmail);
//                recipients.Add(localUser);
//            }
//            else if (string.Equals(request.EquipmentCenter, "Test Equipment Center",
//                         StringComparison.CurrentCultureIgnoreCase))
//            {
//                await CreateTestEqcRecipients(recipients);
//            }
//            else
//            {
//                var brian = await _userProfiles.GetAsync("<EMAIL>");
//                var chandler = await _userProfiles.GetAsync("<EMAIL>");
//                recipients.Add(brian);
//                recipients.Add(chandler);
//            }

//            return recipients;
//        }

//        private static List<EmailAttachment> SetupFileAttachments(IFormFileCollection files)
//        {
//            var emailAttachments = new List<EmailAttachment>();
//            foreach (var file in files)
//            {
//                if (file.Length <= 0) continue;
//                // not using using cause it closes the stream too early
//                var ms = new MemoryStream();
//                file.CopyTo(ms);

//                emailAttachments.Add(new EmailAttachment { FileName = file.FileName, Stream = ms });
//            }

//            return emailAttachments;
//        }

//        private async Task CreateTestEqcRecipients(ICollection<IEmailRecipient> recipients)
//        {
//            var brian = await _userProfiles.GetAsync("<EMAIL>");
//            var chandler = await _userProfiles.GetAsync("<EMAIL>");
//            var sonia = await _userProfiles.GetAsync("<EMAIL>");
//            recipients.Add(brian);
//            recipients.Add(chandler);
//            recipients.Add(sonia);
//        }

//        private async Task<List<IEmailRecipient>> SetupRequestChangeSummaryRecipientsAsync(EquipmentRequest request)
//        {
//            // Only send email to equipment center if we are in staging or production, or if the equipment center is the Test Equipment Center
//            var recipients = new List<IEmailRecipient>();
//            if (_env.IsStaging() || _env.IsProduction())
//                recipients.Add(EquipmentCenter.CreateRecipient(request.EquipmentCenter));
//            else if (string.Equals(request.EquipmentCenter, "Test Equipment Center",
//                         StringComparison.CurrentCultureIgnoreCase))
//                await CreateTestEqcRecipients(recipients);

//            // Send email to the original requester of the equipment demand request
//            recipients.Add(await _userProfiles.GetAsync(request.Requester));

//            // Send email to everyone listed in the Send To field on creation of the equipment demand request
//            recipients.AddRange(
//                await Task.WhenAll(request.SendTo.Select(emailAddress => _userProfiles.GetAsync(emailAddress))));
//            return recipients;
//        }

//        private static void CreateEquipmentUpdateNote(EquipmentRequest originalRequest, EquipmentRequest request,
//            UserProfile user)
//        {
//            const string noteText = "<p>Changes were made to the job while in new status<p>";
//            var originalEquipmentTable =
//                EquipmentRequestEmailSetup.CreateEquipmentTableHtml(originalRequest.JobTypes)
//                    .Replace("<u>Equipment</u>", "<u>Original Equipment</u>");
//            var newEquipmentTable = EquipmentRequestEmailSetup.CreateEquipmentTableHtml(request.JobTypes)
//                .Replace("<u>Equipment</u>", "<u>Updated Equipment</u>");
//            foreach (var jobType in request.JobTypes)
//            {
//                var jobNote = new RequestNote
//                {
//                    Note = noteText + originalEquipmentTable + newEquipmentTable,
//                    UserEmail = user.Id,
//                    UserName = user.GivenName + " " + user.Surname,
//                    Created = DateTime.UtcNow
//                };
//                if (jobType.Notes == null)
//                    jobType.Notes = new List<RequestNote>
//                    {
//                        jobNote
//                    };
//                else
//                    jobType.Notes.Add(jobNote);
//            }
//        }

//    }
//}