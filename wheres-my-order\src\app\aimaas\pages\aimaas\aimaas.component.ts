import {
    Compo<PERSON>,
    <PERSON>ement<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    OnInit,
    ViewChild
} from '@angular/core';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { shareReplay } from 'rxjs/operators';
import { UserProfile } from '../../../profile/models';
import { createSafeResizeObserver } from '../../../shared/helpers';
import { UsersService } from '../../../shared/services';
import {
    EquipmentDashboardComponent,
    InspectionsDashboardComponent
} from '../../components';
import { RecommendationsDashboardComponent } from '../../components/recommendations-dashboard/recommendations-dashboard.component';
import {
    AnomaliesRecommendations,
    Asset,
    AssetManagementSite
} from '../../models';
import { SiteLabelPipe } from '../../pipes';
import { CredoSoftService } from '../../services';

@Component({
    selector: 'app-aimaas',
    templateUrl: './aimaas.component.html',
    styleUrls: ['./aimaas.component.scss']
})
export class AIMaaSComponent implements OnInit, OnDestroy {
    @ViewChild(InspectionsDashboardComponent)
    inspectionsDashboard: InspectionsDashboardComponent;
    @ViewChild(EquipmentDashboardComponent)
    equipmentDashboard: EquipmentDashboardComponent;
    @ViewChild(RecommendationsDashboardComponent)
    recommendationDashboard: RecommendationsDashboardComponent;
    selectionOption = [
        'Equipment Dashboard',
        'Inspection Dashboard',
        'Recommendations Dashboard'
    ];
    availableSites: AssetManagementSite[];
    selectedSite: AssetManagementSite;
    allInspections: any[];
    allAssets: any[];
    currentUser: UserProfile;
    allAnomalies: any[];
    isLoading: boolean;
    anomaliesData: any;
    initialAnomaly: AnomaliesRecommendations = null;
    submissionPopupTitle: string = 'Anomaly Update';
    submissionPopupVisible: boolean = false;
    selectedDashboard: string = 'Equipment Dashboard';
    private _observer: ResizeObserver;
    private _assetsForSite = new BehaviorSubject<Asset[]>([]);
    private _inspectionsForSite = new BehaviorSubject<any[]>([]);
    private _anamoliesForSite = new BehaviorSubject<AnomaliesRecommendations[]>(
        []
    );

    constructor(
        private readonly _credoSoft: CredoSoftService,
        private readonly _users: UsersService,
        private readonly _hostElement: ElementRef,
        private readonly _sitePipe: SiteLabelPipe
    ) {}

    get assetsForSite$(): Observable<Asset[]> {
        return this._assetsForSite.asObservable().pipe(shareReplay());
    }
    get anomaliesForSite$(): Observable<AnomaliesRecommendations[]> {
        return this._anamoliesForSite.asObservable().pipe(shareReplay());
    }

    get inspectionsForSite$(): Observable<any[]> {
        return this._inspectionsForSite.asObservable().pipe(shareReplay());
    }

    ngOnInit(): void {
        this.isLoading = true;
        this._observer = createSafeResizeObserver(() => {
            this.inspectionsDashboard?.renderCharts();
            this.equipmentDashboard?.renderCharts();
            this.recommendationDashboard?.renderCharts();
        });
        this._observer.observe(this._hostElement.nativeElement);

        combineLatest([
            this._credoSoft.assets$,
            this._credoSoft.assetManagementSites$,
            this._credoSoft.inspections$,
            this._users.currentProfile$,
            this._credoSoft.anomalies$
            // this._credoSoft.generalAnalysis$
        ]).subscribe(([assets, sites, inspections, currentUser, anomalies]) => {
            this.availableSites = sites;
            this.currentUser = currentUser;
            const roles = currentUser.roles.map((role) => role.toLowerCase());
            if (roles) {
                if (roles.includes('aimaas:demo')) {
                    this.availableSites = this.availableSites.filter(
                        (site) =>
                            site.locationid == Number('635140707384299520')
                    );
                } else if (
                    !roles.includes('app:admin') &&
                    !roles.includes('aimaas:admin') &&
                    currentUser.assetManagementSiteIds
                ) {
                    this.availableSites = this.availableSites.filter((site) =>
                        currentUser.assetManagementSiteIds.includes(
                            site.locationid
                        )
                    );
                }
            }
            this.allAnomalies = anomalies;
            this.allInspections = inspections;
            this.allAssets = assets;
            this.selectedSite =
                this.availableSites.find(
                    (site) =>
                        site.locationid ==
                        Number(localStorage.getItem('selectedSite'))
                ) ?? this.availableSites[0];
            this.selectedDashboard = localStorage.getItem('selecteddashboard')
                ? localStorage.getItem('selecteddashboard')
                : 'Equipment Dashboard';
            this.isLoading = false;
        });
    }
    ngOnDestroy(): void {
        //this._observer.unobserve(this._hostElement.nativeElement);
    }
    clientSubmitDataOnclick(e: string) {
        if (e === 'frombuttonclick') {
            this.initialAnomaly = null;
        }
        this.submissionPopupVisible = !this.submissionPopupVisible;
    }
    clientSubmissionTitleValueChange(e: any) {
        this.submissionPopupTitle = e;
    }
    clientDataFormSubmitted(e: any) {
        this.submissionPopupVisible = false;
    }

    customDisplayExpr = (site: AssetManagementSite): string => {
        if (site) return this._sitePipe.transform(site);
        else return '';
    };

    changeSite(selectedSite) {
        let _selectedSite = selectedSite.selectedItem;
        if (_selectedSite == null) {
            setTimeout(() => {
                this.selectedSite = this.availableSites[0];
                localStorage.setItem(
                    'selectedSite',
                    String(this.selectedSite.locationid)
                );
            }, 1);
        } else {
            this.selectedSite = selectedSite.selectedItem;
            localStorage.setItem(
                'selectedSite',
                String(this.selectedSite.locationid)
            );
        }
        this.broadcastSiteData();
    }

    dashboardChanged(event) {
        localStorage.setItem('selecteddashboard', event.value);
        this.broadcastSiteData();
    }
    convertHtmlToText(html: string): string {
        if (html == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return doc.documentElement.textContent ?? ' ';
    }
    private broadcastSiteData() {
        if (this.selectedSite) {
            this._inspectionsForSite.next(
                this.allInspections.filter(
                    (inspection) =>
                        inspection.locationid === this.selectedSite.locationid
                )
            );
            const anomaliesWithTextDescription = this.allAnomalies
                .filter(
                    (anomaly) =>
                        anomaly.locationid === this.selectedSite.locationid
                )
                .map((anomaly, index) => ({
                    ...anomaly,
                    anomalydescription: this.convertHtmlToText(
                        anomaly.anomalydescription
                    ),
                    serialnumber: index + 1
                }));

            this._anamoliesForSite.next(anomaliesWithTextDescription);
            this._assetsForSite.next(
                this.allAssets.filter(
                    (asset) => asset.locationid === this.selectedSite.locationid
                )
            );
        }
    }
}
