﻿using System;

namespace OrderTracking.API.Extensions
{
    /// <summary>
    ///     Convenience extensions to DateTime
    /// </summary>
    public static class DateTimeExtensions
    {
        /// <summary>
        ///     Convert date time to central
        /// </summary>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public static DateTime ConvertToCentral(this DateTime dateTime)
        {
            var cstZone = TimeZoneInfo.FindSystemTimeZoneById("Central Standard Time");
            return TimeZoneInfo.ConvertTime(dateTime, cstZone);
        }

        /// <summary>
        ///     Convert date time to central while handle nullable dates
        /// </summary>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public static DateTime? ConvertToCentral(this DateTime? dateTime)
        {
            if (dateTime == null) return null;
            return ConvertToCentral((DateTime) dateTime);
        }
    }
}
