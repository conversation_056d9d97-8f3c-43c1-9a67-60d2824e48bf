.dx-card {
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    border-radius: 2px;
    background-color: #fff;
    margin: 2px 2px 10px;
}
// .doughnut{
//     height: 800px;
// }
.flex-container {
    display: flex;
    & > * {
        flex-grow: 1;
        flex-basis: 0;
    }
}
.flex-container2 {
    display: flex;
    flex-direction: column;
    & > * {
        flex-grow: 1;
        flex-basis: 0;
    }
}

.barchart-container {
    margin: 23px;
}
#thisYear {
    margin: 10px;
}
.overviewbox-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-left: 50px;
    padding-right: 50px;
    padding-top: 10px;
    padding-bottom: 10px;
    width: 50%;
}

.doughnut-chart {
    width: 98%;
    margin-top: 15px;
    margin-left: 1%;
    margin-right: 1%;
    height: auto;
    min-height: 600px;
    min-width: 380px;
}

.overview-number-container {
    border: black;
    border-width: 2px;
    border-style: solid;
    height: 200px;
    width: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.overview-text-container {
    border: black;
    border-width: 2px;
    border-style: solid;
    border-radius: 15px;
    height: 150px;
    width: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
}
.dx-scrollable {
    display: block;
    height: 100%;
    min-height: 700px;
}
.card-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.checkbox-section {
    flex: 1;
    margin: 35px;
}

.fields {
    padding-bottom: 5px;
}
.checkbox-section {
    margin-bottom: 10px;
}

.chart-section {
    flex: 2;
    margin: 10px;
}
#IncludeOutOfServiceCheckBox {
    padding-top: 26px;
    padding-left: 10px;
}
.doughnut-container {
    height: 400px;
}
.checkbox-column {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
}
