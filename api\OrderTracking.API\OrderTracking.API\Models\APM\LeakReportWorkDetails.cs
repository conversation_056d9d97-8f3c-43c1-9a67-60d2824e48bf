﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class LeakReportWorkDetails
    {
        [JsonProperty("client")]
        public LeakReportField<string> Client { get; set; }
        [JsonProperty("state")]
        public LeakReportField<string> State { get; set; }
        [JsonProperty("city")]
        public LeakReportField<string> City { get; set; }
        [JsonProperty("postalCode")]
        public LeakReportField<string> PostalCode { get; set; }
        [JsonProperty("facility")]
        public LeakReportField<string> Facility { get; set; }
        [JsonProperty("area")]
        public LeakReportField<string> Area { get; set; }
        [JsonProperty("clientContact")]
        public LeakReportField<string> ClientContact { get; set; }
        [JsonProperty("clientWorkOrder")]
        public LeakReportField<string> ClientWorkOrder { get; set; }
        [JsonProperty("purchaseOrder")]
        public LeakReportField<string> PurchaseOrder { get; set; }
        [JsonProperty("teamProjectNumber")]
        public LeakReportField<string> TeamProjectNumber { get; set; }
        [JsonProperty("referenceEdition")]
        public LeakReportField<string> ReferenceEdition { get; set; }
        [JsonProperty("inspectedBy")]
        public LeakReportField<string> InspectedBy { get; set; }
        [JsonProperty("jobDescription")]
        public LeakReportField<string> JobDescription { get; set; }
        [JsonProperty("lease")]
        public LeakReportField<string> Lease { get; set; }
        [JsonProperty("clientContactNumber")]
        public LeakReportField<string> ClientContactNumber { get; set; }
        [JsonProperty("teamDistrict")]
        public LeakReportField<string> TeamDistrict { get; set; }
        [JsonProperty("inspectionTypes")]
        public LeakReportField<string[]> InspectionTypes { get; set; }
        [JsonProperty("clientCostCode")]
        public LeakReportField<string> ClientCostCode { get; set; }
        [JsonProperty("inspectionReference")]
        public LeakReportField<string> InspectionReference { get; set; }
        [JsonProperty("inspectionDate")]
        public LeakReportField<string> InspectionDate { get; set; }
        [JsonProperty("inspectorCertificateNumber")]
        public LeakReportField<string> InspectorCertificateNumber { get; set; }
        [JsonProperty("reviewedBy")]
        public LeakReportField<string> ReviewedBy { get; set; }
        [JsonProperty("reviewerEmail")]
        public LeakReportField<string> ReviewerEmail { get; set; }
        [JsonProperty("reviewerCertificateNumber")]
        public LeakReportField<string> ReviewerCertificateNumber { get; set; }

    }
}
