﻿using System.Threading.Tasks;
using ClientPortal.Shared.Extensions;
using ClientPortal.Shared.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderTracking.API.Services;

namespace OrderTracking.API.Controllers
{
    /// <summary> 
    ///     API Controller for reading remote monitoring sensor readings from Smartpims
    /// </summary> 
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Policy = "UserIsActive")]
    public class RemoteMonitoringController : ControllerBase
    {
        private readonly ISensorReadingsSQLService _sensors;
        private readonly IUserProfilesService _userProfiles;

        /// <summary>
        ///     Constructor for controller
        /// </summary>
        /// <param name="sensors"></param>
        public RemoteMonitoringController(ISensorReadingsSQLService sensors, IUserProfilesService userProfiles)
        {
            _userProfiles = userProfiles;
            _sensors = sensors;
        }

        /// <summary>
        ///     GET endpoint
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var email = User?.Identity?.Name?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            if (user == null) return Unauthorized();
            return user.HasRole("app:admin") ? Ok(await _sensors.GetSensorReadingsAsync()) : Ok(await _sensors.GetSensorReadingsForUserAsync(user));
        }
    }
}