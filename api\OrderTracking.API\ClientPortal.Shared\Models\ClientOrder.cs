﻿using System;
using System.Collections.Generic;
using ClientPortal.Shared.Models.Helpers;
using Dapper.Contrib.Extensions;
using Newtonsoft.Json;

namespace ClientPortal.Shared.Models
{
    public class ClientOrder
    {
        [ExplicitKey]
        [JsonProperty(PropertyName = "id")]
        public string Id { get; set; }

        [JsonProperty(PropertyName = "salesId")]
        public string SALESID { get; set; }

        [JsonProperty(PropertyName = "prodId")]
        public string PRODID { get; set; }

        [JsonProperty(PropertyName = "projId")]
        public string PROJID { get; set; }

        [JsonProperty(PropertyName = "jssJobId")]
        public string JSSJOBID { get; set; }

        [JsonProperty(PropertyName = "jssDateToMfg")]
        [JsonConverter(typeof(AsUTCDateTimeConverter))]
        public DateTime? JSSDATETOMFG { get; set; }

        [JsonProperty(PropertyName = "status")]
        public string STATUS { get; set; }
        
        [JsonProperty(PropertyName = "name")] 
        public string NAME { get; set; }

        [JsonProperty(PropertyName = "salesLineItemId")]
        public string SALESLINEITEMID { get; set; }
        
        [JsonProperty(PropertyName = "finishedDate")]
        public DateTime? FINISHEDDATE { get; set; }

        [JsonProperty(PropertyName = "schedStart")]
        public DateTime? SCHEDSTART { get; set; }

        [JsonProperty(PropertyName = "schedEnd")]
        public DateTime? SCHEDEND { get; set; }

        [JsonProperty(PropertyName = "salesName")]
        public string SALESNAME { get; set; }

        [JsonProperty(PropertyName = "purchaseOrderFormNum")]
        public string PURCHORDERFORMNUM { get; set; }

        [JsonProperty(PropertyName = "externalCustomer")]
        public string EXTERNALCUSTOMER { get; set; }

        [JsonProperty(PropertyName = "externalCustomerName")]
        public string EXTERNALCUSTOMERNAME { get; set; }

        [JsonProperty("salesStatus")] 
        public string SALESSTATUS { get; set; }

        [JsonProperty(PropertyName = "salesOrderShipDate")]
        public DateTime? SHIPDATE { get; set; }

        [JsonProperty(PropertyName = "shippingDateRequested")]
        public DateTime? SHIPPINGDATEREQUESTED { get; set; }

        [JsonProperty(PropertyName = "shippingDateConfirmed")]
        public DateTime? SHIPPINGDATECONFIRMED { get; set; }

        [JsonProperty(PropertyName = "receiptDateConfirmed")]
        public DateTime? CONFIRMEDRECEIPTDATE { get; set; }

        [JsonProperty(PropertyName = "packingSlipId")]
        public string PACKINGSLIPID { get; set; }

        [JsonProperty(PropertyName = "customerAccount")]
        public string CUSTACCOUNT { get; set; }

        [JsonProperty(PropertyName = "tisTrackingNumber")]
        public string TIS_TRACKINGNUMBER { get; set; }

        [JsonProperty(PropertyName = "quantityShipped")]
        public decimal? DELIVERED { get; set; }

        [JsonProperty(PropertyName = "quantityRemaining")]
        public decimal? REMAININGQUANTITY { get; set; }

        [JsonProperty(PropertyName = "ordered")]
        public decimal? ORDERED { get; set; }

        [JsonProperty(PropertyName = "files")]
        public ICollection<OrderBlobFile> Files { get; set; }

        [JsonProperty(PropertyName = "canUploadSupportDocuments")]
        public bool CanUploadSupportDocuments { get; set; }

        [JsonProperty(PropertyName = "salesLineRecId")]
        public long? SALESLINE_RECID { get; set; }

        [JsonProperty(PropertyName = "internalCompanyPurchaseId")]
        public string INTERCOMPANYPURCHID { get; set; }

        [JsonProperty(PropertyName = "isCustomOrder")]
        public bool ISCUSTOMORDER { get; private set; }

        [JsonProperty(PropertyName = "deliveryDate")]
        public DateTime? DELIVERYDATE { get; set; }

        [JsonProperty(PropertyName = "internalPurchaseOrderFormNum")]
        public string INTERNAL_PURCHASEORDERFORMNUM { get; set; }

        public static ClientOrder CreateClientOrder(Order order)
        {
            return new ClientOrder
            {
                Id = order.Id,
                SALESID = order.SALESID,
                JSSJOBID = order.JSSJOBID,
                PRODID = order.PRODID,
                PROJID = order.PROJID,
                JSSDATETOMFG = order.JSSDATETOMFG,
                STATUS = order.STATUS,
                NAME = order.NAME,
                SALESLINEITEMID = order.SALESLINEITEMID,
                FINISHEDDATE = order.FINISHEDDATE,
                SCHEDSTART = order.SCHEDSTART,
                SCHEDEND = order.SCHEDEND,
                SALESNAME = order.SALESNAME,
                PURCHORDERFORMNUM = order.PURCHORDERFORMNUM,
                EXTERNALCUSTOMER = order.EXTERNALCUSTOMER,
                EXTERNALCUSTOMERNAME = order.EXTERNALCUSTOMERNAME,
                SALESSTATUS = order.SALESSTATUS,
                SHIPDATE = order.SHIPDATE,
                SHIPPINGDATEREQUESTED = order.SHIPPINGDATEREQUESTED,
                SHIPPINGDATECONFIRMED = order.SHIPPINGDATECONFIRMED,
                CONFIRMEDRECEIPTDATE = order.CONFIRMEDRECEIPTDATE,
                PACKINGSLIPID = order.PACKINGSLIPID,
                CUSTACCOUNT = order.CUSTACCOUNT,
                TIS_TRACKINGNUMBER = order.TIS_TRACKINGNUMBER,
                DELIVERED = order.DELIVERED,
                REMAININGQUANTITY = order.REMAININGQUANTITY,
                ORDERED = order.ORDERED,
                Files = order.Files,
                CanUploadSupportDocuments = order.CanUploadSupportDocuments,
                SALESLINE_RECID = order.SALESLINE_RECID,
                INTERCOMPANYPURCHID = order.INTERCOMPANYPURCHID,
                ISCUSTOMORDER = order.ISCUSTOMORDER,
                DELIVERYDATE = order.DELIVERYDATE,
                INTERNAL_PURCHASEORDERFORMNUM = order.INTERNAL_PURCHASEORDERFORMNUM
            };
        }
    }
}
