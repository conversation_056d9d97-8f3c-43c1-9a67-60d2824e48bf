import { LeakReportPhotoEntries, PhotosEntity } from '../data';

export interface PhotoGroupTransport {
    leakReportID?: string;
    groupDatabaseID: string;
    description: string;
    descriptionComment: string;
    photoComment: string;
    areaOfInterestLatitude: number;
    areaOfInterestLongitude: number;
    areaOfInterestComment: string;
    upstreamTieInLatitude: number;
    upstreamTieInLongitude: number;
    upstreamTieInComment: string;
    downstreamTieInLatitude: number;
    downstreamTieInLongitude: number;
    downstreamTieInComment: string;
    utHighMeasurement: number;
    utHighMeasurementComment: string;
    utLowMeasurement: number;
    utLowMeasurementComment: string;
    photo: PhotosEntity;
    group: LeakReportPhotoEntries;
}
