﻿using APMWebDataInterface.ExampleDataModel;
using APMWebDataInterface.Headless.Entities;
using CommonDataInterface;
using CommonDataInterface.Attributes;
using OrderTracking.API.Models.APM;
using System;
using System.Collections.Generic;
using System.Linq;

namespace OrderTracking.API.Extensions.APM
{
    /// <summary>
    ///     Exception for when a particular inspection type is not found on a work order.
    /// </summary>
    public class VisualInspectionNotFoundException : Exception
    {
        /// <summary>
        ///     Constructor...
        /// </summary>
        /// <param name="inspectionType"></param>
        /// <param name="taskDatabaseId"></param>
        public VisualInspectionNotFoundException(string inspectionType, string taskDatabaseId) : base(
            $"Inspection type not found in work order: {inspectionType}")
        {
            InspectionType = inspectionType;
            TaskDatabaseId = taskDatabaseId;
        }

        /// <summary>
        ///     The inspection type that was provided to search for.
        /// </summary>
        public string InspectionType { get; set; }

        /// <summary>
        ///     The database id of the task
        /// </summary>
        public string TaskDatabaseId { get; set; }
    }

    /// <summary>
    ///     Exception when a visual inspection section is not found
    /// </summary>
    public class VisualInspectionSectionsNotFoundException : Exception
    {
        /// <summary>
        ///     Constructor...
        /// </summary>
        /// <param name="message"></param>
        public VisualInspectionSectionsNotFoundException(string message) : base(message)
        {
        }
    }

    /// <summary>
    ///     Extension methods to update APM Work Order types
    /// </summary>
    public static class WorkOrderExtensions
    {
        public static void Update(this WorkOrder workOrder, VisualInspection inspection)
        {
            var taskForVisualInspection = workOrder.tasks.Find(t => t.id == inspection.TaskDatabaseId);

            if (taskForVisualInspection == null)
            {
                throw new VisualInspectionNotFoundException(inspection.InspectionType, inspection.TaskDatabaseId);
            }

            var incomingSections = inspection.Sections;
            DataModelItem[] taskData = GetTaskData(taskForVisualInspection, inspection);

            if (taskData == null)
            {
                throw new InvalidOperationException($"Unable to find Visual Inspection data with provided information -> TaskID: {inspection.TaskDatabaseId}, InspectionType: {inspection.InspectionType}");
            }

            foreach (var section in taskData)
            {
                ProcessSection(section, incomingSections);
            }
        }

        /// <summary>
        ///     Creates a new work order associated with an asset and a project from a <see cref="NewWorkOrder" />
        /// </summary>
        /// <param name="asset"></param>
        /// <param name="project"></param>
        /// <param name="newWorkOrder"></param>
        /// <returns></returns>
        public static WorkOrder Create(Asset asset, Project project, NewWorkOrder newWorkOrder)
        {
            var workOrder = new WorkOrder(asset, project.id);
            if (newWorkOrder.DueDate != null) workOrder.dueDate.SetValue(newWorkOrder.DueDate);
            if (newWorkOrder.FacilityName != null) workOrder.facilityName.SetValue(newWorkOrder.FacilityName);
            if (newWorkOrder.PlannedEnd != null) workOrder.plannedEnd.SetValue(newWorkOrder.PlannedEnd);
            if (newWorkOrder.PlannedStart != null) workOrder.plannedStart.SetValue(newWorkOrder.PlannedStart);
            if (newWorkOrder.Status != null) workOrder.status.SetValue(newWorkOrder.Status);
            return workOrder;
        }

        /// <summary>
        ///     Adds a new task to a work order from a <see cref="NewTask" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="newTask"></param>
        /// <returns></returns>
        public static APMTask AddNewTask(this WorkOrder workOrder, NewTask newTask)
        {
            var taskType = newTask.TaskType switch
            {
                "Asset Walkdown" => WorkOrder.TaskTypes.AssetWalkdown,
                "External Visual" => WorkOrder.TaskTypes.ExternalVisual,
                "Internal Visual" => WorkOrder.TaskTypes.InternalVisual,
                "Full" => WorkOrder.TaskTypes.Full,
                _ => throw new ArgumentException(nameof(newTask.TaskType))
            };

            var task = workOrder.AddNewTask(taskType);
            task.clientWorkOrderDescription.SetValue(newTask.ClientWorkOrderDescription);
            task.clientWorkOrderNumber.SetValue(newTask.ClientWorkOrderNumber);
            task.leadTech.SetValue(newTask.LeadTechnician);
            task.plannedEnd.SetValue(newTask.PlannedEnd);
            task.dueDate.SetValue(newTask.DueDate);
            task.assignedUsers = newTask.TaskAssignees;
            task.plannedStart.SetValue(newTask.PlannedStart);
            if (newTask.Status != null)
            {
                var status = task.StatusFromString(newTask.Status);
                task.ChangeStatus(status);
            }

            task.taskDetails.supervisor.SetValue(newTask.Supervisor);
            task.purchaseOrderAFE.SetValue(newTask.PurchaseOrderAFE);
            task.clientCostCode.SetValue(newTask.ClientCostCode);
            return task;
        }

        /// <summary>
        ///     Update work order from a <see cref="WorkOrderTransportObject" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="update"></param>
        public static void Update(this WorkOrder workOrder, WorkOrderTransportObject update)
        {
            var shouldUpdateAPMWorkOrderNumber = update.APMWorkOrderNumber != null &&
                                                 update.APMWorkOrderNumber != workOrder.apmWorkOrderNumber.CurrentValue;
            if (shouldUpdateAPMWorkOrderNumber)
                workOrder.apmWorkOrderNumber.SetValue(update.APMWorkOrderNumber);

            var parsed = DateTime.TryParse(workOrder.dueDate.CurrentValue, out var dueDate);
            var shouldUpdateDueDate = update.DueDate != null && (parsed && workOrder.dueDate.CurrentValue != null ||
                                                                 !parsed && workOrder.dueDate.CurrentValue == null) &&
                                      update.DueDate != dueDate;
            if (shouldUpdateDueDate)
                workOrder.dueDate.SetValue(update.DueDate);

            var shouldUpdateFacility =
                update.Facility != null && update.Facility != workOrder.facilityName.CurrentValue;
            if (shouldUpdateFacility)
                workOrder.facilityName.SetValue(update.Facility);

            var shouldUpdateGISLocation = update.Latitude != null && update.Longitude != null;
            if (shouldUpdateGISLocation) workOrder.gisLocation.SetValue(update.Latitude, update.Longitude);

            parsed = DateTime.TryParse(workOrder.plannedEnd.CurrentValue, out var plannedEnd);
            var shouldUpdatePlannedEnd = update.PlannedEndDate != null &&
                                         (parsed && workOrder.plannedEnd.CurrentValue != null ||
                                          !parsed && workOrder.plannedEnd.CurrentValue == null) &&
                                         update.PlannedEndDate != plannedEnd;
            if (shouldUpdatePlannedEnd)
                workOrder.plannedEnd.SetValue(update.PlannedEndDate);

            parsed = DateTime.TryParse(workOrder.plannedStart.CurrentValue, out var plannedStart);
            var shouldUpdatePlannedStart = update.PlannedStartDate != null &&
                                           (parsed && workOrder.plannedStart.CurrentValue != null ||
                                            !parsed && workOrder.plannedStart.CurrentValue == null) &&
                                           update.PlannedStartDate != plannedStart;
            if (shouldUpdatePlannedStart)
                workOrder.plannedStart.SetValue(update.PlannedStartDate);

            var shouldUpdatePrimaryContactName = update.PrimaryContactName != null &&
                                                 update.PrimaryContactName != workOrder.primaryContactName.CurrentValue;
            if (shouldUpdatePrimaryContactName)
                workOrder.primaryContactName.SetValue(update.PrimaryContactName);

            var shouldUpdatePrimaryContactPhone = update.PrimaryContactPhone != null &&
                                                  update.PrimaryContactPhone !=
                                                  workOrder.primaryContactPhone.CurrentValue;
            if (shouldUpdatePrimaryContactPhone)
                workOrder.primaryContactPhone.SetValue(update.PrimaryContactPhone);

            var shouldUpdateStatus = update.Status != null && update.Status != workOrder.status.CurrentValue;
            if (shouldUpdateStatus)
                workOrder.status.SetValue(update.Status);

            if (update.JobScope != null)
                workOrder.jobScope.SetValue(update.JobScope);

            if (update.InspectionSummary != null)
                workOrder.inspectionSummary.SetValue(update.InspectionSummary);

            if (update.ApplicableDamage != null)
                workOrder.applicableDamage.SetValue(update.ApplicableDamage);

            if (update.ReleventIndications != null)
                workOrder.releventIndications.SetValue(update.ReleventIndications);

            if (update.Recommendations != null)
                workOrder.recommendations.SetValue(update.Recommendations);
        }

        private static DataModelItem[] GetTaskData(APMTask taskForVisualInspection, VisualInspection inspection)
        {
            if (taskForVisualInspection.taskType == "Full")
            {
                if (taskForVisualInspection.getTaskData() is FullInspection fullInspection)
                {
                    return inspection.InspectionType switch
                    {
                        "Internal Visual" => fullInspection.InternalSection.GetChildren(),
                        "External Visual" => fullInspection.ExternalSection.GetChildren(),
                        _ => null
                    };
                }
            }

            return taskForVisualInspection.getTaskData().GetChildren();
        }

        private static void ProcessSection(DataModelItem section, ICollection<VisualInspectionSections> incomingSections)
        {
            VisualInspectionSections updatedSection = GetUpdatedSection(section, incomingSections);

            if (updatedSection == null)
            {
                throw new VisualInspectionSectionsNotFoundException("Invalid section");
            }

            var children = section.GetChildren();

            foreach (var child in children)
            {
                if (child is Category)
                {
                    ProcessSection(child, updatedSection.SubSections);
                    continue;
                }

                var question = (AttributeBase)child;
                var updatedQuestion = updatedSection.Questions.FirstOrDefault(q => q.Key == question.DatabaseName);
                if (updatedQuestion is null)
                {
                    continue;
                }

                if (question.GetComment() != updatedQuestion.Comments)
                {
                    question.SetComment(updatedQuestion.Comments);
                }

                if (child is PhotoAttribute)
                {
                    continue;
                }

                if (!question.IsValueEqualTo(updatedQuestion.Response))
                {
                    question.SetGenericValueTo(updatedQuestion.Response);
                }
            }
        }

        private static VisualInspectionSections GetUpdatedSection(DataModelItem section, ICollection<VisualInspectionSections> incomingSections)
        {
            if (section is Category genericSection)
            {
                return incomingSections.FirstOrDefault(incomingSection => string.Equals(incomingSection.DatabaseName, genericSection.DatabaseName, StringComparison.CurrentCultureIgnoreCase));
            }

            return incomingSections.FirstOrDefault(s => string.Equals(s.DatabaseName, section.GetType().Name, StringComparison.CurrentCultureIgnoreCase));
        }
    }
}