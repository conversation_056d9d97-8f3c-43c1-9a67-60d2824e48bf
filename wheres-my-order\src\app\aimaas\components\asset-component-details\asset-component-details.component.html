<section class="scrollsection"
         *ngIf="assetComponent && assetComponent.length > 0">
    <!-- <h3>Asset Component Details</h3> -->
    <div class="fields"
         *ngFor="let component of assetComponent">

        <div class="left-fields">
            <div class="field">
                <span><b> <u>{{ component.componentname }}</u></b></span>
            </div>
            <div class="field">
                <label><b>Description</b></label>
                <span>{{ component.description }}</span>
            </div>
            <div class="field">
                <label><b>Material</b></label>
                <span>{{ component.material }}</span>
            </div>
            <div class="field">
                <label><b>Outside Diameter</b></label>
                <span>{{ component.outsidediameter }}</span>
            </div>
            <div class="field"
                 *ngIf="component.headtype">
                <label><b>Head Type</b></label>
                <span>{{ component.headtype }}</span>
            </div>
            <div class="field">
                <label><b>Notes</b></label>
                <span>{{convertHtmlToText( component.notes) }}</span>
            </div>

        </div>
        <div class="right-fields">
            <div class="field">
                <label><b>Joint Efficiency</b></label>
                <span>{{ component.jointefficiency }}</span>
            </div>
            <div class="field">
                <label><b>Allowable Stress</b></label>
                <span>{{ component.allowablestress }}</span>
            </div>
            <div class="field">
                <label><b>Nominal Thickness</b></label>
                <span>{{ component.nominalthickness }}</span>
            </div>
            <div class="field">
                <label><b>Corrosion Allowance</b></label>
                <span>{{ convertE(component.corrosionallowance) }}</span>
            </div>
            <div class="field">
                <label><b>Nominal minus Corrosion Allowance</b></label>
                <span>{{ calculateLimitThickness(component) }}</span>
            </div>
            <div class="field">
                <label><b>Retirement Thickness</b></label>
                <span>{{ convertE(component.reassesedlimitthickness) }}</span>
            </div>
        </div>
    </div>
</section>
<section *ngIf="assetComponent.length == 0"
         style="padding-left: 500px;">
    <p> No Data</p>
</section>