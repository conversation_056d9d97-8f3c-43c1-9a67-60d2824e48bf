﻿using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Moq;
using NUnit.Framework;
using OrderTracking.API.Authorization.Handlers;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Tests.Authorization.Handlers
{
    [TestFixture]
    public class DistrictAssignmentAuthorizationHandlerTests
    {
        [SetUp]
        public void SetUp()
        {
            _mockService = new Mock<IUserProfilesService>(MockBehavior.Strict);
            _handler = new DistrictAssignmentAuthorizationHandler(_mockService.Object);
        }

        private Mock<IUserProfilesService> _mockService;
        private UserProfile _me;
        private UserProfile _newThem;
        private DistrictAssignmentAuthorizationHandler _handler;
        private AuthorizationHandlerContext _context;

        [Test]
        public async Task HandleAsync_MatchingDistrictIds_ContextHasSucceeded()
        {
            // Arrange
            _me = new UserProfile {Id = "<EMAIL>", DistrictIds = {"A", "B"}, Roles = {"WMO:Admin"}};
            _newThem = new UserProfile {Id = "<EMAIL>", DistrictIds = {"A", "B"}};
            var originalThem = new UserProfile {Id = "<EMAIL>"};
            _mockService.SetupGetAsyncMethod(_me).SetupGetAsyncMethod(originalThem);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_me, _newThem, new[]
            {
                new DistrictAssignmentRequirement()
            });

            // Act
            await _handler.HandleAsync(_context);

            // Assert
            Assert.That(_context.HasSucceeded);
        }

        [Test]
        public async Task HandleAsync_MismatchingDistrictIds_ContextHasFailed()
        {
            // Arrange
            _me = new UserProfile {Id = "<EMAIL>", DistrictIds = {"A"}};
            _newThem = new UserProfile {Id = "<EMAIL>", DistrictIds = {"B"}};
            var originalThem = new UserProfile {Id = "<EMAIL>"};
            _mockService.SetupGetAsyncMethod(_me).SetupGetAsyncMethod(originalThem);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_me, _newThem, new[]
            {
                new DistrictAssignmentRequirement()
            });

            // Act
            await _handler.HandleAsync(_context);

            // Assert
            Assert.That(_context.HasSucceeded, Is.False);
            Assert.That(_context.HasFailed);
        }

        [Test]
        public async Task HandleAsync_AppAdmin_ContextHasSucceeded()
        {
            // Arrange
            _me = new UserProfile {Id = "<EMAIL>", Roles = {"App:Admin"}};
            _newThem = new UserProfile {Id = "<EMAIL>", DistrictIds = {"A"}};
            var originalThem = new UserProfile {Id = "<EMAIL>"};
            _mockService.SetupGetAsyncMethod(_me).SetupGetAsyncMethod(originalThem);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_me, _newThem, new[]
            {
                new DistrictAssignmentRequirement()
            });

            // Act
            await _handler.HandleAsync(_context);

            // Assert
            Assert.That(_context.HasSucceeded);
        }

        [Test]
        public async Task HandleAsync_WMOAdminTryingToGiveMoreDistrictsToThemselves_ContextHasFailed()
        {
            // Arrange
            _me = new UserProfile {Id = "<EMAIL>", Roles = {"WMO:Admin", "WMO:EngineeringUser"}};
            _newThem = new UserProfile
                {Id = "<EMAIL>", Roles = {"WMO:Admin", "WMO:EngineeringUser"}, DistrictIds = {"A"}};
            var originalThem = _me;
            _mockService.SetupGetAsyncMethod(_me).SetupGetAsyncMethod(originalThem);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_me, _newThem, new[]
            {
                new DistrictAssignmentRequirement()
            });

            // Act
            await _handler.HandleAsync(_context);

            // Assert
            Assert.That(_context.HasFailed);
        }

        [Test]
        public async Task HandleAsync_WMOAssignerAndWMOAdmin_ContextHasSucceeded()
        {
            // Arrange
            _me = new UserProfile {Id = "<EMAIL>", Roles = {"WMO:Admin", "WMO:Assigner"}};
            _newThem = new UserProfile {Id = "<EMAIL>", DistrictIds = {"A"}};
            var originalThem = new UserProfile {Id = "<EMAIL>"};
            _mockService.SetupGetAsyncMethod(_me).SetupGetAsyncMethod(originalThem);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_me, _newThem, new[]
            {
                new DistrictAssignmentRequirement()
            });

            // Act
            await _handler.HandleAsync(_context);

            // Assert
            Assert.That(_context.HasSucceeded);
        }

        [Test]
        public async Task HandleAsync_WMOAssignerNotWMOAdmin_ContextHasFailed()
        {
            // Arrange
            _me = new UserProfile {Id = "<EMAIL>", Roles = {"WMO:Assigner"}};
            _newThem = new UserProfile {Id = "<EMAIL>", DistrictIds = {"A"}};
            var originalThem = new UserProfile {Id = "<EMAIL>"};
            _mockService.SetupGetAsyncMethod(_me).SetupGetAsyncMethod(originalThem);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_me, _newThem, new[]
            {
                new DistrictAssignmentRequirement()
            });

            // Act
            await _handler.HandleAsync(_context);

            // Assert
            Assert.That(_context.HasFailed);
        }

        [Test]
        public async Task HandleAsync_UserIdentityNameIsNull_Fails()
        {
            _me = new UserProfile {Id = "<EMAIL>", Roles = {"WMO:Assigner"}};
            _newThem = new UserProfile {Id = "<EMAIL>"};
            var originalThem = new UserProfile {Id = "<EMAIL>"};
            _mockService.SetupGetAsyncMethod(_me).SetupGetAsyncMethod(originalThem);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContextNotAuthenticated(_me, _newThem,
                new[] {new DistrictAssignmentRequirement()});

            await _handler.HandleAsync(_context);

            Assert.That(_context.HasFailed, Is.True);
        }

        [Test]
        public async Task HandleAsync_OriginalUserIsNotTeamEmployee_Fails()
        {
            _me = new UserProfile {Id = "<EMAIL>", Roles = {"WMO:Assigner"}};
            _newThem = new UserProfile {Id = "<EMAIL>", DistrictIds = {"SomeDistrict"}};
            var originalThem = new UserProfile {Id = "<EMAIL>"};
            _mockService.SetupGetAsyncMethod(_me).SetupGetAsyncMethod(originalThem);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_me, _newThem,
                new[] {new DistrictAssignmentRequirement()});

            await _handler.HandleAsync(_context);

            Assert.That(_context.HasFailed, Is.True);
        }
    }

    public static class MockUserProfileServiceExtensions
    {
        public static Mock<IUserProfilesService> SetupGetAsyncMethod(this Mock<IUserProfilesService> mockService,
            UserProfile userProfile)
        {
            mockService.Setup(s => s.GetAsync(userProfile.Id)).ReturnsAsync(userProfile);
            return mockService;
        }
    }
}