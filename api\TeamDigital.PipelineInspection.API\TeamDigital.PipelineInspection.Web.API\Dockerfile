FROM mcr.microsoft.com/dotnet/core/aspnet:3.0-buster-slim AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/core/sdk:3.0-buster AS build
WORKDIR /src
COPY ["PipelineInspection.Web.API.csproj", "PipelineInspection.Web.API/"]
RUN dotnet restore "PipelineInspection.Web.API/PipelineInspection.Web.API.csproj"
COPY . .
WORKDIR "/src/PipelineInspection.Web.API"
COPY . .
RUN dotnet build "PipelineInspection.Web.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "PipelineInspection.Web.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "TeamDigital.PipelineInspection.Web.API.dll"]