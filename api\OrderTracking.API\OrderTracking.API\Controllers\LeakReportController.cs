﻿using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using OrderTracking.API.Extensions.APM;
using OrderTracking.API.Interfaces;
using OrderTracking.API.Models.APM;
using OrderTracking.API.Models.LeakReporting;
using OrderTracking.API.Services;

namespace OrderTracking.API.Controllers
{
    /// <summary>
    ///     Controller for Leak Reporting resources
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class LeakReportController : ControllerBase
    {
        private readonly IJasperSoftService _jasperSoft;
        private readonly ILeakReportService _leakReport;
        private readonly IUserProfilesService _userProfiles;
        /// <summary>
        ///     Constructor for LeakReportController
        /// </summary>
        /// <param name="leakReport"></param>
        /// <param name="jasperSoft"></param>
        public LeakReportController(
            ILeakReportService leakReport, 
            IJasperSoftService jasperSoft,
            IUserProfilesService userProfiles)
        {
            _leakReport = leakReport;
            _jasperSoft = jasperSoft;
            _userProfiles = userProfiles;
        }

        [HttpGet]
        public async Task<IActionResult> GetLeakReports()
        {
            var email = CurrentUserEmail?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            var reports = await _leakReport.GetLeakReportsAsync(email);
            reports = reports.Where(report => report.businessUnitId.CurrentValue == user.SelectedBusinessUnit).ToList();
            return Ok(reports);
        }

        [HttpPost]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> CreateLeakReport(NewLeakReport newReport)
        {
            var email = CurrentUserEmail?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            if (user == null)
            {
                return Unauthorized();
            }
            var report = LeakReportExtensions.CreateNewLeakReport(newReport, User.Identity.Name);

            report.UpdateStatus(newReport.Status);
            report.businessUnitId.SetValue( user.SelectedBusinessUnit);
            await _leakReport.SaveLeakReportAsync(report, User.Identity.Name);

            return StatusCode(201, report);
        }


        [HttpPut("{id}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateLeakReport(string id, LeakReportUpdate update)
        {
            var email = CurrentUserEmail?.ToLower();

            var report = await _leakReport.GetLeakReportAsync(id, email);

            if (report == null) return NotFound();

            report.UpdateStatus(update.Status);

            await report.SavePendingChanges(User.Identity.Name);

            return NoContent();
        }


        [HttpPut("LeakReportInfo/{id}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateLeakReportInfo(string id, LeakReportInfo update)
        {
            var email = CurrentUserEmail?.ToLower();
            
            var report = await _leakReport.GetLeakReportAsync(id, email);

            if (report == null) return NotFound();

            report.UpdateLeakReportInfo(update);

            await _leakReport.SaveLeakReportAsync(report, User.Identity.Name);

            return NoContent();
        }


        [HttpPut("LeakReportWorkDetails/{id}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateLeakReportWorkDetails(string id, LeakReportWorkDetails update)
        {
            var email = CurrentUserEmail?.ToLower();
            
            var report = await _leakReport.GetLeakReportAsync(id, email);

            if (report == null) return NotFound();

            report.UpdateLeakReportWorkDetails(update);

            await _leakReport.SaveLeakReportAsync(report, User.Identity.Name);

            return NoContent();
        }


        [HttpPut("LeakReportPhotoGroup/{id}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateLeakReportPhotoGroup(string id, LeakReportPhotoGroupTransport update)
        {
            var email = CurrentUserEmail?.ToLower();

            var report = await _leakReport.GetLeakReportAsync(update.LeakReportID, email);

            if (report == null) return NotFound();

            var group = _leakReport.GetLeakReportPhotoGroup(report, update.GroupDatabaseID);

            if (group == null) return NotFound();

            group.UpdatePhotoGroup(update);

            await _leakReport.SaveLeakReportAsync(report, User.Identity.Name);

            return NoContent();
        }


        [HttpDelete("LeakReportPhoto/{id}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> DeletePhotoFromPhotoGroup(string id, LeakReportPhotoDeleteTransport ids)
        {
            var email = CurrentUserEmail?.ToLower();

            var report = await _leakReport.GetLeakReportAsync(ids.ReportID, email);

            if (report == null) return NotFound();

            var group = _leakReport.GetLeakReportPhotoGroup(report, ids.PhotoGroupID);

            if (group == null) return NotFound();

            var photo = group.photos.Photos.FirstOrDefault(p => p.DatabaseId == id);

            group.photos.RemovePhoto(photo);

            await _leakReport.SaveLeakReportAsync(report, User.Identity.Name);

            return Ok();
        }

        [HttpPut("LeakReportingPhotoDescription")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateLeakReportingPhotoDescription(LeakReportingPhotoDescriptionUpdate update)
        {
            var email = CurrentUserEmail?.ToLower();

            var report = await _leakReport.GetLeakReportAsync(update.ReportID, email);

            if (report == null) return NotFound();

            var photo = report.GetPhotoMediaEntry(update);

            if (photo == null) return NotFound();

            photo.Description.SetValue(update.Description);

            await _leakReport.SaveLeakReportAsync(report, User.Identity.Name);

            return NoContent();
        }


        [HttpDelete("LeakReportingPhoto")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> DeleteLeakReportPhoto(LeakReportingPhotoDelete photoDelete)
        {
            var email = CurrentUserEmail?.ToLower();

            var report = await _leakReport.GetLeakReportAsync(photoDelete.ReportID, email);

            if (report == null) return NotFound();

            var attribute = report.GetPhotoAttributeBase(photoDelete);

            if (attribute == null) return NotFound();

            attribute.RemovePhoto(attribute.Photos.First(p => p.DatabaseId == photoDelete.PhotoDatabaseID));

            await _leakReport.SaveLeakReportAsync(report, User.Identity.Name);

            return Ok();
        }


        [HttpPost("LeakReportingReport/{reportId}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> GenerateLeakReportingReport(string reportId)
        {
            var email = CurrentUserEmail?.ToLower();

            var report = await _leakReport.GetLeakReportAsync(reportId, email);

            var response = await _jasperSoft.GenerateLeakReportingReport(report);

            //// Send file if successful response from JasperSoft
            //if (response.IsSuccessStatusCode)
            //{
            //    var stream = await response.Content.ReadAsStreamAsync();
            //    return File(stream, "application/pdf");
            //}

            // Return response from JasperSoft if not successful
            //return StatusCode((int)response.StatusCode, response.Content);
            return Ok(response);
        }
        /// <summary>
        ///     Get the email from the current request's user identity.
        /// </summary>
        public string CurrentUserEmail => User?.Identity?.Name;
    }
}