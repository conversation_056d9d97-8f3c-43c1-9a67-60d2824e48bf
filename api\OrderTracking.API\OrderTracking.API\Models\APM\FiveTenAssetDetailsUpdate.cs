﻿using Newtonsoft.Json;
using System;

namespace OrderTracking.API.Models.APM
{
    public class ValueChangedTransport<T>
    {
        [JsonProperty(PropertyName = "value")]
        public T Value { get; set; }
    }

    public class ShellCourseUpdate
    {
        [JsonProperty(PropertyName = "databaseId")]
        public string DatebaseId { get; set; }

        [JsonProperty(PropertyName = "number")]
        public ValueChangedTransport<string?> Number { get; set; }

        [JsonProperty(PropertyName = "materialSpecAndGrade")]
        public ValueChangedTransport<string> MaterialSpecAndGrade { get; set; }

        [JsonProperty(PropertyName = "nominalThickness")]
        public ValueChangedTransport<double?> NominalThickness { get; set; }

        [JsonProperty(PropertyName = "allowableStressAtTemp")]
        public ValueChangedTransport<double?> AllowableStressAtTemp { get; set; }

        [JsonProperty(PropertyName = "corrosionAllowance")]
        public ValueChangedTransport<double?> CorrosionAllowance { get; set; }

        [JsonProperty(PropertyName = "lengthOrHeight")]
        public ValueChangedTransport<double?> LengthOrHeight { get; set; }

        [JsonProperty(PropertyName = "jointEfficiency")]
        public ValueChangedTransport<double?> JointEfficiency { get; set; }
    }

    public class HeadUpdate
    {
        [JsonProperty(PropertyName = "databaseId")]
        public string DatebaseId { get; set; }

        [JsonProperty(PropertyName = "number")]
        public ValueChangedTransport<string?> Number { get; set; }

        [JsonProperty(PropertyName = "location")]
        public ValueChangedTransport<string> Location { get; set; }

        [JsonProperty(PropertyName = "geometry")]
        public ValueChangedTransport<string> Geometry { get; set; }

        [JsonProperty(PropertyName = "geometryComments")]
        public ValueChangedTransport<string> GeometryComments { get; set; }

        [JsonProperty(PropertyName = "materialSpecAndGrade")]
        public ValueChangedTransport<string> MaterialSpecAndGrade { get; set; }

        [JsonProperty(PropertyName = "allowableStressAtTemp")]
        public ValueChangedTransport<double?> AllowableStressAtTemp { get; set; }

        [JsonProperty(PropertyName = "nominalThickness")]
        public ValueChangedTransport<double?> NominalThickness { get; set; }

        [JsonProperty(PropertyName = "corrosionAllowance")]
        public ValueChangedTransport<double?> CorrosionAllowance { get; set; }

        [JsonProperty(PropertyName = "jointEfficiency")]
        public ValueChangedTransport<double?> JointEfficiency { get; set; }
    }

    public class NozzleUpdate
    {
        [JsonProperty(PropertyName = "databaseId")]
        public string DatebaseId { get; set; }

        [JsonProperty(PropertyName = "number")]
        public ValueChangedTransport<string?> Number { get; set; }

        [JsonProperty(PropertyName = "type")]
        public ValueChangedTransport<string> Type { get; set; }

        [JsonProperty(PropertyName = "materialSpecAndGrade")]
        public ValueChangedTransport<string> MaterialSpecAndGrade { get; set; }

        [JsonProperty(PropertyName = "pipeSize")]
        public ValueChangedTransport<string[]> PipeSize { get; set; }

        [JsonProperty(PropertyName = "pipeSchedule")]
        public ValueChangedTransport<string[]> PipeSchedule { get; set; }

        [JsonProperty(PropertyName = "flangeRating")]
        public ValueChangedTransport<string[]> FlangeRating { get; set; }

        [JsonProperty(PropertyName = "reinforcementPadType")]
        public ValueChangedTransport<string> ReinforcementPadType { get; set; }

        [JsonProperty(PropertyName = "reinforcementPadDimensions")]
        public ValueChangedTransport<string> ReinforcementPadDimensions { get; set; }
         
        [JsonProperty(PropertyName = "reinforcementPadThickness")]
        public ValueChangedTransport<double?> ReinforcementPadThickness { get; set; }
    }


    public class ChannelUpdate
    {
        [JsonProperty(PropertyName = "databaseId")]
        public string DatebaseId { get; set; }

        [JsonProperty(PropertyName = "number")]
        public ValueChangedTransport<string?> Number { get; set; }

        [JsonProperty(PropertyName = "location")]
        public ValueChangedTransport<string> Location { get; set; }

        [JsonProperty(PropertyName = "materialSpecAndGrade")]
        public ValueChangedTransport<string> MaterialSpecAndGrade { get; set; }

        [JsonProperty(PropertyName = "allowableStressAtTemp")]
        public ValueChangedTransport<double?> AllowableStressAtTemp { get; set; }

        [JsonProperty(PropertyName = "nominalThickness")]
        public ValueChangedTransport<double?> NominalThickness { get; set; }

        [JsonProperty(PropertyName = "corrosionAllowance")]
        public ValueChangedTransport<double?> CorrosionAllowance { get; set; }

        [JsonProperty(PropertyName = "length")]
        public ValueChangedTransport<double?> Length { get; set; }

        [JsonProperty(PropertyName = "jointEfficiency")]
        public ValueChangedTransport<double?> JointEfficiency { get; set; }
    }


    public class InspectionOpeningUpdate
    {
        [JsonProperty(PropertyName = "type")]
        public ValueChangedTransport<string> Type { get; set; }

        [JsonProperty(PropertyName = "number")]
        public ValueChangedTransport<int?> Number { get; set; }

        [JsonProperty(PropertyName = "size")]
        public ValueChangedTransport<string> Size { get; set; }

        [JsonProperty(PropertyName = "displayName")]
        public ValueChangedTransport<string> DisplayName { get; set; }

        [JsonProperty(PropertyName = "databaseId")]
        public string DatabaseId { get; set; }
    }

    public class RepairUpdate
    {
        [JsonProperty(PropertyName = "databaseId")]
        public string DatebaseId { get; set; }

        [JsonProperty(PropertyName = "dateRepairedOrAltered")]
        public ValueChangedTransport<string> DateRepairedOrAltered { get; set; }

        [JsonProperty(PropertyName = "repairAlterationOrganization")]
        public ValueChangedTransport<string> RepairAlterationOrganization { get; set; }

        [JsonProperty(PropertyName = "purposeOfRepairAlteration")]
        public ValueChangedTransport<string> PurposeOfRepairAlteration { get; set; }

        [JsonProperty(PropertyName = "isNBFormR1Available")]
        public ValueChangedTransport<bool?> IsNBFormR1Available { get; set; }

        [JsonProperty(PropertyName = "isNBFormR2Available")]
        public ValueChangedTransport<bool?> IsNBFormR2Available { get; set; }

        [JsonProperty(PropertyName = "nbRCertificateNumber")]
        public ValueChangedTransport<string> NbRCertificateNumber { get; set; }
    }

    public class FiveTenAssetDetailsUpdate
    {
        [JsonProperty(PropertyName = "projectId")]
        public string? ProjectId { get; set; }

        [JsonProperty(PropertyName = "assetId")]
        public string? AssetId { get; set; }

        [JsonProperty(PropertyName = "workOrderId")]
        public string WorkOrderId { get; set; }

        [JsonProperty(PropertyName = "identificationName")]
        public ValueChangedTransport<string> IdentificationName { get; set; }

        [JsonProperty(PropertyName = "identificationNumber")]
        public ValueChangedTransport<string> IdentificationNumber { get; set; }

        [JsonProperty(PropertyName = "assetType")]
        public ValueChangedTransport<string> AssetType { get; set; }

        [JsonProperty(PropertyName = "equipmentDescription")]
        public ValueChangedTransport<string> EquipmentDescription { get; set; }

        [JsonProperty(PropertyName = "lastKnownInspectionDate")]
        public ValueChangedTransport<DateTime?> LastKnownInspectionDate { get; set; }

        [JsonProperty(PropertyName = "location")]
        public ValueChangedTransport<string> Location { get; set; }

        [JsonProperty(PropertyName = "gisLocationLat")]
        public ValueChangedTransport<double?> GisLocationLat { get; set; }

        [JsonProperty(PropertyName = "gisLocationLong")]
        public ValueChangedTransport<double?> GisLocationLong { get; set; }

        [JsonProperty(PropertyName = "designCode")]
        public ValueChangedTransport<string> DesignCode { get; set; }

        [JsonProperty(PropertyName = "designYear")]
        public ValueChangedTransport<string> DesignYear { get; set; }

        [JsonProperty(PropertyName = "designAddendum")]
        public ValueChangedTransport<string> DesignAddendum { get; set; }

        [JsonProperty(PropertyName = "inspectionCode")]
        public ValueChangedTransport<string> InspectionCode { get; set; }

        [JsonProperty(PropertyName = "inspectionYear")]
        public ValueChangedTransport<string> InspectionYear { get; set; }

        [JsonProperty(PropertyName = "inspectionAddendum")]
        public ValueChangedTransport<string> InspectionAddendum { get; set; }

        [JsonProperty(PropertyName = "dataPlateAttached")]
        public ValueChangedTransport<bool?> DataPlateAttached { get; set; }

        [JsonProperty(PropertyName = "dataPlateLegible")]
        public ValueChangedTransport<bool?> DataPlateLegible { get; set; }

        [JsonProperty(PropertyName = "manufacturerName")]
        public ValueChangedTransport<string> ManufacturerName { get; set; }

        [JsonProperty(PropertyName = "manufacturerDate")]
        public ValueChangedTransport<DateTime?> ManufacturerDate { get; set; }

        [JsonProperty(PropertyName = "manufacturerSerialNumber")]
        public ValueChangedTransport<string> ManufacturerSerialNumber { get; set; }

        [JsonProperty(PropertyName = "nationalBoardNumber")]
        public ValueChangedTransport<string> NationalBoardNumber { get; set; }

        [JsonProperty(PropertyName = "rtNumber")]
        public ValueChangedTransport<string> RtNumber { get; set; }

        [JsonProperty(PropertyName = "serviceProductContents")]
        public ValueChangedTransport<string> ServiceProductContents { get; set; }

        [JsonProperty(PropertyName = "specificGravity")]
        public ValueChangedTransport<double?> SpecificGravity { get; set; }

        [JsonProperty(PropertyName = "orientation")]
        public ValueChangedTransport<string> Orientation { get; set; }

        [JsonProperty(PropertyName = "rt")]
        public ValueChangedTransport<string> Rt { get; set; }

        [JsonProperty(PropertyName = "installationDate")]
        public ValueChangedTransport<DateTime?> InstallationDate { get; set; }

        [JsonProperty(PropertyName = "inServiceDate")]
        public ValueChangedTransport<DateTime?> InServiceDate { get; set; }

        [JsonProperty(PropertyName = "pIDNumber")]
        public ValueChangedTransport<string> PIDNumber { get; set; }

        [JsonProperty(PropertyName = "designDrawingNumber")]
        public ValueChangedTransport<string> DesignDrawingNumber { get; set; }

        [JsonProperty(PropertyName = "lowestFlangeRating")]
        public ValueChangedTransport<string> LowestFlangeRating { get; set; }

        [JsonProperty(PropertyName = "hydroTestPressure")]
        public ValueChangedTransport<int?> HydroTestPressure { get; set; }

        [JsonProperty(PropertyName = "typeOfConstruction")]
        public ValueChangedTransport<string> TypeOfConstruction { get; set; }

        [JsonProperty(PropertyName = "pwht")]
        public ValueChangedTransport<string> PWHT { get; set; }

        [JsonProperty(PropertyName = "ratingChanged")]
        public ValueChangedTransport<string> RatingChanged { get; set; }

        [JsonProperty(PropertyName = "isFiredPressureVessel")]
        public ValueChangedTransport<string> IsFiredPressureVessel { get; set; }

        [JsonProperty(PropertyName = "hasRepairOrAlterationPlate")]
        public ValueChangedTransport<bool?> HasRepairOrAlterationPlate { get; set; }

        [JsonProperty(PropertyName = "repairOrAlterationPlateLegible")]
        public ValueChangedTransport<bool?> RepairOrAlterationPlateLegible { get; set; }

        [JsonProperty(PropertyName = "designConditionsOperatingTemp")]
        public ValueChangedTransport<double?> DesignConditionsOperatingTemp { get; set; }

        [JsonProperty(PropertyName = "shellSideDesignMAWP")]
        public ValueChangedTransport<double?> ShellSideDesignMAWP { get; set; }

        [JsonProperty(PropertyName = "shellSideDesignTemp")]
        public ValueChangedTransport<double?> ShellSideDesignTemp { get; set; }

        [JsonProperty(PropertyName = "shellSideOperatingTemp")]
        public ValueChangedTransport<int?> ShellSideOperatingTemp { get; set; }

        [JsonProperty(PropertyName = "shellSideOperatingPressure")]
        public ValueChangedTransport<double?> ShellSideOperatingPressure { get; set; }

        [JsonProperty(PropertyName = "shellSideSetPressure")]
        public ValueChangedTransport<double?> ShellSideSetPressure { get; set; }

        [JsonProperty(PropertyName = "tubeSideDesignMAWP")]
        public ValueChangedTransport<double?> TubeSideDesignMAWP { get; set; }

        [JsonProperty(PropertyName = "tubeSideDesignTemp")]
        public ValueChangedTransport<double?> TubeSideDesignTemp { get; set; }

        [JsonProperty(PropertyName = "tubeSideOperatingTemp")]
        public ValueChangedTransport<int?> TubeSideOperatingTemp { get; set; }

        [JsonProperty(PropertyName = "tubeSideOperatingPressure")]
        public ValueChangedTransport<double?> TubeSideOperatingPressure { get; set; }

        [JsonProperty(PropertyName = "tubeSideSetPressure")]
        public ValueChangedTransport<double?> TubeSideSetPressure { get; set; }

        [JsonProperty(PropertyName = "diameterMeasurement")]
        public ValueChangedTransport<string> DiameterMeasurement { get; set; }

        [JsonProperty(PropertyName = "diameter")]
        public ValueChangedTransport<double?> Diameter { get; set; }

        [JsonProperty(PropertyName = "hasMultipleDiameters")]
        public ValueChangedTransport<bool?> HasMultipleDiameters { get; set; }

        [JsonProperty(PropertyName = "diameterComments")]
        public ValueChangedTransport<string> DiameterComments { get; set; }

        [JsonProperty(PropertyName = "overallLengthHeight")]
        public ValueChangedTransport<double?> OverallLengthHeight { get; set; }

        [JsonProperty(PropertyName = "hasToriconicalSections")]
        public ValueChangedTransport<bool?> HasToriconicalSections { get; set; }

        [JsonProperty(PropertyName = "operationStatus")]
        public ValueChangedTransport<string> OperationStatus { get; set; }

        [JsonProperty(PropertyName = "shellCourses")]
        public ShellCourseUpdate[] ShellCourses { get; set; }

        [JsonProperty(PropertyName = "channels")]
        public ChannelUpdate[] Channels { get; set; }

        [JsonProperty(PropertyName = "heads")]
        public HeadUpdate[] Heads { get; set; }

        [JsonProperty(PropertyName = "nozzles")]
        public NozzleUpdate[] Nozzles { get; set; }

        [JsonProperty(PropertyName = "inspectionOpenings")]
        public InspectionOpeningUpdate[] InspectionOpenings { get; set; }

        [JsonProperty(PropertyName = "repairs")]
        public RepairUpdate[] Repairs { get; set; }
    }
}
