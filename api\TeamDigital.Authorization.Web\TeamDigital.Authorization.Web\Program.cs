using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;

namespace TeamDigital.Authorization.Web
{
    /// <summary>
    ///     Not being used
    /// </summary>
    // ReSharper disable once ClassNeverInstantiated.Global
#pragma warning disable CA1052 // Static holder types should be Static or NotInheritable
    public class Program
#pragma warning restore CA1052 // Static holder types should be Static or NotInheritable
    {
        /// <summary>
        ///     Not being used
        /// </summary>
        /// <param name="args"></param>
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        /// <summary>
        ///     Not being used
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        // ReSharper disable once MemberCanBePrivate.Global
        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                });
    }
}
