﻿using OpenQA.Selenium;

namespace ClientPortal.Pages
{
    public class LoginPage : Utils.SetupTeardown
    {
        public IWebElement LoginButton => _driver.FindElement(By.CssSelector(".dx-button-content"));
        public IWebElement Username => _driver.FindElement(By.Id("i0116"));
        public IWebElement Nextbutton => _driver.FindElement(By.Id("idSIButton9"));
        public IWebElement Password => _driver.FindElement(By.Id("i0118"));
        public IWebElement SignIn => _driver.FindElement(By.Id("idSIButton9"));
        public IWebElement ConfirmationYes => _driver.FindElement(By.Id("idSIButton9"));
    }
}
