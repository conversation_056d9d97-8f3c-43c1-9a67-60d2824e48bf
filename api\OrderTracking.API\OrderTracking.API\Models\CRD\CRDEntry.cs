﻿//using CsvHelper.Configuration.Attributes;
//using Newtonsoft.Json;
//using System;
//using System.Collections.Generic;
//using System.ComponentModel.DataAnnotations.Schema;
//using System.Linq;
//using System.Threading.Tasks;

//namespace OrderTracking.API.Models.CRD
//{
//    [Table("CRDEntries")]
//    public class CRDEntry
//    {
//        [System.ComponentModel.DataAnnotations.Key]
//        [Ignore]
//        public int ID { get; set; }
//        [Name("Title")] public string Title { get; set; }
//        [Name("New No.")] public string NewNo { get; set; }
//        [JsonProperty("crdNo")]
//        [Name("CRD No.")] public string CRDNo { get; set; }
//        [JsonProperty("ccNo")]
//        [Name("CC No.")] public string CCNo { get; set; }
//        [Name("FORMULA")] public string Formula { get; set; }
//        [Name("Physical Properties")] public string PhysicalProperties { get; set; }
//        [Name("Lethal Service/Reactive")] public string LethalServiceReactive { get; set; }
//        [JsonProperty("teamCritical")]
//        [Name("TEAM CRITICAL")] public string TEAMCritical { get; set; }
//        [Name("Carcinogen")] public string Carcinogen { get; set; }
//        [Name("Medical Surveillance")] public string MedicalSurveillance { get; set; }
//        [JsonProperty("psm")]
//        [Name("PSM")] public string PSM { get; set; }
//        [Name("Flash Point ºC")] public string FlashPoint { get; set; }
//        [JsonProperty("ait")]
//        [Name("AIT ºC")] public string AIT { get; set; }
//        [Name("Flamability Note")] public string FlamabilityNote { get; set; }
//        [JsonProperty("bp")]
//        [Name("BP ºC")] public string BP { get; set; }
//        [JsonProperty("vd")]
//        [Name("VD")] public string VD { get; set; }
//        [Name("Frz. Temp ºC")] public string FrzTemp { get; set; }
//        [Name("8 hr TWA")] public string EightHRTWA { get; set; }
//        [JsonProperty("stel")]
//        [Name("STEL")] public string STEL { get; set; }
//        [JsonProperty("idlh")]
//        [Name("IDLH")] public string IDLH { get; set; }
//        [Name("Purge Media")] public string PurgeMedia { get; set; }
//        [Name("Vapour is heavier than air?")] public string VapourIsHeavierThanAir { get; set; }
//        [Name("Non-Spark Tooling Required")] public string NonSparkToolingRequired { get; set; }
//        [Name("Risk of explosion through shock/friction?")] public string RiskOfExplosionThroughShockFriction { get; set; }
//        [JsonProperty("sccCarbonSteelFasteners")]
//        [Name("SCC Carbon Steel Fasteners")] public string SCCCarbonSteelFasteners { get; set; }
//        [JsonProperty("sccStainlessSteelFasteners")]
//        [Name("SCC Stainless Steel Fasteners")] public string SCCStainlessSteelFasteners { get; set; }
//        [JsonProperty("pwhtRecommended")]
//        [Name("PWHT Recommended")] public string PWHTRecommended { get; set; }
//        [Name("Grounding Cable Required")] public string GroundingCableRequired { get; set; }
//        [Name("Purge")] public string Purge { get; set; }
//        [Name("Oxygen/Chlorine procedures")] public string OxygenChlorineProcedures { get; set; }
//        [Name("Reacts with steam/water/moisture")] public string ReactsWithSteamWaterMoisture { get; set; }
//        [Name("Special Procedures")] public string SpecialProcedures { get; set; }
//        [Name("Toxicity")] public string Toxicity { get; set; }
//        [Name("First Aid")] public string FirstAid { get; set; }
//        [Name("Butyl Acid Suit")] public string ButylAcidSuit { get; set; }
//        [JsonProperty("arButylGloves")]
//        [Name("AR Butyl Gloves")] public string ARButylGloves { get; set; }
//        [JsonProperty("crNitrileGloves")]
//        [Name("CR Nitrile Gloves")] public string CRNitrileGloves { get; set; }
//        [JsonProperty("thirdManRS")]
//        [Name("3rd Man RS")] public string ThirdManRS { get; set; }
//        [JsonProperty("aprFilters")]
//        [Name("APR Filters")] public string APRFilters { get; set; }
//        [JsonProperty("casNo")]
//        [Name("CAS No.")] public string CASNo { get; set; }
//        [JsonProperty("msdsNo")]
//        [Name("MSDS No.")] public string MSDSNo { get; set; }
//        [Name("No Exposure")] public string NoExposure { get; set; }
//        [Name("DRIP OR SEEP")] public string DripOrSeep { get; set; }
//        [Name("DRIP OR SEEP NOTES")] public string DripOrSeepNotes { get; set; }
//        [Name("MEDIUM LEAK")] public string MediumLeak { get; set; }
//        [Name("MEDIUM LEAK NOTES")] public string MediumLeakNotes { get; set; }
//        [Name("SEVERE LEAK")] public string SevereLeak { get; set; }
//        [Name("SEVERE LEAK NOTES")] public string SevereLeakNotes { get; set; }
//        [Name("1B")] public string OneB { get; set; }
//        [Name("1/2A")] public string OneHalfA { get; set; }
//        [Name("2A")] public string TwoA { get; set; }
//        [Name("2AS")] public string TwoAS { get; set; }
//        [Name("2B")] public string TwoB { get; set; }
//        [Name("2D")] public string TwoD { get; set; }
//        [Name("2F")] public string TwoF { get; set; }
//        [Name("2G")] public string TwoG { get; set; }
//        [Name("3A")] public string ThreeA { get; set; }
//        [Name("3C")] public string ThreeC { get; set; }
//        [Name("4A")] public string FourA { get; set; }
//        [Name("5A")] public string FiveA { get; set; }
//        [Name("5B")] public string FiveB { get; set; }
//        [Name("5D")] public string FiveD { get; set; }
//        [Name("5E")] public string FiveE { get; set; }
//        [Name("5C")] public string FiveC { get; set; }
//        [Name("5O")] public string FiveO { get; set; }
//        [Name("5X")] public string FiveX { get; set; }
//        [Name("6B")] public string SixB { get; set; }
//        [Name("6C")] public string SixC { get; set; }
//        [Name("7C")] public string SevenC { get; set; }
//        [Name("9A")] public string NineA { get; set; }
//        [Name("9C")] public string NineC { get; set; }
//        [Name("11A")] public string ElevenA { get; set; }
//        [Name("13A")] public string ThirteenA { get; set; }
//        [Name("13B")] public string ThirteenB { get; set; }
//        [Name("14A")] public string FourteenA { get; set; }
//        [Name("14B")] public string FourteenB { get; set; }
//        [Name("14C")] public string FourteenC { get; set; }
//        [Name("14D")] public string FourteenD { get; set; }
//        [Name("14E")] public string FourteenE { get; set; }
//        [Name("10B")] public string TenB { get; set; }
//        [Name("10C")] public string TenC { get; set; }
//        [Name("10D")] public string TenD { get; set; }
//        [Name("10E")] public string TenE { get; set; }
//        [Name("12F")] public string TwelveF { get; set; }
//        [Name("12S")] public string TwelveS { get; set; }
//        [Name("12A")] public string TwelveA { get; set; }
//        [Name("12L")] public string TwelveL { get; set; }
//        [Name("12W")] public string TwelveW { get; set; }
//        [Name("15F")] public string FifteenF { get; set; }
//        [Name("15S")] public string FifteenS { get; set; }
//        [Name("59D")] public string FiftyNineD { get; set; }
//        [Name("1X")] public string OneX { get; set; }
//        [Name("2X")] public string TwoX { get; set; }
//        [Name("2XH")] public string TwoXH { get; set; }
//        [Name("#4")] public string NumberFour { get; set; }
//        [Name("#6")] public string NumberSix { get; set; }
//        [Name("6HT")] public string SixHT { get; set; }
//        [Name("#10")] public string NumberTen { get; set; }
//        [Name("#11")] public string NumberEleven { get; set; }
//        [Name("155K")] public string OneHundredFiftyFiveK { get; set; }
//        [Name("16X")] public string SixteenX { get; set; }
//        [Name("18X")] public string EighteenX { get; set; }
//        [Name("19X")] public string NineteenX { get; set; }
//        [JsonProperty("vpax")]
//        [Name("VPAX")] public string VPAX { get; set; }
//        [JsonProperty("vpb")]
//        [Name("VPB")] public string VPB { get; set; }
//        [JsonProperty("vper")]
//        [Name("VPER")] public string VPER { get; set; }
//        [Name("VPEPX")] public string VPEPX { get; set; }
//        [JsonProperty("vpf")]
//        [Name("VPF")] public string VPF { get; set; }
//        [JsonProperty("vpgx")]
//        [Name("VPGX")] public string VPGX { get; set; }
//        [JsonProperty("vplx")]
//        [Name("VPLX")] public string VPLX { get; set; }
//        [JsonProperty("vpox")]
//        [Name("VPOX")] public string VPOX { get; set; }
//        [Name("BULK X")] public string BulkX { get; set; }
//        [Name("F-10")] public string FTen { get; set; }
//        [Name("F-11F")] public string FElevenF { get; set; }
//        [Name("F-14")] public string FFourteen { get; set; }
//        [Name("G-FIBER")] public string GFiber { get; set; }
//        [JsonProperty("dbTwentyTwo")]
//        [Name("DB22")] public string DBTwentyTwo { get; set; }
//        [JsonProperty("dbTwentyThree")]
//        [Name("DB23")] public string DBTwentyThree { get; set; }
//        [JsonProperty("esOne")]
//        [Name("ES-1")] public string ESOne { get; set; }
//        [JsonProperty("tcFour")]
//        [Name("TC-4")] public string TCFour { get; set; }
//        [Name("X-36")] public string XThirtySix { get; set; }
//        [JsonProperty("rfThreeHundred")]
//        [Name("RF-300")] public string RFThreeHundred { get; set; }
//        [JsonProperty("buna")]
//        [Name("BUNA")] public string BUNA { get; set; }
//        [JsonProperty("hnbr")]
//        [Name("HNBR")] public string HNBR { get; set; }
//        [JsonProperty("epdm")]
//        [Name("EPDM")] public string EPDM { get; set; }
//        [JsonProperty("viton")]
//        [Name("VITON")] public string VITON { get; set; }
//        [JsonProperty("aflas")]
//        [Name("AFLAS")] public string AFLAS { get; set; }
//        [JsonProperty("neoprene")]
//        [Name("NEOPRENE")] public string NEOPRENE { get; set; }
//        [JsonProperty("silicone")]
//        [Name("SILICONE")] public string SILICONE { get; set; }
//        [JsonProperty("fcxSixHundredFour")]
//        [Name("FCX-604")] public string FCXSixHundredFour { get; set; }
//        [Name("11S")] public string ElevenS { get; set; }
//        [Name("CARBON STEEL")] public string CarbonSteel { get; set; }
//        [Name("STAINLESS STEEL")] public string StainlessSteel { get; set; }
//        [Name("CHROME MOLY")] public string ChromeMoly { get; set; }
//        [JsonProperty("ptfe")]
//        [Name("PTFE")] public string PTFE { get; set; }
//        [Name("CARBON GRAPHITE")] public string CarbonGraphite { get; set; }
//        [JsonProperty("aramid")]
//        [Name("ARAMID")] public string ARAMID { get; set; }
//        [JsonProperty("impres")]
//        [Name("IMPRES")] public string IMPRES { get; set; }
//        [Name("BRASS WIRE")] public string BrassWire { get; set; }
//        [Name("COPPER TUBING")] public string CopperTubing { get; set; }
//        [Name("STAINLESS TUBING")] public string StainlessTubing { get; set; }
//    }
//}
