﻿using System;
using System.Linq;
using System.Threading.Tasks;
using APMWebDataInterface.ExampleDataModel;
using OrderTracking.API.Extensions.APM;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Services.APM
{
    public class ProjectService : IProjectService
    {
        private readonly APM_WebDataInterface _apm;

        public ProjectService(APM_WebDataInterface apm)
        {
            _apm = apm;
        }

        /// <summary>
        ///     TODO: This is where we should include the logic for handling the query parameters and filtering what we return
        ///     here.
        /// </summary>
        /// <returns></returns>
        public Project[] Get(string email) => _apm.GetProjects(email);

        /// <summary>
        ///     Get projects, by id, from the list of project ids provided
        /// </summary>
        /// <param name="email"></param>
        /// <param name="projectIds"></param>
        /// <returns></returns>
        public Project[] Get(string email, params string[] projectIds) => _apm.GetProjects(projectIds, email);

        /// <summary>
        ///     Gets a single project by ID
        /// </summary>
        /// <param name="id"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public Project Get(string id, string email)
        {
            var projects = _apm.GetProjects(new[] {id}, email);
            if (projects == null || projects.Length == 0) return null;
            return projects[0];
        }


        /// <summary>
        ///     Creates a new project from a <see cref="NewProjectTransportObject" />
        /// </summary>
        /// <param name="newProject"></param>
        /// <param name="email"></param>
        /// <param name="businessUnitId"></param>
        /// <returns></returns>
        public async Task<Project> Create(NewProjectTransportObject newProject, string email, string businessUnitId)
        {
            // Check for null LocationId
            if (newProject.LocationId == null)
                throw new InvalidOperationException("Location is required to create a project");

            // Check for valid LocationId
            var locations = await _apm.GetLocations(email);
            if (!locations.Select(l => l.id).Contains(newProject.LocationId))
                throw new InvalidOperationException(
                    "Location Id must be a valid id of a location existing in the database");

            if (locations.Single(l => l.id == newProject.LocationId).businessUnitId.CurrentValue != businessUnitId)
                throw new InvalidOperationException(
                    "Location must have the same business unit as the project being created.");
            
            var project = new Project(newProject.LocationId);
            project.Update(newProject);
            project.businessUnitId.SetValue(businessUnitId);
            await project.SavePendingChanges(email);
            return project;
        }

        /// <summary>
        ///     Adds the asset id to the project's asset ids (creating an association)
        /// </summary>
        /// <param name="project"></param>
        /// <param name="assetId"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task AddAssetToProject(Project project, string assetId, string email)
        {
            project.assetIds.AddValue(assetId);
            await project.SavePendingChanges(email);
        }

        /// <summary>
        ///     Updates a project from a <see cref="WorkOrderTransportObject" />
        /// </summary>
        /// <param name="project"></param>
        /// <param name="workOrderUpdate"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task Update(Project project, WorkOrderTransportObject workOrderUpdate, string email)
        {
            project.Update(workOrderUpdate);
            await project.SavePendingChanges(email);
        }

        /// <summary>
        ///     Updates a project from a <see cref="ProjectTransportObject"/>
        /// </summary>
        /// <param name="project"></param>
        /// <param name="projectUpdate"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task Update(Project project, ProjectTransportObject projectUpdate, string email)
        {
            project.Update(projectUpdate);
            await project.SavePendingChanges(email);
        }

        /// <summary>
        ///     Updates a contact object on a project from a <see cref="ClientContact"/>
        /// </summary>
        /// <param name="project"></param>
        /// <param name="contact"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task<Contact> UpdateContact(Project project, ClientContact contact, string email)
        {
            var projectContact =
                project.clientDetails.contacts.CurrentEntries.FirstOrDefault(c => c.DatabaseId == contact.DatabaseId);
            if (projectContact == null) throw new ProjectContactNotFoundException(contact.DatabaseId, project.id);

            projectContact.Update(contact);

            await project.SavePendingChanges(email);

            return projectContact;
        }

        /// <summary>
        ///     Creates a contact on a project from a <see cref="ClientContact"/>
        /// </summary>
        /// <param name="project"></param>
        /// <param name="contact"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task<Contact> CreateContact(Project project, ClientContact contact, string email)
        {
            var projectContact = project.AddContact(contact);
            await project.SavePendingChanges(email);
            return projectContact;
        }

        /// <summary>
        ///     Removes a contact from a project given the contact's database id
        /// </summary>
        /// <param name="project"></param>
        /// <param name="contactId"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task RemoveContact(Project project, string contactId, string email)
        {
            project.RemoveContact(contactId);
            await project.SavePendingChanges(email);
        }

        /// <summary>
        ///    Transform Project to ProjectVM
        /// </summary>
        /// <param name="project"></param>
        /// <returns></returns>
        public ProjectVM BuildProjectVM(Project project)
        {
            return new ProjectVM
            {
                Id = project.id,
                APMProjectNumber = project.accountingDetails.apmProjectNumber?.CurrentValue,
                PlannedStart = project.plannedStart?.CurrentValueDateTime,
                PlannedEnd = project.plannedEnd?.CurrentValueDateTime,
                Name = project.name?.CurrentValue,
                LocationId = project.locationId,
                ClientProjectNumber = project.accountingDetails.projectNumber?.CurrentValue,
                Description = project.description?.CurrentValue,
                Status = project.status?.CurrentValue,
                TeamDistrictNumber = project.accountingDetails.teamDistrictNumber?.CurrentValue,
                TeamProjectNumber = project.accountingDetails.teamProjectNumber?.CurrentValue,
                AssetIds = project.assetIds?.CurrentValue ?? Array.Empty<string>(),
                Contacts = project.clientDetails.contacts,
                Activities = project.activities,
                BusinessUnitId=project.businessUnitId.CurrentValue
            };
        }
    }

    /// <summary>
    ///     Exception to throw when a contact is not found on a particular project
    /// </summary>
    public class ProjectContactNotFoundException : Exception
    {
        /// <summary>
        ///     Constructor...
        /// </summary>
        /// <param name="contactId"></param>
        /// <param name="projectId"></param>
        public ProjectContactNotFoundException(string contactId, string projectId) : base($"Contact with id {contactId} not found on project with id {projectId}")
        {
            ContactID = contactId;
            ProjectID = projectId;
        }

        /// <summary>
        ///     Contact database id used in search that failed.
        /// </summary>
        public string ContactID { get; set; }

        /// <summary>
        ///     Project database id used in search that failed.
        /// </summary>
        public string ProjectID { get; set; }
    }
}