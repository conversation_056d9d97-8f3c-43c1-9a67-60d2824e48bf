using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Converters;
using ClientPortal.Shared.Extensions;
using ClientPortal.Shared.Helpers;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using OrderTracking.API.Models;
using OrderTracking.API.Services;

namespace OrderTracking.API.Controllers
{
    /// <summary>
    ///     API Controller for managing User Profiles
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    [Authorize]
    public class UsersController : ControllerBase
    {
        #region Constructors

        /// <summary>
        ///     Constructor that injects an IUserProfilesService and a logging factory
        /// </summary>
        /// <param name="authorizationService"></param>
        /// <param name="userProfiles"></param>
        /// <param name="emailService"></param>
        /// <param name="userAgreements"></param>
        /// <param name="configuration"></param>
        /// <param name="logger"></param>
        public UsersController(
            IAuthorizationService authorizationService,
            IUserProfilesService userProfiles,
            IEmailService emailService,
            IUserAgreementService userAgreements,
            IConfiguration configuration,
            ILogger<UsersController> logger
        )
        {
            _logger = logger;
            _authorizationService = authorizationService;
            _userProfiles = userProfiles;
            _emailService = emailService;
            _userAgreements = userAgreements;
            _configuration = configuration;
        }

        #endregion

        #region Fields and Constants

        private readonly IAuthorizationService _authorizationService;
        private readonly IUserProfilesService _userProfiles;
        private readonly IEmailService _emailService;
        private readonly IUserAgreementService _userAgreements;
        private readonly IConfiguration _configuration;
        private readonly ILogger<UsersController> _logger;

        #endregion

        #region Public Methods

        /// <summary>
        ///     Get all User Profiles
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize(Policy = "UserIsModuleAdmin")]
        public async Task<ActionResult<IEnumerable<UserProfile>>> Get()
        {
            var users = await _userProfiles.GetAllAsync();
            return Ok(users);
        }

        /// <summary>
        ///     Delete a specific User Profile
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpDelete("{userId}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [Authorize(Policy = "UserIsActive")]
        [Authorize(Policy = "App:Admin")]
        public async Task<ActionResult> Delete([FromRoute] string userId)
        {
            await _userProfiles.RemoveAsync(userId);
            return NoContent();
        }


        private static readonly object VerificationLock = new();

        /// <summary>
        ///     Returns the UserProfile based on the specified Id.  If the user
        ///     does not exist AND the authenticated user making the call is the user
        ///     being requested, then it will create a new UserProfile and return it.
        /// </summary>
        /// <param name="id"></param>
        /// <returns>UserProfile</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<UserProfile>> Get(string id)
        {
            if (id == null) throw new ArgumentNullException(nameof(id));

            var authenticatedRequesterId = User.Identity.Name?.ToLower();
            var searchId = id.ToLower();

            if (authenticatedRequesterId != searchId) return Unauthorized();

            var user = await _userProfiles.GetAsync(searchId, searchId);
            if (user != null)
            {
                if (user.Active) return Ok(user);

                lock (VerificationLock)
                {
                    if (user.VerificationToken != null &&
                        !VerificationToken.DeserializeToken(user.VerificationToken).IsExpired)
                        return Ok(user);

                    _logger.LogInformation($"Sending Verification email to {user.Id}");
                    _ = SendVerificationEmail(user.Id);
                }

                return Ok(user);
            }

            //Allow authenticated user to create profile for themselves
            var userProfile = new UserProfile
            {
                Id = searchId,
                Email = searchId,
                GivenName = User.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname").Value,
                Surname = User.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname").Value,
                Name = User.FindFirst("name").Value,
                LastVerificationDate = DateTime.UtcNow,
                AcceptedDisclaimerDate = DateTime.UtcNow,
                LastLoginDate = DateTime.UtcNow
            };

            var newUser = await _userProfiles.AddAsync(userProfile);
            var profile = await _userProfiles.GetAsync(newUser.Id, newUser.Id);
            return Ok(profile);
        }

        /// <summary>
        ///     Update a User Profile
        /// </summary>
        /// <param name="userProfile"></param>
        /// <returns></returns>
        [HttpPut("{originalId}")]
        [Authorize(Policy = "UserIsActive")]
        public async Task<ActionResult> Put(UserProfile userProfile)
        {
            if (userProfile == null) throw new ArgumentNullException(nameof(userProfile));

            if (!userProfile.HasWorkEmail())
                return Forbid();

            var authorizationResult = await _authorizationService.AuthorizeAsync(User, userProfile, "AssignDistrict");

            if (authorizationResult.Succeeded)
            {
                var original = await _userProfiles.GetAsync(userProfile.Id);
                var sendUserAgreementEmail =
                    original.IsOnlyUserAgreementChanges(userProfile) &&
                    !userProfile.IsTeamEmployee;

                var newRoles = userProfile.Roles.Except(original.Roles).ToArray();
                var rolesToBeRemoved = original.Roles.Except(userProfile.Roles).ToArray();

                var error = UserProfilesCosmosService.CanRolesBeAdded(userProfile, newRoles, rolesToBeRemoved);
                if (error.Error) return Unauthorized(error.Message);

                var user = await _userProfiles.UpdateAsync(userProfile);

                if (sendUserAgreementEmail)
                {
                    var company = User.FindFirst("extension_Company").Value;
                    await _userAgreements.EmailPDFAsync(userProfile, company);
                }

                if (user != null) return NoContent();
                return BadRequest();
            }

            if (User.Identity.IsAuthenticated) return Forbid();
            return Challenge();
        }

        /// <summary>
        ///     Verify the token for the user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpPut("{userId}/verify/{token}")]
        [AllowAnonymous]
        public async Task<ActionResult> Verify([FromRoute] string userId, [FromRoute] string token)
        {
            var user = await _userProfiles.GetAsync(userId, userId);

            if (user == null) return Unauthorized("Error: User associated with the token is invalid");

            if (user.VerificationToken != token)
                return Unauthorized("Error: Token does not match the token currently associated with this user");

            if (VerificationToken.DeserializeToken(user.VerificationToken).IsExpired)
                return Unauthorized("Your token has expired");

            user.LastVerificationDate = DateTime.UtcNow;
            user.VerificationToken = null;
            user.LastLoginDate = DateTime.UtcNow;

            await _userProfiles.UpdateAsync(user, user.Id);

            return NoContent();
        }

        /// <summary>
        ///     Request verification email
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpPut("send-verification/{userId}")]
        public async Task<ActionResult> SendVerificationEmail([FromRoute] string userId)
        {
            var user = await _userProfiles.GetAsync(userId, userId);

            var dateToken = VerificationToken.Create24HourToken(user.Id);
            user.VerificationToken = VerificationToken.SerializeToken(dateToken);

            await _userProfiles.UpdateAsync(user, user.Id);

            await SendVerificationEmailAsync(user);

            return NoContent();
        }

        /// <summary>
        ///     Deletes one or many user profiles (for QA purposes)
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [Authorize(Policy = "App:QA")]
        [HttpDelete]
        public async Task<IActionResult> DeleteUsers(StringList ids)
        {
            if (ids == null) return BadRequest("Must provide at least one id");
            await _userProfiles.DeleteUsersAsync(ids.ToArray());
            return NoContent();
        }

        #endregion

        #region Private Methods

        private async Task SendVerificationEmailAsync(UserProfile user)
        {
            var verificationUrl = new Uri(new Uri(_configuration.GetSection("Clients:ClientPortal").Value),
                "#/verification-callback?token=" + user.VerificationToken).ToString();

            var clientPortalAddress = _configuration.GetSection("Clients:ClientPortal").Value;
            var logo = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);

            var htmlContent = $@"
{logo}

<p>{user.Name}, your account requires reverification before you can proceed.  Please follow the link below to verify your account and proceed with using OneInsight.</p>

<p>
    Click <a href=""{verificationUrl}"">here</a> to verify your account.
    <br/>Link will expire in 24 hours.
</p>

<p>
    Thank you,
    <br/>OneInsight Team
</p>
";

            var userEmail = new EmailData
            {
                Recipients = new List<IEmailRecipient> { user },
                Subject = "OneInsight Account Verification",
                HtmlContent = htmlContent
            };
            await _emailService.SendEmail(userEmail);
        }

        #endregion
    }
}