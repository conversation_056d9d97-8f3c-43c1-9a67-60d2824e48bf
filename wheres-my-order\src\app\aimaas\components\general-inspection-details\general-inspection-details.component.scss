.scrollsection {
    display: flex;
    flex-direction: column;
}

.fields {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}


.right-fields {
    width: 75%;
}
.left-fields{
    width: 35%;
    margin-top: 22px;
}
.field {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.field label {
    font-weight: bold;
    margin-right: 10px;
    width: 150px;
}

.field span {
    padding: 5px 10px;
    border-radius: 5px;
    min-width: 150px;
    display: inline-block;
      
      
}
.field-text{
    min-height: 25px;  
}



.field span {
    min-height: 25px;
}


.right-fields {
    display: grid;
    grid-template-columns: 100px 1fr;
    padding-top: 23px;
    padding-right: 50px;
    padding-bottom: 20px;
}
.right-fields label {
    font-weight: bold;
    padding-top: 5px;
}
.field-group {
    display: contents;
}
.field-text {
    background-color: #f5f5f5;
    padding: 5px 10px;
    border-radius: 5px;
}
label {
    text-align: left;
}

.no-concern {
    background-color: #c2ffad; /* Green */
  }
  
  .minor-concern {
    background-color: #ffeb99; /* Yellow */
  }
  
  .moderate-concern {
    background-color: #ffd699; /* Orange-like Yellow */
  }
  
  .major-concern {
    background-color: #ffb9b9; /* Light Red */
  }
  
  .severe {
    background-color: #ff8989; /* Bright Red */
  }
  
  .pending-review {
    background-color: #ffffff; /* White */
  } 
  
  .result-span {
    padding: 5px 10px;
    border-radius: 5px;
    min-width: 150px;
    display: inline-block;
    text-align: center;
  }
  