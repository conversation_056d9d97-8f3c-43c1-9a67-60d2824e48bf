import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxLoadIndicatorModule, DxTagBoxModule } from 'devextreme-angular';
import { DxChartModule } from 'devextreme-angular/ui/chart';
import { AssetCategoriesSelectorComponent } from '../asset-categories-selector/asset-categories-selector.component';
import { DailyInspectionCountComponent } from './daily-inspection-count.component';

describe('DailyInspectionCountComponent', () => {
    let component: DailyInspectionCountComponent;
    let fixture: ComponentFixture<DailyInspectionCountComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DxChartModule, DxLoadIndicatorModule, DxTagBoxModule],
            declarations: [
                DailyInspectionCountComponent,
                AssetCategoriesSelectorComponent
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(DailyInspectionCountComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
