import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxTemplateHost } from 'devextreme-angular';
import { DxButtonModule } from 'devextreme-angular/ui/button';
import { DxCheckBoxModule } from 'devextreme-angular/ui/check-box';
import { DxDateBoxModule } from 'devextreme-angular/ui/date-box';
import { DxSelectBoxModule } from 'devextreme-angular/ui/select-box';
import { DxTextAreaModule } from 'devextreme-angular/ui/text-area';
import { DxTextBoxModule } from 'devextreme-angular/ui/text-box';
import { ToastrModule } from 'ngx-toastr';
import { ApmAssetDetailsComponent } from './apm-asset-details.component';

describe('ApmAssetDetailsComponent', () => {
    let component: ApmAssetDetailsComponent;
    let fixture: ComponentFixture<ApmAssetDetailsComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxTextBoxModule,
                DxTextAreaModule,
                DxSelectBoxModule,
                DxDateBoxModule,
                DxCheckBoxModule,
                DxButtonModule,
                HttpClientTestingModule,
                ToastrModule.forRoot()
            ],
            providers: [
                {
                    provide: DxTemplateHost,
                    useValue: { setTemplate: () => {} }
                }
            ],
            declarations: [ApmAssetDetailsComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ApmAssetDetailsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
