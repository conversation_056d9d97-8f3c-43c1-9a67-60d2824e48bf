using System;
using CommonDataInterface.Attributes;

namespace OrderTracking.API.Exceptions.APM
{
    /// <summary>
    ///     Exception to be thrown when a LocationAttribute has an invalid value
    /// </summary>
    public class InvalidLocationException : Exception
    {
        /// <summary>
        ///     Constructor
        /// </summary>
        /// <param name="location"></param>
        public InvalidLocationException(LocationAttribute location) : base(
            $"Invalid GIS Location.  Latitude: {location.GetLatValue()}, Longitude: {location.GetLongValue()}")
        {
            Location = location;
        }

        /// <summary>
        ///     An instance of the location attribute that has an invalid value
        /// </summary>
        public LocationAttribute Location { get; }
    }
}