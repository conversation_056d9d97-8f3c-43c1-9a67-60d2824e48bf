﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using ClientPortal.Shared.Helpers;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Services;
using OrderTracking.API.Controllers;
using OrderTracking.API.Extensions;
using OrderTracking.API.Extensions.EDR;
using OrderTracking.API.Models.EDR;

namespace OrderTracking.API.Helpers
{
    /// <summary>
    ///     Service responsible for the creation of the email bodies to be sent for various EDR activities. Extracted to allow
    ///     for better test coverage
    ///     with less mocking and more actual testing
    /// </summary>
    public static class EquipmentRequestEmailSetup
    {
        private static string CreateJobTypeFieldsUpdatedHtml(JobTypeFieldChangeSummary fieldChangeSummary)
        {
            var sb = new StringBuilder();
            if (!fieldChangeSummary.JSSNumbers.Any()) return "";
            foreach (var jssNumber in fieldChangeSummary.JSSNumbers)
            foreach (var variance in fieldChangeSummary[jssNumber])
            {
                if (variance.PropertyName == nameof(JobType.TargetShipDate)) continue;
                var name = typeof(JobType)
                    .GetProperty(variance.PropertyName)
                    ?.GetCustomAttribute<DisplayNameAttribute>()
                    ?.DisplayName ?? variance.PropertyName;
                sb.AppendLine(
                    $"<li>{name} changed from {variance.OldValue ?? "(blank)"} to {variance.NewValue} on equipment with JSS ID of {jssNumber}</li>");
            }

            sb.AppendLine("</ul>");
            return sb.ToString();
        }

        private static string CreateDelayChangeHtml(EquipmentRequest originalRequest, JobType jobType)
        {
            var changeHtml = "<p><u>The following fields were updated:</u></p>";
            for (var i = originalRequest.JobTypes.First(j => j.Id == jobType.Id).Notes.Count;
                 i < jobType.Notes.Count;
                 i++) changeHtml += $@"<p>{jobType.Notes.ElementAt(i).Note}</p>";
            return changeHtml;
        }


        private static string CreateAttachmentAddedHtml(IEnumerable<EmailAttachment> addedFiles, string jobNumber)
        {
            var attachmentHtml = addedFiles.Aggregate("<p><u>Files Modified</u></p>",
                (current, attachment) =>
                    current + $@"<p>{attachment.FileName} was added as an attachment to the job {jobNumber}.</p>");
            attachmentHtml +=
                "<p>To see the more details of this change, please login to OneInsight and view the job scope tab for the request.<p>";
            return attachmentHtml;
        }

        private static string CreateAttachmentRemovedHtml(IEnumerable<EquipmentRequestBlobFile> removedFiles,
            string jobNumber)
        {
            var attachmentHtml = removedFiles.Aggregate("<p><u>Files Modified</u></p>",
                (current, attachment) => current + $@"<p>{attachment.Name} was removed from the job {jobNumber}.</p>");
            attachmentHtml +=
                "<p>To see the more details of this change, please login to OneInsight and view the job scope tab for the request.<p>";
            return attachmentHtml;
        }

        private static string WithCentralTimeZoneLabel(DateTime? dateTime) =>
            $"{dateTime}{(dateTime != null ? " (CST)" : string.Empty)}";

        /// <summary>
        /// </summary>
        /// <param name="recipients"></param>
        /// <param name="request"></param>
        /// <param name="missingShippingInfo"></param>
        /// <param name="jobType"></param>
        /// <param name="originalJobType"></param>
        /// <param name="clientPortalAddress"></param>
        /// <returns></returns>
        public static async Task<EmailData> CreateMissingShippingEmail(
            List<IEmailRecipient> recipients,
            EquipmentRequest request,
            IEnumerable<string> missingShippingInfo,
            JobType jobType, 
            JobType originalJobType, 
            string clientPortalAddress)
        {
            var headerHtmlContent = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);
            var updateHtml = request.JobTypes.First().RequestStatus.ToLower() == "new"
                ? $@"<p>A change has been made to {jobType.Description} on job <a href=""{clientPortalAddress}/#/edr/equipment-tracker/{request.Id}"">{request.JobNumber}</a> for {request.Client}. </p>"
                : $@"<p>A change has been made to {jobType.Description} on job <a href=""{clientPortalAddress}/#/edr/equipment-tracker/{request.Id}?jobType={jobType.Id}"">{request.JobNumber}</a> for {request.Client}. </p>";
            var statusChangeHtml = $"Request changed from {originalJobType.RequestStatus} to {jobType.RequestStatus}";
            var missingInfoHtml = missingShippingInfo.Aggregate(
                "<p><strong>This request is currently missing shipping information and will not be able to ship until address information has been supplied.</strong></p><p><strong> Missing information:",
                (current, missingField) => current + $" {missingField},");
            missingInfoHtml = missingInfoHtml.TrimEnd(',');
            missingInfoHtml += "</strong></p>";
            var footerHtml = request.JobTypes.First().RequestStatus.ToLower() == "new"
                ? $@"To see the more details of this change, please login to OneInsight and view the job scope tab for the request or <a href=""{clientPortalAddress}/#/edr/equipment-tracker/{request.Id}"">click here</a>"
                : $@"To see the more details of this change, please login to OneInsight and view the job scope tab for the request or <a href=""{clientPortalAddress}/#/edr/equipment-tracker/{request.Id}?jobType={jobType.Id}"">click here</a>";
            return new EmailData
            {
                Recipients = recipients,
                Subject = $"Missing Shipping Info - Equipment Demand Request change on job {request.JobNumber}.",
                HtmlContent = headerHtmlContent + updateHtml + statusChangeHtml + missingInfoHtml + footerHtml
            };
        }

        /// <summary>
        /// </summary>
        /// <param name="originalRequest"></param>
        /// <param name="request"></param>
        /// <param name="jobType"></param>
        /// <param name="clientPortalAddress"></param>
        /// <param name="recipients"></param>
        /// <returns></returns>
        public static async Task<EmailData> CreateDelayUpdateEmail(
            EquipmentRequest originalRequest,
            EquipmentRequest request,
            JobType jobType, 
            string clientPortalAddress, 
            List<IEmailRecipient> recipients
        )
        {
            var headerHtmlContent = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);
            var delayUpdateHtml = request.JobTypes.First().RequestStatus.ToLower() == "new"
                ? $@"<p>A change has been made to {jobType.Description} on job <a href=""{clientPortalAddress}/#/edr/equipment-tracker/{request.Id}"">{request.JobNumber}</a> for {request.Client}. </p>"
                : $@"<p>A change has been made to {jobType.Description} on job <a href=""{clientPortalAddress}/#/edr/equipment-tracker/{request.Id}?jobType={jobType.Id}"">{request.JobNumber}</a> for {request.Client}. </p>";
            var notesHtml = CreateDelayChangeHtml(originalRequest, jobType);
            const string footerHtml =
                "<p>To see the more details of this change, please login to OneInsight and view the job scope tab for the request.</p>";

            return new EmailData
            {
                Recipients = recipients,
                Subject = $"Equipment Demand Request info change on job {request.JobNumber}",
                HtmlContent = headerHtmlContent + delayUpdateHtml + notesHtml + footerHtml
            };
        }

        /// <summary>
        /// </summary>
        /// <param name="requester"></param>
        /// <param name="request"></param>
        /// <param name="clientPortalAddress"></param>
        /// <returns></returns>
        public static async Task<EmailData> CreateRequestAcknowledgedEmail(IEmailRecipient requester,
            EquipmentRequest request,
            string clientPortalAddress)
        {
            var headerHtmlContent = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);
            var acknowledgedHtml =
                $@"<p>Your equipment demand request for job {request.JobNumber} has been acknowledged by {request.EquipmentCenter}. The equipment center is working on your request and will notify you when it ships or of any delays.</p>";
            var requestHtml = CreateRequestHtml(request);
            var noteHtml = CreateNoteHtml(request.JobTypes.FirstOrDefault());
            var attachmentHtml = CreateAttachmentHtml(request);
            var equipmentHtml = CreateEquipmentTableHtml(request.JobTypes);
            var recipients = new List<IEmailRecipient> {requester};
            return new EmailData
            {
                Recipients = recipients,
                Subject = $"Equipment Demand Request for {request.JobNumber} acknowledged by {request.EquipmentCenter}",
                HtmlContent = headerHtmlContent + acknowledgedHtml + requestHtml + noteHtml + attachmentHtml +
                              equipmentHtml
            };
        }

        /// <summary>
        ///     Sends an email when anything is changed in the Job info tab when not in the new state
        /// </summary>
        /// <param name="recipients"></param>
        /// <param name="request"></param>
        /// <param name="changes"></param>
        /// <param name="clientPortalAddress"></param>
        /// <returns></returns>
        public static async Task<EmailData> CreateJobInfoChangeEmail(List<IEmailRecipient> recipients,
            EquipmentRequest request, EquipmentRequestFieldChangeSummary changes, string clientPortalAddress)
        {
            var headerHtmlContent = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);
            var updateHtml = changes.JobTypeChanges.JSSNumbers.Any()
                ? $"This request information has been modified for job {request.JobNumber} on JSS number {changes.JobTypeChanges.JSSNumbers.FirstOrDefault()}."
                : $"This request information has been modified for job {request.JobNumber}.";
            var changeHtml = CreateJobInfoUpdateHtml(changes);
            const string footerHtml =
                "<p>To see the more details of this change, please login to OneInsight and view the job scope tab for the request.<p>";
            return new EmailData
            {
                Recipients = recipients,
                Subject = $"A change has been made to the request for job {request.JobNumber}",
                HtmlContent = headerHtmlContent + updateHtml + changeHtml + footerHtml,
                Attachments = null
            };
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="recipients"></param>
        /// <param name="request"></param>
        /// <param name="clientPortalAddress"></param>
        /// <returns></returns>
        public static async Task<EmailData> CreateAddedHotTappingEquipmentEmail(IEnumerable<IEmailRecipient> recipients,
            EquipmentRequest request, string clientPortalAddress)
        {
            var headerHtmlContent = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);
            const string createdHtml = "An equipment demand request has been successfully submitted.";
            var htmlContent = CreateRequestHtml(request);
            var noteHtml = CreateNoteHtml(request.JobTypes.FirstOrDefault(j => j.RequestStatus == "New"));
            var equipmentHtml = CreateEquipmentTableHtml(request.JobTypes.Where(j => j.IsNew()));
            return new EmailData
            {
                Recipients = new List<IEmailRecipient>(recipients),
                Subject = $"Equipment added to existing job {request.JobNumber}",
                HtmlContent = headerHtmlContent + createdHtml + htmlContent + noteHtml + equipmentHtml
            };
        }

        /// <summary>
        /// </summary>
        /// <param name="recipients"></param>
        /// <param name="request"></param>
        /// <param name="files"></param>
        /// <param name="clientPortalAddress"></param>
        /// <returns></returns>
        public static async Task<EmailData> CreateCreatedEmail(IEnumerable<IEmailRecipient> recipients,
            EquipmentRequest request,
            List<EmailAttachment> files, string clientPortalAddress)
        {
            var headerHtmlContent = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);
            const string createdHtml = "An equipment demand request has been successfully submitted.";
            var htmlContent = CreateRequestHtml(request);
            var noteHtml = CreateNoteHtml(request.JobTypes.FirstOrDefault());
            var attachmentHtml = CreateAttachmentHtml(request);
            var equipmentHtml = CreateEquipmentTableHtml(request.JobTypes);
            return new EmailData
            {
                Recipients = new List<IEmailRecipient>(recipients),
                Subject = $"Equipment Demand Request submitted for {request.JobNumber}",
                HtmlContent = headerHtmlContent + createdHtml + htmlContent + noteHtml + attachmentHtml + equipmentHtml,
                Attachments = files
            };
        }

        /// <summary>
        /// </summary>
        /// <param name="recipients"></param>
        /// <param name="request"></param>
        /// <param name="clientPortalAddress"></param>
        /// <param name="addedFiles"></param>
        /// <returns></returns>
        public static async Task<EmailData> CreateAttachmentAddedEmail(IEnumerable<IEmailRecipient> recipients,
            EquipmentRequest request,
            List<EmailAttachment> addedFiles, string clientPortalAddress)
        {
            var headerHtmlContent = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);
            var bodyHtml = $"Attachments have been modified for job {request.JobNumber}.";
            var htmlContent = CreateAttachmentAddedHtml(addedFiles, request.JobNumber);
            return new EmailData
            {
                Recipients = new List<IEmailRecipient>(recipients),
                Subject = $"Attachments have been modified for job {request.JobNumber}",
                HtmlContent = headerHtmlContent + bodyHtml + htmlContent,
                Attachments = addedFiles
            };
        }

        /// <summary>
        ///     Create an email that is sent out when additional equipment has been added to a request for a particular job type
        /// </summary>
        /// <param name="recipients"></param>
        /// <param name="request"></param>
        /// <param name="jobTypeId"></param>
        /// <param name="clientPortalAddress"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public static async Task<EmailData> CreateAdditionalEquipmentEmail(IEnumerable<IEmailRecipient> recipients,
            EquipmentRequest request, int jobTypeId, string clientPortalAddress)
        {
            var jobType = request.JobTypes.SingleOrDefault(j => j.Id == jobTypeId);
            if (jobType == null) throw new ArgumentException("Invalid job type id.  Unable to find job type");

            var headerHtmlContent = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);
            var bodyHtml = $"Additional equipment added to {jobType.Description} on job {request.JobNumber}";
            var htmlContent = CreateAdditionalEquipmentTableHtml(jobType.AdditionalEquipment);

            return new EmailData
            {
                Recipients = new List<IEmailRecipient>(recipients),
                Subject = $"Additional equipment added to {jobType.Description} on job {request.JobNumber}",
                HtmlContent = headerHtmlContent + bodyHtml + htmlContent
            };
        }

        /// <summary>
        /// </summary>
        /// <param name="recipients"></param>
        /// <param name="request"></param>
        /// <param name="clientPortalAddress"></param>
        /// <param name="removedFiles"></param>
        /// <returns></returns>
        public static async Task<EmailData> CreateAttachmentRemovedEmail(IEnumerable<IEmailRecipient> recipients,
            EquipmentRequest request,
            IEnumerable<EquipmentRequestBlobFile> removedFiles, string clientPortalAddress)
        {
            var headerHtmlContent = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);
            var bodyHtml = $"Attachments have been modified for job {request.JobNumber}.";
            var htmlContent = CreateAttachmentRemovedHtml(removedFiles, request.JobNumber);
            return new EmailData
            {
                Recipients = new List<IEmailRecipient>(recipients),
                Subject = $"Attachments have been modified for job {request.JobNumber}",
                HtmlContent = headerHtmlContent + bodyHtml + htmlContent
            };
        }

        /// <summary>
        /// </summary>
        /// <param name="recipients"></param>
        /// <param name="request"></param>
        /// <param name="jobType"></param>
        /// <param name="clientPortalAddress"></param>
        /// <returns></returns>
        public static async Task<EmailData> CreateDelayedEmail(IEnumerable<IEmailRecipient> recipients,
            EquipmentRequest request, JobType jobType,
            string clientPortalAddress)
        {
            var headerHtmlContent = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);
            var htmlContent = CreateDelayedHtmlContent(request, jobType, clientPortalAddress);
            return new EmailData
            {
                Recipients = new List<IEmailRecipient>(recipients),
                Subject = $"Job {request.JobNumber} for {jobType.Description} Delayed by Equipment Center",
                HtmlContent = headerHtmlContent + htmlContent
            };
        }

        /// <summary>
        /// </summary>
        /// <param name="recipients"></param>
        /// <param name="request"></param>
        /// <param name="jobType"></param>
        /// <param name="clientPortalAddress"></param>
        /// <returns></returns>
        public static async Task<EmailData> CreateCustomNoteEmail(IEnumerable<IEmailRecipient> recipients,
            EquipmentRequest request, JobType jobType,
            string clientPortalAddress, bool isNew = false)
        {
            var headerHtmlContent = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);
            var htmlContent = CreateCustomNoteContent(request, jobType, clientPortalAddress, isNew);
            return new EmailData
            {
                Recipients = new List<IEmailRecipient>(recipients),
                Subject = $"Notes have been added to Job {request.JobNumber}",
                HtmlContent = headerHtmlContent + htmlContent
            };
        }

        /// <summary>
        /// </summary>
        /// <param name="recipients"></param>
        /// <param name="request"></param>
        /// <param name="jobType"></param>
        /// <param name="clientPortalAddress"></param>
        /// <param name="attachments"></param>
        /// <returns></returns>
        public static async Task<EmailData> CreateReadyForPickupEmail(List<IEmailRecipient> recipients,
            EquipmentRequest request,
            JobType jobType, string clientPortalAddress, IEnumerable<EmailAttachment> attachments)
        {
            var headerHtmlContent = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);
            var readyForPickupHtml =
                $@"Your equipment demand request for {jobType.JSSNumber} on job {request.JobNumber} has shipped or is ready for pickup. ";
            var jobInfoHtml = CreateJobInfoHtml(request, jobType);
            var requestHtml = CreateRequestHtml(request);
            var noteHtml = CreateNoteHtml(jobType);
            var attachmentHtml = CreateAttachmentHtml(request);
            var equipmentHtml = CreateEquipmentTableHtml(new[] {jobType});
            return new EmailData
            {
                Recipients = recipients,
                Subject =
                    $"Equipment Demand Request for {jobType.JSSNumber} on job {request.JobNumber} has shipped or is ready for pickup",
                HtmlContent = headerHtmlContent + readyForPickupHtml + jobInfoHtml + requestHtml + noteHtml +
                              attachmentHtml +
                              equipmentHtml,
                Attachments = attachments?.ToList()
            };
        }

        /// <summary>
        ///     Creates an email to send that summarizes the changes that were made to the equipment job types
        ///     while the parent request is in the NEW state
        /// </summary>
        /// <param name="fieldChangeSummary"></param>
        /// <param name="recipients"></param>
        /// <param name="request"></param>
        /// <param name="clientPortalAddress"></param>
        /// <returns></returns>
        public static async Task<EmailData> CreateJobEquipmentUpdatedEmailAsync(
            JobTypeFieldChangeSummary fieldChangeSummary,
            List<IEmailRecipient> recipients,
            EquipmentRequest request,
            string clientPortalAddress)
        {
            // Create header (logo, sub-text, etc.)
            var headerHtmlContent = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress);

            var sb = new StringBuilder();

            // Add a link to the request
            sb.AppendLine(
                $"The following changes were made to a request's equipment.  Please click <a href=\"{clientPortalAddress}/#/edr/equipment-tracker/{request.Id}\">here</a> to navigate to the request with job number {request.JobNumber}");

            // List out the field changes if any where found
            sb.AppendLine("<p><u>Fields updated</u></p>");
            sb.AppendLine("<ul>");
            sb.AppendLine(CreateJobTypeFieldsUpdatedHtml(fieldChangeSummary));

            // Create tables of job types/equipment for both original and new collections
            var originalEquipmentHtml = CreateEquipmentTableHtml(fieldChangeSummary.OriginalJobTypes);
            var newEquipmentHtml = CreateEquipmentTableHtml(fieldChangeSummary.NewJobTypes);

            // Display job types/equipment tables
            sb.AppendLine("<p><u>Original Equipment</u></p>");
            sb.AppendLine(originalEquipmentHtml);
            sb.AppendLine("<p><u>New Equipment</u></p>");
            sb.AppendLine(newEquipmentHtml);

            // Return the email data to be sent
            return new EmailData
            {
                Recipients = recipients,
                Subject = "Changes were made to a request's equipment",
                HtmlContent = headerHtmlContent + sb
            };
        }

        /// <summary>
        ///     Creates html for any changes made to an Equipment Request
        /// </summary>
        /// <param name="changes"></param>
        /// <returns></returns>
        public static string CreateJobInfoUpdateHtml(EquipmentRequestFieldChangeSummary changes)
        {
            var sb = new StringBuilder();
            sb.AppendLine("<p><u>Fields updated</u></p>");
            sb.AppendLine("<ul>");
            foreach (var variance in changes.Changes)
            {
                var name = typeof(EquipmentRequest)
                    .GetProperty(variance.PropertyName)
                    ?.GetCustomAttribute<DisplayNameAttribute>()
                    ?.DisplayName ?? variance.PropertyName;
                sb.AppendLine(
                    $"<li>{name} changed from {variance.OldValue ?? "(blank)"} to {variance.NewValue} on request with job number {changes.UpdatedRequest.JobNumber}</li>");
            }

            sb.AppendLine(CreateJobTypeFieldsUpdatedHtml(changes.JobTypeChanges));
            return sb.ToString();
        }

        /// <summary>
        ///     Creates the main body for the EDR delay email
        /// </summary>
        /// <param name="request"></param>
        /// <param name="jobType"></param>
        /// <param name="clientPortalAddress"></param>
        /// <returns></returns>
        public static string CreateDelayedHtmlContent(EquipmentRequest request, JobType jobType,
            string clientPortalAddress)
        {
            var htmlContent = $@"
<p>Job {request.JobNumber} for {jobType.Description} has been delayed on {DateTime.Today.ConvertToCentral().ToLongDateString()}</p>
<p><strong>Reason for delay:</strong> {jobType.ReasonForDelay}</p>
<p><strong>Notes:</strong> {jobType.DelayComment}</p>
<p><strong>New Expected Date:</strong> {jobType.NewExpectedDate.ConvertToCentral():D}</p>
<p>You can view request information <a href=""{new Uri(new Uri(clientPortalAddress), $"/#/edr/equipment-tracker/{request.Id}?jobType={jobType.Id}")}"">here</a> or you can reach the equipment center at 281-756-3183.</p>
";
            return htmlContent;
        }

        /// <summary>
        ///     Creates the main body for the EDR delay email
        /// </summary>
        /// <param name="request"></param>
        /// <param name="jobType"></param>
        /// <param name="clientPortalAddress"></param>
        /// <returns></returns>
        public static string CreateCustomNoteContent(EquipmentRequest request, JobType jobType,
            string clientPortalAddress, bool isNew = false)
        {
            string href = "";
            if (isNew)
            {
                href = $@"{new Uri(new Uri(clientPortalAddress), $"/#/edr/equipment-tracker/{request.Id}")}";
            }
            else
            {
                href =
                    $@"{new Uri(new Uri(clientPortalAddress), $"/#/edr/equipment-tracker/{request.Id}?jobType={jobType.Id}")}";
            }


            var htmlContent = $@"
<p>Notes have been added to Job {request.JobNumber} on {jobType.Notes.LastOrDefault()?.Created.ConvertToCentral()} by {jobType.Notes.LastOrDefault()?.UserName}</p>
<p><strong>Notes:</strong> {jobType.Notes.LastOrDefault()?.Note}</p>
<p>You can view request information <a href=""{href}"">here</a> or you can reach the equipment center at 281-756-3183.</p>
";
            return htmlContent;
        }


        /// <summary>
        ///     Creates the Job info section for EDR emails
        /// </summary>
        /// <param name="request"></param>
        /// <param name="jobType"></param>
        /// <returns></returns>
        public static string CreateJobInfoHtml(EquipmentRequest request, JobType jobType)
        {
            var jobInfoHtml = $@"        
<p><u>Job Info</u></p>
<p>JSS Number: {jobType.JSSNumber}</p>
<p>Description: {jobType.Description}</p>
<p>Type: {jobType.Type}</p>
<p>Pipe Size: {jobType.PipeSize}</p>
<p>Flange Rating: {jobType.FlangeRating}</p>
<p>Operating Pressure: {jobType.OperatingPressure}</p>
<p>Travel Distance to Top Pipe: {jobType.TravelDistanceToTopPipe}</p>
<p>Target Ship Date: {WithCentralTimeZoneLabel(jobType.TargetShipDate.ConvertToCentral())}</p>
<p>Received Date: {WithCentralTimeZoneLabel(jobType.ReceivedDate.ConvertToCentral())}</p>
<p>Equipment Center: {request.EquipmentCenter}</p>
";
            return jobInfoHtml;
        }

        /// <summary>
        ///     creates the request html of
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static string CreateRequestHtml(EquipmentRequest request)
        {
            var requestHtml = $@"
<p>Request sent to Equipment Center: {request.EquipmentCenter}</p> 
<p><u>Client Info</u></p>
<p>Client Name: {request.Client}</p>
<p>Job Date: {request.JobDate.ConvertToCentral():D} </p>
<p>Time Needed by: {request.JobDate.ConvertToCentral():t} (CST) </p>
<p>Job Number: {request.JobNumber}</p>
<p><u>Shipping</u></p>
<p>Street Address: {request.ShippingAddress?.Line1}</p>
<p>Street Address 2: {request.ShippingAddress?.Line2}</p>
<p>City: {request.ShippingAddress?.City} </p>
<p>State: {request.ShippingAddress?.State} </p>
<p>Zip Code: {request.ShippingAddress?.ZipCode}</p>
<p>Point of Contact: {request.ShippingPointOfContact?.Name}</p>
<p><u>District Info</u></p>
<p>District Name: {request.DistrictName}</p>
";
            return requestHtml;
        }

        /// <summary>
        ///     creates the HTML for the notes section of the EDR email
        /// </summary>
        /// <param name="jobType"></param>
        /// <returns></returns>
        public static string CreateNoteHtml(JobType jobType)
        {
            var noteHtml = "<p><u>Notes</u></p>";
            var requestNotes = jobType.Notes;
            if (requestNotes != null)
                noteHtml = requestNotes.Aggregate(noteHtml, (current, note) => current + $@"<p>{note.Note}</p>");
            return noteHtml;
        }

        /// <summary>
        ///     Creates the attachment HTML for EDR emails
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static string CreateAttachmentHtml(EquipmentRequest request)
        {
            var attachmentHtml = "<p><u>Attachments to request</u></p>";
            foreach (var attachment in request.Files) attachmentHtml += $@"<p>{attachment.Name}</p>";
            return attachmentHtml;
        }

        /// <summary>
        ///     Creates the HTML for the additional equipment section of EDR emails
        /// </summary>
        /// <param name="additionalEquipment"></param>
        /// <returns></returns>
        public static string CreateAdditionalEquipmentTableHtml(
            ICollection<AdditionalEquipmentSubmission> additionalEquipment)
        {
            var html = @"<p><u>Equipment added:</u></p>
<table>
     <tr>
        <th style='padding: 0px 10px 0px 10px'>Equipment Needed</th>
        <th style='padding: 0px 10px 0px 10px'>Quantity</th>
        <th style='padding: 0px 10px 0px 10px'>Added By</th>
        <th style='padding: 0px 10px 0px 10px'>Date Added</th>
        <th style='padding: 0px 10px 0px 10px'>Needed By</th>
        <th style='padding: 0px 10px 0px 10px'>Notes</th>
    </tr>

";
            html = additionalEquipment.SelectMany(submission => submission.Equipment.Select(e => new
                {
                    e.Notes,
                    e.EquipmentNeeded,
                    e.Quantity,
                    submission.AddedBy,
                    submission.DateAdded,
                    submission.NeededBy
                }))
                .Aggregate(html, (current, equipment) => current + $@"
<tr>
    <td style='padding: 0px 10px 0px 10px'>{equipment.EquipmentNeeded}</td>
    <td style='padding: 0px 10px 0px 10px'>{equipment.Quantity}</td>
    <td style='padding: 0px 10px 0px 10px'>{equipment.AddedBy}</td>
    <td style='padding: 0px 10px 0px 10px'>{equipment.DateAdded}</td>
    <td style='padding: 0px 10px 0px 10px'>{equipment.NeededBy}</td>
    <td style='padding: 0px 10px 0px 10px'>{equipment.Notes}</td>
</tr>
");

            html += "</table>";
            return html;
        }

        /// <summary>
        ///     Creates the HTML for the equipment section of EDR emails
        /// </summary>
        /// <param name="jobTypes"></param>
        /// <returns></returns>
        public static string CreateEquipmentTableHtml(IEnumerable<JobType> jobTypes)
        {
            var equipmentHtml = @"<p><u>Equipment</u></p>
<table>
    <tr>
        <th style='padding: 0px 10px 0px 10px'>JSS Number</th>        
        <th style='padding: 0px 10px 0px 10px'>type</th>        
        <th style='padding: 0px 10px 0px 10px'>Pipe Size</th>    
        <th style='padding: 0px 10px 0px 10px'>Tap Size</th> 
        <th style='padding: 0px 10px 0px 10px'>Flange Rating</th>    
        <th style='padding: 0px 10px 0px 10px'>Operating Pressure</th>   
        <th style='padding: 0px 10px 0px 10px'>Operating Temperature</th> 
        <th style='padding: 0px 10px 0px 10px'>Travel Distance to Top Pipe</th>        
        <th style='padding: 0px 10px 0px 10px'>Target Ship Date</th>
    </tr>
";
            foreach (var jobType in jobTypes)
            {
                var targetShipDate = jobType.TargetShipDate.ConvertToCentral();

                equipmentHtml += $@"
<tr>
    <td style='padding: 0px 10px 0px 10px'>{jobType.JSSNumber}</td>
    <td style='padding: 0px 10px 0px 10px'>{jobType.Type}</td>
    <td style='padding: 0px 10px 0px 10px'>{jobType.PipeSize}</td>
    <td style='padding: 0px 10px 0px 10px'>{jobType.TapSize}</td>
    <td style='padding: 0px 10px 0px 10px'>{jobType.FlangeRating}</td>
    <td style='padding: 0px 10px 0px 10px'>{jobType.OperatingPressure}</td>
    <td style='padding: 0px 10px 0px 10px'>{jobType.OperatingTemperature}</td>
    <td style='padding: 0px 10px 0px 10px'>{jobType.TravelDistanceToTopPipe}</td>
    <td style='padding: 0px 10px 0px 10px'>{WithCentralTimeZoneLabel(targetShipDate)}</td>
</tr>
";
            }

            equipmentHtml += "</table>";
            return equipmentHtml;
        }
    }
}