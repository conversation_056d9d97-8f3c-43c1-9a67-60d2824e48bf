import { TreeViewBusinessUnit } from './tree-view-business-unit';

export class TreeViewClient {
    constructor(
        public id: string,
        public text: string,
        public items: TreeViewBusinessUnit[] = []
    ) {
        items?.forEach((item) => item.setParent(this));
    }

    addBusinessUnit(businessUnit: TreeViewBusinessUnit) {
        this.items.push(businessUnit);
        businessUnit.setParent(this);
    }

    /** TODO: Update me if this class changes in any way! */
    static clone(client: TreeViewClient): TreeViewClient {
        return new TreeViewClient(
            client.id,
            client.text,
            client.items?.map((i) => TreeViewBusinessUnit.clone(i))
        );
    }
}
