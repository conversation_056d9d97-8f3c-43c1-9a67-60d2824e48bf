using System;
using APMWebDataInterface.ExampleDataModel;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    /// <summary>
    ///     View Model for interacting with the Data Grid from DevExtreme
    /// </summary>
    public class WorkOrderVM
    {
        [JsonProperty(PropertyName = "id")]
        public string ID { get; set; }

        [JsonProperty(PropertyName = "projectId")]
        public string ProjectId { get; set; }

        [JsonProperty(PropertyName = "assetId")]
        public string AssetId { get; set; }

        [JsonProperty(PropertyName = "apmWorkOrderNumber")]
        public string ApmWorkOrderNumber { get; set; }

        [JsonProperty(PropertyName = "status")]
        public string Status { get; set; }

        [JsonProperty(PropertyName = "facilityName")]
        public string FacilityName { get; set; }

        [JsonProperty(PropertyName = "assetCategory")]
        public string AssetCategory { get; set; }

        [JsonProperty(PropertyName = "plannedStart")]
        public DateTime? PlannedStart { get; set; }

        [JsonProperty(PropertyName = "plannedEnd")]
        public DateTime? PlannedEnd { get; set; }

        [JsonProperty(PropertyName = "fieldWorkCompleted")]
        public DateTime? FieldWorkCompleted { get; set; }

        [JsonProperty(PropertyName = "dueDate")]
        public DateTime? DueDate { get; set; }

        [JsonProperty(PropertyName = "asset")]
        public Asset Asset { get; set; }

        [JsonProperty(PropertyName = "businessUnitId")]
        public String BusinessUnitId { get; set; }
    }
}