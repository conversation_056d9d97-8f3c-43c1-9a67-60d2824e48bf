import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { ProfileModule } from '../profile/profile.module';
import { SharedModule } from './../shared/shared.module';
import { AdminRoutingModule } from './admin-routing.module';
import { RolesComponent, UsersComponent } from './pages';

@NgModule({
    declarations: [RolesComponent, UsersComponent],
    imports: [CommonModule, SharedModule, AdminRoutingModule, ProfileModule],
})
export class AdminModule {}
