import {
    Component,
    EventEmitter,
    Input,
    Output,
    ViewChild
} from '@angular/core';
import cloneDeep from 'clone-deep';
import {
    DxFormComponent,
    DxGalleryComponent,
    DxPopupComponent,
    DxTextAreaComponent
} from 'devextreme-angular';
import { confirm } from 'devextreme/ui/dialog';
import { FieldDataChangedEvent } from 'devextreme/ui/form';
import { ValidationCallbackData } from 'devextreme/ui/validation_rules';
import { saveAs } from 'file-saver';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, firstValueFrom } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { isNullOrUndefined } from '../../../shared/helpers';
import {
    AssetDetailsPhoto,
    AssetDetailsPhotoTransport,
    AssetPath,
    ChannelUpdate,
    FiveTenAssetDetails,
    HeadUpdate,
    InspectionOpeningUpdate,
    NozzleUpdate,
    Option,
    RepairUpdate,
    ShellCourseUpdate,
    SupportedAssetCategories
} from '../../models';
import { ApmService } from '../../services';

@Component({
    selector: 'app-apm-asset-details-five-ten',
    templateUrl: './apm-asset-details-five-ten.component.html',
    styleUrls: ['./apm-asset-details-five-ten.component.scss']
})
export class ApmAssetDetailsFiveTenComponent {
    allPhotos: AssetDetailsPhoto[];
    private _originalPhotoDescription: string | undefined;
    private _isEditingPhotoDescription = new BehaviorSubject<boolean>(false);
    private _isEditing = new BehaviorSubject<boolean>(false);
    private _isSaving = new BehaviorSubject<boolean>(false);

    assetPathLoadingCompleted = false;
    assetPathsArray: AssetPath = {
        allPhotos: {},
        frontPhotos: {},
        backPhotos: {},
        leftPhotos: {},
        rightPhotos: {}
    };

    @Output() photoDelete = new EventEmitter<AssetDetailsPhotoTransport>();
    @Output() photoDescriptionUpdate =
        new EventEmitter<AssetDetailsPhotoTransport>();

    private _assetDetails: FiveTenAssetDetails;
    popupGallerySelectedIndex = 0;

    @Input() allowEditing: boolean;
    @Input()
    set assetDetails(value: FiveTenAssetDetails) {
        this._isEditing.next(false);
        this._assetDetails = value;
        if (this._assetDetails) {
            this.updateAssetPaths();
            this.allPhotos = this._assetDetails.allPhotos;
            this.geometryOptions = this._assetDetails.geometryOptions;
        }
    }
    get assetDetails() {
        return this._assetDetails;
    }

    showPhotoPopup = false;

    get geoOther() {
        return this.selectedGeometry.toLowerCase() === 'other';
    }

    readonly isEditing$ = this._isEditing.asObservable();
    readonly isEditingPhotoDescription$ =
        this._isEditingPhotoDescription.asObservable();
    readonly isSaving$ = this._isSaving.asObservable();
    originalAssetDetails: FiveTenAssetDetails;

    selectedGeometry = '';
    multiDiameter = false;
    dataPlateAttached = false;
    repairPlateAttached = false;
    assetClassOptions = SupportedAssetCategories;
    inspectionCodes = ['API 510', 'API 570', 'API 653'];
    pos = ['Vertical', 'Horizontal'];
    sideOptions = ['Shell Side', 'Tube Side'];
    geometryOptions: Option[];
    rtOptions = ['Full', 'Spot', 'None'];
    diamterMeasurementOptions = ['ID', 'OD'];
    operationStatusOptions = ['In-Service', 'Out-Of-Service', 'Standby'];
    overallLocationOptions = ['On-Plot (Facility)', 'Off-Plot (Field)'];
    locationOptions = ['Top', 'Bottom', 'North', 'South', 'East', 'West'];
    lowestFlangeItems = ['150', '300', '400', '600', '900', '1500', '2500'];
    constructionTypeItems = [
        'Welded',
        'Pressure Welded',
        'Brazed',
        'Resistance Welded'
    ];
    pipeSizeItems = [
        '0.5',
        '0.75',
        '1',
        '1.25',
        '1.5',
        '2',
        '2.5',
        '3',
        '3.5',
        '4',
        '4.5',
        '5',
        '6',
        '8',
        '10',
        '12',
        '14',
        '16',
        '18',
        '20',
        '24',
        '30',
        '36',
        '42'
    ];
    pipeScheduleItems = [
        '5',
        '10',
        '20',
        '30',
        '40',
        '50',
        '60',
        '70',
        '80',
        '100',
        '120',
        '140',
        '160',
        'STD',
        'EH',
        'DBL.EH'
    ];
    flangeRatingItems = ['150', '300', '400', '600', '900', '1500', '2500'];
    yesNoUnknownItems = ['Yes', 'No', 'Unknown'];
    yesNoItems = ['Yes', 'No'];

    @ViewChild(DxFormComponent) form: DxFormComponent;
    validateGIS = (e: ValidationCallbackData) => {
        const lat = this.form.instance
            .getEditor('gisLocationLat')
            .option('value');
        const long = this.form.instance
            .getEditor('gisLocationLong')
            .option('value');
        if (isNullOrUndefined(lat) && isNullOrUndefined(long)) return true;
        if (isNullOrUndefined(lat) && !isNullOrUndefined(long)) return false;
        if (!isNullOrUndefined(lat) && isNullOrUndefined(long)) return false;
        return true;
    };

    positiveLatitude = () => 90;

    negativeLatitude = () => -90;

    positiveLongitude = () => 180;

    negativeLongitude = () => -180;

    constructor(
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService
    ) {}

    onFieldDataChanged(e: FieldDataChangedEvent) {
        if (
            e.dataField === 'gisLocationLat' ||
            e.dataField === 'gisLocationLong'
        ) {
            e.component.validate();
        }
    }

    onEditClicked(e) {
        this._isEditing.next(true);
        this.originalAssetDetails = cloneDeep(this.assetDetails, true);
    }

    onCancelClicked(e) {
        this._isEditing.next(false);
        this.assetDetails = cloneDeep(this.originalAssetDetails, true);
    }

    onSaveClicked(e) {
        const result = this.form.instance.validate();
        if (!result.isValid) return;
        this._isSaving.next(true);
        let channelUpdates: ChannelUpdate[] = [];
        if (this.originalAssetDetails.channels) {
            for (var i in this.assetDetails.channels) {
                channelUpdates.push({
                    databaseId: this.assetDetails.channels[i].databaseId,
                    allowableStressAtTemp:
                        this.assetDetails.channels[i].allowableStressAtTemp ===
                        this.originalAssetDetails.channels[i]
                            .allowableStressAtTemp
                            ? null
                            : {
                                  value: this.assetDetails.channels[i]
                                      .allowableStressAtTemp
                              },
                    corrosionAllowance:
                        this.assetDetails.channels[i].corrosionAllowance ===
                        this.originalAssetDetails.channels[i].corrosionAllowance
                            ? null
                            : {
                                  value: this.assetDetails.channels[i]
                                      .corrosionAllowance
                              },
                    jointEfficiency:
                        this.assetDetails.channels[i].jointEfficiency ===
                        this.originalAssetDetails.channels[i].jointEfficiency
                            ? null
                            : {
                                  value: this.assetDetails.channels[i]
                                      .jointEfficiency
                              },
                    length:
                        this.assetDetails.channels[i].length ===
                        this.originalAssetDetails.channels[i].length
                            ? null
                            : { value: this.assetDetails.channels[i].length },
                    location:
                        this.assetDetails.channels[i].location ===
                        this.originalAssetDetails.channels[i].location
                            ? null
                            : { value: this.assetDetails.channels[i].location },
                    materialSpecAndGrade:
                        this.assetDetails.channels[i].materialSpecAndGrade ===
                        this.originalAssetDetails.channels[i]
                            .materialSpecAndGrade
                            ? null
                            : {
                                  value: this.assetDetails.channels[i]
                                      .materialSpecAndGrade
                              },
                    nominalThickness:
                        this.assetDetails.channels[i].nominalThickness ===
                        this.originalAssetDetails.channels[i].nominalThickness
                            ? null
                            : {
                                  value: this.assetDetails.channels[i]
                                      .nominalThickness
                              },
                    number:
                        this.assetDetails.channels[i].number ===
                        this.originalAssetDetails.channels[i].number
                            ? null
                            : { value: this.assetDetails.channels[i].number }
                });
            }
        }

        let headUpdates: HeadUpdate[] = [];
        if (this.originalAssetDetails.heads) {
            for (var i in this.assetDetails.heads) {
                headUpdates.push({
                    databaseId: this.assetDetails.heads[i].databaseId,
                    allowableStressAtTemp:
                        this.assetDetails.heads[i].allowableStressAtTemp ===
                        this.originalAssetDetails.heads[i].allowableStressAtTemp
                            ? null
                            : {
                                  value: this.assetDetails.heads[i]
                                      .allowableStressAtTemp
                              },
                    corrosionAllowance:
                        this.assetDetails.heads[i].corrosionAllowance ===
                        this.originalAssetDetails.heads[i].corrosionAllowance
                            ? null
                            : {
                                  value: this.assetDetails.heads[i]
                                      .corrosionAllowance
                              },
                    geometry:
                        this.assetDetails.heads[i].geometry ===
                        this.originalAssetDetails.heads[i].geometry
                            ? null
                            : { value: this.assetDetails.heads[i].geometry },
                    geometryComments:
                        this.assetDetails.heads[i].geometryComments ===
                        this.originalAssetDetails.heads[i].geometryComments
                            ? null
                            : {
                                  value: this.assetDetails.heads[i]
                                      .geometryComments
                              },
                    jointEfficiency:
                        this.assetDetails.heads[i].jointEfficiency ===
                        this.originalAssetDetails.heads[i].jointEfficiency
                            ? null
                            : {
                                  value: this.assetDetails.heads[i]
                                      .jointEfficiency
                              },
                    location:
                        this.assetDetails.heads[i].location ===
                        this.originalAssetDetails.heads[i].location
                            ? null
                            : { value: this.assetDetails.heads[i].location },
                    materialSpecAndGrade:
                        this.assetDetails.heads[i].materialSpecAndGrade ===
                        this.originalAssetDetails.heads[i].materialSpecAndGrade
                            ? null
                            : {
                                  value: this.assetDetails.heads[i]
                                      .materialSpecAndGrade
                              },
                    nominalThickness:
                        this.assetDetails.heads[i].nominalThickness ===
                        this.originalAssetDetails.heads[i].nominalThickness
                            ? null
                            : {
                                  value: this.assetDetails.heads[i]
                                      .nominalThickness
                              },
                    number:
                        this.assetDetails.heads[i].number ===
                        this.originalAssetDetails.heads[i].number
                            ? null
                            : { value: this.assetDetails.heads[i].number }
                });
            }
        }

        let repairUpdates: RepairUpdate[] = [];
        if (this.originalAssetDetails.repairs) {
            for (var i in this.assetDetails.repairs) {
                repairUpdates.push({
                    databaseId: this.assetDetails.repairs[i].databaseId,
                    dateRepairedOrAltered:
                        this.assetDetails.repairs[i].dateRepairedOrAltered ===
                        this.originalAssetDetails.repairs[i]
                            .dateRepairedOrAltered
                            ? null
                            : {
                                  value: this.assetDetails.repairs[i]
                                      .dateRepairedOrAltered
                              },
                    isNBFormR1Available:
                        this.assetDetails.repairs[i].isNBFormR1Available ===
                        this.originalAssetDetails.repairs[i].isNBFormR1Available
                            ? null
                            : {
                                  value: this.assetDetails.repairs[i]
                                      .isNBFormR1Available
                              },
                    isNBFormR2Available:
                        this.assetDetails.repairs[i].isNBFormR2Available ===
                        this.originalAssetDetails.repairs[i].isNBFormR2Available
                            ? null
                            : {
                                  value: this.assetDetails.repairs[i]
                                      .isNBFormR2Available
                              },
                    nbRCertificateNumber:
                        this.assetDetails.repairs[i].nbRCertificateNumber ===
                        this.originalAssetDetails.repairs[i]
                            .nbRCertificateNumber
                            ? null
                            : {
                                  value: this.assetDetails.repairs[i]
                                      .nbRCertificateNumber
                              },
                    repairAlterationOrganization:
                        this.assetDetails.repairs[i]
                            .repairAlterationOrganization ===
                        this.originalAssetDetails.repairs[i]
                            .repairAlterationOrganization
                            ? null
                            : {
                                  value: this.assetDetails.repairs[i]
                                      .repairAlterationOrganization
                              },
                    purposeOfRepairAlteration:
                        this.assetDetails.repairs[i]
                            .purposeOfRepairAlteration ===
                        this.originalAssetDetails.repairs[i]
                            .purposeOfRepairAlteration
                            ? null
                            : {
                                  value: this.assetDetails.repairs[i]
                                      .purposeOfRepairAlteration
                              }
                });
            }
        }

        let shellCourseUpdates: ShellCourseUpdate[] = [];
        if (this.originalAssetDetails.shellCourses) {
            for (var i in this.assetDetails.shellCourses) {
                shellCourseUpdates.push({
                    databaseId: this.assetDetails.shellCourses[i].databaseId,
                    allowableStressAtTemp:
                        this.assetDetails.shellCourses[i]
                            .allowableStressAtTemp ===
                        this.originalAssetDetails.shellCourses[i]
                            .allowableStressAtTemp
                            ? null
                            : {
                                  value: this.assetDetails.shellCourses[i]
                                      .allowableStressAtTemp
                              },
                    corrosionAllowance:
                        this.assetDetails.shellCourses[i].corrosionAllowance ===
                        this.originalAssetDetails.shellCourses[i]
                            .corrosionAllowance
                            ? null
                            : {
                                  value: this.assetDetails.shellCourses[i]
                                      .corrosionAllowance
                              },
                    jointEfficiency:
                        this.assetDetails.shellCourses[i].jointEfficiency ===
                        this.originalAssetDetails.shellCourses[i]
                            .jointEfficiency
                            ? null
                            : {
                                  value: this.assetDetails.shellCourses[i]
                                      .jointEfficiency
                              },
                    lengthOrHeight:
                        this.assetDetails.shellCourses[i].lengthOrHeight ===
                        this.originalAssetDetails.shellCourses[i].lengthOrHeight
                            ? null
                            : {
                                  value: this.assetDetails.shellCourses[i]
                                      .lengthOrHeight
                              },
                    materialSpecAndGrade:
                        this.assetDetails.shellCourses[i]
                            .materialSpecAndGrade ===
                        this.originalAssetDetails.shellCourses[i]
                            .materialSpecAndGrade
                            ? null
                            : {
                                  value: this.assetDetails.shellCourses[i]
                                      .materialSpecAndGrade
                              },
                    nominalThickness:
                        this.assetDetails.shellCourses[i].nominalThickness ===
                        this.originalAssetDetails.shellCourses[i]
                            .nominalThickness
                            ? null
                            : {
                                  value: this.assetDetails.shellCourses[i]
                                      .nominalThickness
                              },
                    number:
                        this.assetDetails.shellCourses[i].number ===
                        this.originalAssetDetails.shellCourses[i].number
                            ? null
                            : {
                                  value: this.assetDetails.shellCourses[i]
                                      .number
                              }
                });
            }
        }

        let nozzleUpdates: NozzleUpdate[] = [];
        if (this.originalAssetDetails.nozzles) {
            for (var i in this.assetDetails.nozzles) {
                nozzleUpdates.push({
                    databaseId: this.assetDetails.nozzles[i].databaseId,
                    flangeRating:
                        this.assetDetails.nozzles[i].flangeRating ===
                        this.originalAssetDetails.nozzles[i].flangeRating
                            ? null
                            : {
                                  value: this.assetDetails.nozzles[i]
                                      .flangeRating
                              },
                    materialSpecAndGrade:
                        this.assetDetails.nozzles[i].materialSpecAndGrade ===
                        this.originalAssetDetails.nozzles[i]
                            .materialSpecAndGrade
                            ? null
                            : {
                                  value: this.assetDetails.nozzles[i]
                                      .materialSpecAndGrade
                              },
                    number:
                        this.assetDetails.nozzles[i].number ===
                        this.originalAssetDetails.nozzles[i].number
                            ? null
                            : { value: this.assetDetails.nozzles[i].number },
                    pipeSchedule:
                        this.assetDetails.nozzles[i].pipeSchedule ===
                        this.originalAssetDetails.nozzles[i].pipeSchedule
                            ? null
                            : {
                                  value: this.assetDetails.nozzles[i]
                                      .pipeSchedule
                              },
                    pipeSize:
                        this.assetDetails.nozzles[i].pipeSize ===
                        this.originalAssetDetails.nozzles[i].pipeSize
                            ? null
                            : { value: this.assetDetails.nozzles[i].pipeSize },
                    reinforcementPadDimensions:
                        this.assetDetails.nozzles[i]
                            .reinforcementPadDimensions ===
                        this.originalAssetDetails.nozzles[i]
                            .reinforcementPadDimensions
                            ? null
                            : {
                                  value: this.assetDetails.nozzles[i]
                                      .reinforcementPadDimensions
                              },
                    reinforcementPadThickness:
                        this.assetDetails.nozzles[i]
                            .reinforcementPadThickness ===
                        this.originalAssetDetails.nozzles[i]
                            .reinforcementPadThickness
                            ? null
                            : {
                                  value: this.assetDetails.nozzles[i]
                                      .reinforcementPadThickness
                              },
                    reinforcementPadType:
                        this.assetDetails.nozzles[i].reinforcementPadType ===
                        this.originalAssetDetails.nozzles[i]
                            .reinforcementPadType
                            ? null
                            : {
                                  value: this.assetDetails.nozzles[i]
                                      .reinforcementPadType
                              },
                    type:
                        this.assetDetails.nozzles[i].type ===
                        this.originalAssetDetails.nozzles[i].type
                            ? null
                            : { value: this.assetDetails.nozzles[i].type }
                });
            }
        }

        let inspectionOpeningUpdates: InspectionOpeningUpdate[] = [];
        if (this.originalAssetDetails.inspectionOpenings) {
            for (var i in this.assetDetails.inspectionOpenings) {
                inspectionOpeningUpdates.push({
                    databaseId:
                        this.assetDetails.inspectionOpenings[i].databaseId,
                    displayName:
                        this.assetDetails.inspectionOpenings[i].displayName ===
                        this.originalAssetDetails.inspectionOpenings[i]
                            .displayName
                            ? null
                            : {
                                  value: this.assetDetails.inspectionOpenings[i]
                                      .displayName
                              },
                    number:
                        this.assetDetails.inspectionOpenings[i].number ===
                        this.originalAssetDetails.inspectionOpenings[i].number
                            ? null
                            : {
                                  value: this.assetDetails.inspectionOpenings[i]
                                      .number
                              },
                    size:
                        this.assetDetails.inspectionOpenings[i].size ===
                        this.originalAssetDetails.inspectionOpenings[i].size
                            ? null
                            : {
                                  value: this.assetDetails.inspectionOpenings[i]
                                      .size
                              },
                    type:
                        this.assetDetails.inspectionOpenings[i].type ===
                        this.originalAssetDetails.inspectionOpenings[i].type
                            ? null
                            : {
                                  value: this.assetDetails.inspectionOpenings[i]
                                      .type
                              }
                });
            }
        }

        this._apm
            .updateFiveTenAssetDetails({
                assetCategory:
                    this.assetDetails.assetCategory ===
                    this.originalAssetDetails.assetCategory
                        ? null
                        : { value: this.assetDetails.assetCategory },
                assetId: this.assetDetails.assetId,
                workOrderId: this.assetDetails.workOrderId,
                projectId: this.assetDetails.projectId,
                assetType:
                    this.assetDetails.assetType ===
                    this.originalAssetDetails.assetType
                        ? null
                        : { value: this.assetDetails.assetType },
                dataPlateAttached:
                    this.assetDetails.dataPlateAttached ===
                    this.originalAssetDetails.dataPlateAttached
                        ? null
                        : { value: this.assetDetails.dataPlateAttached },
                dataPlateLegible:
                    this.assetDetails.dataPlateLegible ===
                    this.originalAssetDetails.dataPlateLegible
                        ? null
                        : { value: this.assetDetails.dataPlateLegible },
                designAddendum:
                    this.assetDetails.designAddendum ===
                    this.originalAssetDetails.designAddendum
                        ? null
                        : { value: this.assetDetails.designAddendum },
                designCode:
                    this.assetDetails.designCode ===
                    this.originalAssetDetails.designCode
                        ? null
                        : { value: this.assetDetails.designCode },
                designConditionsOperatingTemp:
                    this.assetDetails.designConditionsOperatingTemp ===
                    this.originalAssetDetails.designConditionsOperatingTemp
                        ? null
                        : {
                              value: this.assetDetails
                                  .designConditionsOperatingTemp
                          },
                designDrawingNumber:
                    this.assetDetails.designDrawingNumber ===
                    this.originalAssetDetails.designDrawingNumber
                        ? null
                        : { value: this.assetDetails.designDrawingNumber },
                designYear:
                    this.assetDetails.designYear ===
                    this.originalAssetDetails.designYear
                        ? null
                        : { value: this.assetDetails.designYear },
                diameter:
                    this.assetDetails.diameter ===
                    this.originalAssetDetails.diameter
                        ? null
                        : { value: this.assetDetails.diameter },
                diameterComments:
                    this.assetDetails.diameterComments ===
                    this.originalAssetDetails.diameterComments
                        ? null
                        : { value: this.assetDetails.diameterComments },
                diameterMeasurement:
                    this.assetDetails.diameterMeasurement ===
                    this.originalAssetDetails.diameterMeasurement
                        ? null
                        : { value: this.assetDetails.diameterMeasurement },
                equipmentDescription:
                    this.assetDetails.equipmentDescription ===
                    this.originalAssetDetails.equipmentDescription
                        ? null
                        : { value: this.assetDetails.equipmentDescription },
                gisLocationLat:
                    this.assetDetails.gisLocationLat ===
                    this.originalAssetDetails.gisLocationLat
                        ? null
                        : { value: this.assetDetails.gisLocationLat },
                gisLocationLong:
                    this.assetDetails.gisLocationLong ===
                    this.originalAssetDetails.gisLocationLong
                        ? null
                        : { value: this.assetDetails.gisLocationLong },
                hasMultipleDiameters:
                    this.assetDetails.hasMultipleDiameters ===
                    this.originalAssetDetails.hasMultipleDiameters
                        ? null
                        : { value: this.assetDetails.hasMultipleDiameters },
                hasRepairOrAlterationPlate:
                    this.assetDetails.hasRepairOrAlterationPlate ===
                    this.originalAssetDetails.hasRepairOrAlterationPlate
                        ? null
                        : {
                              value: this.assetDetails
                                  .hasRepairOrAlterationPlate
                          },
                hasToriconicalSections:
                    this.assetDetails.hasToriconicalSections ===
                    this.originalAssetDetails.hasToriconicalSections
                        ? null
                        : { value: this.assetDetails.hasToriconicalSections },
                hydroTestPressure:
                    this.assetDetails.hydroTestPressure ===
                    this.originalAssetDetails.hydroTestPressure
                        ? null
                        : { value: this.assetDetails.hydroTestPressure },
                identificationName:
                    this.assetDetails.identificationName ===
                    this.originalAssetDetails.identificationName
                        ? null
                        : { value: this.assetDetails.identificationName },
                identificationNumber:
                    this.assetDetails.identificationNumber ===
                    this.originalAssetDetails.identificationNumber
                        ? null
                        : { value: this.assetDetails.identificationNumber },
                inServiceDate:
                    this.assetDetails.inServiceDate ===
                    this.originalAssetDetails.inServiceDate
                        ? null
                        : { value: this.assetDetails.inServiceDate },
                inspectionAddendum:
                    this.assetDetails.inspectionAddendum ===
                    this.originalAssetDetails.inspectionAddendum
                        ? null
                        : { value: this.assetDetails.inspectionAddendum },
                inspectionCode:
                    this.assetDetails.inspectionCode ===
                    this.originalAssetDetails.inspectionCode
                        ? null
                        : { value: this.assetDetails.inspectionCode },
                inspectionYear:
                    this.assetDetails.inspectionYear ===
                    this.originalAssetDetails.inspectionYear
                        ? null
                        : { value: this.assetDetails.inspectionYear },
                installationDate:
                    this.assetDetails.installationDate ===
                    this.originalAssetDetails.installationDate
                        ? null
                        : { value: this.assetDetails.installationDate },
                isFiredPressureVessel:
                    this.assetDetails.isFiredPressureVessel ===
                    this.originalAssetDetails.isFiredPressureVessel
                        ? null
                        : { value: this.assetDetails.isFiredPressureVessel },
                lastKnownInspectionDate:
                    this.assetDetails.lastKnownInspectionDate ===
                    this.originalAssetDetails.lastKnownInspectionDate
                        ? null
                        : { value: this.assetDetails.lastKnownInspectionDate },
                location:
                    this.assetDetails.location ===
                    this.originalAssetDetails.location
                        ? null
                        : { value: this.assetDetails.location },
                lowestFlangeRating:
                    this.assetDetails.lowestFlangeRating ===
                    this.originalAssetDetails.lowestFlangeRating
                        ? null
                        : { value: this.assetDetails.lowestFlangeRating },
                manufacturerDate:
                    this.assetDetails.manufacturerDate ===
                    this.originalAssetDetails.manufacturerDate
                        ? null
                        : { value: this.assetDetails.manufacturerDate },
                manufacturerName:
                    this.assetDetails.manufacturerName ===
                    this.originalAssetDetails.manufacturerName
                        ? null
                        : { value: this.assetDetails.manufacturerName },
                manufacturerSerialNumber:
                    this.assetDetails.manufacturerSerialNumber ===
                    this.originalAssetDetails.manufacturerSerialNumber
                        ? null
                        : { value: this.assetDetails.manufacturerSerialNumber },
                nationalBoardNumber:
                    this.assetDetails.nationalBoardNumber ===
                    this.originalAssetDetails.nationalBoardNumber
                        ? null
                        : { value: this.assetDetails.nationalBoardNumber },
                operationStatus:
                    this.assetDetails.operationStatus ===
                    this.originalAssetDetails.operationStatus
                        ? null
                        : { value: this.assetDetails.operationStatus },
                orientation:
                    this.assetDetails.orientation ===
                    this.originalAssetDetails.orientation
                        ? null
                        : { value: this.assetDetails.orientation },
                overallLengthHeight:
                    this.assetDetails.overallLengthHeight ===
                    this.originalAssetDetails.overallLengthHeight
                        ? null
                        : { value: this.assetDetails.overallLengthHeight },
                pIDNumber:
                    this.assetDetails.pIDNumber ===
                    this.originalAssetDetails.pIDNumber
                        ? null
                        : { value: this.assetDetails.pIDNumber },
                pwht:
                    this.assetDetails.pwht === this.originalAssetDetails.pwht
                        ? null
                        : { value: this.assetDetails.pwht },
                ratingChanged:
                    this.assetDetails.ratingChanged ===
                    this.originalAssetDetails.ratingChanged
                        ? null
                        : { value: this.assetDetails.ratingChanged },
                repairOrAlterationPlateLegible:
                    this.assetDetails.repairOrAlterationPlateLegible ===
                    this.originalAssetDetails.repairOrAlterationPlateLegible
                        ? null
                        : {
                              value: this.assetDetails
                                  .repairOrAlterationPlateLegible
                          },
                rt:
                    this.assetDetails.rt === this.originalAssetDetails.rt
                        ? null
                        : { value: this.assetDetails.rt },
                rtNumber:
                    this.assetDetails.rtNumber ===
                    this.originalAssetDetails.rtNumber
                        ? null
                        : { value: this.assetDetails.rtNumber },
                serviceProductContents:
                    this.assetDetails.serviceProductContents ===
                    this.originalAssetDetails.serviceProductContents
                        ? null
                        : { value: this.assetDetails.serviceProductContents },
                shellSideDesignMAWP:
                    this.assetDetails.shellSideDesignMAWP ===
                    this.originalAssetDetails.shellSideDesignMAWP
                        ? null
                        : { value: this.assetDetails.shellSideDesignMAWP },
                shellSideDesignTemp:
                    this.assetDetails.shellSideDesignTemp ===
                    this.originalAssetDetails.shellSideDesignTemp
                        ? null
                        : {
                              value: this.assetDetails.shellSideDesignTemp
                          },
                shellSideOperatingPressure:
                    this.assetDetails.shellSideOperatingPressure ===
                    this.originalAssetDetails.shellSideOperatingPressure
                        ? null
                        : {
                              value: this.assetDetails
                                  .shellSideOperatingPressure
                          },
                shellSideOperatingTemp:
                    this.assetDetails.shellSideOperatingTemp ===
                    this.originalAssetDetails.shellSideOperatingTemp
                        ? null
                        : { value: this.assetDetails.shellSideOperatingTemp },
                shellSideSetPressure:
                    this.assetDetails.shellSideOperatingPressure ===
                    this.originalAssetDetails.shellSideOperatingPressure
                        ? null
                        : {
                              value: this.assetDetails
                                  .shellSideOperatingPressure
                          },
                specificGravity:
                    this.assetDetails.specificGravity ===
                    this.originalAssetDetails.specificGravity
                        ? null
                        : { value: this.assetDetails.specificGravity },
                tubeSideDesignMAWP:
                    this.assetDetails.tubeSideDesignMAWP ===
                    this.originalAssetDetails.tubeSideDesignMAWP
                        ? null
                        : { value: this.assetDetails.tubeSideDesignMAWP },
                tubeSideDesignTemp:
                    this.assetDetails.tubeSideDesignTemp ===
                    this.originalAssetDetails.tubeSideDesignTemp
                        ? null
                        : { value: this.assetDetails.tubeSideDesignTemp },
                tubeSideOperatingPressure:
                    this.assetDetails.tubeSideOperatingPressure ===
                    this.originalAssetDetails.tubeSideOperatingPressure
                        ? null
                        : {
                              value: this.assetDetails.tubeSideOperatingPressure
                          },
                tubeSideOperatingTemp:
                    this.assetDetails.tubeSideOperatingTemp ===
                    this.originalAssetDetails.tubeSideOperatingTemp
                        ? null
                        : { value: this.assetDetails.tubeSideOperatingTemp },
                tubeSideSetPressure:
                    this.assetDetails.tubeSideSetPressure ==
                    this.originalAssetDetails.tubeSideSetPressure
                        ? null
                        : { value: this.assetDetails.tubeSideSetPressure },
                typeOfConstruction:
                    this.assetDetails.typeOfConstruction ===
                    this.originalAssetDetails.typeOfConstruction
                        ? null
                        : { value: this.assetDetails.typeOfConstruction },
                channels: channelUpdates,
                heads: headUpdates,
                repairs: repairUpdates,
                shellCourses: shellCourseUpdates,
                nozzles: nozzleUpdates,
                inspectionOpenings: inspectionOpeningUpdates
            })
            .pipe(finalize(() => this._isSaving.next(false)))
            .subscribe(() => {
                this._isEditing.next(false);

                this._toasts.success(
                    'Asset task data updated successfully',

                    'Update successful'
                );
            });
    }

    onPopupGalleryContentReady(
        e,
        popup: DxPopupComponent,
        gallery: DxGalleryComponent
    ) {
        const title = gallery.selectedItem?.section;
        if (title && popup.title !== title) {
            popup.instance.option('title', title);
        }
    }

    onPopupGallerySelectionChanged(
        e,
        popup: DxPopupComponent,
        gallery: DxGalleryComponent
    ) {
        const title = e.addedItems[0].section;
        if (title && popup.title !== title) {
            popup.instance.option('title', title);
        }
    }

    onItemClick(e) {
        this.showPhotoPopup = true;

        this.popupGallerySelectedIndex = this.allPhotos.findIndex(
            (somePhoto) => somePhoto.photo.databaseId === e.itemData.databaseId
        );
    }

    onEditDescriptionClicked(e, currentValue: string) {
        this._isEditingPhotoDescription.next(true);
        this._originalPhotoDescription = currentValue;
    }

    onDescriptionSave(e, photoInfo: AssetDetailsPhoto, description: string) {
        const update: AssetDetailsPhotoTransport = {
            assetType: '510',
            projectId: this._assetDetails.projectId,
            workOrderId: this._assetDetails.workOrderId,
            photoDatabaseId: photoInfo.photo.databaseId,
            taskId: photoInfo.taskId,
            section: photoInfo.section,
            description
        };
        this.photoDescriptionUpdate.next(update);
        this._isEditingPhotoDescription.next(false);
    }

    onDescriptionCancel(e, editor: DxTextAreaComponent) {
        this._isEditingPhotoDescription.next(false);
        editor.instance.option('value', this._originalPhotoDescription);
    }

    async onDeletePhotoClicked(e, photoInfo: AssetDetailsPhoto) {
        const photoTransport: AssetDetailsPhotoTransport = {
            assetType: '510',
            projectId: this._assetDetails.projectId,
            workOrderId: this._assetDetails.workOrderId,
            photoDatabaseId: photoInfo.photo.databaseId,
            taskId: photoInfo.taskId,
            section: photoInfo.section
        };
        const result = await confirm(
            'Are you sure you want to remove this photo?',
            'Are you sure?'
        );
        if (result) this.photoDelete.next(photoTransport);
    }

    async onDownloadClicked(fileName: string) {
        const url = await firstValueFrom(this._apm.getSignedUrl(fileName));
        const blob = await this._apm.downloadFileFromUrl(url).toPromise();
        saveAs(blob, fileName);
    }

    getAssetImage(type, blobPath) {
        return this.assetPathsArray[type][blobPath]
            ? this.assetPathsArray[type][blobPath]
            : '';
    }
    async updateAssetPaths() {
        const _assets = this._assetDetails;
        if (_assets?.frontPhotos) {
            _assets.frontPhotos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.frontPhotos[photo.blobName] = assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        }
        if (_assets?.backPhotos) {
            _assets.backPhotos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.backPhotos[photo.blobName] = assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        }

        if (_assets?.leftPhotos) {
            _assets.leftPhotos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.leftPhotos[photo.blobName] = assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        }

        if (_assets?.rightPhotos) {
            _assets.rightPhotos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.rightPhotos[photo.blobName] = assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        }
        this.assetPathLoadingCompleted = true;
    }
}
