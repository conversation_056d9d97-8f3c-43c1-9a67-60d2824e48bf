import { Component, EventEmitter, Input, Output } from '@angular/core';
import cloneDeep from 'clone-deep';
import { DxGalleryComponent, DxTextAreaComponent } from 'devextreme-angular';
import dxForm from 'devextreme/ui/form';
import { saveAs } from 'file-saver';
import { firstValueFrom } from 'rxjs';
import { IMediaEntry } from '../../../shared/models/attributes';
import {
    AssetPath,
    LeakReportInfo,
    PhotoDelete,
    PhotoDescriptionUpdate
} from '../../models';
import { ApmService } from '../../services';

@Component({
    selector: 'app-leak-report-tab',
    templateUrl: './leak-report-tab.component.html',
    styleUrls: ['./leak-report-tab.component.scss']
})
export class LeakReportTabComponent {
    private _originalPhotoDescription: string | undefined;
    private _report: LeakReportInfo;
    private _original: LeakReportInfo;
    private _changes: Partial<LeakReportInfo> = {};
    assetPathLoadingCompleted = false;
    assetPathsArray: AssetPath = {
        allPhotos: {},
        leakReportPhotos: {
            equipmentID: undefined,
            equipmentIDAtLineStart: undefined,
            pipeSize: undefined,
            processService: undefined,
            distanceBetweenTieInPoints: undefined,
            estimatedLossRate: undefined,
            featureFittingCount: undefined,
            equipmentDescription: undefined,
            equipmentIDAtLineEnd: undefined,
            pipeSchedule: undefined,
            pipeCover: undefined,
            corrosionType: undefined,
            existingClampCount: undefined,
            observationSummary: undefined,
            affectedLength: undefined
        }
    };

    @Output() saving = new EventEmitter<Partial<LeakReportInfo>>();
    @Output() photoDescriptionUpdate =
        new EventEmitter<PhotoDescriptionUpdate>();
    @Output() photoDelete = new EventEmitter<PhotoDelete>();

    @Input() allowEditing: boolean;
    @Input() set report(value: LeakReportInfo) {
        this._report = value;
        this._original = cloneDeep(this._report);
        this._changes = {};
        this.allPhotos = [
            ...value.equipmentID.photos,
            ...value.equipmentIDAtLineStart.photos,
            ...value.pipeSize.photos,
            ...value.processService.photos,
            ...value.distanceBetweenTieInPoints.photos,
            ...value.estimatedLossRate.photos,
            ...value.featureFittingCount.photos,
            ...value.equipmentDescription.photos,
            ...value.equipmentIDAtLineEnd.photos,
            ...value.pipeSchedule.photos,
            ...value.pipeCover.photos,
            ...value.corrosionType.photos,
            ...value.existingClampCount.photos,
            ...value.observationSummary.photos,
            ...value.affectedLength.photos
        ];
        this.updateAssetPaths();
    }
    get report(): LeakReportInfo {
        return this._report;
    }

    isEditing: boolean;
    showPhotoPopup: boolean;
    popupGallerySelectedIndex = 0;
    isEditingPhotoDescription = false;
    allPhotos: IMediaEntry[] | undefined;

    constructor(private readonly _apm: ApmService) {}

    onEditClicked(e) {
        this.isEditing = true;
    }

    onCancelClicked(e) {
        this.isEditing = false;
        this.report = this._original;
    }

    onSaveClicked(e) {
        this.isEditing = false;
        this.saving.next(this._changes);
    }

    thumbnailDoubleClicked(e: MouseEvent, gallery: DxGalleryComponent) {
        this.showPhotoPopup = !this.showPhotoPopup;
        const index = this.allPhotos.findIndex(
            (p) => p.databaseId === gallery.selectedItem.databaseId
        );
        this.popupGallerySelectedIndex = index;
    }

    onFieldDataChanged(e: {
        dataField: string;
        value: any;
        component: dxForm;
    }) {
        if (!e.dataField.includes('.')) return;
        const [field, valueOrComment] = e.dataField.split('.');
        this._changes = {
            ...this._changes,
            [field]: { ...this._changes[field], [valueOrComment]: e.value }
        };
    }

    onEditDescriptionClicked(e, currentValue: string) {
        this.isEditingPhotoDescription = true;
        this._originalPhotoDescription = currentValue;
    }

    onDescriptionSave(e, photoInfo: IMediaEntry, description: string) {
        const update: PhotoDescriptionUpdate = {
            reportId: this._report.id,
            photoDatabaseId: photoInfo.databaseId,
            description
        };
        this.photoDescriptionUpdate.next(update);
        this.isEditingPhotoDescription = false;
    }

    onDescriptionCancel(e, editor: DxTextAreaComponent) {
        this.isEditingPhotoDescription = false;
        editor.instance.option('value', this._originalPhotoDescription);
    }

    onDeletePhotoClicked(e, photoInfo: IMediaEntry) {
        const photoTransport: PhotoDelete = {
            reportId: this._report.id,
            photoDatabaseId: photoInfo.databaseId
        };
        this.photoDelete.next(photoTransport);
    }

    async onDownloadClicked(fileName: string) {
        const url = await firstValueFrom(this._apm.getSignedUrl(fileName));
        const blob = await this._apm.downloadFileFromUrl(url).toPromise();
        saveAs(blob, fileName);
    }

    getAssetImage(type: string, blobPath: string) {
        if (type) {
            return this.assetPathsArray.leakReportPhotos[type][blobPath]
                ? this.assetPathsArray.leakReportPhotos[type][blobPath]
                : '';
        } else {
            return this.assetPathsArray.allPhotos[blobPath]
                ? this.assetPathsArray.allPhotos[blobPath]
                : '';
        }
    }

    async updateAssetPaths() {
        const subTypes = [
            'equipmentID',
            'equipmentIDAtLineStart',
            'pipeSize',
            'processService',
            'distanceBetweenTieInPoints',
            'estimatedLossRate',
            'featureFittingCount',
            'equipmentDescription',
            'equipmentIDAtLineEnd',
            'pipeSchedule',
            'pipeCover',
            'corrosionType',
            'existingClampCount',
            'observationSummary',
            'affectedLength'
        ];
        subTypes.forEach((subType) => {
            this._report[subType].photos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.leakReportPhotos[subType][photo.blobName] =
                    assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        });
        this.assetPathLoadingCompleted = true;
    }
}
