﻿using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Moq;
using NUnit.Framework;
using OrderTracking.API.Authorization.Handlers;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Tests.Authorization.Handlers
{
    [TestFixture]
    public class UserIsModuleAdminAuthorizationHandlerTests
    {
        [SetUp]
        public void SetUp()
        {
            _mockService = new Mock<IUserProfilesService>(MockBehavior.Loose);
            _handler = new UserIsModuleAdminHandler(_mockService.Object);
        }

        private Mock<IUserProfilesService> _mockService;
        private UserIsModuleAdminHandler _handler;
        private AuthorizationHandlerContext _context;
        private UserProfile _userProfile;



        [Test]
        public async Task HandleAsync_UserIsModuleAdmin_ContextHasSucceeded()
        {
            _userProfile = new UserProfile { Id = "<EMAIL>"};
            _userProfile.Roles.Add("Some:Admin");
            _mockService.Setup(service => service.GetAsync(It.Is<string>(s => s == "<EMAIL>"))).ReturnsAsync(_userProfile);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_userProfile, null, new[] { new UserIsModuleAdminRequirement() });

            await _handler.HandleAsync(_context);

            Assert.That(_context.HasSucceeded);
        }

        [Test]
        public async Task HandleAsync_IsTeamEmployee_ContextHasFailed()
        {
            _userProfile = new UserProfile {Id = "<EMAIL>"};
            _userProfile.Roles.Add("Some:NotAdmin");
            _mockService.Setup(service => service.GetAsync(It.Is<string>(s => s == "<EMAIL>")))
                .ReturnsAsync(_userProfile);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_userProfile, null, new[] { new UserIsModuleAdminRequirement()});
            await _handler.HandleAsync(_context);
            Assert.That(!_context.HasSucceeded);
        }

        [Test]
        public async Task HandleAsync_UserIsActive_NotAuthenticated_ContextHasFailed()
        {
            _userProfile = new UserProfile { Id = "<EMAIL>" };
            _mockService.Setup((service => service.GetAsync("<EMAIL>")))
                .ReturnsAsync(_userProfile);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContextNotAuthenticated(_userProfile, _userProfile,
                new[] { new UserIsModuleAdminRequirement() });
            await _handler.HandleAsync(_context);
            Assert.That(_context.HasFailed);
        }


        [Test]
        public async Task HandleAsync_UserIsActive_UserIsNull_ContextHasFailed()
        {
            _userProfile = new UserProfile { Id = "<EMAIL>" };
            _mockService.Setup((service => service.GetAsync("<EMAIL>"))).ReturnsAsync((UserProfile)null);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_userProfile, _userProfile,
                new[] { new UserIsModuleAdminRequirement() });
            await _handler.HandleAsync(_context);
            Assert.That(_context.HasFailed);
        }
    }
}
