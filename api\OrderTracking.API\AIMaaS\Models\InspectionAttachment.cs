﻿using System;

// (These properties map to database columns and we are not in control of the naming convention)
// <PERSON><PERSON><PERSON><PERSON> disable InconsistentNaming
// <PERSON><PERSON><PERSON><PERSON> disable UnusedMember.Global
// ReSharper disable IdentifierTypo

namespace AIMaaS.Models
{
    /// <summary>
    ///     Meta data of file attachment associated with inspections in CredoSoft
    /// </summary>
    // Re<PERSON><PERSON>per disable once ClassNeverInstantiated.Global (this class is used for serialization/deserialization purposes)
    public class InspectionAttachment
    {
#pragma warning disable CA1707 // Identifiers should not contain underscores
        /// <summary>
        ///     Name of object
        /// </summary>
        public string OBJNAME { get; set; }

        /// <summary>
        ///     File name
        /// </summary>
        public string URLR_FILE_NAME { get; set; }

        /// <summary>
        ///     Url
        /// </summary>
#pragma warning disable CA1056 // URI-like properties should not be strings (This is mapped to a database column of string type)
        public string URLR_URL { get; set; }
#pragma warning restore CA1056 // URI-like properties should not be strings

        /// <summary>
        ///     No clue.
        /// </summary>
        public string URLR_PROP_TYPE_DOC { get; set; }

        /// <summary>
        ///     Time of creation
        /// </summary>
        public DateTime? URLR_CREATETIME { get; set; }

        /// <summary>
        ///     ID of object
        /// </summary>
        public long OBJID { get; set; }

        /// <summary>
        ///     ID of event management record
        /// </summary>
        public long EMID { get; set; }

        /// <summary>
        ///     No clue.
        /// </summary>
        public long URLAE_RID { get; set; }

        /// <summary>
        ///     No clue.
        /// </summary>
        public long URLR_RID { get; set; }

        /// <summary>
        ///     No clue.
        /// </summary>
        public long RSITE_RID { get; set; }
#pragma warning restore CA1707 // Identifiers should not contain underscores
    }
}