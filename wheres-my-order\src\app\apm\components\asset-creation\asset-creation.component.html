<dx-form *ngIf="formData"
         [(formData)]="formData"
         [colCount]="2">
    <dxi-item dataField="projectId"
              [editorType]="'dxSelectBox'"
              [editorOptions]="{dataSource: projectsDataSource, valueExpr: 'id', displayExpr: 'name'}">
        <dxo-label text="Project"></dxo-label>
        <dxi-validation-rule [type]="'required'"></dxi-validation-rule>
    </dxi-item>
    <dxi-item dataField="assetId">
        <dxo-label text="Asset ID"></dxo-label>
        <dxi-validation-rule [type]="'required'"></dxi-validation-rule>
    </dxi-item>
    <dxi-item dataField="assetName">
        <dxo-label text="Asset Name"></dxo-label>
        <dxi-validation-rule [type]="'required'"></dxi-validation-rule>
    </dxi-item>
    <dxi-item dataField="category"
              [editorType]="'dxSelectBox'"
              [editorOptions]="{items: assetCategories}">
        <dxi-validation-rule [type]="'required'"></dxi-validation-rule>
    </dxi-item>
    <dxi-item dataField="startLat"
              editorType="dxNumberBox">
        <dxo-label
                   [text]="formData.category === 'Piping' ? 'Start Latitude' : 'Latitude'">
        </dxo-label>
        <dxi-validation-rule [type]="'custom'"
                             [reevaluate]="true"
                             [validationCallback]="validateStartGIS"
                             message="Invalid GIS location">
        </dxi-validation-rule>
        <dxi-validation-rule [type]="'range'"
                             [min]="-90"
                             [max]="90"
                             [message]="'Latitude must be between -90 and 90'">
        </dxi-validation-rule>
    </dxi-item>
    <dxi-item dataField="startLong"
              editorType="dxNumberBox"
              [editorOptions]="{onFocusOut: startLongFocusOut}">
        <dxo-label
                   [text]="formData.category === 'Piping' ? 'Start Longitude' : 'Longitude'">
        </dxo-label>
        <dxi-validation-rule [type]="'custom'"
                             [reevaluate]="true"
                             [validationCallback]="validateStartGIS"
                             message="Invalid GIS location">
        </dxi-validation-rule>
        <dxi-validation-rule [type]="'range'"
                             [min]="-180"
                             [max]="180"
                             [message]="'Longitude must be between -90 and 90'">
        </dxi-validation-rule>
    </dxi-item>
    <dxi-item *ngIf="formData.category === 'Piping'"
              dataField="endLat"
              editorType="dxNumberBox">
        <dxo-label text="End Latitude"></dxo-label>
        <dxi-validation-rule [type]="'custom'"
                             [reevaluate]="true"
                             [validationCallback]="validateEndGIS"
                             message="Invalid GIS location">
        </dxi-validation-rule>
        <dxi-validation-rule [type]="'range'"
                             [min]="-90"
                             [max]="90"
                             [message]="'Latitude must be between -90 and 90'">
        </dxi-validation-rule>
    </dxi-item>
    <dxi-item *ngIf="formData.category === 'Piping'"
              dataField="endLong"
              editorType="dxNumberBox">
        <dxo-label text="End Longitude"></dxo-label>
        <dxi-validation-rule [type]="'custom'"
                             [reevaluate]="true"
                             [validationCallback]="validateEndGIS"
                             message="Invalid GIS location">
        </dxi-validation-rule>
        <dxi-validation-rule [type]="'range'"
                             [min]="-180"
                             [max]="180"
                             [message]="'Longitude must be between -90 and 90'">
        </dxi-validation-rule>
    </dxi-item>
</dx-form>
