﻿using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class ActivityUpdate
    {
        [JsonProperty(PropertyName = "Date")]
        public string? Date { get; set; }

        [JsonProperty(PropertyName = "clientWorkOrder")]
        public string? ClientWorkOrder { get; set; }

        [JsonProperty(PropertyName = "technician")]
        public string? Technician { get; set; }

        [JsonProperty(PropertyName = "taskType")]
        public string? TaskType { get; set; }

        [JsonProperty(PropertyName = "previousTaskType")]
        public string? PreviousTaskType { get; set; }

        [JsonProperty(PropertyName = "duration")]
        public double? Duration { get; set; }

        [JsonProperty(PropertyName = "count")]
        public double? Count { get; set; }

        [JsonProperty(PropertyName = "databaseId")]
        public string? DatabaseId { get; set; }

        [JsonProperty(PropertyName = "isInserting")]
        public bool IsInserting { get; set; }

        [JsonProperty(PropertyName = "projectId")]
        public string? ProjectId { get; set; }

        [JsonProperty(PropertyName = "taskId")]
        public string TaskID { get; set; }

        [JsonProperty(PropertyName = "activityType")]
        public string ActivityType { get; set; }
    }
}
