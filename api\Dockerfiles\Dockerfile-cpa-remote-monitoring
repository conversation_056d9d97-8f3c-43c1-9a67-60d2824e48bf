FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src
COPY . .

ARG GITLAB_DEPLOY_USERNAME
ARG GITLAB_DEPLOY_TOKEN

RUN dotnet nuget add source "https://gitlab.com/api/v4/projects/55854683/packages/nuget/index.json" --name "NuGet" --username ${GITLAB_DEPLOY_USERNAME} --password ${GITLAB_DEPLOY_TOKEN} --store-password-in-clear-text

RUN sed 's/GITLAB_DEPLOY_USERNAME/'"${GITLAB_DEPLOY_USERNAME}"'/g; s/GITLAB_DEPLOY_TOKEN/'"${GITLAB_DEPLOY_TOKEN}"'/g' ./api/OrderTracking.API/NuGet.Config > ./api/OrderTracking.API/UpdatedNuGet.Config

RUN dotnet restore "./api/OrderTracking.API/RemoteMonitoringJob/RemoteMonitoringJob.csproj" --configfile ./api/OrderTracking.API/UpdatedNuGet.Config

RUN dotnet build "./api/OrderTracking.API/RemoteMonitoringJob/RemoteMonitoringJob.csproj" -c Release -o /app/build

FROM build as publish
RUN dotnet publish "./api/OrderTracking.API/RemoteMonitoringJob/RemoteMonitoringJob.csproj" -c Release -o /app/publish

FROM base as final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "RemoteMonitoringJob.dll"]
