
﻿using System.Threading.Tasks;
using ClientPortal.Shared.Models.MOS;
using MechanicalAndOnStreamServices.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace MechanicalAndOnStreamServices
{
    internal class Program
    {
        private static async Task Main(string[] args)
        {
            using var host = CreateHostBuilder(args).Build();

            var db = host.Services.GetRequiredService<MOSContext>();
            await db.Database.MigrateAsync();
            
            var service = host.Services.GetService<ITestService>();
            service?.DisplaySummary();

            await host.RunAsync();
        }

        private static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration(config => config.AddUserSecrets<Program>())
                .ConfigureServices(ConfigureServices);

        private static void ConfigureServices(HostBuilderContext context, IServiceCollection services)
        {
            services.AddDbContext<MOSContext>();
            services.AddScoped<ITestService, TestService>();
        }
    }
}