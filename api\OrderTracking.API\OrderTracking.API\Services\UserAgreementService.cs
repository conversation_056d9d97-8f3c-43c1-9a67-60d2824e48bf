using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using ClientPortal.Shared.Helpers;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Services;
using DevExpress.XtraRichEdit;
using DevExpress.XtraRichEdit.API.Native;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     Service class for dealing with client User Agreements
    /// </summary>
    public class UserAgreementService : IUserAgreementService
    {
        private static readonly string FileName = Path.Combine(
            AppDomain.CurrentDomain.BaseDirectory,
            "Files/2021-1-12 - Client Portal Authorized User Terms of Use.docx");

        private readonly IEmailService _emails;
        private readonly IConfiguration _configuration;
        private readonly ILogger<UserAgreementService> _logger;

        /// <summary>
        ///     Constructor for UserAgreementService
        /// </summary>
        /// <param name="emails"></param>
        /// <param name="configuration"></param>
        /// <param name="logger"></param>
        public UserAgreementService(IEmailService emails, IConfiguration configuration, ILogger<UserAgreementService> logger)
        {
            _emails = emails;
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        ///     Email a PDF copy of the user agreement to the recipient
        /// </summary>
        /// <param name="recipient"></param>
        /// <param name="clientName"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task EmailPDFAsync(UserProfile recipient, string clientName)
        {
            if (recipient == null) throw new ArgumentNullException(nameof(recipient));
            var clientPortalAddress = new Uri(_configuration.GetSection("Clients:ClientPortal").Value);
            var moduleRequestAddress = new Uri(clientPortalAddress, "/#/request-access");
            var helpAndFeedbackAddress = new Uri(clientPortalAddress, "/#/help-and-feedback");
            var clientPortalAddressString = clientPortalAddress.ToString();
            var prettyClientPortalAddress = clientPortalAddressString.Remove(clientPortalAddressString.Length - 1, 1);

            var stream = await GeneratePDFStreamAsync($"{recipient.GivenName} {recipient.Surname}", clientName);

            var attachments = new List<EmailAttachment>
            {
                new() {FileName = "OneInsight User Agreement.pdf", Stream = stream}
            };

            var logo = await EmailSetup.CreateHeaderHtmlContentAsync(clientPortalAddress.ToString());

            var htmlContent = $@"
{logo}

<br>

<p>Hello {recipient.GivenName},</p>

<p>Thank you for registering on <a href=""{clientPortalAddress}"">{prettyClientPortalAddress}</a>!  You now have the ability to login and should receive access to a module soon.</p>

<br>

<p>If you would like to send a module access request, please click <a href=""{moduleRequestAddress}"">here</a>.</p>

<p>If you have forgotten your password, please click <a href=""https://teamincb2c.b2clogin.com/teamincb2c.onmicrosoft.com/b2c_1_forgotpassword/oauth2/v2.0/authorize?response_type=id_token&scope=openid%20profile&client_id=2e305521-1e55-42bf-a6ca-aa1ff0d7bff3&redirect_uri=https%3A%2F%2Fdigital.teaminc.com&state=eyJpZCI6IjhlYTY3YTY5LTNjMGMtNGU5Yy1iY2MxLTkyZDQ4MDhmZWQ1YyIsInRzIjoxNjEwNTc5Nzc5LCJtZXRob2QiOiJyZWRpcmVjdEludGVyYWN0aW9uIn0%3D&nonce=71914bd4-c706-4cbb-b295-35b37cd96a45&client_info=1&x-client-SKU=MSAL.JS&x-client-Ver=1.4.0&client-request-id=ed9aeae5-d896-474d-81f5-a916e4b25e6d&response_mode=fragment"">here</a>.</p>

<p>If you have questions about OneInsight or would like to submit an issue, please click <a href=""{helpAndFeedbackAddress}"">here</a>.</p>

<br>

<p>Our records indicate that you accepted the end user agreement on {recipient.AcceptedDisclaimerDate.ToLocalTime():D} for the email {recipient.Id}.  We�ve included a copy of the end user agreement for your records.</p>
";
            var welcomeEmail = new EmailData
            {
                Recipients = new List<IEmailRecipient> {recipient}, Subject = "Welcome to OneInsight",
                HtmlContent = htmlContent, Attachments = attachments
            };
            await _emails.SendEmail(welcomeEmail);

            // Make sure that we close the streams when we are done with them.
            foreach (var attachment in attachments)
            {
                attachment.Stream?.Close();
            }
        }

        private async Task<MemoryStream> GeneratePDFStreamAsync(string userName, string clientName)
        {
            using var server = new RichEditDocumentServer();
            var success = false;

            try
            {
                success = await server.LoadDocumentAsync(FileName, DocumentFormat.OpenXml);

            }
            catch (Exception ex)
            {
                _logger.LogWarning("error " + ex.Message + "stacktrace : " + ex.StackTrace);
            }

            if (!success)
            {
                _logger.LogWarning($"Unable to load file {FileName}.  Does file exist?  {File.Exists(FileName)}");
                return null;
            }

            FindAndReplace(server.Document, "[INSERT USER NAME]", userName);
            FindAndReplace(server.Document, "[INSERT CLIENT NAME]", clientName);

            // Don't use `using` here, it'll close the stream at the end of this method, and make
            // the streams not usable to the caller.
            var stream = new MemoryStream();
            await server.ExportToPdfAsync(stream);
            stream.Position = 0;
            return stream;
        }

        private static void FindAndReplace(SubDocument document, string textToFind, string replacementText)
        {
            var ranges = document.FindAll(textToFind, SearchOptions.None);
            foreach (var range in ranges)
            {
                document.Replace(range, replacementText);
                var characterProperties = document.BeginUpdateCharacters(range);
                characterProperties.BackColor = Color.White;
                document.EndUpdateCharacters(characterProperties);
            }
        }
    }
}