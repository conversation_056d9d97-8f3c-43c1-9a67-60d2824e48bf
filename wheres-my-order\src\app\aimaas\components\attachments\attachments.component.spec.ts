import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxDataGridModule } from 'devextreme-angular/ui/data-grid';
import { ToastrModule } from 'ngx-toastr';
import { CredoSoftService } from '../../services';
import { AttachmentsComponent } from './attachments.component';

describe('AttachmentsComponent', () => {
    let component: AttachmentsComponent;
    let fixture: ComponentFixture<AttachmentsComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ToastrModule.forRoot(), DxDataGridModule],
            declarations: [AttachmentsComponent],
            providers: [{ provide: CredoSoftService, useValue: {} }]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(AttachmentsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
