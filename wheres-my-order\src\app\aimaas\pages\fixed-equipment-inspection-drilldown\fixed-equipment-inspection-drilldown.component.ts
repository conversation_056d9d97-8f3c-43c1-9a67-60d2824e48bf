import { Location } from '@angular/common';
import {
    AfterViewInit,
    Component,
    OnDestroy,
    OnInit,
    ViewChild
} from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { DxDataGridComponent } from 'devextreme-angular/ui/data-grid';
import ArrayStore from 'devextreme/data/array_store';
import DataSource from 'devextreme/data/data_source';
import { exportDataGrid } from 'devextreme/excel_exporter';
import dxDataGrid from 'devextreme/ui/data_grid';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver';
import { combineLatest, Subscription } from 'rxjs';
import { filter, finalize } from 'rxjs/operators';
import { UserProfile } from '../../../profile/models/user-profile';
import { Breadcrumb } from '../../../shared/components';
import { DataGridService, UsersService } from '../../../shared/services';
import {
    AlarmCalc,
    AnomaliesRecommendations,
    Asset,
    AssetAttachment,
    AssetComponent,
    AssetInspection,
    AssetManagementSite,
    InspectionAttachment,
    RowExpandingHandler
} from '../../models';
import { SiteLabelPipe } from '../../pipes';
import { CredoSoftService } from '../../services';
@Component({
    selector: 'app-fixed-equipment-inspection-drilldown',
    templateUrl: './fixed-equipment-inspection-drilldown.component.html',
    styleUrls: ['./fixed-equipment-inspection-drilldown.component.scss']
})
export class FixedEquipmentInspectionDrilldownComponent
    implements OnInit, AfterViewInit, OnDestroy
{
    isLoading: boolean;
    selectedAssetID: any;
    lastDateOfSelectedAsset: any;
    popupVisible = false;
    inspectionDataSource: DataSource;
    assets: Asset[] = [];
    components: any[] = [];
    alarmCalcs: AlarmCalc[] = [];
    inspectionComponentMap: object = {};
    currentFilter: any = [];
    userId: string;
    generalInspectionDetails: AssetInspection[];
    visibleInspectionAnamolies: AnomaliesRecommendations[];
    inspectionAnamolies: AnomaliesRecommendations[] = [];
    corrosionAnalysis: any;
    inspectionAttachments: InspectionAttachment[] = [];
    assetAttachments: AssetAttachment[] = [];
    visibleInspectionAttachments: InspectionAttachment[];
    visibleAssetAttachments: AssetAttachment[];
    tabs: { title: string; template: string }[] = [];
    currentComponentCalcs: AlarmCalc;
    selectedAsset: Asset;
    availableSites: AssetManagementSite[];
    selectedSite: AssetManagementSite;
    anomaliesData: any;
    submissionPopupVisible: boolean = false;
    initialAnomaly: AnomaliesRecommendations = null;
    submissionPopupTitle: string = 'Anomaly Update';
    breaCrumbLabel: string = 'Inspections List';
    selectedOperationId: any;
    currentUser: UserProfile;
    allInspections: AssetInspection[] = [];
    canShowInspectionList: boolean = true;
    assetDetailsPopupVisible: boolean = false;
    private routerSubscription: Subscription;
    currentAssetId: string;
    currentAssetDetails: Asset;
    inspectionSchedule: any[];
    assetComponentMap: AssetComponent[];
    equipmentPopupTitle: string = ' ';
    inspectionPopupTitle: string = ' ';
    saveState = (state) => {
        this._users.currentProfile$.subscribe((user: any) => {
            this.userId = user.id;
            //    console.log(this.userId, 'current user email');
        });
        this.addUserPreferences(
            this.userId,
            'inspection',
            JSON.stringify(state)
        );
    };
    loadState = async () => {
        try {
            const response = await this.getUserPreference();
            if (response && response.inspection) {
                const equipmentValue = JSON.parse(response.inspection);
                equipmentValue.filterValue = this.currentFilter;
                return equipmentValue;
            } else {
                console.error('No equipment preference found in response.');
                return null;
            }
        } catch (error) {
            console.error('Error loading preference:', error);
            return null;
        }
    };
    addUserPreferences = (
        useri: string,
        storageKey: string,
        values: string
    ) => {
        if (this.credoService) {
            this.credoService
                .addPreference(useri, storageKey, values)
                .subscribe(
                    (response) => {
                        console.log('Preference added successfully:', response);
                    },
                    (error) => {
                        //    console.error('Error adding preference:', error);
                    }
                );
        } else {
            console.error('CredoService is undefined!');
        }
    };
    @ViewChild(DxDataGridComponent)
    grid: DxDataGridComponent;
    isDataLoaded: boolean = false;

    constructor(
        private readonly credoService: CredoSoftService,
        private readonly _sitePipe: SiteLabelPipe,
        private readonly _users: UsersService,
        private readonly _grid: DataGridService,
        private route: ActivatedRoute,
        private router: Router,
        private location: Location
    ) {}

    crumbs: Breadcrumb[];

    ngAfterViewInit(): void {
        this.grid.instance.beginCustomLoading('');
    }
    getUserPreference(): Promise<any> {
        this._users.currentProfile$.subscribe((user: any) => {
            this.userId = user.id;
            // console.log(this.userId, 'current user email');
        });
        return new Promise((resolve, reject) => {
            this.credoService
                .getPreference(this.userId, 'inspection')
                .subscribe(
                    (response) => {
                        if (response && response.inspection) {
                            resolve(response);
                        } else {
                            reject(
                                'No equipment preference found in response.'
                            );
                        }
                    },
                    (error) => {
                        reject('Error fetching preference: ' + error);
                    }
                );
        });
    }
    addUserPreference(useri: string, storageKey: string, values: string): void {
        if (this.credoService) {
            this.credoService
                .addPreference(useri, storageKey, values)
                .subscribe(
                    (response) => {
                        // console.log('Preference added successfully:', response);
                    },
                    (error) => {
                        // console.error('Error adding preference:', error);
                    }
                );
        } else {
            console.error('CredoService is undefined!');
        }
    }
    onCellPrepared(event) {
        if (event.rowType == 'data' || this.isDataLoaded) {
            this.isLoading = false;
        } else {
            this.isLoading = true;
        }
    }
    ngOnInit(): void {
        combineLatest([
            this.credoService.getAllInspectionsAsDataSource(),
            this.credoService.assets$,
            this.credoService.inspections$,
            this.credoService.assetManagementSites$,
            this.credoService.anomalies$,
            this._users.currentProfile$
        ])
            .pipe(finalize(() => this.grid.instance.endCustomLoading()))
            .subscribe(
                ([ds, assets, inspections, sites, anomalies, currentUser]: //
                [
                    DataSource,
                    Asset[],
                    AssetInspection[],
                    AssetManagementSite[],
                    AnomaliesRecommendations[],
                    UserProfile
                ]) => {
                    this.inspectionDataSource = ds;
                    this.inspectionDataSource.load();
                    this.assets = assets;
                    this.allInspections = inspections;
                    this.currentUser = currentUser;
                    if (history.state?.data?.currentFilter)
                        this.currentFilter = history.state.data.currentFilter;
                    this.availableSites = sites;
                    this.userId = currentUser.email;
                    this.inspectionAnamolies = anomalies;
                    const roles = currentUser.roles.map((role) =>
                        role.toLowerCase()
                    );
                    if (roles) {
                        if (roles.includes('aimaas:demo')) {
                            this.availableSites = this.availableSites.filter(
                                (site) =>
                                    site.locationid ==
                                    Number('635140707384299520')
                            );
                        } else if (
                            !roles.includes('app:admin') &&
                            !roles.includes('aimaas:admin') &&
                            currentUser.assetManagementSiteIds
                        ) {
                            this.availableSites = this.availableSites.filter(
                                (site) =>
                                    currentUser.assetManagementSiteIds.includes(
                                        site.locationid
                                    )
                            );
                        }
                    }

                    this.selectedSite =
                        this.availableSites.find(
                            (site) =>
                                site.locationid ==
                                Number(localStorage.getItem('selectedSite'))
                        ) ?? this.availableSites[0];
                    this.isLoading = false;
                    this.isDataLoaded = true;
                    this.route.paramMap.subscribe(() => {
                        this.handleQueryParams();
                    });
                    this.routerSubscription = this.router.events
                        .pipe(
                            filter((event) => event instanceof NavigationEnd),
                            filter(() =>
                                this.router.url.includes(
                                    'aimaas/inspection-drilldown'
                                )
                            )
                        )
                        .subscribe(() => {
                            this.handleQueryParams();
                        });
                    this.updateBreadcrumbs();
                }
            );
    }
    ngOnDestroy(): void {
        if (this.routerSubscription) {
            this.routerSubscription.unsubscribe();
        }
    }
    handleQueryParams() {
        this.route.queryParams.subscribe((params) => {
            const operationId = params['op'];
            const scheduleId = params['sched'];
            // Early exit if no operationId or scheduleId
            if (!operationId && !scheduleId) {
                this.canShowInspectionList = true;
                return;
            }

            // Filter inspections based on the query params
            let inspectionDetails = this.allInspections?.filter(
                (insp) =>
                    (operationId
                        ? insp.planoperationid == operationId
                        : true) &&
                    (scheduleId ? insp.scheduleid == scheduleId : true)
            );
            // If no inspections found, do not show the list
            if (!inspectionDetails?.length) {
                this.canShowInspectionList = false;
                return;
            }

            // Check if the inspection's location matches the selected site's location
            const inspectionLocationId = inspectionDetails[0].locationid;
            if (String(this.selectedSite.locationid) !== inspectionLocationId) {
                const filteredAssetSite = this.availableSites.find(
                    (site) => site.locationid == Number(inspectionLocationId)
                );

                // If a matching site is found, change the site and update the inspection
                if (filteredAssetSite) {
                    this.changeSite({ selectedItem: filteredAssetSite });
                    this.inspectionSelectionChanged({
                        data: inspectionDetails[0]
                    });
                    this.canShowInspectionList = true;
                } else {
                    this.canShowInspectionList = false;
                }
            } else {
                // If the inspection's location matches the selected site, update the inspection directly
                this.inspectionSelectionChanged({ data: inspectionDetails[0] });
                this.canShowInspectionList = true;
            }
        });
    }
    assetSelectionChanged(data) {
        const inspection = data?.row?.data;

        this.location.replaceState(
            `/aimaas/drilldown?assetid=${inspection?.assetid}`
        );
        const asset = this.assets?.find(
            (asset) => asset.id == inspection?.assetid
        );
        this.assetComponentMap = [];
        var comp = this.assetComponentMap;
        this.currentAssetId = String(asset?.id);
        this.credoService.getAllComponents(asset.id).subscribe((data) => {
            this.assetComponentMap = data;
            comp = this.assetComponentMap.sort((a, b) => {
                const clientCompare = a.componentname.localeCompare(
                    b.componentname
                );
                if (clientCompare !== 0) {
                    return clientCompare;
                }
            });
        });
        this.currentAssetDetails = asset;
        this.inspectionSchedule = this.getInspectionsForAsset(String(asset.id));
        this.equipmentPopupTitle = `EQUIPMENT - ${asset.assetid}`;
        this.assetDetailsPopupVisible = true;
    }
    getInspectionsForAsset(id: string): AssetInspection[] {
        if (!this.allInspections) {
            return [];
        }

        return this.allInspections?.filter((item) => item.assetid === id);
    }
    restoreAssetsDefaultsClicked = async (e) => {
        const result = await this._grid.resetGridState(this.grid);
        if (result) {
            this.addUserPreferences(
                this.userId,
                'inspection',
                JSON.stringify('')
            );
        }
    };
    CompleteHistoryButtonClicked(event) {
        if (event) {
            this.currentFilter = [['assetidname', '=', event]];
            this.assetDetailsPopupVisible = false;
        }
    }
    clientSubmissionTitleValueChange(e: any) {
        this.submissionPopupTitle = e;
    }
    clientDataFormSubmitted(e: any) {
        this.submissionPopupVisible = false;
    }
    clientSubmitDataOnclick(e: string) {
        if (e === 'frombuttonclick') {
            this.initialAnomaly = null;
        }
        this.submissionPopupVisible = !this.submissionPopupVisible;
    }
    updateBreadcrumbs() {
        if (history.state?.breadCrumbLabel) {
            this.breaCrumbLabel = history.state.breadCrumbLabel
                ? history.state.breadCrumbLabel
                : 'Inspections';
        }

        this.crumbs = [
            { label: 'KPI Dashboards', route: '/aimaas/dashboards' },
            { label: this.breaCrumbLabel, route: '/aimaas/drilldown' }
        ];
    }
    onContentReady(event) {
        this.grid?.instance?.endCustomLoading();
    }

    getComponentsForInspection(key: any): any {
        return (this.inspectionComponentMap[key.objid] = this
            .inspectionComponentMap[key.objid] || {
            store: new ArrayStore({
                data: this.components.filter(
                    (component) => component.objparent === key.objid
                )
            })
        });
    }

    inspectionSelectionChanged(e) {
        this.popupVisible = true;
        const inspection = e.data;
        if (
            inspection?.scheduleid !== null &&
            inspection?.planoperationid !== null
        ) {
            this.location.replaceState(
                `/aimaas/inspection-drilldown?op=${inspection.planoperationid}&sched=${inspection.scheduleid}`
            );
        } else if (inspection?.planoperationid !== null) {
            this.location.replaceState(
                `/aimaas/inspection-drilldown?op=${inspection.planoperationid}`
            );
        } else if (inspection?.operationid !== null) {
            this.location.replaceState(
                `/aimaas/inspection-drilldown?sched=${inspection.scheduleid}`
            );
        }
        this.inspectionPopupTitle =
            inspection.operationtype !== null
                ? `INSPECTION - ${inspection?.assetidname} - ${
                      inspection?.operationtype
                  } - ${inspection?.date.replace(/-/g, '/')} `
                : `SCHEDULE - ${inspection?.assetidname} - ${
                      inspection?.scheduletype
                  } - ${
                      inspection?.nextinspectiondue
                          ? inspection.nextinspectiondue.replace(/-/g, '/')
                          : ''
                  }`;
        this.generalInspectionDetails = inspection;
        this.selectedAssetID = inspection.assetid;
        this.lastDateOfSelectedAsset = inspection.lastdate;
        this.selectedOperationId = inspection.planoperationid;
        this.visibleInspectionAttachments = this.inspectionAttachments.filter(
            (attachment) =>
                attachment.inspectionid === inspection.planoperationid
        );
        this.visibleAssetAttachments = this.assetAttachments.filter(
            (attachment) => attachment.CLIENTID === inspection.objid
        );

        this.visibleInspectionAnamolies = this.inspectionAnamolies.filter(
            (anomaly) => anomaly.operationid == inspection.planoperationid
        );
        this.selectedAsset = this.assets.find(
            (asset) => asset.id === inspection.assetid
        );

        this.tabs = [
            {
                title: 'Asset Details',
                template: 'asset-details'
            },
            {
                title: 'Inspection Attachments',
                template: 'inspection-attachments'
            },
            {
                title: 'Asset Attachments',
                template: 'asset-attachments'
            }
        ];
    }
    closePopup() {
        this.popupVisible = false;
        this.equipmentPopupTitle = ' ';
        this.inspectionPopupTitle = ' ';
        this.location.replaceState('/aimaas/inspection-drilldown');
    }
    componentSelectionChanged(e) {
        const component = e.selectedRowsData[0];
        this.currentComponentCalcs = this.alarmCalcs.find(
            (calc) => calc.objid === component.objid
        );
        this.tabs = [
            {
                title: 'Asset Details',
                template: 'asset-details'
            },
            {
                title: 'Calculations',
                template: 'calculations'
            }
        ];
    }

    getCurrentCompnentCalcs() {}

    onRowExpanding(e: {
        cancel: boolean;
        component: dxDataGrid;
        element: HTMLElement;
        key: any;
        model: any;
    }) {
        RowExpandingHandler.handle(e);
    }

    customDisplayExpr = (site: any): string => {
        if (site) return this._sitePipe.transform(site);
    };
    changeSite(selectedSite) {
        let _selectedSite = selectedSite.selectedItem;
        if (_selectedSite == null) {
            setTimeout(() => {
                this.selectedSite = this.availableSites[0];
                localStorage.setItem(
                    'selectedSite',
                    String(this.selectedSite.locationid)
                );
            }, 1);
        } else {
            this.selectedSite = selectedSite.selectedItem;
            localStorage.setItem(
                'selectedSite',
                String(this.selectedSite.locationid)
            );
        }
        this.inspectionDataSource.filter(
            (inspection) =>
                inspection.locationid === this.selectedSite.locationid
        );
        this.inspectionDataSource.load();
    }

    async onExporting(event) {
        const workbook = new Workbook();
        const worksheet = workbook.addWorksheet('Inspections');
        await exportDataGrid({
            component: event.component,
            worksheet
        });
        const buffer: BlobPart = await workbook.xlsx.writeBuffer();
        saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'Inspections.xlsx'
        );
    }
}
