import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { DxLoadIndicatorModule } from 'devextreme-angular';
import { OverviewBoxComponent } from '../../../shared/components';
import { OverviewBoxesComponent } from './overview-boxes.component';

describe('OverviewBoxesComponent', () => {
    let component: OverviewBoxesComponent;
    let fixture: ComponentFixture<OverviewBoxesComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [RouterTestingModule, DxLoadIndicatorModule],
            declarations: [OverviewBoxesComponent, OverviewBoxComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(OverviewBoxesComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
