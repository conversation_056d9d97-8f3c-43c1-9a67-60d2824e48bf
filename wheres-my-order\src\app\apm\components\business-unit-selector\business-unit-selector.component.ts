import { Component, ViewChild } from '@angular/core';
import { NavigationStart, Router } from '@angular/router';
import { DxSelectBoxComponent } from 'devextreme-angular';
import { SelectionChangedEvent } from 'devextreme/ui/select_box';
import { ApmService } from '../../../apm/services';
import { UsersService } from '../../../shared/services';

@Component({
    selector: 'app-business-unit-selector',
    templateUrl: './business-unit-selector.component.html',
    styleUrls: ['./business-unit-selector.component.scss']
})
export class BusinessUnitSelectorComponent {
    @ViewChild(DxSelectBoxComponent) selectBox: DxSelectBoxComponent;
    currentRoute = null;
    currentUser$ = this._users.currentProfile$;
    showSelector =
        !this._router.url.includes('client-management') &&
        !this._router.url.includes('work-order-details') &&
        !this._router.url.includes('assign-tasks') &&
        !this._router.url.includes('edit-task');

    constructor(
        private readonly _router: Router,
        private readonly _users: UsersService,
        private readonly _apm: ApmService
    ) {
        this._router.events.subscribe((event) => {
            if (event instanceof NavigationStart) {
                this.showSelector =
                    !event.url.includes('client-management') &&
                    !event.url.includes('work-order-details') &&
                    !event.url.includes('assign-tasks') &&
                    !event.url.includes('edit-task');
            }
        });
    }

    businessUnits$ = this._apm.getMyBusinessUnits();

    selectBusinessUnit(e: SelectionChangedEvent) {
        this._apm.selectBusinessUnit(e.selectedItem.id);
    }
}
