import { Location } from '../data/location';

export class ProjectDetail {
    projectName: string;
    teamDistrictNumber: string;
    clientName: string;
    apmProjectNumber: string;
    teamProjectNumber: string;
    contactName: string;
    location: Location;
    contactTitle: string;
    projectCity: string;
    clientProjectNumber: string;
    contactPhoneNumber: string;
    projectState: string;
    contactEmail: string;
    description: string;
    status: string;
    plannedStart: Date;
    plannedEnd: Date;
    id: string;

    public constructor(init?: Partial<ProjectDetail>) {
        Object.assign(this, init);
    }
}
