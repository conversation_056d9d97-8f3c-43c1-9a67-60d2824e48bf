import { Pipe, PipeTransform } from '@angular/core';

import { UserProfile } from '../../../profile/models';
import { Role } from '../../models';
import { ChangeEvent } from '../models/change-event';

@Pipe({
    name: 'diff',
})
export class DiffPipe implements PipeTransform {
    transform(change: ChangeEvent<Role | UserProfile>): object {
        const diff = ChangeEvent.diff(change.old, change.new);
        return diff;
    }
}
