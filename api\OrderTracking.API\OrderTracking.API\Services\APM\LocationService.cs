﻿using System.Linq;
using System.Threading.Tasks;
using APMWebDataInterface.ExampleDataModel;
using OrderTracking.API.Extensions.APM;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Services.APM
{
    public interface ILocationService
    {
        /// <summary>
        ///     Get Locations
        /// </summary>
        /// <returns></returns>
        /// <param name="email"></param>
        public Task<Location[]> Get(string email);

        /// <summary>
        ///     Get a location by id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task<Location> Get(string id, string email);

        /// <summary>
        ///     Updates a location from a <see cref="ProjectTransportObject"/>
        /// </summary>
        /// <param name="location"></param>
        /// <param name="projectUpdate"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task Update(Location location, ProjectTransportObject projectUpdate, string email);

        /// <summary>
        ///     Creates a location from a <see cref="LocationTransportObject"/>
        /// </summary>
        /// <param name="locationTransport"></param>
        /// <param name="email"></param>
        /// <param name="businessUnitId"></param>
        /// <returns></returns>
        Task<Location> Create(LocationTransportObject locationTransport, string email, string businessUnitId);

        /// <summary>
        ///     Updates a location from a <see cref="LocationTransportObject"/>
        /// </summary>
        /// <param name="location"></param>
        /// <param name="locationTransport"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task Update(Location location, LocationTransportObject locationTransport, string email);
    }

    public class LocationService : ILocationService
    {
        private readonly APM_WebDataInterface _apm;

        public LocationService(APM_WebDataInterface apm)
        {
            _apm = apm;
        }

        /// <summary>
        ///     Get Locations
        /// </summary>
        /// <returns></returns>
        /// <param name="email"></param>
        public async Task<Location[]> Get(string email)
        {
            return await _apm.GetLocations(email);
        }

        /// <summary>
        ///     Get a location by id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task<Location> Get(string id, string email)
        {
            var locations = await _apm.GetLocations(email);
            var location = locations.FirstOrDefault(l => l.id == id);
            return location;
        }

        /// <summary>
        ///     Updates a location from a <see cref="ProjectTransportObject"/>
        /// </summary>
        /// <param name="location"></param>
        /// <param name="projectUpdate"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task Update(Location location, ProjectTransportObject projectUpdate, string email)
        {
            location.Update(projectUpdate);
            await location.SavePendingChanges(email);
        }

        /// <summary>
        ///     Creates a location from a <see cref="LocationTransportObject"/>
        /// </summary>
        /// <param name="locationTransport"></param>
        /// <param name="email"></param>
        /// <param name="businessUnitId"></param>
        /// <returns></returns>
        public async Task<Location> Create(LocationTransportObject locationTransport, string email, string businessUnitId)
        {
            var location = new Location();
            location.Update(locationTransport);
            location.businessUnitId.SetValue(businessUnitId);
            await location.SavePendingChanges(email);
            return location;
        }

        /// <summary>
        ///     Updates a location from a <see cref="LocationTransportObject"/>
        /// </summary>
        /// <param name="location"></param>
        /// <param name="locationTransport"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task Update(Location location, LocationTransportObject locationTransport, string email)
        {
            location.Update(locationTransport);
            await location.SavePendingChanges(email);
        }
    }
}