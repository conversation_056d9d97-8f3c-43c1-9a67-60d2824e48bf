<div class="responsive-paddings content-block details-tab">
    <dx-form #form
             *ngIf="report"
             [(formData)]="report"
             [colCount]="2"
             [readOnly]="!isEditing || !allowEditing"
             (onFieldDataChanged)="onFieldDataChanged($event)">

        <!-- EQUIPMENT ID -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Equipment ID"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="equipmentID.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="equipmentID.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.equipmentID?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('equipmentID',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- EQUIPMENT ID AT LINE START -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Equipment ID at Line START"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="equipmentIDAtLineStart.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="equipmentIDAtLineStart.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.equipmentIDAtLineStart?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('equipmentIDAtLineStart',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- PIPE SIZE -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Pipe Size"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="pipeSize.value"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: report?.pipeSize.options}">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="pipeSize.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.pipeSize?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('pipeSize',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- PROCESS / SERVICE -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Process/Service"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="processService.value"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: report?.processService.options, acceptCustomValue: true}">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="processService.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.processService?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('processService',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- DISTANCE BETWEEN TIE-IN POINTS -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Distance Between Tie-in Points"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="distanceBetweenTieInPoints.value"
                          editorType="dxNumberBox">
                    <dxo-label text="Value">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="distanceBetweenTieInPoints.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.distanceBetweenTieInPoints?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('distanceBetweenTieInPoints',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- ESTIMATED LOSS RATE -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Estimated Loss Rate"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="estimatedLossRate.value"
                          editorType="dxNumberBox">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="estimatedLossRate.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.estimatedLossRate?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('estimatedLossRate',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- FEATURE / FITTING COUNT (SAME LINE) -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Feature/Fitting Count (Same Line)"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="featureFittingCount.value"
                          editorType="dxNumberBox">
                    <dxo-label text="Value">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="featureFittingCount.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.featureFittingCount?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('featureFittingCount',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- EQUIPMENT DESCRIPTION -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Equipment Description"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="equipmentDescription.value"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: report?.equipmentDescription.options}">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="equipmentDescription.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.equipmentDescription?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('equipmentDescription',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- EQUIPMENT ID AT LINE END -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Equipment ID at Line End"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="equipmentIDAtLineEnd.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="equipmentIDAtLineEnd.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.equipmentIDAtLineEnd?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('equipmentIDAtLineEnd',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- PIPE SCHEDULE -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Pipe Schedule"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="pipeSchedule.value"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: report?.pipeSchedule.options}">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="pipeSchedule.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.pipeSchedule?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('pipeSchedule',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- PIPE COVER -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Pipe Cover"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="pipeCover.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="pipeCover.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.pipeCover?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('pipeCover',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- CORROSION TYPE -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Corrosion Type"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="corrosionType.value"
                          editorType="dxTagBox"
                          [editorOptions]="{items: report.corrosionType.options}">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="corrosionType.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.corrosionType?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('corrosionType',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- EXISTING CLAMP COUNT (SAME LINE) -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Existing Clamp Count (Same Line)"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="existingClampCount.value"
                          editorType="dxNumberBox">
                    <dxo-label text="Value">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="existingClampCount.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.existingClampCount?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('existingClampCount',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- OBSERVATION SUMMARY -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Observation Summary"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="observationSummary.value">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="observationSummary.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.observationSummary?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('observationSummary',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <!-- Affected Length -->
        <dxi-item itemType="group"
                  [colCount]="2"
                  caption="Affected Length"
                  cssClass="item-bottom-spacing">
            <dxi-item itemType="group">
                <dxi-item dataField="affectedLength.value"
                          editorType="dxNumberBox">
                    <dxo-label text="Value"></dxo-label>
                </dxi-item>
                <dxi-item dataField="affectedLength.comment">
                    <dxo-label text="Comment"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item>
                <div *dxTemplate>
                    <dx-gallery #thumbnailGallery
                                [dataSource]="report?.affectedLength?.photos"
                                [showNavButtons]="true"
                                [height]="105"
                                noDataText="No Photos"
                                (dblclick)="thumbnailDoubleClicked($event, thumbnailGallery)">
                        <div *dxTemplate="let galleryItem of 'item'">
                            <div *ngIf="galleryItem.blobName">
                                <img [height]="150"
                                     *ngIf="assetPathLoadingCompleted"
                                     [src]="getAssetImage('affectedLength',galleryItem.blobName)" />
                            </div>
                        </div>
                    </dx-gallery>
                </div>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="empty"></dxi-item>

        <dxi-item itemType="group"
                  [colCount]="1"
                  [colSpan]="2">
            <dxi-item [template]="'buttons'"></dxi-item>
            <div *dxTemplate="let data of 'buttons'"
                 class="buttons">
                <dx-button *ngIf="!isEditing; else saveAndCancel"
                           text="Edit"
                           type="default"
                           [disabled]="!allowEditing"
                           (onClick)="onEditClicked($event)"></dx-button>
                <ng-template #saveAndCancel>
                    <dx-button text="Cancel"
                               type="danger"
                               [disabled]="!allowEditing"
                               (onClick)="onCancelClicked($event, form)">
                    </dx-button>
                    <dx-button text="Save"
                               type="success"
                               [disabled]="!allowEditing"
                               (onClick)="onSaveClicked($event)"></dx-button>
                </ng-template>
            </div>
        </dxi-item>

    </dx-form>

</div>

<dx-popup [(visible)]="showPhotoPopup"
          [showTitle]="true"
          [showCloseButton]="true"
          [hideOnOutsideClick]="true"
          [title]="'Photo'"
          #popup>
    <dx-gallery #gallery
                id="gallery"
                [dataSource]="allPhotos"
                [loop]="true"
                width="100%"
                height="100%"
                [showNavButtons]="true"
                [showIndicator]="true"
                [(selectedIndex)]="popupGallerySelectedIndex">

        <div *dxTemplate="let photoInfo of 'item'"
             class="popup-template">

            <div *ngIf="photoInfo?.blobName"
                 class="image-container">
                <img *ngIf="assetPathLoadingCompleted"
                     [src]="getAssetImage(null,photoInfo?.blobName)"
                     alt="">

            </div>
            <div class="info">
                <div class="text-content">
                    <h6>Description</h6>
                    <dx-text-area #descriptionEditor
                                  [readOnly]="!isEditingPhotoDescription"
                                  [value]="photoInfo?.description?.currentValue"
                                  [autoResizeEnabled]="true">
                    </dx-text-area>
                    <div class="description-buttons">
                        <dx-button *ngIf="!isEditingPhotoDescription; else saveCancelPhotoDescription"
                                   icon="edit"
                                   text="Edit"
                                   [disabled]="!allowEditing"
                                   (onClick)="onEditDescriptionClicked($event, photoInfo?.description?.currentValue)">
                        </dx-button>
                        <ng-template #saveCancelPhotoDescription>
                            <dx-button icon="save"
                                       text="Save"
                                       type="success"
                                       [disabled]="!allowEditing"
                                       (onClick)="onDescriptionSave($event, photoInfo, descriptionEditor.value)">
                            </dx-button>
                            <dx-button text="Cancel"
                                       type="danger"
                                       [disabled]="!allowEditing"
                                       (onClick)="onDescriptionCancel($event, descriptionEditor)">
                            </dx-button>
                        </ng-template>
                    </div>
                </div>
                <div class="buttons">

                    <dx-button icon="remove"
                               text="Delete"
                               type="danger"
                               [disabled]="!allowEditing"
                               (onClick)="onDeletePhotoClicked($event, photoInfo)">
                    </dx-button>

                    <dx-button icon="download"
                               text="Download"
                               type="default"
                               (onClick)="onDownloadClicked(photoInfo?.blobName)">
                    </dx-button>
                </div>
            </div>
        </div>
    </dx-gallery>
</dx-popup>