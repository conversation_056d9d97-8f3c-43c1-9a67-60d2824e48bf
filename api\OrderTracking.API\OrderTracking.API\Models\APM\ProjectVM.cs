﻿using APMWebDataInterface;
using APMWebDataInterface.DataModel.Activity;
using APMWebDataInterface.ExampleDataModel;
using Newtonsoft.Json;
using System;
using CommonDataInterface;

namespace OrderTracking.API.Models.APM
{
    public class ProjectVM
    {
        [JsonProperty(PropertyName = "id")]
        public string Id { get; set; }
        [JsonProperty(PropertyName = "name")]
        public string Name { get; set; }
        [JsonProperty(PropertyName = "apmProjectNumber")]
        public string APMProjectNumber { get; set; }
        [JsonProperty(PropertyName = "plannedStart")]
        public DateTime? PlannedStart { get; set; }
        [JsonProperty(PropertyName = "plannedEnd")]
        public DateTime? PlannedEnd { get; set; }
        [JsonProperty(PropertyName = "teamProjectNumber")]
        public string TeamProjectNumber { get; set; }
        [JsonProperty(PropertyName = "status")]
        public string Status { get; set; }
        [JsonProperty(PropertyName = "locationId")]
        public string LocationId { get; set; }
        [JsonProperty(PropertyName = "teamDistrictNumber")]
        public string TeamDistrictNumber { get; set; }
        [JsonProperty(PropertyName = "description")]
        public string Description { get; set; }
        [JsonProperty(PropertyName = "clientProjectNumber")]
        public string ClientProjectNumber { get; set; }
        [JsonProperty(PropertyName = "clientName")]
        public string ClientName { get; set; }
        [JsonProperty(PropertyName = "assetIds")]
        public string[] AssetIds { get; set; }
        [JsonProperty(PropertyName = "contacts")]
        public DataModelCollection<Contact> Contacts { get; set; }
        [JsonProperty(PropertyName = "activities")]
        public DataModelCollection<ProjectActivity> Activities { get; set; }
        [JsonProperty(PropertyName = "businessUnitId")]
        public string BusinessUnitId { get; set; }

    }
}
