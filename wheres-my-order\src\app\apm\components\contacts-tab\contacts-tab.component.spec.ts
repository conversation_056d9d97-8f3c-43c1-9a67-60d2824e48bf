import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
    DxButtonModule,
    DxFormModule,
    DxListModule,
    DxPopupModule
} from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { ApmService } from '../../services';
import { ContactsTabComponent } from './contacts-tab.component';

describe('ContactsTabComponent', () => {
    let component: ContactsTabComponent;
    let fixture: ComponentFixture<ContactsTabComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                HttpClientTestingModule,
                DxFormModule,
                DxButtonModule,
                DxPopupModule,
                DxListModule,
                ToastrModule.forRoot()
            ],
            declarations: [ContactsTabComponent],
            providers: [{ provide: ApmService, useValue: {} }]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ContactsTabComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
