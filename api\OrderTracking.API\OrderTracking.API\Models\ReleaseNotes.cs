using System;
// Migrated from Google Cloud Firestore to Azure Cosmos DB
// using Google.Cloud.Firestore;
using Newtonsoft.Json;

namespace OrderTracking.API.Models
{
    /// <summary>
    ///     Release notes model for OneInsight (migrated to Azure Cosmos DB)
    /// </summary>
    public class ReleaseNotes
    {
        /// <summary>
        ///     Unique identifier for release notes record
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; }

        /// <summary>
        ///     The email address of the user who created the release notes record
        /// </summary>
        [JsonProperty("createdBy")]
        public string CreatedBy { get; set; }

        /// <summary>
        ///     The release notes contents
        /// </summary>
        [JsonProperty("notes")]
        public string Notes { get; set; }

        /// <summary>
        ///     When the release note was created.
        /// </summary>
        [JsonProperty("createdAt")]
        public DateTime CreatedAt { get; set; }
    }
}