﻿using System;
using System.Reflection;
using Microsoft.Azure.Cosmos;

namespace ClientPortal.Shared.Models
{
    /// <summary>
    ///     Extension method class for model classes.
    /// </summary>
    public static class ModelExtensions
    {
        /// <summary>
        ///     A T extension method that gets property value.
        /// </summary>
        /// <typeparam name="T">Generic type parameter.</typeparam>
        /// <param name="this">The @this to act on.</param>
        /// <param name="propertyName">Name of the property.</param>
        /// <returns>The property value.</returns>
        public static object GetPropertyValue<T>(this T @this, string propertyName)
        {
            Type type = @this.GetType();
            PropertyInfo property = type.GetProperty(propertyName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static);

            return property.GetValue(@this, null);
        }

        /// <summary>
        ///     Returns a <see cref="PartitionKey"/> instance for a particular property of a C# class.
        ///     <remarks>This should only be used for models that represent data being stored in Cosmos DB.</remarks>
        /// </summary>
        /// <param name="this"></param>
        /// <param name="propertyName"></param>
        /// <typeparam name="TEntity"></typeparam>
        /// <returns></returns>
        public static PartitionKey GetPartitionKey<TEntity>(this TEntity @this, string propertyName) 
        {
            return new PartitionKey(@this.GetPropertyValue(propertyName).ToString());
        }

    }
}
