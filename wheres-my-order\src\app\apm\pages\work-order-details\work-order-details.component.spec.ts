import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import {
    DxButtonModule,
    DxCheckBoxModule,
    DxDataGridModule,
    DxDateBoxModule,
    DxFormModule,
    DxGalleryModule,
    DxLoadIndicatorModule,
    DxPopupModule,
    DxSelectBoxModule,
    DxTabPanelModule,
    DxTagBoxModule,
    DxTextAreaModule,
    DxTextBoxModule,
    DxTileViewModule
} from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import { HasRolePipe, HasRolesPipe, SafePipe } from '../../../shared/pipes';
import { ToSentencePipe } from '../../../shared/pipes/to-sentence.pipe';
import { AuthService, UsersService } from '../../../shared/services';
import {
    ApmAssetDetailsComponent,
    ApmAssetDetailsFiveTenComponent,
    AssetAccessComponent,
    AssetPpeComponent,
    AttachmentsTabComponent,
    InspectionInformationComponent,
    InspectionPhotosTabComponent,
    InspectionResultsComponent,
    ReportTabComponent
} from '../../components';
import {
    AssetAccessPipe,
    AssetPpePipe,
    InspectionInfoPipe,
    InspectionResultsPipe
} from '../../pipes';
import { ApmService } from '../../services';
import { WorkOrderDetailsComponent } from './work-order-details.component';

describe('WorkOrderDetailsComponent', () => {
    let component: WorkOrderDetailsComponent;
    let fixture: ComponentFixture<WorkOrderDetailsComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxFormModule,
                HttpClientTestingModule,
                DxTabPanelModule,
                DxTextBoxModule,
                DxTextAreaModule,
                DxSelectBoxModule,
                DxDateBoxModule,
                DxCheckBoxModule,
                DxButtonModule,
                DxDataGridModule,
                RouterTestingModule,
                ToastrModule.forRoot(),
                DxTagBoxModule,
                DxPopupModule,
                DxGalleryModule,
                DxTileViewModule,
                DxLoadIndicatorModule
            ],
            declarations: [
                WorkOrderDetailsComponent,
                ToSentencePipe,
                ApmAssetDetailsComponent,
                AssetPpeComponent,
                AssetAccessComponent,
                InspectionInformationComponent,
                InspectionResultsComponent,
                InspectionPhotosTabComponent,
                AttachmentsTabComponent,
                ReportTabComponent,
                ApmAssetDetailsFiveTenComponent,
                SafePipe,
                AssetPpePipe,
                AssetAccessPipe,
                InspectionInfoPipe,
                InspectionResultsPipe,
                HasRolesPipe
            ],
            providers: [
                HasRolePipe,
                { provide: ApmService, useValue: { getUsers: () => {} } },
                { provide: UsersService, useValue: {} },
                {
                    provide: AuthService,
                    useValue: {
                        acquireTokenSuccess$: of({ accessToken: 'lalala' })
                    }
                }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(WorkOrderDetailsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
