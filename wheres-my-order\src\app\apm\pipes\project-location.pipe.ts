import { Pipe, PipeTransform } from '@angular/core';
import { Location, ProjectLocationTabItem } from '../models';

@Pipe({
    name: 'projectLocation'
})
export class ProjectLocationPipe implements PipeTransform {
    public transform(selectedLocation: Location): ProjectLocationTabItem {
        if (!selectedLocation) {
            return null;
        }

        return new ProjectLocationTabItem({
            id: selectedLocation?.id,
            city: selectedLocation?.city?.currentValue,
            description: selectedLocation?.description?.currentValue,
            latitude: selectedLocation?.latitude?.currentValue,
            longitude: selectedLocation?.longitude?.currentValue,
            elevation: selectedLocation?.elevation?.currentValue,
            name: selectedLocation?.name?.currentValue,
            postalCode: selectedLocation?.postalCode?.currentValue,
            region: selectedLocation?.region?.currentValue,
            street1: selectedLocation?.street1?.currentValue,
            street2: selectedLocation?.street2?.currentValue
        });
    }
}
