﻿<Project Sdk="Microsoft.NET.Sdk.Worker">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<UserSecretsId>dotnet-ClientPortal.WMO.ETL-105ABC9F-7C87-4AF8-818E-068AAFF599CA</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	</PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper.Contrib" Version="2.0.78" />
	<!-- Replaced Google Cloud Diagnostics with Azure Application Insights -->
	<PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.21.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="6.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.16.1" />
    <PackageReference Include="Z.Dapper.Plus" Version="4.0.29" />
  </ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\ClientPortal.Shared\ClientPortal.Shared.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="appsettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
</Project>
