import { Component, Input, ViewChild } from '@angular/core';
import { DxPieChartComponent } from 'devextreme-angular';

@Component({
    selector: 'app-inspections-without-due-dates',
    templateUrl: './inspections-without-due-dates.component.html',
    styleUrls: ['./inspections-without-due-dates.component.scss']
})
export class InspectionsWithoutDueDatesComponent {
    @Input() chartData: {
        assetCategory: string;
        count: number;
        percent: number;
    }[];
    @Input() loading = false;

    @ViewChild(DxPieChartComponent)
    chart: DxPieChartComponent;

    get taskCount(): number {
        // Maps the chart data to the counts and reduces it to the sum of all counts, giving a task count
        return (
            this.chartData?.map((x) => x.count)?.reduce((a, b) => a + b, 0) ?? 0
        );
    }

    constructor() {}

    renderChart() {
        this.chart?.instance?.render({ force: true });
    }

    customizeLabel = (args) => {
        return `${args.valueText} (${args.percentText})`;
    };
}
