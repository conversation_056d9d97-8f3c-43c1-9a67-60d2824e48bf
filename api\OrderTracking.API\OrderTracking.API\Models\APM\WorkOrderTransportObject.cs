﻿using System;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class WorkOrderTransportObject
    {
        [JsonProperty("projectId")] public string ProjectID { get; set; }

        [JsonProperty("id")] public string ID { get; set; }

        [JsonProperty("apmWorkOrderNumber")] public string APMWorkOrderNumber { get; set; }

        [JsonProperty("plannedStartDate")] public DateTime? PlannedStartDate { get; set; }

        [JsonProperty("latitude")] public double? Latitude { get; set; }

        [JsonProperty("longitude")] public double? Longitude { get; set; }

        [JsonProperty("status")] public string Status { get; set; }

        [JsonProperty("plannedEndDate")] public DateTime? PlannedEndDate { get; set; }

        [JsonProperty("assetCategory")] public string AssetCategory { get; set; }

        [Json<PERSON>roperty("primaryContactName")] public string PrimaryContactName { get; set; }

        [JsonProperty("facility")] public string Facility { get; set; }

        [JsonProperty("dueDate")] public DateTime? DueDate { get; set; }

        [JsonProperty("primaryContactPhone")] public string PrimaryContactPhone { get; set; }

        [JsonProperty("apmProjectNumber")] public string APMProjectNumber { get; set; }

        [JsonProperty("teamProjectNumber")] public string TEAMProjectNumber { get; set; }
        
        [JsonProperty("jobScope")] public string JobScope { get; set; }
        
        [JsonProperty("inspectionSummary")] public string InspectionSummary { get; set; }
        
        [JsonProperty("applicableDamage")] public string ApplicableDamage { get; set; }

        [JsonProperty("releventIndications")] public string ReleventIndications { get; set; }

        [JsonProperty("recommendations")] public string Recommendations { get; set; }
    }
}