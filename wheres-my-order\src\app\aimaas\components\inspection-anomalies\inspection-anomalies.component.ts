import { Component, Input, OnInit } from '@angular/core';
import { AnomaliesRecommendations } from '../../models';

@Component({
    selector: 'app-inspection-anomalies',
    templateUrl: './inspection-anomalies.component.html',
    styleUrls: ['./inspection-anomalies.component.scss']
})
export class InspectionAnomaliesComponent implements OnInit {
    @Input() inspectionanomalies: AnomaliesRecommendations[];
    submissionPopupVisible: boolean = false;
    submissionPopupTitle: string = 'Anomaly Update';
    initialAnomaly: AnomaliesRecommendations = null;
    constructor() {}

    ngOnInit(): void {
        console.log(this.inspectionanomalies, 'inspection anamolies');
    }
    clientSubmissionTitleValueChange(e: any) {
        this.submissionPopupTitle = e;
    }
    clientDataFormSubmitted(e: any) {
        this.submissionPopupVisible = false;
    }
    anomalyCellTemplate = (cellElement, cellInfo) => {
        const anomalyNumber = cellInfo.value ? cellInfo.value : '';
        const link = document.createElement('a');
        link.href = '#';
        link.innerText = anomalyNumber;
        link.onclick = (event) => {
            event.preventDefault();
            this.onAnomalyClick(cellInfo.data);
        };
        cellElement.appendChild(link);
    };
    onAnomalyClick(data: AnomaliesRecommendations) {
        console.log(data, 'data clicked');
        this.initialAnomaly = data;
        this.clientSubmitDataOnclick('fromanomaly');
    }
    clientSubmitDataOnclick(e: string) {
        if (e === 'frombuttonclick') {
            this.initialAnomaly = null;
        }
        this.submissionPopupVisible = !this.submissionPopupVisible;
    }
    convertHtmlToText(html: string): string {
        if (html == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return doc.documentElement.textContent ?? ' ';
    }
    formatAnomalyDescription(data) {
        if (data.anomalydescription == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(
            data.anomalydescription,
            'text/html'
        );
        return doc.documentElement.textContent ?? ' ';
    }
    formatAnomalyProposedRecom(data) {
        if (data.proposedrecommemendation == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(
            data.proposedrecommemendation,
            'text/html'
        );
        return doc.documentElement.textContent ?? ' ';
    }
}
