﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Net;
//using System.Threading.Tasks;
//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Mvc;
//using Newtonsoft.Json;
//using OrderTracking.API.Extensions;
//using TeamDigital.PipelineInspection.APIManager;
//using TeamDigital.PipelineInspection.APIManager.Transport;

//namespace OrderTracking.API.Controllers
//{
//    public class ProjectUpdate
//    {
//        public Project OldProject { get; set; }
//        public Project NewProject { get; set; }
//    }

//    public class ClientUpdate
//    {
//        public Client OldClient { get; set; }
//        public Client NewClient { get; set; }
//    }

//    public class DigSiteWithInspectionData : DigSite
//    {
//        public DigSiteWithInspectionData(DigSite digSite)
//        {
//            if (digSite == null) return;

//            Address = digSite.Address;
//            AssignedUsers = digSite.AssignedUsers;
//            ClientIdentity = digSite.ClientIdentity;
//            Code = digSite.Code;
//            DigNumber = digSite.DigNumber;
//            FinalReportSentTime = digSite.FinalReportSentTime;
//            Identity = digSite.Identity;
//            InitialReportSentTime = digSite.InitialReportSentTime;
//            LineName = digSite.LineName;
//            LocationDescription = digSite.LocationDescription;
//            LocationLatitude = digSite.LocationLatitude;
//            LocationLongitude = digSite.LocationLongitude;
//            ReAssignmentForBackfillDate = digSite.ReAssignmentForBackfillDate;
//            ReAssignmentForRepairDate = digSite.ReAssignmentForRepairDate;
//            State = digSite.State;
//            TimeChanged = digSite.TimeChanged;
//            TimeStarted = digSite.TimeStarted;
//            InspectionDataIdentity = digSite.InspectionDataIdentity;
//            ProjectIdentity = digSite.ProjectIdentity;
//        }

//        public DigSiteInspectionData InspectionData { get; set; }
//    }

//    /// <summary>
//    ///     Controller handling http requests for Pipeline Inspection data (PIA)
//    /// </summary>
//    [Route("api/[controller]")]
//    [ApiController]
//    [Authorize]
//    [Authorize("PIA")]
//    public class PipelineInspectionController : ControllerBase
//    {
//        private readonly WebAPIManager _service;

//        /// <summary>
//        ///     Constructs a PipelineInspectionController, injecting a <see cref="WebAPIManager" />
//        /// </summary>
//        /// <param name="service"></param>
//        public PipelineInspectionController(WebAPIManager service)
//        {
//            _service = service;
//        }

//        /// <summary>
//        ///     Get clients
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("Clients")]
//        [Authorize(Policy = "GetPipelineClients")]
//        public async Task<IActionResult> GetClients()
//        {
//            var clients = await Task.Run(() => _service.Clients.GetAllClients());
//            return Ok(clients.OrderBy(client => client.Name));
//        }

//        /// <summary>
//        ///     Get projects
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("Projects")]
//        [Authorize(Policy = "GetPipelineProjects")]
//        public async Task<IActionResult> GetProjects()
//        {
//            var projects = await Task.Run(() => _service.Projects.GetAllProjects());
//            return Ok(projects.OrderBy(project => project.Name));
//        }

//        /// <summary>
//        ///     Get dig sites
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("DigSites")]
//        [Authorize(Policy = "GetPipelineDigsites")]
//        public async Task<IActionResult> GetDigSites()
//        {
//            var digSites = await Task.Run(() => _service.DigSites.GetAllDigSites());

//            digSites = new List<DigSite>(digSites).OrderBy(digSite => digSite.Code).ToArray();

//            return Ok(digSites);
//        }

//        /// <summary>
//        ///     Get a single dig site by its id
//        /// </summary>
//        /// <param name="id"></param>
//        /// <returns></returns>
//        [HttpGet("DigSites/{id}")]
//        public async Task<IActionResult> GetDigSite(string id)
//        {
//            var digSite = await Task.Run(() => _service.DigSites.FindDigSiteByID(id));

//            return Ok(digSite);
//        }

//        /// <summary>
//        ///     Get Dig Sites with their inspection data so that we don't force
//        ///     client applications to make too many http calls
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("DigSitesWithInspectionData")]
//        [Authorize(Policy = "GetPipelineInspections")]
//        public async Task<IActionResult> GetDigSitesWithInspectionData()
//        {
//            // Get dig sites
//            var digSites = await Task.Run(() => _service.DigSites.GetAllDigSites());

//            // Get inspection data with them as well
//            var response = await Task.WhenAll(digSites.Select(
//                digSite => Task.Run(() =>
//                {
//                    if (digSite.InspectionDataIdentity == null) return new DigSiteWithInspectionData(digSite);
//                    var inspectionData = _service.InspectionData.FindInspectionData(digSite.InspectionDataIdentity);
//                    return new DigSiteWithInspectionData(digSite) {InspectionData = inspectionData};
//                })));

//            return Ok(response);
//        }

//        /// <summary>
//        ///     Create a dig site
//        /// </summary>
//        /// <param name="digSite"></param>
//        /// <returns></returns>
//        [HttpPost("DigSites")]
//        [Authorize(Policy = "IsTeamEmployee")]
//        [Authorize(Policy = "CreatePipelineDigsites")]
//        public async Task<InsertResponse> CreateDigSite([FromBody] DigSite digSite)
//        {
//            if (digSite == null) throw new ArgumentNullException(nameof(digSite));

//            var response = await Task.Run(() => _service.DigSites.AddNewDigSite(digSite, digSite.ProjectIdentity));
//            return response;
//        }

//        /// <summary>
//        ///     Create a new project
//        /// </summary>
//        /// <param name="project"></param>
//        /// <param name="clientId"></param>
//        /// <returns></returns>
//        [HttpPost("DigSites/Project/{clientId}")]
//        [Authorize(Policy = "IsTeamEmployee")]
//        [Authorize(Policy = "CreatePipelineProjects")]
//        public async Task<Project> CreateProject([FromBody] Project project, string clientId)
//        {
//            if (project == null) throw new ArgumentNullException(nameof(project));

//            var response = await Task.Run(() => _service.Projects.AddNewProject(clientId, project));
//            project = await Task.Run(() => _service.Projects.GetAllProjects().FirstOrDefault(p => p.ID == project.ID));
//            return project;
//        }

//        /// <summary>
//        ///     Create a new client
//        /// </summary>
//        /// <param name="client"></param>
//        /// <returns></returns>
//        [HttpPost("DigSites/Client")]
//        [Authorize(Policy = "CreatePipelineClients")]
//        public async Task<Client> CreateClient([FromBody] Client client)
//        {
//            if (client == null) throw new ArgumentNullException(nameof(client));

//            var response = await Task.Run(() => _service.Clients.AddNewClient(client));
//            var c = await Task.Run(() => _service.Clients.GetAllClients().FirstOrDefault(c => c.ID == client.ID));
//            return c;
//        }

//        /// <summary>
//        ///     Update a project with new information
//        /// </summary>
//        /// <param name="projectUpdate"></param>
//        /// <returns></returns>
//        [HttpPut("DigSites/Project")]
//        [Authorize(Policy = "UpdatePipelineProjects")]
//        public async Task<UpdateResponse> UpdateProject(ProjectUpdate projectUpdate)
//        {
//            if (projectUpdate == null) throw new ArgumentNullException(nameof(projectUpdate));

//            var response = await Task.Run(() =>
//                _service.Projects.UpdateProject(projectUpdate.OldProject, projectUpdate.NewProject));
//            return response;
//        }

//        /// <summary>
//        ///     Update a client with new information
//        /// </summary>
//        /// <param name="clientUpdate"></param>
//        /// <returns></returns>
//        [HttpPut("DigSites/Client")]
//        [Authorize(Policy = "UpdatePipelineClients")]
//        public async Task<UpdateResponse> UpdateClient(ClientUpdate clientUpdate)
//        {
//            if (clientUpdate == null) throw new ArgumentNullException(nameof(clientUpdate));

//            var response = await Task.Run(() =>
//                _service.Clients.UpdateClient(clientUpdate.OldClient, clientUpdate.NewClient));
//            return response;
//        }

//        /// <summary>
//        ///     Update a dig site
//        /// </summary>
//        /// <param name="update"></param>
//        /// <returns></returns>
//        [HttpPut("DigSites")]
//        [Authorize(Policy = "IsTeamEmployee")]
//        [Authorize(Policy = "UpdatePipelineDigsites")]
//        public async Task<IActionResult> UpdateDigSite([FromBody] DigSiteUpdate update)
//        {
//            if (update == null) throw new ArgumentNullException(nameof(update));
//            if (update.HasChangesToAssignedUsers())
//            {
//                var newIds = update.GetIdsOfNewlyAssignedUsers();
//                foreach (var user in update.NewDigSite.AssignedUsers.Where(u => newIds.Contains(u.ID)))
//                {
//                    var piaUser = await Task.Run(() => _service.Users.FindUserByEmail(user.EmailAddress));
//                    if (piaUser == null)
//                    {
//                        var userInsertResponse = await Task.Run(() => _service.Users.AddUser(user));
//                        if (userInsertResponse.Error != null) return UnprocessableEntity(userInsertResponse.Error);
//                    }
//                    piaUser = await Task.Run(() => _service.Users.FindUserByEmail(user.EmailAddress));
//                    var piaUserIndex = update.NewDigSite.AssignedUsers.ToList().FindIndex(0, u => user.EmailAddress == u.EmailAddress);
//                    update.NewDigSite.AssignedUsers[piaUserIndex] = piaUser;
//                }
//            }

//            var response = await Task.Run(() => _service.DigSites.UpdateDigSite(update));
//            return Ok(response);
//        }

//        /// <summary>
//        ///     Gets users that have been added to the PIA database
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("Users")]
//        public async Task<IActionResult> GetUsers()
//        {
//            var response = await Task.Run(() => _service.Users.GetAllUsers());
//            return Ok(response);
//        }


//        /// <summary>
//        ///     Get inspection data for a dig site
//        /// </summary>
//        /// <param name="identity"></param>
//        /// <returns></returns>
//        [HttpGet("InspectionData/{identity}")]
//        [Authorize(Policy = "GetPipelineInspections")]
//        public async Task<IActionResult> GetInspectionData(string identity)
//        {
//            var data = await Task.Run(() => _service.InspectionData.FindInspectionData(identity));
//            if (data == null) return NotFound();

//            return Ok(data);
//        }

//        /// <summary>
//        ///     Update inspection data
//        /// </summary>
//        /// <param name="update"></param>
//        /// <returns></returns>
//        [HttpPut("InspectionData")]
//        [Authorize(Policy = "IsTeamEmployee")]
//        [Authorize(Policy = "UpdatePipelineInspectionData")]
//        public async Task<IActionResult> UpdateInspectionData([FromBody] InspectionDataUpdate update)
//        {
//            if (update == null) throw new ArgumentNullException(nameof(update));

//            FixPropertyNamesInSections(update.OldInspectionData);
//            FixPropertyNamesInSections(update.NewInspectionData);

//            update.User = User.Identity.Name;

//            var response = await Task.Run(() => _service.InspectionData.UpdateInspectionData(update));
//            if (response.Error == null)
//                return Ok(response);
//            return response.StatusCode == HttpStatusCode.Conflict
//                ? Ok(response)
//                : StatusCode((int) response.StatusCode, response.Error);
//        }

//        /// <summary>
//        ///     Get media object at specified file path
//        /// </summary>
//        /// <param name="filePath"></param>
//        /// <returns></returns>
//        [HttpGet("Media/{filePath}")]
//        [Authorize(Policy = "GetPipelineInspectionMedia")]
//        public async Task<IActionResult> GetMedia(string filePath)
//        {
//            if (filePath == null) throw new ArgumentNullException(nameof(filePath));

//            var transportFile = await Task.Run(() => _service.Media.DownloadFile(filePath));
//            return File(transportFile.Data, "image/jpeg");
//        }

//        /// <summary>
//        ///     Download a report for a dig site
//        /// </summary>
//        /// <param name="digSiteIdentity"></param>
//        /// <returns></returns>
//        [HttpGet("Reports/{digSiteIdentity}")]
//        [Authorize(Policy = "IsTeamEmployee")]
//        [Authorize(Policy = "GetPipelineInspectionReports")]
//        public async Task<IActionResult> DownloadReport(string digSiteIdentity)
//        {
//            if (digSiteIdentity == null) throw new ArgumentNullException(nameof(digSiteIdentity));

//            var fileData = await Task.Run(() => _service.Reports.DownloadReport(digSiteIdentity));
//            return File(fileData.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
//        }

//        /// <summary>
//        ///     Download a report for a dig site
//        /// </summary>
//        /// <param name="digSiteIdentity"></param>
//        /// <returns></returns>
//        [HttpGet("PhotoReports/{digSiteIdentity}")]
//        [Authorize(Policy = "IsTeamEmployee")]
//        [Authorize(Policy = "GetPipelineInspectionReports")]
//        public async Task<IActionResult> DownloadPhotoReport(string digSiteIdentity)
//        {
//            if (digSiteIdentity == null) throw new ArgumentNullException(nameof(digSiteIdentity));

//            var fileData = await Task.Run(() => _service.Reports.DownloadPhotoReport(digSiteIdentity));
//            return File(fileData.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
//        }

//        #region Private Methods

//        private static void FixPropertyNamesInSections(DigSiteInspectionData inspectionData)
//        {
//            inspectionData.Sections[0] =
//                ForceTypedDeserialization<SouthernCompany_Section1_SiteOverviewSection>(inspectionData.Sections[0]);
//            inspectionData.Sections[1] =
//                ForceTypedDeserialization<SouthernCompany_Section2_Excavation>(inspectionData.Sections[1]);
//            inspectionData.Sections[2] =
//                ForceTypedDeserialization<SouthernCompany_Section3_CoatingCondition>(inspectionData.Sections[2]);
//            inspectionData.Sections[3] =
//                ForceTypedDeserialization<SouthernCompany_Section4_PipeCondition>(inspectionData.Sections[3]);
//            inspectionData.Sections[4] =
//                ForceTypedDeserialization<SouthernCompany_Section5_DefectEvaluation>(inspectionData.Sections[4]);
//            inspectionData.Sections[5] =
//                ForceTypedDeserialization<SouthernCompany_Section6_OtherTests>(inspectionData.Sections[5]);
//            inspectionData.Sections[6] =
//                ForceTypedDeserialization<SouthernCompany_Section7_ExaminationVariance>(inspectionData.Sections[6]);
//            inspectionData.Sections[7] =
//                ForceTypedDeserialization<SouthernCompany_Section8_ReapirsAndReplacements>(inspectionData.Sections[7]);
//            inspectionData.Sections[8] = ForceTypedDeserialization<SignatureSection>(inspectionData.Sections[8]);
//        }

//        private static T ForceTypedDeserialization<T>(object section)
//        {
//            var serialized = JsonConvert.SerializeObject(section);
//            var deserialized = JsonConvert.DeserializeObject<T>(serialized);
//            return deserialized;
//        }

//        #endregion
//    }
//}