﻿//using System.Linq;
//using System.Threading.Tasks;
//using DevExtreme.AspNet.Data.ResponseModel;
//using OrderTracking.API.Models;

//namespace OrderTracking.API.Interfaces
//{
//    /// <summary>
//    ///     Interface for OrderResultsLoader
//    /// </summary>
//    public interface IClientPortalResultsLoader
//    {
//        /// <summary>
//        ///     Load the query result based on the DataSourceLoadOptions and the original IQueryable of T
//        /// </summary>
//        /// <param name="loadOptions"></param>
//        /// <param name="queryable"></param>
//        /// <returns></returns>
//        Task<LoadResult> LoadResult<T>(DataSourceLoadOptions loadOptions, IQueryable<T> queryable);
//    }
//}