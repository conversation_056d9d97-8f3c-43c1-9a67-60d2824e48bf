import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxFormModule } from 'devextreme-angular/ui/form';

import { WoDetailsTabComponent } from './wo-details-tab.component';

describe('WoDetailsTabComponent', () => {
    let component: WoDetailsTabComponent;
    let fixture: ComponentFixture<WoDetailsTabComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DxFormModule],
            declarations: [WoDetailsTabComponent],
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(WoDetailsTabComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
