import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxButtonModule } from 'devextreme-angular';
import { DxFormModule } from 'devextreme-angular/ui/form';
import { DxTabPanelModule } from 'devextreme-angular/ui/tab-panel';
import { ToastrModule } from 'ngx-toastr';
import { ProjectDetailPipe } from '../../pipes/project-detail.pipe';
import { ApmService } from '../../services';
import { DetailsTabComponent } from '../details-tab/details-tab.component';
import { ProjectDetailsComponent } from './project-details.component';

describe('ProjectDetailsComponent', () => {
    let component: ProjectDetailsComponent;
    let fixture: ComponentFixture<ProjectDetailsComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxTabPanelModule,
                DxFormModule,
                DxButtonModule,
                HttpClientTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [
                ProjectDetailsComponent,
                DetailsTabComponent,
                ProjectDetailPipe
            ],
            providers: [
                {
                    provide: ApmService,
                    useValue: {}
                }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ProjectDetailsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
