﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration" version="3.1.2" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="3.1.2" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.FileExtensions" version="3.1.2" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Json" version="3.1.2" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.UserSecrets" version="3.1.2" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="3.1.2" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Physical" version="3.1.2" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileSystemGlobbing" version="3.1.2" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="3.1.2" targetFramework="net472" />
  <package id="Microsoft.NETCore.Platforms" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.Win32.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="NETStandard.Library" version="1.6.1" targetFramework="net472" />
  <package id="NUnit" version="3.12.0" targetFramework="net45" />
  <package id="NUnit3TestAdapter" version="3.16.1" targetFramework="net472" developmentDependency="true" />
  <package id="Selenium.WebDriver" version="3.141.0" targetFramework="net472" />
  <package id="Selenium.WebDriver.NetCoreWebDriverFactory" version="3.0.0" targetFramework="net472" />
  <package id="Selenium.WebDriver.WebDriverFactoryNunitConfig" version="3.0.0" targetFramework="net472" />
  <package id="System.AppContext" version="4.3.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.0" targetFramework="net472" />
  <package id="System.Collections" version="4.3.0" targetFramework="net472" />
  <package id="System.Collections.Concurrent" version="4.3.0" targetFramework="net472" />
  <package id="System.ComponentModel" version="4.3.0" targetFramework="net472" />
  <package id="System.Console" version="4.3.0" targetFramework="net472" />
  <package id="System.Diagnostics.Contracts" version="4.3.0" targetFramework="net472" />
  <package id="System.Diagnostics.Debug" version="4.3.0" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.3.0" targetFramework="net472" />
  <package id="System.Diagnostics.Tools" version="4.3.0" targetFramework="net472" />
  <package id="System.Diagnostics.Tracing" version="4.3.0" targetFramework="net472" />
  <package id="System.Globalization" version="4.3.0" targetFramework="net472" />
  <package id="System.Globalization.Calendars" version="4.3.0" targetFramework="net472" />
  <package id="System.IO" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Compression.ZipFile" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Linq" version="4.3.0" targetFramework="net472" />
  <package id="System.Linq.Expressions" version="4.3.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.3" targetFramework="net472" />
  <package id="System.Net.Http" version="4.3.0" targetFramework="net472" />
  <package id="System.Net.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Net.Sockets" version="4.3.0" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.ObjectModel" version="4.3.0" targetFramework="net472" />
  <package id="System.Reflection" version="4.3.0" targetFramework="net472" />
  <package id="System.Reflection.Extensions" version="4.3.0" targetFramework="net472" />
  <package id="System.Reflection.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Resources.ResourceManager" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.7.0" targetFramework="net472" />
  <package id="System.Runtime.Extensions" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.Handles" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.Numerics" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encoding.Extensions" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="4.7.0" targetFramework="net472" />
  <package id="System.Text.Json" version="4.7.1" targetFramework="net472" />
  <package id="System.Text.RegularExpressions" version="4.3.0" targetFramework="net472" />
  <package id="System.Threading" version="4.3.0" targetFramework="net472" />
  <package id="System.Threading.Tasks" version="4.3.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.3" targetFramework="net472" />
  <package id="System.Threading.Timer" version="4.3.0" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
  <package id="System.Xml.ReaderWriter" version="4.3.0" targetFramework="net472" />
  <package id="System.Xml.XDocument" version="4.3.0" targetFramework="net472" />
</packages>