import {
    ChangeDetectorRef,
    Component,
    ElementRef,
    Input,
    OnDestroy,
    OnInit,
    ViewChild
} from '@angular/core';
import { Router } from '@angular/router';
import {
    DxChartModule,
    DxDateBoxModule,
    DxPieChartComponent,
    DxTagBoxComponent
} from 'devextreme-angular';
import { DxChartComponent } from 'devextreme-angular/ui/chart';
import { DxRangeSelectorComponent } from 'devextreme-angular/ui/range-selector';
import CheckBox from 'devextreme/ui/check_box';
import { createSafeResizeObserver } from '../../../shared/helpers/safe-resize-observer';
import { ToastNotificationService, ToastType } from '../../../shared/services';
import {
    AggregateStatusInspection,
    Asset,
    AssetInspection,
    InspectionType
} from '../../models';
import { CredoSoftService } from '../../services/credo-soft.service';

@Component({
    selector: 'app-inspections-dashboard',
    templateUrl: './inspections-dashboard.component.html',
    styleUrls: ['./inspections-dashboard.component.scss']
})
export class InspectionsDashboardComponent implements OnInit, OnDestroy {
    @ViewChild('checkbox') checkbox: CheckBox;
    @ViewChild('chart') chart: DxChartModule;
    @ViewChild('overdueInspectionsPie')
    overdueInspectionsPie: DxPieChartComponent;
    @ViewChild('dueThisYearInspectionsPie')
    dueThisYearInspectionsPie: DxPieChartComponent;
    @ViewChild('dueNextYearInspectionsPie')
    dueNextYearInspectionsPie: DxPieChartComponent;
    @ViewChild('selectedInspectionsThisYearChart')
    selectedInspectionsThisYearChart: DxChartComponent;
    @ViewChild('aggregateInspectionsThisYearRange')
    aggregateInspectionsThisYearRange: DxRangeSelectorComponent;
    @ViewChild('selectedInspectionsNextYearChart')
    selectedInspectionsNextYearChart: DxChartComponent;
    @ViewChild('aggregateInspectionsNextYearRange')
    aggregateInspectionsNextYearRange: DxRangeSelectorComponent;
    @ViewChild('stackedChart')
    stackedChart: DxChartComponent;
    @ViewChild('ToDate')
    ToDate: DxDateBoxModule;
    @ViewChild('FromDate')
    FromDate: DxDateBoxModule;
    stackedChartInspectionIds: any;
    isInitStateInspection: boolean = false;
    private _observer: ResizeObserver;
    @Input() set inspectionsData(value: AssetInspection[]) {
        this.resetChartData();
        this.selectedDoughnutCategories = localStorage.getItem(
            'assetCategoryOptionsInspectionDoughnut'
        )
            ? JSON.parse(
                  localStorage.getItem('assetCategoryOptionsInspectionDoughnut')
              )
            : [];
        this.selectedBarGraphCategories = localStorage.getItem(
            'assetCategoryOptionsInspectionYearChart'
        )
            ? JSON.parse(
                  localStorage.getItem(
                      'assetCategoryOptionsInspectionYearChart'
                  )
              )
            : [];
        this.inspections = value;
        this.pieChartinspections = this.inspections.filter(
            (inspection: AssetInspection) => {
                return inspection.schedulestatus.trim() === 'Active';
            }
        );
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        this.fromDate = localStorage.getItem(
            'fromdateinspectionmonthlytasktracker'
        )
            ? new Date(
                  JSON.parse(
                      localStorage.getItem(
                          'fromdateinspectionmonthlytasktracker'
                      )
                  )
              )
            : new Date(currentYear, 0, 1);
        this.toDate = localStorage.getItem('todateinspectionmonthlytasktracker')
            ? new Date(
                  JSON.parse(
                      localStorage.getItem('todateinspectionmonthlytasktracker')
                  )
              )
            : new Date(currentYear, 11, 31);
        this.isincludeoutofservicechecked = localStorage.getItem(
            'inspectiondoughnutoutofservicecheckbox'
        )
            ? JSON.parse(
                  localStorage.getItem('inspectiondoughnutoutofservicecheckbox')
              )
            : true;
        this.loadCategoriesFromLocalStorage();
        this.changeStateGraphTitle();
        this.buildDataForThisYearCharts(value);
        this.buildDataForOverDueDoughnut(this.pieChartinspections);
        this.buildDataForDueNextYearDoughnut(this.pieChartinspections);
        this.buildDataForDueThisYearDoughnut(this.pieChartinspections);
        this.buildDataForStateCharts(this.inspections);
    }
    @Input() set assets(value: Asset[]) {
        this._assets = value;
    }
    selectedDoughnutCategories: any[];
    pieChartinspections: AssetInspection[];
    selectedBarGraphCategories: any[];
    doughnutInspections: AssetInspection[];
    thisYearDoughnutInspections: AssetInspection[];
    nextYearDoughnutInspections: AssetInspection[];
    inspections: AssetInspection[];
    _assets: Asset[];
    thisYearInspections: AssetInspection[];
    nextYearInspections: AssetInspection[];
    graphTitle: string = '';
    overdueInspections: InspectionType[] = [];
    dueThisYearInspections: InspectionType[] = [];
    dueNextYearInspections: InspectionType[] = [];
    selectedInspectionsForThisYear: AggregateStatusInspection[];
    aggregateInspectionsForThisYear: AggregateStatusInspection[];
    selectedInspectionsForNextYear: AggregateStatusInspection[];
    aggregateInspectionsForNextYear: AggregateStatusInspection[] = [];
    stateInspectionThisYearCount = 0;
    stateInspectionNextYearCount = 0;
    selectedOption: any[] = [];
    filteredDataThisYear: AggregateStatusInspection[] = [];
    filteredDataNextYear: AggregateStatusInspection[] = [];
    fromDate: Date = new Date(new Date().getFullYear(), 0, 1);
    toDate: Date = new Date(new Date().getFullYear(), 11, 31);
    overdueMap: Map<string, number> = new Map<string, number>();
    dueThisYearMap: Map<string, number> = new Map<string, number>();
    dueNextYearMap: Map<string, number> = new Map<string, number>();
    categories: any[] = [];
    resolveOverlappingTypes = ['shift', 'hide', 'none'];
    emptyCategories: any[] = [];
    managemnetCategoryStore: any[] = [];
    shortForms: any[] = [];
    categoryList: any[] = [];
    pastYearsData: any[] = [];
    currentYearData: any[] = [];
    currentYearPlus1Data: any[] = [];
    currentYearPlus2Data: any[] = [];
    currentYearPlus3Data: any[] = [];
    currentYearPlus4Data: any[] = [];
    stackedChartData: any[] = [];
    @ViewChild('doughnutAssetOptions', { static: false })
    doughnutAssetOptions: DxTagBoxComponent;
    @ViewChild('currentYearAssetOptions', { static: false })
    currentYearAssetOptions: DxTagBoxComponent;
    @ViewChild('nextYearAssetOptions', { static: false })
    nextYearAssetOptions: DxTagBoxComponent;
    @ViewChild('doughnutContainer') doughnutContainer: ElementRef;
    selectedOptions: any[] = [];
    private readonly _months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
    ];
    dropdownOptions: any[] = ['Piping', 'Tanks', 'Vessels'];
    systemManagementCategories: any[] = [];
    isincludeoutofservicechecked: boolean = true;
    noSystemCategoryChecked: boolean = true;
    selectAllCategoriesChecked: boolean = false;
    constructor(
        private readonly cdr: ChangeDetectorRef,
        private readonly _router: Router,
        private readonly credoService: CredoSoftService,
        private readonly _toasts: ToastNotificationService,
        private readonly _hostElement: ElementRef
    ) {}
    ngOnInit(): void {
        this._observer = createSafeResizeObserver(() => {
            this.renderCharts();
        });
        this._observer.observe(this._hostElement.nativeElement);
        // Map categories to their short forms
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        this.fromDate = localStorage.getItem(
            'fromdateinspectionmonthlytasktracker'
        )
            ? new Date(
                  JSON.parse(
                      localStorage.getItem(
                          'fromdateinspectionmonthlytasktracker'
                      )
                  )
              )
            : new Date(currentYear, 0, 1);
        this.toDate = localStorage.getItem('todateinspectionmonthlytasktracker')
            ? new Date(
                  JSON.parse(
                      localStorage.getItem('todateinspectionmonthlytasktracker')
                  )
              )
            : new Date(currentYear, 11, 31);
        this.inspections = this.populateAssetType(
            this.inspections,
            this._assets
        );

        this.credoService.getSystemManagementCategories().subscribe((data) => {
            this.systemManagementCategories = data;
            this.loadCategoriesFromLocalStorage();
            this.changeStateGraphTitle();
        });
        this.selectedDoughnutCategories = localStorage.getItem(
            'assetCategoryOptionsInspectionDoughnut'
        )
            ? JSON.parse(
                  localStorage.getItem('assetCategoryOptionsInspectionDoughnut')
              )
            : [];
        this.selectedBarGraphCategories = localStorage.getItem(
            'assetCategoryOptionsInspectionYearChart'
        )
            ? JSON.parse(
                  localStorage.getItem(
                      'assetCategoryOptionsInspectionYearChart'
                  )
              )
            : [];

        this.isincludeoutofservicechecked = localStorage.getItem(
            'inspectiondoughnutoutofservicecheckbox'
        )
            ? JSON.parse(
                  localStorage.getItem('inspectiondoughnutoutofservicecheckbox')
              )
            : true;
    }
    ngOnDestroy(): void {
        //    this._observer.unobserve(this._hostElement.nativeElement);
    }
    ///////////////////////////Selected Management Systems Inspections Required by Year graph started////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    selectAllManagementCategoryButtonClick() {
        this.systemManagementCategories = this.systemManagementCategories.map(
            (item) => ({
                ...item,
                isChecked: true
            })
        );
        this.noSystemCategoryChecked = true;
        if (this.inspections) {
            this.buildDataForStateCharts(this.inspections);
        }
        this.changeStateGraphTitle();
        this.saveCategoriesToLocalStorage();
    }
    unSelectAllManagementCategoryButtonClick() {
        this.systemManagementCategories = this.systemManagementCategories.map(
            (item) => ({
                ...item,
                isChecked: false
            })
        );
        this.noSystemCategoryChecked = false;
        if (this.inspections) {
            this.buildDataForStateCharts(this.inspections);
        }
        this.changeStateGraphTitle();
        this.saveCategoriesToLocalStorage();
    }
    onNoSystemCategoryChanged(e) {
        this.noSystemCategoryChecked = e.value;

        // if (this.noSystemCategoryChecked) {
        //     // Uncheck all other categories and "Select All" checkbox
        //     this.systemManagementCategories =
        //         this.systemManagementCategories.map((item) => ({
        //             ...item,
        //             isChecked: false
        //         }));
        //     this.selectAllCategoriesChecked = false;
        // }
        if (this.inspections) {
            this.buildDataForStateCharts(this.inspections);
        }
        this.changeStateGraphTitle();
        this.saveCategoriesToLocalStorage();
    }
    // onSelectAllCategoriesChanged(e) {
    //     this.selectAllCategoriesChecked = e.value;

    //     if (this.selectAllCategoriesChecked) {
    //         // Check all categories and uncheck "No Management System Categories"
    //         this.systemManagementCategories =
    //             this.systemManagementCategories.map((item) => ({
    //                 ...item,
    //                 isChecked: true
    //             }));
    //         this.noSystemCategoryChecked = false;
    //     }
    //     if (this.inspections) {
    //         this.buildDataForStateCharts(this.inspections);
    //     }
    //     this.changeStateGraphTitle();
    //     this.saveCategoriesToLocalStorage();
    // }
    onSystemManagementCategoryChanged(e) {
        //let selectedManagementValue: string = e.component._props.text;
        this.selectAllCategoriesChecked = this.systemManagementCategories.every(
            (category) => category.isChecked
        );
        const anyCategoryChecked = (this.systemManagementCategories || []).some(
            (category) => category.isChecked
        );

        // if (!anyCategoryChecked) {
        //     // If no categories are checked, automatically select 'No Management System Categories'
        //     this.noSystemCategoryChecked = true;
        // } else {
        //     this.noSystemCategoryChecked = false;
        // }
        if (this.inspections) {
            this.buildDataForStateCharts(this.inspections);
        }
        this.changeStateGraphTitle();
        this.saveCategoriesToLocalStorage();
    }

    saveCategoriesToLocalStorage() {
        const data = {
            systemManagementCategories: this.systemManagementCategories,
            selectAllCategoriesChecked: this.selectAllCategoriesChecked,
            noSystemCategoryChecked: this.noSystemCategoryChecked
        };
        localStorage.setItem('managementSystemState', JSON.stringify(data));
    }
    loadCategoriesFromLocalStorage() {
        const savedState = localStorage.getItem('managementSystemState');
        if (savedState) {
            const data = JSON.parse(savedState);
            this.systemManagementCategories =
                data.systemManagementCategories || [];
            this.selectAllCategoriesChecked =
                data.selectAllCategoriesChecked || false;
            this.noSystemCategoryChecked =
                data.noSystemCategoryChecked || false;
        }
    }
    changeStateGraphTitle() {
        const selectedCategories = (this.systemManagementCategories || [])
            .filter((category) => category.isChecked)
            .map((category) => category.code); // Get the short forms (codes) of the selected categories

        const allCategoriesChecked = this.systemManagementCategories.every(
            (category) => category.isChecked
        );

        const anyCategoryChecked = selectedCategories.length > 0;
        // Title changes as per requirement:
        // 1. If all categories are selected, the title should be "All Inspections Required by Year"
        if (allCategoriesChecked && this.noSystemCategoryChecked) {
            this.graphTitle = 'All Inspections Required by Year';

            // 2. If only "No Management System Categories" is selected, the title should be "No Management Category - Inspections Required by Year"
        } else if (this.noSystemCategoryChecked && !anyCategoryChecked) {
            this.graphTitle =
                'No Management Category - Inspections Required by Year';

            // 3. If all categories are selected and "No Management System Categories" is unselected, the title should be "Inspections Required by Year, All Categories"
        } else if (allCategoriesChecked && !this.noSystemCategoryChecked) {
            this.graphTitle = 'Inspections Required by Year, All Categories';

            // 4. If some categories are selected and "No Management System Categories" is also selected, append "None" and join short forms of selected categories
        } else if (this.noSystemCategoryChecked && anyCategoryChecked) {
            const joinedCategories = selectedCategories.join(', ');
            this.graphTitle = `None, ${joinedCategories} Inspections Required by Year`;

            // Default case: If some categories are selected and "No Management System Categories" is not selected, join short forms of selected categories
        } else if (anyCategoryChecked) {
            const joinedCategories = selectedCategories.join(', ');
            this.graphTitle = `${joinedCategories} Inspections Required by Year`;
            // Fallback default: If no categories are selected, just display a generic title
        } else {
            this.graphTitle = 'Inspections Required by Year';
        }
    }

    // changeStateGraphTitle() {
    //     if (this.noSystemCategoryChecked) {
    //         this.graphTitle = 'All Inspections Required by Year';
    //     } else if (this.selectAllCategoriesChecked) {
    //         this.graphTitle = 'Inspections Required by Year, All Categories';
    //     } else {
    //         // Filter selected categories
    //         const selectedCategories = (this.systemManagementCategories || [])
    //             .filter((category) => category.isChecked)
    //             .map((category) => category.code) // Use code or systemcategory for display
    //             .join(', ');
    //         // Set the graph title with the selected categories
    //         this.graphTitle = selectedCategories
    //             ? `${selectedCategories} Inspections Required by Year`
    //             : 'Inspections Required by Year'; // Default fallback if none selected
    //     }
    // }
    private buildDataForStateCharts(inspections: AssetInspection[]) {
        const selectedCategories = this.systemManagementCategories
            .filter((category) => category.isChecked)
            .map((category) => category.code);
        let filteredInspections = [];
        if (this.noSystemCategoryChecked) {
            //filteredInspections = inspections;
            filteredInspections = inspections.filter((inspection) => {
                const hasNoCategory = !inspection.assetmanagementcategory; // Check if the inspection has no category
                const matchesSelectedCategory = selectedCategories.some(
                    (categoryCode) =>
                        inspection.assetmanagementcategory?.includes(
                            categoryCode
                        )
                );
                return hasNoCategory || matchesSelectedCategory;
            });
        } else if (selectedCategories.length > 0) {
            filteredInspections = inspections.filter((inspection) => {
                return selectedCategories.some((categoryCode) =>
                    inspection.assetmanagementcategory?.includes(categoryCode)
                );
            });
        } else {
            filteredInspections = [];
        }
        const currentYear = new Date().getFullYear();
        const yearData = {
            pastYears: { upcoming: 0, overdue: 0 },
            currentYear: { upcoming: 0, overdue: 0 },
            currentYearPlus1: { upcoming: 0, overdue: 0 },
            currentYearPlus2: { upcoming: 0, overdue: 0 },
            currentYearPlus3: { upcoming: 0, overdue: 0 },
            currentYearPlus4: { upcoming: 0, overdue: 0 }
        };
        const yearsDataInspectionIds = {
            pastYears: {
                completed: [],
                upcoming: [],
                overdue: []
            },
            currentYear: {
                completed: [],
                upcoming: [],
                overdue: []
            },
            currentYearPlus1: {
                completed: [],
                upcoming: [],
                overdue: []
            },
            currentYearPlus2: {
                completed: [],
                upcoming: [],
                overdue: []
            },
            currentYearPlus3: {
                completed: [],
                upcoming: [],
                overdue: []
            },
            currentYearPlus4: {
                completed: [],
                upcoming: [],
                overdue: []
            }
        };
        for (const inspection of filteredInspections) {
            const dueDate = new Date(inspection.nextinspectiondue);
            const year = dueDate.getFullYear();
            const lastDate = new Date(inspection.lastdate);
            const lastDateYear = lastDate.getFullYear();
            if (inspection.isUpcoming()) {
                this.addToYearData(
                    yearData,
                    year,
                    'upcoming',
                    yearsDataInspectionIds,
                    inspection.scheduleid
                );
            }
            if (inspection.isOverdue()) {
                this.addToYearData(
                    yearData,
                    year,
                    'overdue',
                    yearsDataInspectionIds,
                    inspection.scheduleid
                );
            }
        }
        // Combine data into stacked chart format
        this.stackedChartData = [
            { year: 'Past Years', ...yearData.pastYears },
            { year: currentYear.toString(), ...yearData.currentYear },
            {
                year: (currentYear + 1).toString(),
                ...yearData.currentYearPlus1
            },
            {
                year: (currentYear + 2).toString(),
                ...yearData.currentYearPlus2
            },
            {
                year: (currentYear + 3).toString(),
                ...yearData.currentYearPlus3
            },
            { year: (currentYear + 4).toString(), ...yearData.currentYearPlus4 }
        ];
        this.stackedChartInspectionIds = yearsDataInspectionIds;
    }

    private addToYearData(
        yearData: any,
        year: number,
        status: string,
        yearsDataInspectionIds: any,
        scheduleid: string
    ) {
        if (year < new Date().getFullYear()) {
            yearData.pastYears[status]++;
            yearsDataInspectionIds.pastYears[status].push(scheduleid);
        } else if (year === new Date().getFullYear()) {
            yearData.currentYear[status]++;
            yearsDataInspectionIds.currentYear[status].push(scheduleid);
        } else if (year === new Date().getFullYear() + 1) {
            yearData.currentYearPlus1[status]++;
            yearsDataInspectionIds.currentYearPlus1[status].push(scheduleid);
        } else if (year === new Date().getFullYear() + 2) {
            yearData.currentYearPlus2[status]++;
            yearsDataInspectionIds.currentYearPlus2[status].push(scheduleid);
        } else if (year === new Date().getFullYear() + 3) {
            yearData.currentYearPlus3[status]++;
            yearsDataInspectionIds.currentYearPlus3[status].push(scheduleid);
        } else if (year === new Date().getFullYear() + 4) {
            yearData.currentYearPlus4[status]++;
            yearsDataInspectionIds.currentYearPlus4[status].push(scheduleid);
        }
    }
    stateInspectionsByYearPointClicked(e) {
        const now = new Date();
        now.setHours(0, 0, 0, 0);
        const currentYear = now.getFullYear();
        const year = e.target.data.year;
        const series = e.target.series.name.toLowerCase();
        const filter: any = [];
        const selectedCategories = this.systemManagementCategories
            ?.filter((category) => category.isChecked)
            .map((category) => category.systemcategory);
        if (year == 'Past Years') {
            filter.push([
                'nextinspectiondue',
                '<',
                new Date(currentYear, 0, 1)
            ]);
        } else if (year == currentYear.toString()) {
            if (series == 'upcoming') {
                // Filter for upcoming inspections: greater than or equal to today, and less than this year
                filter.push(['nextinspectiondue', '>', now], 'and', [
                    'nextinspectiondue', // Greater than or equal to today
                    '<',
                    new Date(currentYear + 1, 0, 1)
                ]);
            } else if (series == 'overdue') {
                // Filter for overdue inspections: less than today, and greater than this year
                filter.push(
                    ['nextinspectiondue', '<', now],
                    'and',
                    // Less than today
                    [
                        'nextinspectiondue',
                        '>=',
                        new Date(currentYear, 0, 1) // Greater than or equal to start of this year (Jan 1, 2024)
                    ]
                );
            }
        } else if (year == (currentYear + 1).toString()) {
            filter.push(
                ['nextinspectiondue', '>=', new Date(currentYear + 1, 0, 1)],
                'and',
                ['nextinspectiondue', '<=', new Date(currentYear + 1, 11, 31)]
            );
        } else if (year == (currentYear + 2).toString()) {
            filter.push(
                ['nextinspectiondue', '>=', new Date(currentYear + 2, 0, 1)],
                'and',
                ['nextinspectiondue', '<=', new Date(currentYear + 2, 11, 31)]
            );
        } else if (year == (currentYear + 3).toString()) {
            filter.push(
                ['nextinspectiondue', '>=', new Date(currentYear + 3, 0, 1)],
                'and',
                ['nextinspectiondue', '<=', new Date(currentYear + 3, 11, 31)]
            );
        } else if (year == (currentYear + 4).toString()) {
            filter.push(
                ['nextinspectiondue', '>=', new Date(currentYear + 4, 0, 1)],
                'and',
                ['nextinspectiondue', '<=', new Date(currentYear + 4, 11, 31)]
            );
        }
        const noSystemCategoryChecked = this.noSystemCategoryChecked;
        const categoryFilters: any[] = [];

        if (selectedCategories.length > 0) {
            selectedCategories.forEach((item) => {});

            // Add each category with "or" between them
            selectedCategories.forEach((category, index) => {
                if (index > 0) {
                    categoryFilters.push('or'); // Add "or" between the conditions
                }
                categoryFilters.push([
                    'assetmanagementcategory',
                    'contains',
                    category
                ]);
            });

            // Combine category filters with the date filters using an "and" condition
            // filter.push('and');
            // filter.push([...categoryFilters]);
        }
        // If "No Management System Categories" is checked, add null filter
        if (noSystemCategoryChecked) {
            if (categoryFilters.length > 0) {
                categoryFilters.push('or'); // Add "or" between existing category filters and the null condition
            }
            categoryFilters.push(['assetmanagementcategory', '=', null]);
        }
        if (categoryFilters.length > 0) {
            filter.push('and', [...categoryFilters]);
        }
        this.drillDown(
            filter,
            'Selected Management Systems Inspections Required by Year '
        );
    }
    ////////////////// //////////Selected Management Systems Inspections Required by Year graph ended////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    renderCharts() {
        this.overdueInspectionsPie?.instance?.render();
        this.dueThisYearInspectionsPie?.instance?.render();
        this.dueNextYearInspectionsPie?.instance?.render();
        this.selectedInspectionsThisYearChart?.instance?.render();
        // this.aggregateInspectionsThisYearRange?.instance?.render();
        // this.selectedInspectionsNextYearChart?.instance?.render();
        // this.aggregateInspectionsNextYearRange?.instance?.render();
        this.stackedChart?.instance?.render();
    }

    applyFilter() {
        if (this.toDate < this.fromDate) {
            this._toasts.show(
                'Invalid Date Range',
                '"To" date cannot be earlier than the "From" date',
                ToastType.error,
                3000
            );
            this.toDate = new Date(new Date().getFullYear(), 11, 31);
            this.fromDate = new Date(new Date().getFullYear(), 0, 1);
        } else if (this.toDate != null && this.toDate < this.fromDate) {
            this._toasts.show(
                'Invalid Date Range',
                '"From" date cannot be later than the "To" date',
                ToastType.error,
                3000
            );
            this.toDate = new Date(new Date().getFullYear(), 11, 31);
            this.fromDate = new Date(new Date().getFullYear(), 0, 1);
        } else if (this.fromDate.getTime() == this.toDate.getTime()) {
            this._toasts.show(
                'Same Date',
                'You have selected the same date for both "From" and "To".',
                ToastType.warning,
                3000
            );
        }
        localStorage.setItem(
            'fromdateinspectionmonthlytasktracker',
            JSON.stringify(this.fromDate)
        );
        localStorage.setItem(
            'todateinspectionmonthlytasktracker',
            JSON.stringify(this.toDate)
        );
        this.selectedOption = this.currentYearAssetOptions.value;
        if (this.selectedOption.length == 0) {
            this.thisYearInspections = this.inspections;
        } else if (this.inspections) {
            this.thisYearInspections = this.inspections.filter((inspection) =>
                this.selectedOption.includes(inspection.inspectionassetcategory)
            );
        }
        this.filteredDataThisYear = [];
        this.aggregateInspectionsForThisYear = [];
        this.buildDataForThisYearCharts(this.thisYearInspections);
    }
    onDoughnutDropdownChange(event) {
        localStorage.setItem(
            'assetCategoryOptionsInspectionDoughnut',
            JSON.stringify(event.value)
        );
        const selectedValue = event.value;
        if (selectedValue.length == 0) {
            this.doughnutInspections = this.pieChartinspections;
        } else if (this.pieChartinspections) {
            this.doughnutInspections = this.pieChartinspections.filter(
                (inspection) =>
                    selectedValue.includes(inspection.inspectionassetcategory)
            );
        }
        this.overdueInspections = [];
        this.dueThisYearInspections = [];
        this.dueNextYearInspections = [];
        this.overdueMap = new Map<string, number>();
        this.dueThisYearMap = new Map<string, number>();
        this.dueNextYearMap = new Map<string, number>();
        this.buildDataForDoughnut(this.doughnutInspections);
    }
    private buildDataForDoughnut(inspections: AssetInspection[]) {
        if (!this.isincludeoutofservicechecked) {
            inspections = inspections.filter((inspection) => {
                return (
                    inspection.assetstatus !== 'Removed from Unit' &&
                    inspection.assetstatus !== 'Out of Service'
                );
            });
        }
        inspections = inspections.filter((inspection: AssetInspection) => {
            return inspection.schedulestatus.trim() === 'Active';
        });

        const selectedValue = this.selectedDoughnutCategories;
        if (selectedValue?.length !== 0 && inspections?.length !== 0) {
            inspections = inspections.filter((inspection) =>
                selectedValue.includes(inspection.inspectionassetcategory)
            );
        }
        for (const inspection of inspections) {
            if (inspection.isOverdue()) {
                this.buildInspectionHistogram(inspection, this.overdueMap);
            } else if (inspection.isDueThisYear()) {
                this.buildInspectionHistogram(inspection, this.dueThisYearMap);
            } else if (inspection.isDueNextYear()) {
                this.buildInspectionHistogram(inspection, this.dueNextYearMap);
            }
        }
        for (const [key, value] of this.overdueMap) {
            this.overdueInspections.push({ scheduletype: key, count: value });
        }

        for (const [key, value] of this.dueThisYearMap) {
            this.dueThisYearInspections.push({
                scheduletype: key,
                count: value
            });
        }

        for (const [key, value] of this.dueNextYearMap) {
            this.dueNextYearInspections.push({
                scheduletype: key,
                count: value
            });
        }
    }
    onThisYearChartDropdownChange(event) {
        localStorage.setItem(
            'assetCategoryOptionsInspectionYearChart',
            JSON.stringify(event.value)
        );
        const selectedValue = event.value;
        if (selectedValue.length == 0) {
            this.thisYearInspections = this.inspections;
        } else if (this.inspections) {
            this.thisYearInspections = this.inspections.filter((inspection) =>
                selectedValue.includes(inspection.inspectionassetcategory)
            );
        }
        this.aggregateInspectionsForThisYear = [];
        this.buildDataForThisYearCharts(this.thisYearInspections);
    }
    populateAssetType(
        assetInspections: AssetInspection[],
        assets: Asset[]
    ): AssetInspection[] {
        if (!assetInspections || !assets) {
            return [];
        }
        return assetInspections.map((inspection) => {
            const matchedAsset = assets.find(
                (asset) => String(asset.id) === inspection.assetid
            );
            if (matchedAsset) {
                inspection.inspectionassetcategory = matchedAsset.assetcategory;
            }
            return inspection;
        });
    }

    overduePointClicked(e) {
        const { scheduletype } = e.target.data;
        const selectedValues = this.doughnutAssetOptions.value;
        const filter: any = [
            ['nextinspectiondue', '<', new Date()],
            'and',
            [
                'scheduletype',
                '=',
                scheduletype === 'null' ? null : scheduletype
            ],
            'and',
            ['schedulestatus', '=', 'Active']
        ];

        if (selectedValues.length > 0) {
            filter.push('and', [
                'inspectionassetcategory',
                'anyof',
                selectedValues
            ]);
        }
        if (!this.isincludeoutofservicechecked) {
            filter.push('and', [
                'assetstatus',
                'noneof',
                ['Removed from Unit', 'Out of Service']
            ]);
        }
        this.drillDown(filter, 'Overdue Inspections');
    }
    overdueCenterTextClicked(e) {
        const selectedValues = this.doughnutAssetOptions.value;
        const filter: any = [
            ['nextinspectiondue', '<', new Date()],
            'and',
            ['schedulestatus', '=', 'Active']
        ];

        if (selectedValues.length > 0) {
            filter.push('and', [
                'inspectionassetcategory',
                'anyof',
                selectedValues
            ]);
        }
        if (!this.isincludeoutofservicechecked) {
            filter.push('and', [
                'assetstatus',
                'noneof',
                ['Removed from Unit', 'Out of Service']
            ]);
        }
        this.drillDown(filter, 'Overdue Inspections');
    }

    dueThisYearPointClicked(e) {
        const currentYear = new Date().getFullYear();
        const { scheduletype } = e.target.data;
        const selectedValues = this.doughnutAssetOptions.value;
        const now = new Date();
        now.setHours(0, 0, 0, 0);

        const filter: any = [
            ['nextinspectiondue', '>=', new Date(currentYear, 0, 1)],
            'and',
            ['nextinspectiondue', '<', new Date(currentYear + 1, 0, 1)],
            'and',
            [
                'scheduletype',
                '=',
                scheduletype === 'null' ? null : scheduletype
            ],
            'and',
            ['nextinspectiondue', '>=', now],
            'and',
            ['schedulestatus', '=', 'Active']
        ];

        if (selectedValues.length > 0) {
            filter.push('and', [
                'inspectionassetcategory',
                'anyof',
                selectedValues
            ]);
        }
        if (!this.isincludeoutofservicechecked) {
            filter.push('and', [
                'assetstatus',
                'noneof',
                ['Removed from Unit', 'Out of Service']
            ]);
        }
        this.drillDown(filter, 'Inspections Due This Year');
    }

    dueThisYearCenterTextClicked(e) {
        const currentYear = new Date().getFullYear();
        const now = new Date();
        now.setHours(0, 0, 0, 0);
        const selectedValues = this.doughnutAssetOptions.value;

        const filter: any = [
            ['nextinspectiondue', '>=', new Date(currentYear, 0, 1)],
            'and',
            ['nextinspectiondue', '<', new Date(currentYear + 1, 0, 1)],
            'and',
            ['nextinspectiondue', '>=', now],
            'and',
            ['schedulestatus', '=', 'Active']
        ];

        if (selectedValues.length > 0) {
            filter.push('and', [
                'inspectionassetcategory',
                'anyof',
                selectedValues
            ]);
        }
        if (!this.isincludeoutofservicechecked) {
            filter.push('and', [
                'assetstatus',
                'noneof',
                ['Removed from Unit', 'Out of Service']
            ]);
        }
        this.drillDown(filter, 'Inspections Due This Year');
    }

    dueNextYearPointClicked(e) {
        const currentYear = new Date().getFullYear();
        const { scheduletype } = e.target.data;
        const selectedValues = this.doughnutAssetOptions.value;

        const filter: any = [
            ['nextinspectiondue', '>=', new Date(currentYear + 1, 0, 1)],
            'and',
            ['nextinspectiondue', '<', new Date(currentYear + 2, 0, 1)],
            'and',
            [
                'scheduletype',
                '=',
                scheduletype === 'null' ? null : scheduletype
            ],
            'and',
            ['schedulestatus', '=', 'Active']
        ];

        if (selectedValues.length > 0) {
            filter.push('and', [
                'inspectionassetcategory',
                'anyof',
                selectedValues
            ]);
        }
        if (!this.isincludeoutofservicechecked) {
            filter.push('and', [
                'assetstatus',
                'noneof',
                ['Removed from Unit', 'Out of Service']
            ]);
        }
        this.drillDown(filter, 'Inspections Due Next Year');
    }

    dueNextYearCenterTextClicked(e) {
        const currentYear = new Date().getFullYear();
        const selectedValues = this.doughnutAssetOptions.value;

        const filter: any = [
            ['nextinspectiondue', '>=', new Date(currentYear + 1, 0, 1)],
            'and',
            ['nextinspectiondue', '<', new Date(currentYear + 2, 0, 1)],
            'and',
            ['schedulestatus', '=', 'Active']
        ];

        if (selectedValues.length > 0) {
            filter.push('and', [
                'inspectionassetcategory',
                'anyof',
                selectedValues
            ]);
        }
        if (!this.isincludeoutofservicechecked) {
            filter.push('and', [
                'assetstatus',
                'noneof',
                ['Removed from Unit', 'Out of Service']
            ]);
        }
        this.drillDown(filter, 'Inspections Due Next Year');
    }

    aggregateInspectionsForThisYearPointClicked($event) {
        const now = new Date();
        now.setHours(0, 0, 0, 0);
        const currentYear = now.getFullYear();
        const selectedOptions = this.nextYearAssetOptions.value;
        const monthIndex = this._months.indexOf($event.target.data.month);
        const filter: any = [
            ['nextinspectiondue', '>', new Date(currentYear, monthIndex, 0)],
            'and',
            [
                'nextinspectiondue',
                '<=',
                new Date(currentYear, monthIndex + 1, 0)
            ]
        ];
        const series = $event.target.series.name;
        if (series === 'Complete') {
            filter.push('and');
            filter.push(['completedinspectiondue', '<>', null]);
        } else if (series === 'Overdue') {
            filter.push('and');
            filter.push(['nextinspectiondue', '<', now]);
            filter.push('and');
            filter.push(['date', '=', null]);
        } else if (series === 'Upcoming') {
            filter.push('and');
            filter.push(['nextinspectiondue', '>=', now]);
            filter.push('and');
            filter.push(['date', '=', null]);
        }
        if (selectedOptions.length > 0) {
            filter.push('and');
            filter.push(['inspectionassetcategory', 'anyof', selectedOptions]);
        }
        this.drillDown(filter, 'Monthly Task Tracker');
    }

    aggregateInspectionsForNextYearPointClicked($event) {
        const now = new Date();
        now.setHours(0, 0, 0, 0);
        const currentYear = now.getFullYear();
        const monthIndex = this._months.indexOf($event.target.data.month);
        const selectedOptions = this.nextYearAssetOptions.value;
        const filter: any = [];
        const series = $event.target.series.name;
        if (series === 'Complete') {
            filter.push([
                'completedinspectiondue',
                '>',
                new Date(currentYear + 1, monthIndex, 0)
            ]);
            filter.push('and');
            filter.push([
                'completedinspectiondue',
                '<=',
                new Date(currentYear + 1, monthIndex + 1, 0)
            ]);
        } else if (series === 'Overdue') {
            filter.push(['nextinspectiondue', '<', now]);
            filter.push('and');
            filter.push([
                'nextinspectiondue',
                '>',
                new Date(currentYear + 1, monthIndex, 0)
            ]);
            filter.push('and');
            filter.push([
                'nextinspectiondue',
                '<=',
                new Date(currentYear + 1, monthIndex + 1, 0)
            ]);
        } else if (series === 'Upcoming') {
            filter.push(['nextinspectiondue', '>=', now]);
            filter.push('and');
            filter.push([
                'nextinspectiondue',
                '>',
                new Date(currentYear + 1, monthIndex, 0)
            ]);
            filter.push('and');
            filter.push([
                'nextinspectiondue',
                '<=',
                new Date(currentYear + 1, monthIndex + 1, 0)
            ]);
        }
        if (selectedOptions.length > 0) {
            filter.push('and');
            filter.push(['inspectionassetcategory', 'anyof', selectedOptions]);
        }

        this.drillDown(filter, 'Next Year Monthly Task Tracker');
    }

    private drillDown(currentFilter: any[], breadCrumbLabel: string) {
        this._router.navigate(['/aimaas/inspection-drilldown'], {
            state: { data: { currentFilter }, breadCrumbLabel: breadCrumbLabel }
        });
    }

    /**
     * building out the histograms for doughnut charts
     */
    private buildInspectionHistogram(
        inspection: AssetInspection,
        histogram: Map<string, number>
    ) {
        if (
            inspection.scheduletype === undefined ||
            inspection.scheduletype === null
        ) {
            const nullValue = 'null';
            histogram.set(
                nullValue,
                histogram.has(nullValue) ? histogram.get(nullValue) + 1 : 1
            );
        } else {
            histogram.set(
                inspection.scheduletype,
                histogram.has(inspection.scheduletype)
                    ? histogram.get(inspection.scheduletype) + 1
                    : 1
            );
        }
    }

    private buildAggregateInspectionHistogram(
        inspection: AssetInspection,
        checkDate: Date,
        scheduletype: string
    ) {
        const month = this._months[checkDate.getMonth()];
        if (scheduletype === 'completed') {
            if (inspection.isComplete()) {
                const index = this.aggregateInspectionsForThisYear.findIndex(
                    (i) => i.monthyear == month + ' ' + checkDate.getFullYear()
                );
                if (index !== -1) {
                    this.aggregateInspectionsForThisYear[index].complete += 1;
                } else {
                    let monthData: AggregateStatusInspection = {
                        monthyear: month + ' ' + checkDate.getFullYear(),
                        upcoming: 0,
                        overdue: 0,
                        complete: 1
                    };
                    this.aggregateInspectionsForThisYear.push(monthData);
                }
            }
        } else if (scheduletype === 'overdue/upcoming') {
            const now = new Date();
            now.setHours(0, 0, 0, 0);
            if (checkDate < now) {
                const index = this.aggregateInspectionsForThisYear.findIndex(
                    (i) => i.monthyear == month + ' ' + checkDate.getFullYear()
                );
                if (index !== -1) {
                    this.aggregateInspectionsForThisYear[index].overdue += 1;
                } else {
                    let monthData: AggregateStatusInspection = {
                        monthyear: month + ' ' + checkDate.getFullYear(),
                        upcoming: 0,
                        overdue: 1,
                        complete: 0
                    };
                    this.aggregateInspectionsForThisYear.push(monthData);
                }
            } else if (checkDate >= now) {
                const index = this.aggregateInspectionsForThisYear.findIndex(
                    (i) => i.monthyear == month + ' ' + checkDate.getFullYear()
                );
                if (index !== -1) {
                    this.aggregateInspectionsForThisYear[index].upcoming += 1;
                } else {
                    let monthData: AggregateStatusInspection = {
                        monthyear: month + ' ' + checkDate.getFullYear(),
                        upcoming: 1,
                        overdue: 0,
                        complete: 0
                    };
                    this.aggregateInspectionsForThisYear.push(monthData);
                }
            }
        }
    }

    private resetChartData() {
        this.stateInspectionThisYearCount = 0;
        this.stateInspectionNextYearCount = 0;
        this.overdueInspections = [];
        this.dueThisYearInspections = [];
        this.dueNextYearInspections = [];
        this.aggregateInspectionsForNextYear = [];
        this.aggregateInspectionsForThisYear = [];
        this.overdueMap = new Map<string, number>();
        this.dueNextYearMap = new Map<string, number>();
        this.dueThisYearMap = new Map<string, number>();
    }

    filterInspections(
        inspections: AssetInspection[],
        fromDate: Date,
        toDate: Date
    ) {
        const selectedValue = this.selectedBarGraphCategories;
        if (selectedValue?.length !== 0 && inspections?.length !== 0) {
            inspections = inspections.filter((inspection) =>
                selectedValue.includes(inspection.inspectionassetcategory)
            );
        }
        inspections.filter((inspection) => {
            const lastDate = new Date(inspection.completedinspectiondue);
            lastDate.setHours(0, 0, 0, 0);
            const nextinspectiondue = new Date(inspection.nextinspectiondue);
            nextinspectiondue.setHours(0, 0, 0, 0);
            if (fromDate && toDate) {
                if (
                    nextinspectiondue.getTime() <= toDate.getTime() &&
                    nextinspectiondue.getTime() >= fromDate.getTime()
                ) {
                    this.buildAggregateInspectionHistogram(
                        inspection,
                        nextinspectiondue,
                        'overdue/upcoming'
                    );
                }
                if (
                    lastDate.getTime() <= toDate.getTime() &&
                    lastDate.getTime() >= fromDate.getTime()
                ) {
                    this.buildAggregateInspectionHistogram(
                        inspection,
                        lastDate,
                        'completed'
                    );
                }
            } else if (fromDate) {
                // Only fromDate is provided
                // return lastDate >= fromDate || nextDate >= fromDate;
                if (nextinspectiondue >= fromDate) {
                    this.buildAggregateInspectionHistogram(
                        inspection,
                        nextinspectiondue,
                        'overdue/upcoming'
                    );
                }
                if (lastDate >= fromDate) {
                    this.buildAggregateInspectionHistogram(
                        inspection,
                        lastDate,
                        'completed'
                    );
                }
            } else if (toDate) {
                // Only toDate is provided
                //return lastDate <= toDate || nextDate <= toDate;
                if (nextinspectiondue <= toDate) {
                    this.buildAggregateInspectionHistogram(
                        inspection,
                        nextinspectiondue,
                        'overdue/upcoming'
                    );
                }
                if (lastDate <= toDate) {
                    this.buildAggregateInspectionHistogram(
                        inspection,
                        lastDate,
                        'completed'
                    );
                }
            }
        });
    }
    private buildDataForThisYearCharts(inspections: AssetInspection[]) {
        this.filterInspections(inspections, this.fromDate, this.toDate);
        this.filteredDataThisYear = this.aggregateInspectionsForThisYear;
        this.sortDataByMonthYear(this.filteredDataThisYear);
    }
    sortDataByMonthYear(data: AggregateStatusInspection[]) {
        data.sort((a, b) => {
            const [monthA, yearA] = a.monthyear.split(' ');
            const [monthB, yearB] = b.monthyear.split(' ');
            // Compare year first
            if (yearA !== yearB) {
                return parseInt(yearA) - parseInt(yearB);
            }
            // Compare month using the _months array index
            return this._months.indexOf(monthA) - this._months.indexOf(monthB);
        });
    }
    private buildDataForNextYearCharts(inspections: AssetInspection[]) {
        for (const inspection of inspections) {
            const currentYear = new Date().getFullYear();
            const dueDate = new Date(inspection.nextinspectiondue);
            const aggregate = this.aggregateInspectionsForNextYear;
        }

        this.aggregateInspectionsForNextYear = [
            ...this.aggregateInspectionsForNextYear
        ];
        this.filteredDataNextYear = this.aggregateInspectionsForNextYear;
    }

    private buildDataForOverDueDoughnut(inspections: AssetInspection[]) {
        if (!this.isincludeoutofservicechecked) {
            inspections = inspections?.filter((inspection) => {
                return (
                    inspection.assetstatus !== 'Removed from Unit' &&
                    inspection.assetstatus !== 'Out of Service'
                );
            });
        }
        const selectedValue = this.selectedDoughnutCategories;
        if (selectedValue?.length !== 0 && inspections?.length !== 0) {
            inspections = inspections.filter((inspection) =>
                selectedValue.includes(inspection.inspectionassetcategory)
            );
        }
        inspections = inspections.filter((inspection: AssetInspection) => {
            return inspection.schedulestatus.trim() === 'Active';
        });
        for (const inspection of inspections) {
            if (inspection.isOverdue() && inspection.scheduletype !== null) {
                this.buildInspectionHistogram(inspection, this.overdueMap);
            }
        }
        for (const [key, value] of this.overdueMap) {
            this.overdueInspections.push({ scheduletype: key, count: value });
        }
    }
    private buildDataForDueThisYearDoughnut(inspections: AssetInspection[]) {
        if (!this.isincludeoutofservicechecked) {
            inspections = inspections.filter((inspection) => {
                return (
                    inspection.assetstatus !== 'Removed from Unit' &&
                    inspection.assetstatus !== 'Out of Service'
                );
            });
        }
        const selectedValue = this.selectedDoughnutCategories;
        if (selectedValue?.length !== 0 && inspections?.length !== 0) {
            inspections = inspections.filter((inspection) =>
                selectedValue.includes(inspection.inspectionassetcategory)
            );
        }
        inspections = inspections.filter((inspection: AssetInspection) => {
            return inspection.schedulestatus.trim() === 'Active';
        });
        for (const inspection of inspections) {
            if (
                inspection.isDueThisYear() &&
                inspection.scheduletype !== null
            ) {
                this.buildInspectionHistogram(inspection, this.dueThisYearMap);
            }
        }
        for (const [key, value] of this.dueThisYearMap) {
            this.dueThisYearInspections.push({
                scheduletype: key,
                count: value
            });
        }
    }
    private buildDataForDueNextYearDoughnut(inspections: AssetInspection[]) {
        if (!this.isincludeoutofservicechecked) {
            inspections = inspections.filter((inspection) => {
                return (
                    inspection.assetstatus !== 'Removed from Unit' &&
                    inspection.assetstatus !== 'Out of Service'
                );
            });
        }
        const selectedValue = this.selectedDoughnutCategories;
        if (selectedValue?.length !== 0 && inspections?.length !== 0) {
            inspections = inspections.filter((inspection) =>
                selectedValue.includes(inspection.inspectionassetcategory)
            );
        }

        inspections = inspections.filter((inspection: AssetInspection) => {
            return inspection.schedulestatus.trim() === 'Active';
        });
        for (const inspection of inspections) {
            if (
                inspection.isDueNextYear() &&
                inspection.scheduletype !== null
            ) {
                this.buildInspectionHistogram(inspection, this.dueNextYearMap);
            }
        }

        for (const [key, value] of this.dueNextYearMap) {
            this.dueNextYearInspections.push({
                scheduletype: key,
                count: value
            });
        }
    }

    onThisYearChartPointClicked($event) {
        const selectedOptions = this.currentYearAssetOptions.value;
        const now = new Date();
        now.setHours(0, 0, 0, 0);
        const dateStr = $event.target.data.monthyear;
        const [month, year] = dateStr.split(' ');

        const currentYear = year;
        const monthIndex = this._months.indexOf(month);
        const filter: any = [];
        const series = $event.target.series.name;
        if (series === 'Complete') {
            filter.push([
                'completedinspectiondue',
                '>',
                new Date(currentYear, monthIndex, 0)
            ]);
            filter.push('and');
            filter.push([
                'completedinspectiondue',
                '<=',
                new Date(currentYear, monthIndex + 1, 0)
            ]);
            filter.push('and');
            filter.push(['completedinspectiondue', '<>', null]);
        } else if (series === 'Overdue') {
            filter.push(['nextinspectiondue', '<', now]);
            filter.push('and');
            filter.push([
                'nextinspectiondue',
                '>',
                new Date(currentYear, monthIndex, 0)
            ]);
            filter.push('and');
            filter.push([
                'nextinspectiondue',
                '<=',
                new Date(currentYear, monthIndex + 1, 0)
            ]);
        } else if (series === 'Upcoming') {
            filter.push(['nextinspectiondue', '>=', now]);
            filter.push('and');
            filter.push([
                'nextinspectiondue',
                '>',
                new Date(currentYear, monthIndex, 0)
            ]);
            filter.push('and');
            filter.push([
                'nextinspectiondue',
                '<=',
                new Date(currentYear, monthIndex + 1, 0)
            ]);
        }
        if (selectedOptions.length > 0) {
            filter.push('and');
            filter.push(['inspectionassetcategory', 'anyof', selectedOptions]);
        }
        this.drillDown(filter, 'Monthly Task Tracker');
    }
    getMonthIndex(monthName: string): number {
        return this._months.indexOf(monthName);
    }
    onincludeoutofserviceValueChanged(e) {
        localStorage.setItem(
            'inspectiondoughnutoutofservicecheckbox',
            JSON.stringify(this.isincludeoutofservicechecked)
        );
        const selectedValue = this.doughnutAssetOptions.value;
        if (selectedValue.length == 0) {
            this.doughnutInspections = this.pieChartinspections;
        } else if (this.pieChartinspections) {
            this.doughnutInspections = this.pieChartinspections.filter(
                (inspection) =>
                    selectedValue.includes(inspection.inspectionassetcategory)
            );
        }
        this.overdueInspections = [];
        this.dueThisYearInspections = [];
        this.dueNextYearInspections = [];
        this.overdueMap = new Map<string, number>();
        this.dueThisYearMap = new Map<string, number>();
        this.dueNextYearMap = new Map<string, number>();
        this.buildDataForDoughnut(this.doughnutInspections);
    }
    customizeLabel(point) {
        return point.argument; // Display the legend name and value
    }
    clearFilter() {
        this.toDate = new Date(new Date().getFullYear(), 11, 31);
        this.fromDate = new Date(new Date().getFullYear(), 0, 1);
        localStorage.setItem(
            'fromdateinspectionmonthlytasktracker',
            JSON.stringify(this.fromDate)
        );
        localStorage.setItem(
            'todateinspectionmonthlytasktracker',
            JSON.stringify(this.toDate)
        );
        this.thisYearInspections = this.inspections;
        this.filteredDataThisYear = [];
        this.aggregateInspectionsForThisYear = [];
        this.selectedBarGraphCategories = [];
        this.buildDataForThisYearCharts(this.thisYearInspections);
    }
}
