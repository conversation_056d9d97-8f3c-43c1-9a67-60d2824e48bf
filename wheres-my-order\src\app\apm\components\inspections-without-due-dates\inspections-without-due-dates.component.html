<div class="dx-card responsive-paddings content-block"
     style="position: relative">
    <dx-load-indicator *ngIf="loading"
                       style="position: absolute; left: calc(50% - 30px); top: calc(50% - 30px)"
                       height="60"
                       width="60"></dx-load-indicator>

    <dx-pie-chart [dataSource]="chartData"
                  [resolveLabelOverlapping]="'shift'"
                  [palette]="'Soft Pastel'">
        <dxo-title [text]="'Inspections Without Due Dates'"></dxo-title>
        <dxi-series [argumentField]="'assetCategory'"
                    [valueField]="'count'">
            <dxo-label [visible]="true"
                       [customizeText]="customizeLabel">
                <dxo-connector [visible]="true">
                </dxo-connector>
            </dxo-label>
        </dxi-series>
        <dxo-legend [visible]="true"
                    [horizontalAlignment]="'center'"
                    [verticalAlignment]="'bottom'"></dxo-legend>
    </dx-pie-chart>
    <p style="display: block; text-align: center;">
        Total Inspections Without Due Date: {{taskCount}}
    </p>
</div>