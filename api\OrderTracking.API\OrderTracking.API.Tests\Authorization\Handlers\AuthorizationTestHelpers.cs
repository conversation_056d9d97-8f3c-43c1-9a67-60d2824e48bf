﻿using System.Collections.Generic;
using System.Security.Claims;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;

namespace OrderTracking.API.Tests.Authorization.Handlers
{
    public static class AuthorizationTestHelpers
    {
        public static AuthorizationHandlerContext CreateAuthorizationHandlerContext(UserProfile currentUserProfile,
            UserProfile resource, IEnumerable<IAuthorizationRequirement> requirements)
        {
            var user =
                new ClaimsPrincipal(new ClaimsIdentity(
                    new[] {new Claim(ClaimsIdentity.DefaultNameClaimType, currentUserProfile.Id)}, "Basic"));
            return new AuthorizationHandlerContext(requirements, user, resource);
        }

        public static AuthorizationHandlerContext CreateAuthorizationHandlerContextNotAuthenticated(
            UserProfile currentUserProfile,
            UserProfile resource, IEnumerable<IAuthorizationRequirement> requirements)
        {
            var user = new ClaimsPrincipal(new ClaimsIdentity());
            return new AuthorizationHandlerContext(requirements, user, resource);
        }
    }
}