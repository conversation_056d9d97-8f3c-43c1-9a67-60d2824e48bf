import { Component, Input, ViewChild } from '@angular/core';
import { DxChartComponent } from 'devextreme-angular/ui/chart';
import { EquipmentByArea } from '../../models';

@Component({
    selector: 'app-equipment-by-area-and-type',
    templateUrl: './equipment-by-area-and-type.component.html',
    styleUrls: ['./equipment-by-area-and-type.component.scss']
})
export class EquipmentByAreaAndTypeComponent {
    _chartData: EquipmentByArea[];
    @Input() set chartData(values: EquipmentByArea[]) {
        if (!values) {
            return;
        }
        this._chartData = values;
    }
    get chartData() {
        return this._chartData;
    }
    @Input() loading: boolean;
    @ViewChild(DxChartComponent) chart: DxChartComponent;

    constructor() {}

    renderChart() {
        this.chart?.instance?.render({ force: true });
    }

    resetZoom(chart: DxChartComponent) {
        chart.instance.resetVisualRange();
    }
}
