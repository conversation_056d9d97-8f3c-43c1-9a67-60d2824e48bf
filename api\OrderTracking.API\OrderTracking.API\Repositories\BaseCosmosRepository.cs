using ClientPortal.Shared.Models;
using Microsoft.Azure.Cosmos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    /// Base repository for Azure Cosmos DB operations (migrated from Firebase)
    /// </summary>
    public abstract class BaseCosmosRepository<TEntity, TKey> : IAsyncCosmosRepository<TEntity, TKey>
        where TEntity : ICosmosEntity<TKey>
    {
        protected Container Container { get; }
        protected string PartitionKeyPath { get; }

        protected BaseCosmosRepository(Container container, string partitionKeyPath)
        {
            Container = container ?? throw new ArgumentNullException(nameof(container));
            PartitionKeyPath = partitionKeyPath ?? throw new ArgumentNullException(nameof(partitionKeyPath));
        }

        public virtual async Task<TEntity> AddAsync(TEntity entity)
        {
            try
            {
                var response = await Container.CreateItemAsync(entity, GetPartitionKey(entity));
                return response.Resource;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual async Task<IEnumerable<TEntity>> GetAllAsync()
        {
            try
            {
                var query = Container.GetItemQueryIterator<TEntity>();
                var results = new List<TEntity>();
                
                while (query.HasMoreResults)
                {
                    var response = await query.ReadNextAsync();
                    results.AddRange(response.ToList());
                }
                
                return results;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public abstract Task<TEntity> GetAsync(TKey entityId);

        public virtual async Task<TEntity> GetAsync(TKey id, string partitionId)
        {
            try
            {
                var response = await Container.ReadItemAsync<TEntity>(id.ToString(), new PartitionKey(partitionId));
                return response.Resource;
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return default;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual async Task RemoveAsync(TKey id)
        {
            try
            {
                // For simple removal, we need to get the item first to determine partition key
                var entity = await GetAsync(id);
                if (entity != null)
                {
                    await Container.DeleteItemAsync<TEntity>(id.ToString(), GetPartitionKey(entity));
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public abstract Task RemoveAsync(TKey id, string partitionId);

        public virtual async Task RemoveAsync(TEntity entity)
        {
            try
            {
                await Container.DeleteItemAsync<TEntity>(entity.Id.ToString(), GetPartitionKey(entity));
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual async Task<TEntity> UpdateAsync(TEntity entity, TEntity originalEntity)
        {
            try
            {
                var response = await Container.ReplaceItemAsync(entity, entity.Id.ToString(), GetPartitionKey(entity));
                return response.Resource;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public abstract Task<TEntity> UpdateAsync(TEntity entity, TKey originalId);

        public async Task<TEntity> UpdateAsync(TEntity entity)
        {
            try
            {
                var response = await Container.ReplaceItemAsync(entity, entity.Id.ToString(), GetPartitionKey(entity));
                return response.Resource;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        /// <summary>
        /// Get the partition key for an entity
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        protected virtual PartitionKey GetPartitionKey(TEntity entity)
        {
            // Default implementation uses the entity ID as partition key
            // Override in derived classes if different partition key logic is needed
            return new PartitionKey(entity.Id.ToString());
        }
    }
}
