export class InspectionAttachment {
    documentid: string;
    inspectionid: string;
    description: string;
    filename: string;
    documenttype: string;
    creationdate: string;
    filelink: string;
    filetype: string;
    // objid: number;
    // emid: number;
    // objname: string;
    // urlR_CREATETIME: Date;
    // urlR_FILE_NAME: string;
    // urlR_PROP_TYPE_DOC: string;
    // urlR_RID: number;
    // urlR_URL: string;
    // urlaE_RID: number;
    // rsitE_RID: number;

    constructor(options?: Partial<InspectionAttachment>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }
    }
}
