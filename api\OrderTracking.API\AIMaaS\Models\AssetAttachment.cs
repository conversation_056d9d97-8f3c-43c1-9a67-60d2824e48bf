﻿using System;

// (properties are mapped to database column names and we are not in control of the naming convention)
// <PERSON><PERSON><PERSON><PERSON> disable InconsistentNaming
// <PERSON><PERSON><PERSON><PERSON> disable UnusedMember.Global
// ReSharper disable IdentifierTypo

namespace AIMaaS.Models
{
    /// <summary>
    ///     Attachment meta data for files associated to assets in CredoSoft
    /// </summary>
    // Re<PERSON><PERSON>per disable once ClassNeverInstantiated.Global
    public class AssetAttachment
    {
#pragma warning disable CA1707 // Identifiers should not contain underscores
        /// <summary>
        ///     Name of object
        /// </summary>
        public string OBJNAME { get; set; }

        /// <summary>
        ///     File name
        /// </summary>
        public string URLR_FILE_NAME { get; set; }

        /// <summary>
        ///     Really not sure...
        /// </summary>
        public string URLR_PROP_TYPE_DOC { get; set; }

        /// <summary>
        ///     File extension
        /// </summary>
        public string URLR_FILE_EXT { get; set; }

        /// <summary>
        ///     Time of creation
        /// </summary>
        public DateTime? URLR_CREATETIME { get; set; }

        /// <summary>
        ///     Url
        /// </summary>
#pragma warning disable CA1056 // URI-like properties should not be strings (this is mapped to a database column that we do not control)
        public string URLR_URL { get; set; }
#pragma warning restore CA1056 // URI-like properties should not be strings

        /// <summary>
        ///     Object ID
        /// </summary>
        public long OBJID { get; set; }

        /// <summary>
        ///     Absolutely zero clues 
        /// </summary>
        public long URLAO_RID { get; set; }

        /// <summary>
        ///     Not a clue here either.
        /// </summary>
        public long URLR_RID { get; set; }

        /// <summary>
        ///     Not a clue here either.
        /// </summary>
        public long RSITE_RID { get; set; }
#pragma warning restore CA1707 // Identifiers should not contain underscores
    }
}