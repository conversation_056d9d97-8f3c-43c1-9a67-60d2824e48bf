import {
    Component,
    EventEmitter,
    Input,
    OnInit,
    Output,
    ViewChild
} from '@angular/core';
import { DxDataGridComponent } from 'devextreme-angular';
import { ToastrService } from 'ngx-toastr';
import { finalize, firstValueFrom } from 'rxjs';

import cloneDeep from 'clone-deep';
import { of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import {
    PhotoObject,
    ReportTypes
} from '../../../report/models/report-data.model';
import { ReportRendererFactory } from '../../../report/models/report-renderers.model';
import {
    AssetWalkdown,
    FullPackage,
    TaskTypes
} from '../../../report/models/report-source.model';
import { SourceReportRendererFactory } from '../../../report/models/source-report-renderer.factory';
import { isNullOrUndefined } from '../../../shared/helpers';
import { LocalBlobService, UsersService } from '../../../shared/services';
import { Task, WorkOrder, WorkOrderDetail } from '../../models';
import { PublishUnpublishWorkOrderUpdate } from '../../models/transport-objects/publish-unpublish-work-order-update';
import { ApmService, ReportTransport } from '../../services';

@Component({
    selector: 'app-report-tab',
    templateUrl: './report-tab.component.html',
    styleUrls: ['./report-tab.component.scss']
})
export class ReportTabComponent implements OnInit {
    private _workOrderDetail: WorkOrderDetail;
    @Input() set workOrderDetail(value: WorkOrderDetail) {
        this._workOrderDetail = cloneDeep(value);
        // hiding Asset walkdown
        // if (
        //     !['Piping', 'Vessel'].includes(this._workOrderDetail.assetCategory)
        // ) {
        //     this._workOrderDetail.inspectionTypes = cloneDeep(
        //         this._workOrderDetail.inspectionTypes.filter(
        //             (item) => item.taskType != 'Asset Walkdown'
        //         )
        //     );
        // }

        this.isMarkedAsPublished = value.status?.toLowerCase() === 'published';
        this.publishing = false;
        if (!isNaN(value?.fieldWorkCompleted?.getTime())) {
            this.fieldWorkCompletedDate = value.fieldWorkCompleted;
        }
    }
    get workOrderDetail(): WorkOrderDetail {
        return this._workOrderDetail;
    }

    @Input() workOrder: WorkOrder;
    @Input() allowEditing: boolean;
    @Output() publishedUnpublished =
        new EventEmitter<PublishUnpublishWorkOrderUpdate>();
    @ViewChild(DxDataGridComponent) dataGrid: DxDataGridComponent;
    isReportPopupDisplayed: boolean;
    hasSelections: boolean;
    isMarkedAsPublished: boolean;
    publishing: boolean;
    fieldWorkCompletedDate: Date;

    currentUser$ = this._users.currentProfile$;
    generatingReports = false;

    constructor(
        private readonly _users: UsersService,
        private readonly _apm: ApmService,
        private readonly _localBlob: LocalBlobService,
        private readonly _toasts: ToastrService
    ) {}

    async ngOnInit() {}

    onGenerateNewReportClicked(e) {
        this.isReportPopupDisplayed = true;
    }

    onReportsForSelectionsClicked(e) {
        this.generatingReports = true;
        const task: Task = this.dataGrid.instance.getSelectedRowsData()[0];
        let taskType = task.taskType;

        const payload: ReportTransport = {
            workOrderId: this.workOrderDetail.id,
            projectId: this.workOrderDetail.projectId,
            reportType: this.determineReportType(taskType),
            taskType: taskType,
            assetCategory: this.workOrderDetail.assetCategory
        };

        let adjustFull = false;

        switch (taskType) {
            case 'External Visual':
                payload.externalTaskId = task.id;
                break;
            case 'Full':
                adjustFull = true;
                payload.internalTaskId = task.id;
                taskType = 'Internal Visual';
                payload.taskType = taskType;
                payload.reportType = this.determineReportType(taskType);
                break;
            case 'Internal Visual':
                payload.internalTaskId = task.id;
                break;
            case 'Asset Walkdown':
                this.getAssetWalkdownReport(e);
                return;
            default:
                break;
        }

        this._apm
            .getFullPackage(payload)
            .pipe(
                finalize(() => {
                    this.generatingReports = false;
                })
            )
            .subscribe({
                next: async (fullPackage) => {
                    if (adjustFull) {
                        fullPackage.fullInspection.internalTask.taskData =
                            fullPackage.fullInspection.internalTask.taskData[
                                'internalSection'
                            ];
                    }

                    const fullPackageWithTaskType = {
                        ...fullPackage,
                        taskType: taskType
                    };
                    const sourceReportRenderer =
                        SourceReportRendererFactory.getRenderer(
                            fullPackageWithTaskType
                        );

                    fullPackageWithTaskType.fullInspection.photoBlobs = [];
                    const photoBlobsPromises =
                        fullPackageWithTaskType.fullInspection.photos.map(
                            async (photo) => {
                                try {
                                    const url = await firstValueFrom(
                                        this._apm.getSignedUrl(
                                            photo.mediaEntry.blobName
                                        )
                                    );

                                    const blob = await this._apm
                                        .downloadFileFromUrl(url)
                                        .toPromise();

                                    return new PhotoObject(
                                        window.URL.createObjectURL(blob),
                                        ''
                                    );
                                } catch (error) {
                                    return new PhotoObject('', '');
                                }
                            }
                        );

                    fullPackageWithTaskType.fullInspection.photoBlobs =
                        await Promise.all(photoBlobsPromises);

                    const reportData = sourceReportRenderer.handle(
                        fullPackageWithTaskType
                    );
                    const model = {
                        type: ReportTypes.Report,
                        models: reportData
                    };
                    const renderer = ReportRendererFactory.getRenderer(model);
                    const encodedHtml = renderer.render(model);
                    const windowReference = window.open();
                    windowReference.document.title = 'Generated Report';
                    windowReference.document.body.innerHTML = encodedHtml;
                    window.addEventListener('beforeunload', () => {
                        windowReference.close();
                    });
                    window.setTimeout(() => {
                        windowReference.print();
                    }, 1000);
                },
                error: () => {
                    this._toasts.error(
                        'This report is currently not supported',
                        'Failed to generate report',
                        { disableTimeOut: true }
                    );
                }
            });
    }

    getAssetWalkdownReport(e) {
        this.generatingReports = true;
        const type = this.dataGrid.instance.getSelectedRowsData()[0];

        const payload: ReportTransport = {
            workOrderId: this.workOrderDetail.id,
            projectId: this.workOrderDetail.projectId,
            reportType: this.determineReportType(type.taskType),
            taskType: type.taskType,
            assetCategory: this.workOrderDetail.assetCategory,
            walkDownTaskId: type.id
        };

        this._apm
            .getAssetWalkdownData(payload)
            .pipe(
                tap(async (fullPackage) => {
                    const fullPackageWithTaskType: FullPackage = {
                        fullInspection: null,
                        assetWalkdown: { ...(fullPackage as AssetWalkdown) },
                        taskType: TaskTypes.AssetWalkdown
                    };
                    const sourceReportRenderer =
                        SourceReportRendererFactory.getRenderer(
                            fullPackageWithTaskType as FullPackage
                        );

                    fullPackageWithTaskType.assetWalkdown.photoBlobs = [];
                    const photoBlobsPromises =
                        fullPackageWithTaskType.assetWalkdown.photos.map(
                            async (photo) => {
                                const url = await firstValueFrom(
                                    this._apm.getSignedUrl(
                                        photo.mediaEntry.blobName
                                    )
                                );

                                const blob = await this._apm
                                    .downloadFileFromUrl(url)
                                    .toPromise();

                                return new PhotoObject(
                                    window.URL.createObjectURL(blob),
                                    ''
                                );
                            }
                        );

                    fullPackageWithTaskType.assetWalkdown.photoBlobs =
                        await Promise.all(photoBlobsPromises);

                    const reportData = sourceReportRenderer.handle(
                        fullPackageWithTaskType
                    );
                    const model = {
                        type: ReportTypes.Report,
                        models: reportData
                    };
                    let title = '';
                    if (isNullOrUndefined(this.workOrderDetail.assetID)) {
                        title = 'NULL-ID';
                    } else if (
                        type.taskType.toLowerCase() === 'asset walkdown'
                    ) {
                        title = `${this.workOrderDetail.assetID} - Scoping`;
                    } else {
                        title = this.workOrderDetail.assetID;
                    }

                    const renderer = ReportRendererFactory.getRenderer(model);
                    const encodedHtml = renderer.render(model);
                    const windowReference = window.open();
                    windowReference.document.title = title;
                    windowReference.document.body.innerHTML = encodedHtml;
                    window.addEventListener('beforeunload', () => {
                        windowReference.close();
                    });
                    window.setTimeout(() => {
                        windowReference.print();
                    }, 1000);
                }),
                catchError((e) => {
                    if (e.status === 501) {
                        this._toasts.error(
                            'This report is currently not supported',
                            'Failed to generate report',
                            { disableTimeOut: true }
                        );
                    } else if (e.status === 400) {
                        this._toasts.error(
                            'This is likely due to the report payload exceeding the java memory of the reporting server',
                            'Error in creating report',
                            { disableTimeOut: true }
                        );
                    } else {
                        this._toasts.error(
                            'Report generation failed for an unknown reason',
                            'Error'
                        );
                    }

                    return of(e);
                })
            )
            .pipe(finalize(() => (this.generatingReports = false)))
            .subscribe(console.log);
    }

    onSelectionChanged(e) {
        this.hasSelections = e.selectedRowsData.length > 0;
    }

    onFieldWorkCompletedChanged = (e: any) => {
        if (
            this.fieldWorkCompletedDate !==
            this.workOrderDetail.fieldWorkCompleted
        ) {
            this._apm
                .updateFieldWorkCompletedDate({
                    fieldWorkCompletedDate: this.fieldWorkCompletedDate,
                    id: this.workOrderDetail.id,
                    projectId: this.workOrderDetail.projectId
                })
                .subscribe({
                    next: (success) =>
                        this._toasts.success(
                            'Updated Field Work Completed Date',
                            'Success'
                        ),
                    error: (error) => {
                        console.error(error);
                        const message = `Unable to update Field Work Completed Date (${error.statusText})`;
                        this._toasts.error(message, 'Error');
                    }
                });
        }
    };

    determineReportType(taskType: string): string {
        switch (this.workOrderDetail.assetCategory) {
            case 'Vessel': {
                switch (taskType) {
                    case 'External Visual':
                        return '510_Inspection_Ext.pdf';
                    case 'Full':
                    case 'Internal Visual':
                        return '510_Inspection_Full.pdf';
                    case 'Asset Walkdown':
                        return '510_Walkdown.pdf';
                    case 'Full':
                        return 'Full';
                    default:
                        return 'Not Implemented';
                }
            }

            case 'Piping': {
                switch (taskType) {
                    case 'External Visual':
                        return '570_Inspection.pdf';
                    case 'Internal Visual':
                        return 'Not Implemented';
                    case 'Asset Walkdown':
                        return '570_Walkdown.pdf';
                    case 'Full':
                        return 'Full';
                    default:
                        return 'Not Implemented';
                }
            }

            case 'Tank': {
                switch (taskType) {
                    case 'External Visual':
                        return '653_Inspection_Ext.pdf';
                    case 'Internal Visual':
                        return '653_Inspection_Full.pdf';
                    case 'Asset Walkdown':
                        return '653_Walkdown.pdf';
                    case 'Full':
                        return 'Full';
                    default:
                        return 'Not Implemented';
                }
            }

            default: {
                return 'Not Implemented';
            }
        }
    }

    onPublishUnpublishClicked(e) {
        this.publishing = true;
        this.publishedUnpublished.next({
            projectId: this.workOrderDetail.projectId,
            workOrderId: this.workOrderDetail.id,
            isPublishing: !this.isMarkedAsPublished
        });
    }

    onHidingPopup(e) {
        this.dataGrid.instance.deselectAll();
    }
}
