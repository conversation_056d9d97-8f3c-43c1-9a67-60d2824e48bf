import { HttpParams } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import cloneDeep from 'clone-deep';
import { DxDataGridComponent } from 'devextreme-angular/ui/data-grid';
import { DxFormComponent } from 'devextreme-angular/ui/form';
import ArrayStore from 'devextreme/data/array_store';
import DataSource from 'devextreme/data/data_source';
import { ClickEvent } from 'devextreme/ui/button';
import { FocusedRowChangedEvent } from 'devextreme/ui/data_grid';
import { confirm } from 'devextreme/ui/dialog';
import { ToastrService } from 'ngx-toastr';
import { forkJoin, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { AssetManagementSite } from '../../../aimaas/models';
import { CredoSoftService } from '../../../aimaas/services/credo-soft.service';
import { UserProfile } from '../../../profile/models';
import { SensorReading } from '../../../remote-monitoring/models';
import { SensorsService } from '../../../remote-monitoring/services';
import { Breadcrumb } from '../../../shared/components';
import { DistrictService, UsersService } from '../../../shared/services';
import { ExternalCustomer } from '../../models';
import { CustomerAccountsService } from '../../services';
import { RolesService } from './../../services';
import { customerAccountsDataSource } from './customer-accounts.data-source';

@Component({
    selector: 'app-users',
    templateUrl: './users.component.html',
    styleUrls: ['./users.component.scss']
})
export class UsersComponent implements OnInit {
    @ViewChild(DxFormComponent, { static: false }) userProfile: DxFormComponent;
    @ViewChild(DxDataGridComponent, { static: false })
    searchList: DxDataGridComponent;

    isFormValid = false;
    isUserSelected = false;
    focusedRowKey: any;
    usersDataSource: DataSource;
    userEdit: UserProfile = new UserProfile();
    userOriginal: UserProfile = new UserProfile();
    crumbs: Breadcrumb[] = [
        { label: 'Administration', route: '/admin' },
        { label: 'Users', route: '/admin/users' }
    ];
    customerAccounts: DataSource;

    districts$: Observable<string[]>;
    availableRoles$: Observable<string[]>;

    currentProfile$: Observable<UserProfile>;
    assetManagementSites$: Observable<AssetManagementSite[]>;
    remoteMonitoringIds: any;
    customerAccountsTooltipVisible: boolean;

    availableRolesForUser$: Observable<string[]>;
    readings$: Observable<SensorReading[]>;
    constructor(
        private readonly _users: UsersService,
        private readonly _roles: RolesService,
        private readonly _districts: DistrictService,
        private readonly _toasts: ToastrService,
        private readonly _customerAccountsService: CustomerAccountsService,
        private readonly _credoSoftService: CredoSoftService,
        private readonly _remoteMonitoringService: SensorsService,
        private ref: ChangeDetectorRef
    ) {
        this.customerAccounts = customerAccountsDataSource(
            (params: HttpParams) =>
                this._customerAccountsService
                    .getCustomerAccounts(params)
                    .toPromise()
        );

        this.districts$ = this._districts.getDistrictNumbers();
        this.availableRoles$ = this._roles.getRoleKeys();

        this.currentProfile$ = this._users.currentProfile$;

        // Local Site example
        // TODO: Remove once refactored
        // var exampleSite = new AssetManagementSite();
        // exampleSite.clientid = 1;
        // exampleSite.clientname = 'Test Client Name';
        // exampleSite.locationid = 1;
        // exampleSite.locationname = 'Location Name';
        // this.assetManagementSites$ = of([exampleSite]);
        this._credoSoftService.assetManagementSites$.subscribe((data) => {
            console.log(data, 'Assett sides in users');
            this.assetManagementSites$ = of(data);
        });

        /* ====== OLD IMPLEMENTATION ======
           ======TODO: Remove once refactored ======
        this.assetManagementSites = 
            this._credoSoftService.assetManagementSites$.pipe(
                catchError((error) => of([]))
            ); */

        this.readings$ = this._remoteMonitoringService.readings$;
    }

    ngOnInit() {
        this.loadSearchList();
    }

    toggleCustomerAccountTooltip() {
        this.customerAccountsTooltipVisible =
            !this.customerAccountsTooltipVisible;
    }

    customerDisplayExpr(customer: ExternalCustomer): string {
        return `${customer.externalCustomer} - ${customer.externalCustomerName}`;
    }

    assetManagementSiteDisplayExpr(
        assetManagementSite: AssetManagementSite
    ): string {
        return `${assetManagementSite?.locationid} - ${assetManagementSite?.clientname} - ${assetManagementSite?.locationname}`;
    }

    hasCredoViewRole(user: UserProfile) {
        return user?.roles
            ?.map((role) => role.toLowerCase())
            .includes('aimaas:view');
    }

    userSelected(e: FocusedRowChangedEvent) {
        if (e.rowIndex >= 0) {
            this.userOriginal = new UserProfile();
            Object.assign(this.userOriginal, e.row.data);
            this.userEdit = cloneDeep(this.userOriginal);
            this.isUserSelected = true;
            this.availableRolesForUser$ = forkJoin([
                this.availableRoles$,
                of(this.userOriginal.roles)
            ]).pipe(map(([s1, s2]) => Array.from(new Set([...s1, ...s2]))));
        }
    }

    async userDeleteClicked(e: ClickEvent) {
        const result = await confirm(
            'Are you sure you would like to delete this user from the OneInsight web portal?  This will not prevent them from logging in, but will remove all roles and permissions.',
            'Confirm Delete'
        );
        if (result) {
            this.deleteUser(e);
        }
    }

    updateClicked(e: ClickEvent) {
        const result = e.validationGroup.validate();
        this.isFormValid = result.isValid;
        if (this.isFormValid) {
            this.updateUser();
        }
    }

    refreshClicked(e: ClickEvent) {
        this.searchList.instance.clearFilter('search');
        this.setDefaultFormState(e);
        this.loadSearchList();
    }

    cancelClicked(e: ClickEvent) {
        this.setDefaultFormState(e);
    }

    onRolesChanged = (e) => {
        if (
            e.removedItems
                .map((item) => item.toLowerCase())
                .includes('aimaas:view' || 'connectedworker:view')
        ) {
            this.userEdit.assetManagementSiteIds = [];
        }
        if (
            e.removedItems
                .map((item) => item.toLowerCase())
                .includes('remotemonitoring:view')
        ) {
            this.userEdit.remoteMonitoringSiteIds = [];
        }
    };

    private loadSearchList() {
        this._users
            .getAll()
            .pipe(map((users) => this.getDataSource(users)))
            .subscribe((ds) => {
                this.usersDataSource = ds;
                console.log(ds, 'users data source');
                this.usersDataSource.load();
                this.searchList.instance.refresh();
            });
    }

    private getDataSource(users: UserProfile[]): DataSource {
        return new DataSource({
            store: new ArrayStore({
                data: users,
                key: 'id'
            }),
            sort: 'name'
        });
    }

    private setDefaultFormState(e) {
        this.isUserSelected = false;
        this.focusedRowKey = undefined;
        this.userEdit = new UserProfile();
        this.userOriginal = new UserProfile();
        this.ref.detectChanges();
        if (e) e.validationGroup.reset();
    }

    private updateUser() {
        // call api to update user
        this._users.update(this.userEdit, this.userOriginal.id).subscribe({
            next: (success) => {
                this._toasts.success(
                    `User successfully updated (${this.userEdit.id})`,
                    'Success'
                );
                // Update the local data array
                this.usersDataSource
                    .store()
                    .update(this.userEdit.id, this.userEdit)
                    .catch((error) => {
                        this.loadSearchList(); // since local update failed, just reload from api
                    });
            },
            error: (error) => {
                let message: string;
                if (error.status === 400) {
                    message = `Cannot update user. User with new Id already exists (${this.userEdit.id}).`;
                } else {
                    message = `Unable to update user (Error: ${error.message})`;
                }

                if (error.status !== 401) {
                    this._toasts.error(message, 'Error');
                }
                console.log(error);
            }
        });
    }

    private deleteUser(e) {
        this._users.delete(this.userEdit.id).subscribe({
            next: (success) => {
                this._toasts.success(
                    `User successfully deleted (${this.userEdit.id})`,
                    'Success'
                );
                this.usersDataSource
                    .store()
                    .remove(this.userEdit.id)
                    .then(
                        (key) => {
                            this.setDefaultFormState(e);
                            this.searchList.instance.refresh();
                        },
                        (error) => {
                            this.loadSearchList(); // since local update failed, just reload from api
                        }
                    );
            },
            error: (error) => {
                console.error(error);
                const message = `Unable to delete user (${error.statusText})`;
                this._toasts.error(message, 'Error');
            }
        });
    }
}
