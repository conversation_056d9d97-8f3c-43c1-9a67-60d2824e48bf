import { AfterViewInit, Component, Input, ViewChild } from '@angular/core';
import { DxChartComponent, DxRangeSelectorComponent } from 'devextreme-angular';
import { AssetCategoriesSelectorComponent } from '../../components';
import { AssetCategoryAPICode, statusColor, StatusesByBin } from '../../models';

@Component({
    selector: 'app-task-status-by-week',
    templateUrl: './task-status-by-week.component.html',
    styleUrls: ['./task-status-by-week.component.scss']
})
export class TaskStatusByWeekComponent implements AfterViewInit {
    private _axisLabel: string[] = [];
    private _statusesByWeek: StatusesByBin[];

    @Input() loading = false;
    @Input() set statusesByWeek(value: StatusesByBin[]) {
        this._statusesByWeek = value;
        if (this._statusesByWeek) {
            this.populateSeries();
        }
    }
    get statusesByWeek(): StatusesByBin[] {
        return this._statusesByWeek;
    }

    @ViewChild(DxChartComponent) chart: DxChartComponent;
    @ViewChild(DxRangeSelectorComponent)
    rangeSelector: DxRangeSelectorComponent;
    @ViewChild(AssetCategoriesSelectorComponent)
    categorySelector: AssetCategoriesSelectorComponent;

    get axisLabel(): string[] {
        return this._axisLabel;
    }

    visualRange: any = {};

    constructor() {}

    ngAfterViewInit(): void {
        this.populateSeries();
    }

    renderChart() {
        this.chart?.instance.render({ force: true });
        this.rangeSelector?.instance.render(false);
    }

    onCategoryChanged(_: AssetCategoryAPICode[]) {
        this.populateSeries();
    }

    private populateSeries() {
        if (!this.chart) return;

        this._axisLabel = [];
        this.chart.series = [];

        const isSelected = (category: '510' | '570' | '653') =>
            this.categorySelector.selectedCategories.includes(category);

        if (isSelected('510')) {
            this.addSeriesToChart('510');
        }

        if (isSelected('570')) {
            this.addSeriesToChart('570');
        }

        if (isSelected('653')) {
            this.addSeriesToChart('653');
        }
    }

    private addSeriesToChart(name: string) {
        this._axisLabel.push(name);
        const assetName =
            name === '510'
                ? 'vessel'
                : name === '570'
                ? 'piping'
                : name === '653'
                ? 'tank'
                : '?';

        this.chart.series.push({
            valueField: `${assetName}CompleteCount`,
            name: `${name} Completed`,
            color: statusColor('Completed'),
            stack: name
        });
        this.chart.series.push({
            valueField: `${assetName}InProgressCount`,
            name: `${name} In Progress`,
            color: statusColor('In Progress'),
            stack: name
        });
        this.chart.series.push({
            valueField: `${assetName}NotStartedCount`,
            name: `${name} Not Started`,
            color: statusColor('Not Started'),
            stack: name
        });
        this.chart.series.push({
            valueField: `${assetName}OnHoldCount`,
            name: `${name} On Hold`,
            color: statusColor('On Hold'),
            stack: name
        });
        this.chart.series.push({
            valueField: `${assetName}PublishedCount`,
            name: `${name} Published`,
            color: statusColor('Published'),
            stack: name
        });
        this.chart.series.push({
            valueField: `${assetName}ScheduledCount`,
            name: `${name} Scheduled`,
            color: statusColor('Scheduled'),
            stack: name
        });
        this.chart.series.push({
            valueField: `${assetName}OverdueCount`,
            name: `${name} Overdue`,
            color: statusColor('Overdue'),
            stack: name
        });
        this.chart.series.push({
            valueField: `${assetName}CanceledCount`,
            name: `${name} Canceled`,
            color: statusColor('Canceled'),
            stack: name
        });
        this.chart.series.push({
            valueField: `${assetName}UnknownCount`,
            name: `${name} Unknown`,
            color: statusColor('Unknown'),
            stack: name
        });
    }
}
