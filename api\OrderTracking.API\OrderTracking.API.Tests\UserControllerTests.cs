﻿using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Security.Principal;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using OrderTracking.API.Controllers;
using OrderTracking.API.Models;
using OrderTracking.API.Services;
using SendGrid;

namespace OrderTracking.API.Tests
{
    [TestFixture]
    public class UsersControllerTests
    {
        [SetUp]
        public void SetUp()
        {
            _newUser1 = new UserProfile
            {
                Id = Email1,
                Name = Email1
            };

            _newUser2 = new UserProfile
            {
                Id = Email2,
                Name = Email2
            };
            
            _usersList = new List<UserProfile> {_newUser1, _newUser2};
            _authorizationService = new Mock<IAuthorizationService>(MockBehavior.Strict);
            _userProfiles = new Mock<IUserProfilesService>(MockBehavior.Strict);
            _emailService = new Mock<IEmailService>(MockBehavior.Strict);
            _userAgreements = new Mock<IUserAgreementService>(MockBehavior.Loose);
            _configuration = new Mock<IConfiguration>(MockBehavior.Strict);
            _mockLogger = new Mock<ILogger<UsersController>>();

            _controller = new UsersController(_authorizationService.Object, _userProfiles.Object, _emailService.Object,
                _userAgreements.Object, _configuration.Object, _mockLogger.Object);
        }

        private const string Email1 = "<EMAIL>";
        private const string Email2 = "<EMAIL>";
        private UserProfile _newUser1;
        private UserProfile _newUser2;
        private IEnumerable<UserProfile> _usersList;
        private Mock<IUserProfilesService> _userProfiles;
        private Mock<ILogger<UsersController>> _mockLogger;
        private UsersController _controller;
        private Mock<IAuthorizationService> _authorizationService;
        private Mock<IUserAgreementService> _userAgreements;
        private Mock<IEmailService> _emailService;
        private Mock<IConfiguration> _configuration;

        private static ControllerContext GetControllerContextForUserIdentity(string authedUserId)
        {
            var httpContext = new Mock<HttpContext>();
            var fakeIdentity = new GenericIdentity(authedUserId);
            fakeIdentity.AddClaim(new Claim("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname",
                "GivenName"));
            fakeIdentity.AddClaim(new Claim("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname",
                "Surname"));
            fakeIdentity.AddClaim(new Claim("name", "Name"));
            var principal = new GenericPrincipal(fakeIdentity, null);
            httpContext.Setup(m => m.User).Returns(principal);
            var context = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(),
                new ControllerActionDescriptor()));
            return context;
        }


        [Test]
        public async Task GetAllUserProfiles_ShouldReturnUserProfiles()
        {
            // Arrange            
            _userProfiles.Setup(service => service.GetAllAsync())
                .ReturnsAsync(_usersList);

            // Act
            var response = await _controller.Get();
            var result = response.Result as OkObjectResult;

            // Assert
            Assert.That(result, Is.TypeOf<OkObjectResult>());
            Assert.That(result.Value as IEnumerable<UserProfile>, Is.EqualTo(_usersList));
        }

        [Test]
        public async Task GetUserProfile_ShouldReturnUserProfile()
        {
            // Arrange
            _userProfiles.Setup(service => service.GetAsync(Email1, Email1))
                .ReturnsAsync(_newUser1);
            _controller.ControllerContext = GetControllerContextForUserIdentity(Email1);

            // Act
            var response = await _controller.Get(Email1);
            var result = response.Result as OkObjectResult;

            // Assert
            Assert.That(result, Is.TypeOf<OkObjectResult>());
            Assert.AreEqual(_newUser1, result.Value as UserProfile);
        }

        [Test]
        public async Task GetUserProfile_ThatDoesNotMatchAuthUser_ShouldReturnNotFound()
        {
            // Arrange
            var badId = "<EMAIL>";
            var authedId = "<EMAIL>";

            _controller.ControllerContext = GetControllerContextForUserIdentity(authedId);

            // Act
            var response = await _controller.Get(badId);
            var result = response.Result as UnauthorizedResult;

            // Assert
            Assert.That(result, Is.TypeOf<UnauthorizedResult>());
        }

        [Test]
        public async Task GetUserProfile_ThatDoesNotExistButIsYourAuthenticatedSelf_ShouldReturnNewUser()
        {
            // Arrange
            var authedUserId = "<EMAIL>";
            var authedUser = new UserProfile {Id = authedUserId, Name = authedUserId};

            _userProfiles.Setup(service => service.GetAsync(authedUserId, authedUserId))
                .ReturnsAsync(new Queue<UserProfile>(new[] {null, authedUser}).Dequeue());

            _userProfiles.Setup(service => service.AddAsync(It.Is<UserProfile>(p => p.Id == authedUser.Id)))
                .ReturnsAsync(authedUser);

            _controller.ControllerContext = GetControllerContextForUserIdentity(authedUserId);

            // Act
            var response = await _controller.Get(authedUserId);
            var result = response.Result as OkObjectResult;

            // Assert
            Assert.That(result, Is.TypeOf<OkObjectResult>());
            Assert.That(result.StatusCode, Is.EqualTo(200));
        }


        [Test]
        public async Task GetUserProfile_WithUpperCaseId_ShouldReturnUserProfile()
        {
            // Arrange
            var email = Email1.ToUpper();
            _userProfiles.Setup(service => service.GetAsync(email.ToLower(), email.ToLower()))
                .ReturnsAsync(_newUser1);
            _controller.ControllerContext = GetControllerContextForUserIdentity(Email1);

            // Act
            var response = await _controller.Get(email);
            var result = response.Result as OkObjectResult;

            // Assert
            Assert.That(result, Is.TypeOf<OkObjectResult>());
            Assert.AreEqual(_newUser1, result.Value as UserProfile);
        }

        [Test]
        public async Task UpdateUser_ShouldReturnNoContent()
        {
            // Arrange
            _userProfiles.Setup(service => service.GetAsync(_newUser1.Id)).ReturnsAsync(_newUser1);
            _userProfiles.Setup(service => service.UpdateAsync(_newUser1))
                .ReturnsAsync(_newUser1);
            _controller.ControllerContext = GetControllerContextForUserIdentity("<EMAIL>");
            _authorizationService.Setup(s =>
                    s.AuthorizeAsync(It.IsAny<GenericPrincipal>(), It.IsAny<UserProfile>(), "AssignDistrict"))
                .ReturnsAsync(AuthorizationResult.Success);
            _authorizationService.Setup(s =>
                    s.AuthorizeAsync(It.IsAny<GenericPrincipal>(), It.IsAny<UserProfile>(), "AssignClientAccount"))
                .ReturnsAsync(AuthorizationResult.Success);
            _authorizationService
                .Setup(s => s.AuthorizeAsync(It.IsAny<GenericPrincipal>(), It.IsAny<UserProfile>(),
                    "AssignAssetManagementSite")).ReturnsAsync(AuthorizationResult.Success);

            // Act
            var response = await _controller.Put(_newUser1);

            // Assert
            Assert.That(response, Is.TypeOf<NoContentResult>());
        }

        [Test]
        public async Task UpdateUser_CurrentUserFailsAuthorization_ShouldForbid()
        {
            // Arrange
            _controller.ControllerContext = GetControllerContextForUserIdentity("<EMAIL>");
            _authorizationService.Setup(s =>
                    s.AuthorizeAsync(It.IsAny<GenericPrincipal>(), It.IsAny<UserProfile>(), "AssignDistrict"))
                .ReturnsAsync(AuthorizationResult.Failed);
            _authorizationService.Setup(s =>
                    s.AuthorizeAsync(It.IsAny<GenericPrincipal>(), It.IsAny<UserProfile>(), "AssignClientAccount"))
                .ReturnsAsync(AuthorizationResult.Failed);
            _authorizationService.Setup(s => s.AuthorizeAsync(It.IsAny<GenericPrincipal>(), It.IsAny<UserProfile>(),
                "AssignAssetManagementSite")).ReturnsAsync(AuthorizationResult.Failed);

            // Act
            var response = await _controller.Put(new UserProfile {Id = "<EMAIL>"});

            // Assert
            Assert.That(response, Is.TypeOf<ForbidResult>());
        }

        [Test]
        public async Task Verify_UserIsNull_Unauthorized()
        {
            // Arrange
            _controller.ControllerContext = GetControllerContextForUserIdentity("<EMAIL>");
            _userProfiles.Setup(u => u.GetAsync("<EMAIL>", "<EMAIL>"))
                .ReturnsAsync((UserProfile) null);

            // Act
            var response = await _controller.Verify("<EMAIL>", "abc");

            // Assert
            Assert.That(response, Is.TypeOf<UnauthorizedObjectResult>());
            Assert.That((response as UnauthorizedObjectResult).Value,
                Is.EqualTo("Error: User associated with the token is invalid"));
        }

        [Test]
        public async Task Verify_UsersTokenDoesNotMatch_Unauthorized()
        {
            // Arrange
            _controller.ControllerContext = GetControllerContextForUserIdentity("<EMAIL>");
            _userProfiles.Setup(u => u.GetAsync("<EMAIL>", "<EMAIL>"))
                .ReturnsAsync(new UserProfile
                    {Id = "<EMAIL>", Email = "<EMAIL>", VerificationToken = "123"});

            // Act
            var response = await _controller.Verify("<EMAIL>", "abc");

            // Assert
            Assert.That(response, Is.TypeOf<UnauthorizedObjectResult>());
            Assert.That((response as UnauthorizedObjectResult).Value,
                Is.EqualTo("Error: Token does not match the token currently associated with this user"));
        }

        [Test]
        public async Task Verify_TokenExpired_Unauthorized()
        {
            // Arrange
            _controller.ControllerContext = GetControllerContextForUserIdentity("<EMAIL>");
            var verificationToken = new VerificationToken(1000, "<EMAIL>");
            var userFromDb = new UserProfile
            {
                Id = "<EMAIL>", Email = "<EMAIL>",
                VerificationToken = VerificationToken.SerializeToken(verificationToken)
            };
            _userProfiles.Setup(u => u.GetAsync("<EMAIL>", "<EMAIL>"))
                .ReturnsAsync(userFromDb);

            // Act
            var response = await _controller.Verify("<EMAIL>",
                VerificationToken.SerializeToken(verificationToken));

            // Assert
            Assert.That(response, Is.TypeOf<UnauthorizedObjectResult>());
            Assert.That((response as UnauthorizedObjectResult).Value, Is.EqualTo("Your token has expired"));
        }

        [Test]
        public async Task Verify_UpdatesUser_ReturnsNoContent()
        {
            // Arrange
            _controller.ControllerContext = GetControllerContextForUserIdentity("<EMAIL>");
            var expiration = (int) DateTime.UtcNow.Subtract(new DateTime(1970, 1, 1)).TotalSeconds +
                             new TimeSpan(24, 0, 0).TotalSeconds;
            var verificationToken = new VerificationToken(expiration, "<EMAIL>");
            var userFromDb = new UserProfile
            {
                Id = "<EMAIL>", Email = "<EMAIL>",
                VerificationToken = VerificationToken.SerializeToken(verificationToken)
            };
            _userProfiles.Setup(u => u.GetAsync("<EMAIL>", "<EMAIL>"))
                .ReturnsAsync(userFromDb);
            _userProfiles.Setup(u => u.UpdateAsync(userFromDb, "<EMAIL>")).ReturnsAsync(userFromDb);

            // Act
            var response = await _controller.Verify("<EMAIL>",
                VerificationToken.SerializeToken(verificationToken));

            // Assert
            _userProfiles.Verify(u => u.UpdateAsync(userFromDb, "<EMAIL>"), Times.Once);
            Assert.That(response, Is.TypeOf<NoContentResult>());
            Assert.That(userFromDb.LastVerificationDate, Is.EqualTo(DateTime.UtcNow).Within(TimeSpan.FromSeconds(1)));
            Assert.That(userFromDb.VerificationToken, Is.Null);
            Assert.That(userFromDb.LastLoginDate, Is.EqualTo(DateTime.UtcNow).Within(TimeSpan.FromSeconds(1)));
        }

        [Test]
        public async Task SendVerificationEmail_Test()
        {
            // Arrange
            _controller.ControllerContext = GetControllerContextForUserIdentity("<EMAIL>");
            var userFromDb = new UserProfile
                {Id = "<EMAIL>", Email = "<EMAIL>", Name = "Someone"};
            _userProfiles.Setup(u => u.GetAsync("<EMAIL>", "<EMAIL>"))
                .ReturnsAsync(userFromDb);
            _configuration.Setup(c => c.GetSection("Clients:ClientPortal").Value).Returns("https://cool-website.com");
            _userProfiles.Setup(u => u.UpdateAsync(userFromDb, "<EMAIL>")).ReturnsAsync(userFromDb);
            _emailService.Setup(
                    e => e.SendEmail(It.Is<EmailData>(data =>
                        data.Recipients.Contains(userFromDb) && data.Subject == "OneInsight Account Verification" &&
                        data.HtmlContent.Contains(
                            "Someone, your account requires reverification before you can proceed.  Please follow the link below to verify your account and proceed with using OneInsight.") &&
                        data.HtmlContent.Contains("https://cool-website.com/#/verification-callback?"))))
                .ReturnsAsync((Response) null);

            // Act
            var response = await _controller.SendVerificationEmail("<EMAIL>");

            // Assert
            Assert.That(response, Is.TypeOf<NoContentResult>());
            _userProfiles.Verify(u => u.GetAsync("<EMAIL>", "<EMAIL>"), Times.Once);
            _userProfiles.Verify(u => u.UpdateAsync(userFromDb, "<EMAIL>"), Times.Once);
            _emailService.Verify(e => e.SendEmail(It.IsAny<EmailData>()), Times.Once);
        }
    }
}