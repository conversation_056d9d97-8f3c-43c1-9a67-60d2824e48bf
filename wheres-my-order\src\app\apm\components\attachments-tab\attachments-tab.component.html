<div class="content-block repsonse-paddings">
    <div *ngIf="(blobs$ | async) === null">
        <dx-load-indicator style="position: relative; left: 50%;"
                           id="large-indicator"
                           height="60"
                           width="60"></dx-load-indicator>
    </div>
    <dx-data-grid *ngIf="blobs$ | async"
                  [dataSource]="blobs$ | async"
                  [allowColumnReordering]="true"
                  [allowColumnResizing]="true"
                  (onToolbarPreparing)="onToolbarPreparing($event)"
                  (onRowRemoving)="onRowRemoving($event)">
        <dxo-scrolling [useNative]="true"></dxo-scrolling>
        <dxo-paging [pageSize]="5"></dxo-paging>
        <dxo-pager [showPageSizeSelector]="true"
                   [visible]="true"
                   [allowedPageSizes]="[5, 10, 25, 50]"></dxo-pager>
        <dxo-header-filter [visible]="true"></dxo-header-filter>
        <dxo-filter-row [visible]="true"></dxo-filter-row>
        <dxo-search-panel [visible]="true"
                          [highlightCaseSensitive]="true">
        </dxo-search-panel>
        <dxo-filter-panel [visible]="true"></dxo-filter-panel>
        <dxo-column-chooser [enabled]="true"
                            mode="dragAndDrop">
        </dxo-column-chooser>
        <dxo-state-storing [enabled]="true"
                           [type]="'localStorage'"
                           [storageKey]="'apmInspectionFilesGridState'">
        </dxo-state-storing>

        <dxo-editing mode="row"
                     [allowDeleting]="allowEditing"
                     [useIcons]="true"></dxo-editing>
        <dxi-column dataField="name"
                    caption="File Name"
                    [customizeText]="fileName"></dxi-column>
        <dxi-column dataField="properties.lastModified"
                    caption="Date Uploaded"
                    dataType="datetime"></dxi-column>
        <dxi-column dataField="metadata.user"
                    caption="Uploaded By"></dxi-column>
        <dxi-column dataField="properties.contentLength"
                    caption="Size"
                    cellTemplate="size"></dxi-column>
        <dxi-column dataField="properties.contentType"
                    caption="Type"></dxi-column>
        <dxi-column type="buttons"
                    [width]="110">
            <dxi-button hint="Download"
                        icon="download"
                        [onClick]="download"></dxi-button>
            <dxi-button name="delete"></dxi-button>
        </dxi-column>

        <div *dxTemplate="let cell of 'size'">
            {{cell.value | fileSize}}
        </div>
    </dx-data-grid>
</div>

<dx-popup [(visible)]="showUploadPopup"
          title="Upload File(s)"
          [showCloseButton]="true"
          [hideOnOutsideClick]="true"
          height="auto">
    <div *dxTemplate="let data of 'content'"
         style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center;">
        <dx-file-uploader [multiple]="true"
                          [uploadUrl]="uploadUrl"
                          [uploadHeaders]="uploadHeaders$ | async"
                          uploadMode="useButtons"
                          selectButtonText="Select File(s)"
                          labelText="or Drop file(s) here"
                          (onFilesUploaded)="onFilesUploaded($event)">
        </dx-file-uploader>
    </div>
</dx-popup>
