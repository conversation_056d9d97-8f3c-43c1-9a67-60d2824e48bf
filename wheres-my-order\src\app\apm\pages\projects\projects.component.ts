import { Component, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { EMPTY, Observable, ReplaySubject } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { UserProfile } from '../../../profile/models';
import {
    ProjectDetailsComponent,
    ProjectsGridComponent
} from '../../components';
import { Asset, ProjectUpdate, ProjectVm } from '../../models';
import { ApmService } from '../../services/apm.service';

@Component({
    selector: 'app-projects',
    templateUrl: './projects.component.html',
    styleUrls: ['./projects.component.scss']
})
export class ProjectsComponent implements OnInit {
    @ViewChild(ProjectsGridComponent) projectsGrid: ProjectsGridComponent;
    @ViewChild(ProjectDetailsComponent) projectDetails: ProjectDetailsComponent;
    locations$ = this._apm.locations$;
    private _selectedProject = new ReplaySubject<ProjectVm>();
    selectedProjectAssets$: Observable<Asset[]>;
    assetsForLocation$: Observable<Asset[]>;
    users$: Observable<UserProfile[]>;
    allowEditing$ = this._apm.allowEditing$;

    constructor(
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService
    ) {}

    get selectedProject$(): Observable<ProjectVm> {
        return this._selectedProject.asObservable();
    }

    selectedLocation$ = this.selectedProject$.pipe(
        switchMap((project) => this._apm.getLocation(project.locationId))
    );

    ngOnInit(): void {
        this.assetsForLocation$ = this._apm.locationAssets$;
        this.selectedProjectAssets$ = this._apm.projectAssets$;
        this.users$ = this._apm.getUsers();
        this._apm.getLocations();

        this.selectedProject$
            .pipe(
                switchMap((project) =>
                    project ? this._apm.getProjectAssets(project.id) : EMPTY
                )
            )
            .subscribe();

        this.selectedProject$
            .pipe(
                switchMap((project) =>
                    project
                        ? this._apm.getLocationAssets(project.locationId)
                        : EMPTY
                )
            )
            .subscribe();
    }

    onProjectSave(update: Partial<ProjectUpdate>) {
        this._apm.putProject(update).subscribe((updatedProject) => {
            this._selectedProject.next(updatedProject);
            this.projectDetails.projectDetailsTab.isSaving.next(false);
            this.projectsGrid.refreshGrid();
            this._toasts.success(
                'Changes to project successfully saved',
                'Project updated'
            );
        });
    }

    onProjectUpdated() {}
    onSelectionChanged(e) {
        this._selectedProject.next(e?.selectedRowsData[0]);
    }
}
