using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
// using Microsoft.Identity.Web;

namespace TeamDigital.PipelineInspection.Web.API
{
    public class Startup
    {
        #region Constructors

        public Startup(
            IConfiguration configuration)
        {
            Configuration = configuration;
        }

        #endregion

        #region Properties, Indexers

        #region Properties

        public IConfiguration Configuration { get; }

        #endregion

        #endregion

        #region Public Methods

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers();

            // TODO: Is this needed?
            services.Configure<CookiePolicyOptions>(options =>
            {
                // This lambda determines whether user consent for non-essential cookies is needed for a given request.
                options.CheckConsentNeeded = context => true;
                options.MinimumSameSitePolicy = SameSiteMode.None;
            });


            // services.AddProtectedWebApi(Configuration);

            services.AddMvc().AddNewtonsoftJson();

            // services.AddCosmosDBServices(Configuration);
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(
            IApplicationBuilder app,
            IWebHostEnvironment env,
            ILoggerFactory loggerFactory)
        {
            if (env.IsDevelopment()) app.UseDeveloperExceptionPage();

            app.UseCors(builder =>
                builder.WithOrigins(
                        "http://localhost:4200",
                        // TODO: When we are ready to stop deploying to kubernetes, these can go away
                        "https://ordertrackingportal.azurewebsites.net",
                        "https://teamdigitaldev.teaminc.com",
                        // TODO: These below are the urls for our Angular app in various environments.
                        //       Should we make these only for the environment we are building for?
                        "https://teamdigitalclientportal.azurewebsites.net",
                        "https://teamdigitalclientportal-stg.azurewebsites.net",
                        "https://teamdigitalclientportal-test.azurewebsites.net",
                        "https://teamdigitalclientportal-dev.azurewebsites.net",
                        "https://digital.teaminc.com",
                        "https://digitaldev.teaminc.com",
                        "https://digitaltest.teaminc.com",
                        "https://digitalstaging.teaminc.com"
                        "https://run-clientportal-frontend-dev-usc1-2conmidenq-uc.a.run.app",
                        "https://run-clientportal-frontend-stg-usc1-2atiipqgyq-uc.a.run.app"
                    )
                    .AllowAnyHeader()
                    .AllowAnyMethod());

            app.UseHttpsRedirection();

            app.UseRouting();

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints => { endpoints.MapControllers(); });
        }

        #endregion
    }
}