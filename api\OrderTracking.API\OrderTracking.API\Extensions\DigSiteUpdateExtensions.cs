﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using SendGrid.Helpers.Mail;
////using TeamDigital.PipelineInspection.APIManager;

//namespace OrderTracking.API.Extensions
//{
//    public static class DigSiteUpdateExtensions
//    {
//        /// <summary>
//        ///     Get the Ids of users that are being assigned (assigned users in NewDigSite that
//        ///     are not in OldDigSite)
//        /// </summary>
//        /// <param name="update"></param>
//        /// <returns></returns>
//        public static IEnumerable<string> GetIdsOfNewlyAssignedUsers(this DigSiteUpdate update)
//        {
//            var oldDigSiteUserIds = update
//                .OldDigSite
//                .AssignedUsers
//                ?.Select(u => u.ID)
//                .ToArray() ?? Array.Empty<string>();
//            var newDigSiteUserIds = update
//                .NewDigSite
//                .AssignedUsers
//                .Select(u => u.ID);
//            return newDigSiteUserIds.Except(oldDigSiteUserIds);
//        }

//        /// <summary>
//        ///     Determine if this dig site update object has changes made to the assigned users
//        ///     of the dig site being updated
//        /// </summary>
//        /// <param name="update"></param>
//        /// <returns></returns>
//        public static bool HasChangesToAssignedUsers(this DigSiteUpdate update)
//        {
//            return update.OldDigSite.AssignedUsers == null && update.NewDigSite.AssignedUsers != null &&
//                update.NewDigSite.AssignedUsers?.Length > 0 ||
//                update.OldDigSite.AssignedUsers?.Length < update.NewDigSite.AssignedUsers?.Length;
//        }
//    }
//}