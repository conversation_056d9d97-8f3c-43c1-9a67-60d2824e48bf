import { Component, OnInit, ViewChild } from '@angular/core';
import { DxDataGridComponent } from 'devextreme-angular/ui/data-grid';
import { DxFormComponent } from 'devextreme-angular/ui/form';
import { DxListComponent } from 'devextreme-angular/ui/list';
import { DxSelectBoxComponent } from 'devextreme-angular/ui/select-box';
import DataSource from 'devextreme/data/data_source';
import { confirm } from 'devextreme/ui/dialog';
import { Observable, of } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { UserProfile } from '../../../profile/models';
import { Breadcrumb } from '../../../shared/components';
import {
    ToastNotificationService,
    ToastType,
    UsersService
} from '../../../shared/services';
import { Role } from '../../models';
import { RolesService } from './../../services';

export type FormMode = 'roleSelected' | 'editRole' | 'newRole' | 'none';

@Component({
    selector: 'app-roles',
    templateUrl: './roles.component.html',
    styleUrls: ['./roles.component.scss']
})
export class RolesComponent implements OnInit {
    constructor(
        private readonly _users: UsersService,
        private readonly _roles: RolesService,
        private readonly _toastNotificationService: ToastNotificationService
    ) {}

    get title() {
        switch (this.mode) {
            case 'editRole':
                return 'Edit Role';
            case 'newRole':
                return 'New Role';
            default:
                return 'Role Membership';
        }
    }
    @ViewChild(DxFormComponent, { static: false })
    roleEditForm: DxFormComponent;

    @ViewChild(DxDataGridComponent, { static: false })
    searchList: DxDataGridComponent;

    @ViewChild(DxListComponent, { static: false })
    userList: DxListComponent;

    @ViewChild(DxSelectBoxComponent, { static: false })
    userSelectBox: DxSelectBoxComponent;

    crumbs: Breadcrumb[] = [
        { label: 'Administration', route: '/admin' },
        { label: 'Roles', route: '/admin/roles' }
    ];

    availableRoles: Observable<string[]>;
    availableUsers: Observable<UserProfile[]>;
    focusedRowKey: any;
    isFormValid = false;
    loadingVisible = true;
    mode: FormMode = 'none';
    roleEdit: Role = new Role();
    roleOriginal: Role = new Role();
    rolesDataSource: DataSource;
    selectedUser: UserProfile;
    usersForSelectedRole$: Observable<UserProfile[]>;
    selectedUserInList: Observable<any[]>;
    currentUser: UserProfile;
    isUserAppAdmin: boolean;

    ngOnInit() {
        this.loadSearchList();
        this.availableUsers = this._users.getAll();
        const userProfile$ = this._users.currentProfile$;

        userProfile$.subscribe((user) => {
            this.currentUser = user;
            this.isUserAppAdmin = this.currentUser.roles.some(
                (a) => a.toLowerCase() === 'app:admin'
            );
        });
    }

    roleSelected(e) {
        if (e.rowIndex >= 0 && e.row.rowType === 'data') {
            this.mode = 'roleSelected';
            this.roleOriginal = new Role();
            Object.assign(this.roleOriginal, e.row.data);
            Object.assign(this.roleEdit, this.roleOriginal);

            this.usersForSelectedRole$ = this._roles.getUsersForRole(
                this.roleOriginal.id
            );
        }
    }

    addUserClicked(e) {
        if (!this.selectedUser) return;

        const roleId = this.roleOriginal.id;
        const userId = this.selectedUser.id;
        this._roles.addUserToRole(roleId, userId).subscribe(
            (_) =>
                (this.usersForSelectedRole$ =
                    this._roles.getUsersForRole(roleId)),
            (error) => {
                if (error.status === 403) {
                    this._toastNotificationService.show(
                        'Forbidden',
                        'Accounts with common personal email domains cannot be given permissions. Please inform the user to create a new account with their work email and retry.',
                        ToastType.error
                    );
                }
            }
        );

        this.selectedUserInList = this.usersForSelectedRole$.pipe(
            map((_) => [this.selectedUser.id]),
            tap((_) => this.userSelectBox.instance.reset())
        );
    }

    removeUserClicked(e) {
        const roleId = this.roleOriginal.id;
        const userId = e.itemData.id;
        this._roles.deleteUserFromRole(roleId, userId).subscribe();
    }

    addMenuItems(e) {
        if (e.target === 'content') {
            if (e.row.rowType === 'data') {
                if (!e.items) e.items = [];
                e.items.push({
                    text: 'Edit Role Properties...',
                    icon: 'fa fa-pencil-square-o',
                    onItemClick: () => {
                        this.roleEditClicked(e.row);
                    }
                });
            }
            if (e.row.rowType === 'group') {
                if (!e.items) e.items = [];
                e.items.push({
                    text: 'Add New Role...',
                    icon: 'fa fa-plus',
                    onItemClick: () => {
                        this.newRoleClicked(e.row);
                    }
                });
            }
        }
    }

    roleEditClicked(e) {
        this.mode = 'editRole';
    }

    roleDeleteClicked(e) {
        const result = confirm(
            `Are you sure you want to delete Role (${this.roleEdit.id})?`,
            'Confirm Delete'
        ).then((dialogResult: any) => {
            if (dialogResult) {
                this.deleteRole();
            }
        });
    }

    createClicked(e) {
        this.validateAndExecute(e, () => this.createRole());
    }

    updateClicked(e) {
        this.validateAndExecute(e, () => this.updateRole());
    }

    newRoleClicked(e) {
        console.warn(e);
        this.mode = 'newRole';
        this.focusedRowKey = undefined;
        this.roleOriginal = new Role();
        this.roleEdit = new Role();
        if (e.data) {
            this.roleEdit.id = e.data.key + ':';
        }
    }

    cancelClicked(e) {
        if (this.mode === 'newRole') {
            this.roleEdit = new Role();
            this.roleOriginal = new Role();
            this.mode = 'none';
        } else {
            Object.assign(this.roleEdit, this.roleOriginal);
            this.mode = 'roleSelected';
            this.usersForSelectedRole$ = this._roles.getUsersForRole(
                this.roleOriginal.id
            );
        }
        this.selectedUser = undefined;
    }

    refreshRolesClicked(e) {
        this.setDefaultState();
    }

    private setDefaultState() {
        this.searchList.instance.clearFilter('search');
        this.selectedUser = undefined;
        this.focusedRowKey = undefined;
        this.roleEdit = new Role();
        this.roleOriginal = new Role();
        this.mode = 'none';
        this.loadSearchList();
    }

    private loadSearchList() {
        this._roles.getAllAsDataSource().subscribe((ds) => {
            this.rolesDataSource = ds;
            this.rolesDataSource.load();
            this.searchList.instance.refresh();
        });
    }

    private validateAndExecute(e: any, action: any) {
        const result = e.validationGroup.validate();
        this.isFormValid = result.isValid;
        if (this.isFormValid) {
            action();
        } else {
            console.warn('Form is not valid; action not performed.');
        }
    }

    private deleteRole() {
        this._roles.delete(this.roleEdit.id).subscribe((success) => {
            this.showToast(
                ToastType.success,
                'Success',
                `Role successfully deleted (${this.roleEdit.id})`
            );
            this.setDefaultState();
        });
    }

    private createRole() {
        this._roles.create(this.roleEdit).subscribe(
            (success) => {
                this.showToast(
                    ToastType.success,
                    'Success',
                    `Role successfully added (${this.roleEdit.id})`
                );
                // Insert function of datasource not working, so reload search list
                const tempRole = this.roleEdit;
                this.setDefaultState();
                this.mode = 'roleSelected';
                this.roleOriginal = new Role();
                Object.assign(this.roleOriginal, tempRole);
                Object.assign(this.roleEdit, this.roleOriginal);

                this.usersForSelectedRole$ = of([]);
            },
            (error) => {
                console.error(error);
                let message: string;
                if (error.status === 409) {
                    message = 'Role already exists with the specified Id.';
                } else {
                    message = `Unable to add role (${error.statusText})`;
                }
                this.showToast(ToastType.error, 'Error', message);
            }
        );
    }

    private updateRole() {
        // call api to update role
        this._roles.update(this.roleEdit, this.roleOriginal.id).subscribe(
            (success) => {
                this.showToast(
                    ToastType.success,
                    'Success',
                    `Role successfully updated (${this.roleEdit.id})`
                );
                // Update the local data array
                this.rolesDataSource
                    .store()
                    .update(this.roleEdit.id, this.roleEdit)
                    .catch((error: string) => {
                        console.warn(
                            'Error updating to local DataSource: ' + error
                        );
                        this.loadSearchList(); // since local update failed, just reload from api
                    });
                Object.assign(this.roleOriginal, this.roleEdit);
                this.mode = 'roleSelected';
            },
            (error) => {
                console.error(error);
                let message: string;
                if (error.status === 400) {
                    message = `Cannot update role. Role with new Id already exists (${this.roleEdit.id}).`;
                } else {
                    message = `Unable to update role (Error: ${error.message})`;
                }
                this.showToast(ToastType.error, 'Error', message);
            }
        );
    }

    private showToast(type: ToastType, header: string, message: string) {
        this._toastNotificationService.show(header, message, type);
    }
}
