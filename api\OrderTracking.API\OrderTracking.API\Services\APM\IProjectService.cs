﻿using System.Threading.Tasks;
using APMWebDataInterface.ExampleDataModel;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Services.APM
{
    public interface IProjectService
    {
        /// <summary>
        ///     TODO: This is where we should include the logic for handling the query parameters and filtering what we return
        ///     here.
        /// </summary>
        /// <returns></returns>
        Project[] Get(string email);

        /// <summary>
        ///     Gets a single project by ID
        /// </summary>
        /// <param name="email"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        Project Get(string id, string email);

        /// <summary>
        ///     Creates a new project from a <see cref="NewProjectTransportObject" />
        /// </summary>
        /// <param name="newProject"></param>
        /// <param name="email"></param>
        /// <param name="businessUnitId"></param>
        /// <returns></returns>
        Task<Project> Create(NewProjectTransportObject newProject, string email, string businessUnitId);

        /// <summary>
        ///     Adds the asset id to the project's asset ids (creating an association)
        /// </summary>
        /// <param name="project"></param>
        /// <param name="assetId"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task AddAssetToProject(Project project, string assetId, string email);

        /// <summary>
        ///     Updates a project from a <see cref="WorkOrderTransportObject" />
        /// </summary>
        /// <param name="project"></param>
        /// <param name="workOrderUpdate"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task Update(Project project, WorkOrderTransportObject workOrderUpdate, string email);

        /// <summary>
        ///     Updates a project from a <see cref="ProjectTransportObject"/>
        /// </summary>
        /// <param name="project"></param>
        /// <param name="projectUpdate"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task Update(Project project, ProjectTransportObject projectUpdate, string email);

        /// <summary>
        ///     Updates a contact object on a project from a <see cref="ClientContact"/>
        /// </summary>
        /// <param name="project"></param>
        /// <param name="contact"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task<Contact> UpdateContact(Project project, ClientContact contact, string email);

        /// <summary>
        ///     Creates a contact on a project from a <see cref="ClientContact"/>
        /// </summary>
        /// <param name="project"></param>
        /// <param name="contact"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task<Contact> CreateContact(Project project, ClientContact contact, string email);

        /// <summary>
        ///     Removes a contact from a project given the contact's database id
        /// </summary>
        /// <param name="project"></param>
        /// <param name="contactId"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task RemoveContact(Project project, string contactId, string email);

        /// <summary>
        ///     Get projects, by id, from the list of project ids provided
        /// </summary>
        /// <param name="email"></param>
        /// <param name="projectIds"></param>
        /// <returns></returns>
        Project[] Get(string email, params string[] projectIds);

        /// <summary>
        ///    Transform Project to ProjectVM
        /// </summary>
        /// <param name="project"></param>
        /// <returns></returns>
        ProjectVM BuildProjectVM(Project project);
    }
}