<div class="responsive-paddings content-block">
    <div *ngIf="!assetPPE">
        <dx-load-indicator style="position: relative; left: 50%;"
                           id="large-indicator"
                           height="60"
                           width="60"></dx-load-indicator>
    </div>
    <dx-form *ngIf="assetPPE"
             #form
             [(formData)]="assetPPE"
             [readOnly]="!isEditing || !allowEditing"
             (onFieldDataChanged)="onFieldDataChanged($event)">
        <dxi-item itemType="group"
                  caption="PPE Requirements"
                  [colCount]="3">
            <dxi-item dataField="hardHatRequired"
                      editorType="dxSwitch">
            </dxi-item>
            <dxi-item dataField="eyeProtection"
                      editorType="dxTagBox"
                      [editorOptions]="{items: assetPPE.eyeProtections}">
            </dxi-item>
            <dxi-item dataField="earProtection"
                      editorType="dxTagBox"
                      [editorOptions]="{items: assetPPE.hearingProtections}">
            </dxi-item>
            <dxi-item dataField="fireRetardantClothing"
                      editorType="dxTagBox"
                      [editorOptions]="{items: assetPPE.fireRetardantClothings}">
            </dxi-item>
            <dxi-item dataField="safetyGloves"
                      editorType="dxTagBox"
                      [editorOptions]="{items: assetPPE.safetyGloveOptions}">
            </dxi-item>
            <dxi-item dataField="snakeChapsRequired"
                      editorType="dxSwitch">
            </dxi-item>
            <dxi-item dataField="footProtection"
                      editorType="dxTagBox"
                      [editorOptions]="{items: assetPPE.footProtections}">
            </dxi-item>
            <dxi-item dataField="chemicalSuit"
                      editorType="dxTagBox"
                      [editorOptions]="{items: assetPPE.chemicalSuits}">
            </dxi-item>
            <dxi-item dataField="fallProtection"
                      editorType="dxTagBox"
                      [editorOptions]="{items: assetPPE.fallProtections}">
            </dxi-item>
            <dxi-item dataField="breathingProtection"
                      editorType="dxTagBox"
                      [editorOptions]="{items: assetPPE.breathingProtections}">
            </dxi-item>
            <dxi-item dataField="atmosphere"
                      editorType="dxTagBox"
                      [editorOptions]="{items: assetPPE.atmospheres}">
            </dxi-item>

        </dxi-item>

        <dxi-item itemType="group"
                  caption="General Site Conditions"
                  [colCount]="2">
            <dxi-item itemType="group"
                      caption="Permitting Required"
                      cssClass="smaller-caption"
                      [colCount]="3">
                <dxi-item dataField="generalWork"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item dataField="generalHotWork"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item dataField="openFlameHotWork"
                          editorType="dxSwitch"></dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      caption="Confined Space Requirements"
                      cssClass="smaller-caption"
                      [colCount]="4">
                <dxi-item dataField="permitRequired"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item dataField="holeWatchNeeded"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item dataField="controlAreaPermit"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item dataField="hazardousAreaPermit"
                          editorType="dxSwitch"></dxi-item>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  [colCount]="3">

            <dxi-item itemType="group"
                      [colCount]="2">
                <dxi-item dataField="standingWater"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item *ngIf="assetPPE?.standingWater"
                          dataField="drainageNeeded"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item dataField="overgrownVegetation"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item *ngIf="assetPPE?.overgrownVegetation"
                          dataField="abatementRequired"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item *ngIf="assetPPE?.overgrownVegetation"
                          dataField="powerAvailable"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item *ngIf="assetPPE?.overgrownVegetation"
                          dataField="waterAvailable"
                          editorType="dxSwitch"></dxi-item>
            </dxi-item>

            <dxi-item itemType="group">
                <dxi-item dataField="personnelAccessConditions"
                          editorType="dxTagBox"
                          [editorOptions]="{acceptCustomValue: true, items: assetPPE.onSiteConditions}">
                </dxi-item>
                <dxi-item dataField="personnelAccessConditionNotes"
                          editorType="dxTextArea"></dxi-item>
            </dxi-item>

            <dxi-item itemType="group">
                <dxi-item dataField="vehicleAccessibility"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: assetPPE.vehicleAccessibilities}">
                </dxi-item>
                <dxi-item dataField="vehicleAccessibilityComments"
                          editorType="dxTextArea"></dxi-item>
            </dxi-item>

            <dxi-item itemType="group">
                <dxi-item dataField="onSiteLeaks"
                          editorType="dxSwitch"></dxi-item>
                <dxi-item dataField="onSiteLeaksComments"
                          editorType="dxTextArea"></dxi-item>
            </dxi-item>
        </dxi-item>


        <dxi-item [template]="'buttons'"></dxi-item>

        <div *dxTemplate="let data of 'buttons'"
             class="buttons">
            <dx-button *ngIf="!isEditing; else saveAndCancel"
                       text="Edit"
                       type="default"
                       (onClick)="onEditClicked($event)"
                       [disabled]="!allowEditing"></dx-button>
            <ng-template #saveAndCancel>
                <dx-button text="Cancel"
                           type="danger"
                           [disabled]="isSaving"
                           (onClick)="onCancelClicked($event, form)">
                </dx-button>
                <dx-button text="Save"
                           type="success"
                           (onClick)="onSaveClicked($event)"
                           [disabled]="!allowEditing || isSaving"></dx-button>
            </ng-template>
        </div>

    </dx-form>
</div>
