import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import {
    DxFileUploaderModule,
    DxLoadIndicatorModule,
    DxPopupModule
} from 'devextreme-angular';
import { DxDataGridModule } from 'devextreme-angular/ui/data-grid';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import { AuthService, UsersService } from '../../../shared/services';
import { ApmService } from '../../services';
import { AttachmentsTabComponent } from './attachments-tab.component';

describe('AttachmentsTabComponent', () => {
    let component: AttachmentsTabComponent;
    let fixture: ComponentFixture<AttachmentsTabComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxDataGridModule,
                DxPopupModule,
                DxFileUploaderModule,
                DxLoadIndicatorModule,
                HttpClientTestingModule,
                RouterTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [AttachmentsTabComponent],
            providers: [
                {
                    provide: AuthService,
                    useValue: {
                        acquireTokenSuccess$: of({ accessToken: 'lalala' })
                    }
                },
                { provide: ApmService, useValue: {} },
                { provide: UsersService, useValue: {} }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(AttachmentsTabComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
