<div class="responsive-paddings content-block">
    <dx-form [colCount]=2
             [(formData)]="contactTabComponent"
             [readOnly]="true">
        <dxi-item itemType="group">
            <dxi-item [template]="'contactsTemplate'">
                <dxo-label text="Existing Contacts"></dxo-label>
            </dxi-item>
        </dxi-item>
        <dxi-item itemType="group"
                  [colCount]="2">
            <dxi-item dataField="selectedContact.contactName">
                <dxo-label text="Contact Name"></dxo-label>
            </dxi-item>
            <dxi-item dataField="selectedContact.contactTitle">
                <dxo-label text="Title"></dxo-label>
            </dxi-item>
            <dxi-item dataField="selectedContact.contactPhoneNumber">
                <dxo-label text="Contact Phone Number"></dxo-label>
            </dxi-item>
            <dxi-item dataField="selectedContact.contactEmail">
                <dxo-label text="Contact Email"></dxo-label>
            </dxi-item>
        </dxi-item>


        <div *dxTemplate="let data of 'contactsTemplate'">
            <dx-list [dataSource]="contactDataSource"
                     selectionMode="single"
                     (onSelectionChanged)="selectionChanged($event)"
                     [noDataText]="'No Contacts to display'"
                     keyExpr="databaseId">
                <div *dxTemplate="let item of 'item'">
                    <div>{{item?.contactName}}</div>
                </div>
            </dx-list>
        </div>
    </dx-form>

    <div class="content-block responsive-paddings form-buttons">
        <dx-button text="Create New Contact"
                   type="success"
                   hint="Create New Contact"
                   [disabled]="!allowEditing"
                   (onClick)="createClicked()">
        </dx-button>
        <dx-button text="Edit Selected Contact"
                   type="default"
                   hint="Edit Selected Contact"
                   [disabled]="!selectedContact || !allowEditing"
                   (onClick)="editClicked()">
        </dx-button>

        <dx-button text="Delete Selected Contact"
                   type="error"
                   hint="Edit Selected Contact"
                   [disabled]="!selectedContact|| !allowEditing"
                   (onClick)="deleteClicked()">
        </dx-button>
    </div>
</div>
<!--This popup is for editing-->
<dx-popup minWidth="450"
          width="auto"
          height="auto"
          [showTitle]="true"
          title="Contact"
          [dragEnabled]="true"
          [hideOnOutsideClick]="false"
          [showCloseButton]="false"
          [(visible)]="editPopupVisible">
    <dx-form #editContactForm
             [(formData)]="editContact">
        <dxi-item dataField="contactName">
            <dxi-validation-rule type="required"
                                 message="Contact Name is required">
            </dxi-validation-rule>
            <dxo-label text="Contact Name"></dxo-label>
        </dxi-item>
        <dxi-item dataField="contactTitle">
            <dxo-label text="Contact Title"></dxo-label>
        </dxi-item>
        <dxi-item dataField="contactPhoneNumber">
            <dxo-label text="Contact Phone Number"></dxo-label>
        </dxi-item>
        <dxi-item dataField="contactEmail">
            <dxo-label text="Contact Email"></dxo-label>
        </dxi-item>
    </dx-form>
    <div class="content-block responsive-paddings form-buttons">
        <dx-button text="cancel"
                   type="error"
                   hint="cancel"
                   (onClick)="cancelEditClicked()">
        </dx-button>
        <dx-button text="Save"
                   type="success"
                   hint="save"
                   (onClick)="saveClicked()">
        </dx-button>

    </div>

</dx-popup>
