namespace OrderTracking.API.Models
{
    /// <summary>
    ///     Model that represents connection properties to cosmos db in App Settings
    /// </summary>
    public class Connections
    {

        /// <summary>
        ///     Project Id
        /// </summary>
        public string ProjectId { get; set; }


        /// <summary>
        ///     FirestoreDB Id
        /// </summary>
        public string FirestoreDb { get; set; }

        /// <summary>
        ///     Database name
        /// </summary>
        public string Database { get; set; }

        /// <summary>
        ///     UserProfiles container name
        /// </summary>
        public string UserProfiles { get; set; }

        /// <summary>
        ///     Roles container name
        /// </summary>
        public string Roles { get; set; }

        /// <summary>
        ///     EquipmentRequests container name
        /// </summary>
        public string EquipmentRequests { get; set; }

        /// <summary>
        ///     FlangeCalculations container name
        /// </summary>
        public string FlangeCalculations { get; set; }

        /// <summary>
        ///     Notifications container name
        /// </summary>
        public string Notifications { get; set; }

        /// <summary>
        ///     ReleaseNotes container name
        /// </summary>
        public string ReleaseNotes { get; set; }

        public string AuthHistory { get; set; }

        /// <summary>
        ///     Endpoint for cosmos db
        /// </summary>
        public string Endpoint { get; set; }

        /// <summary>
        ///     AuthKey for cosmos db
        /// </summary>
        public string AuthKey { get; set; }

        public string DatabaseName { get; set; }

        /// <summary>
        ///     Cloud Storage Bucket Name used to store Antea Attachments
        /// </summary>
        public string AnteaAttachmentsBucketName { get; set; }
        /// <summary>
        ///     Cloud Storage Bucket Name used for Antea Submissions
        /// </summary>
        public string AnteaSubmissionsBucketName { get; set; }
    }
}