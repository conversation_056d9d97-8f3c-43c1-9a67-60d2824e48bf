import {
    AfterViewInit,
    Component,
    Input,
    OnDestroy,
    ViewChild
} from '@angular/core';
import { DxFormComponent } from 'devextreme-angular';
import { ValueChangedEvent } from 'devextreme/ui/select_box';
import { AssetVm } from '../../models';
import { ApmService } from '../../services';

export type NewWorkOrderTemp = {
    projectId: string;
    assetId: string;
    dueDate: Date;
    facilityName: string;
    plannedStart: Date;
    plannedEnd: Date;
    status: 'Scheduled' | 'In Progress' | 'Completed';
};

@Component({
    selector: 'app-work-order-creation',
    templateUrl: './work-order-creation.component.html',
    styleUrls: ['./work-order-creation.component.scss']
})
export class WorkOrderCreationComponent implements AfterViewInit, OnDestroy {
    private _assets: AssetVm[];
    readonly formData: NewWorkOrderTemp = {
        assetId: undefined,
        projectId: undefined,
        dueDate: undefined,
        facilityName: undefined,
        plannedEnd: undefined,
        plannedStart: undefined,
        status: undefined
    };
    projectsDataSource = this._apm.projectDataSource;
    assetsForDropdown: AssetVm[] = [];

    @ViewChild(DxFormComponent) form: DxFormComponent;

    @Input() projectId: string;
    @Input() assetId: string;

    constructor(private readonly _apm: ApmService) {}

    async ngAfterViewInit(): Promise<void> {
        this._apm
            .loadAllAssets(null)
            .subscribe((assets: { data: AssetVm[] }) => {
                this._assets = assets.data;
                this.assetsForDropdown = this._assets;
                if (this.assetId) {
                    this.formData.assetId = this.assetId;
                    const assetVm = this._assets.find(
                        (a) => a.id === this.assetId
                    );
                    this.projectsDataSource.filter([
                        'locationId',
                        '=',
                        assetVm.locationId
                    ]);
                    this.projectsDataSource.reload();
                }
                if (this.projectId) this.formData.projectId = this.projectId;
            });
    }

    ngOnDestroy(): void {
        this.projectsDataSource?.filter(null);
    }

    validate(): boolean {
        return this.form.instance.validate().isValid;
    }

    assetDisplayExpr = (e: AssetVm) =>
        e ? `${e.equipmentId} - ${e?.assetCategory}` : null;

    onProjectSelected = async (e: ValueChangedEvent) => {
        const project = await this.projectsDataSource.store().byKey(e.value);
        const assetIds = project.assetIds;
        this.assetsForDropdown = this._assets.filter((a) =>
            assetIds.includes(a.id)
        );
    };
}
