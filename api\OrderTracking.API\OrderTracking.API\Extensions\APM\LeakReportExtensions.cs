﻿using System;
using System.Linq;
using APMWebDataInterface.DataModel.LeakReport;
using CommonDataInterface;
using CommonDataInterface.Attributes;
using OrderTracking.API.Models.APM;
using OrderTracking.API.Models.LeakReporting;

namespace OrderTracking.API.Extensions.APM
{
    /// <summary>
    ///     Extension methods to assist with working with leak reports
    /// </summary>
    public static class LeakReportExtensions
    {
        /// <summary>
        ///     updates a leak report's status
        /// </summary>
        /// <param name="leakReport"></param>
        /// <param name="status"></param>
        public static void UpdateStatus(this LeakReport leakReport, string status)
        {
            if (status == null) return;

            var statusFromString = status.ToLower() switch
            {
                "active" => LeakReport.LeakReportStatuses.Active,
                "closed" => LeakReport.LeakReportStatuses.Closed,
                _ => throw new ArgumentException($"invalid status value: {status}")
            };
            leakReport.SetStatus(statusFromString);
        }

        /// <summary>
        ///     Updates values for work details on an existing leak report
        /// </summary>
        /// <param name="report"></param>
        /// <param name="update"></param>
        public static void UpdateLeakReportWorkDetails(this LeakReport report, LeakReportWorkDetails update)
        {
            if (update.Area?.Value != null)
                report.workDetail.attributeArea.SetValue(update.Area.Value);
            if (update?.Area?.Comment != null)
                report.workDetail.attributeArea.SetComment(update.Area.Comment);

            if (update.City?.Value != null)
                report.workDetail.attributeCity.SetValue(update.City.Value);
            if (update?.City?.Comment != null)
                report.workDetail.attributeCity.SetComment(update.City.Comment);

            if (update.Client?.Value != null)
                report.workDetail.attributeClient.SetValue(update.Client.Value);
            if (update?.Client?.Comment != null)
                report.workDetail.attributeClient.SetComment(update.Client.Comment);

            if (update.ClientContact?.Value != null)
                report.workDetail.attributeClient_Contact.SetValue(update.ClientContact.Value);
            if (update?.ClientContact?.Comment != null)
                report.workDetail.attributeClient_Contact.SetComment(update.ClientContact.Comment);

            if (update.ClientContactNumber?.Value != null)
                report.workDetail.attributeClient_Contact_Number.SetValue(update.ClientContactNumber.Value);
            if (update?.ClientContactNumber?.Comment != null)
                report.workDetail.attributeClient_Contact_Number.SetComment(update.ClientContactNumber.Comment);

            if (update.ClientCostCode?.Value != null)
                report.workDetail.attributeClient_Cost_Code.SetValue(update.ClientCostCode.Value);
            if (update?.ClientCostCode?.Comment != null)
                report.workDetail.attributeClient_Cost_Code.SetComment(update.ClientCostCode.Comment);

            if (update.ClientWorkOrder?.Value != null)
                report.workDetail.attributeClient_Work_Order.SetValue(update.ClientWorkOrder.Value);
            if (update?.ClientWorkOrder?.Comment != null)
                report.workDetail.attributeClient_Work_Order.SetComment(update.ClientWorkOrder.Comment);

            if (update.Facility?.Value != null)
                report.workDetail.attributeFacility.SetValue(update.Facility.Value);
            if (update?.Facility?.Comment != null)
                report.workDetail.attributeFacility.SetComment(update.Facility.Comment);

            if (update.InspectedBy?.Value != null)
                report.workDetail.attributeInspected_By.SetValue(update.InspectedBy.Value);
            if (update?.InspectedBy?.Comment != null)
                report.workDetail.attributeInspected_By.SetComment(update.InspectedBy.Comment);

            if (update.InspectionDate?.Value != null)
                report.workDetail.attributeInspection_Date.SetValue(DateTime.Parse(update.InspectionDate.Value));
            if (update?.InspectionDate?.Comment != null)
                report.workDetail.attributeInspection_Date.SetComment(update.InspectionDate.Comment);

            if (update.InspectionReference?.Value != null)
                report.workDetail.attributeInspection_Reference.SetValue(update.InspectionReference.Value);
            if (update?.InspectionReference?.Comment != null)
                report.workDetail.attributeInspection_Reference.SetComment(update.InspectionReference.Comment);

            if (update.InspectionTypes?.Value != null)
                report.workDetail.attributeInspection_Type.SetValue(update.InspectionTypes.Value);
            if (update?.InspectionTypes?.Comment != null)
                report.workDetail.attributeInspection_Type.SetComment(update.InspectionTypes.Comment);

            if (update.JobDescription?.Value != null)
                report.workDetail.attributeJob_Description.SetValue(update.JobDescription.Value);
            if (update?.JobDescription?.Comment != null)
                report.workDetail.attributeJob_Description.SetComment(update.JobDescription.Comment);

            if (update.Lease?.Value != null)
                report.workDetail.attributeLease.SetValue(update.Lease.Value);
            if (update?.Lease?.Comment != null)
                report.workDetail.attributeLease.SetComment(update.Lease.Comment);

            if (update.PostalCode?.Value != null)
                report.workDetail.attributePostal_Code.SetValue(update.PostalCode.Value);
            if (update?.PostalCode?.Comment != null)
                report.workDetail.attributePostal_Code.SetComment(update.PostalCode.Comment);

            if (update.PurchaseOrder?.Value != null)
                report.workDetail.attributePurchase_OrderAFE.SetValue(update.PurchaseOrder.Value);
            if (update?.PurchaseOrder?.Comment != null)
                report.workDetail.attributePurchase_OrderAFE.SetComment(update.PurchaseOrder.Comment);

            if (update.ReferenceEdition?.Value != null)
                report.workDetail.attributeReference_EditionRevision.SetValue(update.ReferenceEdition.Value);
            if (update?.ReferenceEdition?.Comment != null)
                report.workDetail.attributeReference_EditionRevision.SetComment(update.ReferenceEdition.Comment);

            if (update.State?.Value != null)
                report.workDetail.attributeState.SetValue(update.State.Value);
            if (update?.State?.Comment != null)
                report.workDetail.attributeState.SetComment(update.State.Comment);

            if (update.TeamDistrict?.Value != null)
                report.workDetail.attributeTeam_District.SetValue(update.TeamDistrict.Value);
            if (update?.TeamDistrict?.Comment != null)
                report.workDetail.attributeTeam_District.SetComment(update.TeamDistrict.Comment);

            if (update.TeamProjectNumber?.Value != null)
                report.workDetail.attributeTeam_Project_Number.SetValue(update.TeamProjectNumber.Value);
            if (update?.TeamProjectNumber?.Comment != null)
                report.workDetail.attributeTeam_Project_Number.SetComment(update.TeamProjectNumber.Comment);

            if (update.InspectorCertificateNumber?.Value != null)
                report.workDetail.attributeInspector_Certificate_Number.SetValue(
                    update.InspectorCertificateNumber.Value);
            if (update?.InspectorCertificateNumber?.Comment != null)
                report.workDetail.attributeInspector_Certificate_Number.SetComment(update.InspectorCertificateNumber
                    .Comment);

            if (update.ReviewedBy?.Value != null)
                report.workDetail.attributeReviewed_By.SetValue(update.ReviewedBy.Value);
            if (update?.ReviewedBy?.Comment != null)
                report.workDetail.attributeReviewed_By.SetComment(update.ReviewedBy.Comment);

            if (update.ReviewerEmail?.Value != null)
                report.workDetail.attributeReviewer_Email.SetValue(update.ReviewerEmail.Value);
            if (update?.ReviewerEmail?.Comment != null)
                report.workDetail.attributeReviewer_Email.SetComment(update.ReviewerEmail.Comment);

            if (update.ReviewerCertificateNumber?.Value != null)
                report.workDetail.attributeReviewer_Certificate_Number.SetValue(update.ReviewerCertificateNumber.Value);
            if (update?.ReviewerCertificateNumber?.Comment != null)
                report.workDetail.attributeReviewer_Certificate_Number.SetComment(update.ReviewerCertificateNumber
                    .Comment);
        }

        /// <summary>
        ///     updates an existing leak report's report info
        /// </summary>
        /// <param name="update"></param>
        /// <param name="report"></param>
        public static void UpdateLeakReportInfo(this LeakReport report, LeakReportInfo update)
        {
            if (update.CorrosionType?.Value != null)
                report.report.attributeCorrosion_Type.SetValue(update.CorrosionType.Value);
            if (update.CorrosionType?.Comment != null)
                report.report.attributeCorrosion_Type.SetComment(update.CorrosionType.Comment);

            if (update.DistanceBetweenTieInPoints?.Value != null)
                report.report.attributeDistance_between_tie_in_points.SetValue(update.DistanceBetweenTieInPoints.Value);
            if (update.DistanceBetweenTieInPoints?.Comment != null)
                report.report.attributeDistance_between_tie_in_points.SetComment(update.DistanceBetweenTieInPoints
                    .Comment);

            if (update.EquipmentDescription?.Value != null)
                report.report.attributeEquipment_Description.SetValue(update.EquipmentDescription.Value);
            if (update.EquipmentDescription?.Comment != null)
                report.report.attributeEquipment_Description.SetComment(update.EquipmentDescription.Comment);

            if (update.EquipmentID?.Value != null)
                report.report.attributeEquipment_ID.SetValue(update.EquipmentID.Value);
            if (update.EquipmentID?.Comment != null)
                report.report.attributeEquipment_ID.SetComment(update.EquipmentID.Comment);

            if (update.EquipmentIDAtLineEnd?.Value != null)
                report.report.attributeEquipment_ID_at_line_END.SetValue(update.EquipmentIDAtLineEnd.Value);
            if (update.EquipmentIDAtLineEnd?.Comment != null)
                report.report.attributeEquipment_ID_at_line_END.SetComment(update.EquipmentIDAtLineEnd.Comment);

            if (update.EquipmentIDAtLineStart?.Value != null)
                report.report.attributeEquipment_ID_at_line_START.SetValue(update.EquipmentIDAtLineStart.Value);
            if (update.EquipmentIDAtLineStart?.Comment != null)
                report.report.attributeEquipment_ID_at_line_START.SetComment(update.EquipmentIDAtLineStart.Comment);

            if (update.EstimatedLossRate?.Value != null)
                report.report.attributeEstimated_Loss_Rate.SetValue(update.EstimatedLossRate.Value);
            if (update.EstimatedLossRate?.Comment != null)
                report.report.attributeEstimated_Loss_Rate.SetComment(update.EstimatedLossRate.Comment);

            if (update.ExistingClampCount?.Value != null)
                report.report.attributeExisting_clamp_count_same_line.SetValue(update.ExistingClampCount.Value);
            if (update.ExistingClampCount?.Comment != null)
                report.report.attributeExisting_clamp_count_same_line.SetComment(update.ExistingClampCount.Comment);

            if (update.FeatureFittingCount?.Value != null)
                report.report.attributeFeatureFitting_count_same_line.SetValue(update.FeatureFittingCount.Value);
            if (update.FeatureFittingCount?.Comment != null)
                report.report.attributeFeatureFitting_count_same_line.SetComment(update.FeatureFittingCount.Comment);

            if (update.ObservationSummary?.Value != null)
                report.report.attributeObservation_Summary.SetValue(update.ObservationSummary.Value);
            if (update.ObservationSummary?.Comment != null)
                report.report.attributeObservation_Summary.SetComment(update.ObservationSummary.Comment);

            if (update.PipeCover?.Value != null)
                report.report.attributePipe_Cover.SetValue(update.PipeCover.Value);
            if (update.PipeCover?.Comment != null)
                report.report.attributePipe_Cover.SetComment(update.PipeCover.Comment);

            if (update.PipeSchedule?.Value != null)
                report.report.attributePipe_Schedule.SetValue(update.PipeSchedule.Value);
            if (update.PipeSchedule?.Comment != null)
                report.report.attributePipe_Schedule.SetComment(update.PipeSchedule.Comment);

            if (update.PipeSize?.Value != null)
                report.report.attributePipe_Size.SetValue(update.PipeSize.Value);
            if (update.PipeSize?.Comment != null)
                report.report.attributePipe_Size.SetComment(update.PipeSize.Comment);

            if (update.ProcessService?.Value != null)
                report.report.attributeProcessService.SetValue(update.ProcessService.Value);
            if (update.ProcessService?.Comment != null)
                report.report.attributeProcessService.SetComment(update.ProcessService.Comment);

            if (update.AffectedLength?.Value != null)
                report.report.attributeAffected_Length.SetValue(update.AffectedLength.Value);
            if (update.AffectedLength?.Comment != null)
                report.report.attributeAffected_Length.SetComment(update.AffectedLength.Comment);
        }

        /// <summary>
        ///     creates a new leak report
        /// </summary>
        /// <param name="newReport"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public static LeakReport CreateNewLeakReport(NewLeakReport newReport, string user)
        {
            var report = new LeakReport(user, null);
            if (newReport.Area != null)
                report.workDetail.attributeArea.SetValue(newReport.Area);
            if (newReport.Lease != null)
                report.workDetail.attributeLease.SetValue(newReport.Lease);
            if (newReport.EquipmentID != null)
                report.report.attributeEquipment_ID.SetValue(newReport.EquipmentID);
            if (newReport.JobDescription != null)
                report.workDetail.attributeJob_Description.SetValue(newReport.JobDescription);
            return report;
        }

        /// <summary>
        ///     updates a leak report photo group
        /// </summary>
        /// <param name="group"></param>
        /// <param name="update"></param>
        public static void UpdatePhotoGroup(this LeakReportPhoto group, LeakReportPhotoGroupTransport update)
        {
            if (update.AreaOfInterestComment != null)
                group.areaOfInterestCoordinate.SetComment(update.AreaOfInterestComment);
            if (update.AreaOfInterestLatitude != null)
            {
                var longitude = group.areaOfInterestCoordinate.GetLongValue();
                group.areaOfInterestCoordinate.SetValue(update.AreaOfInterestLatitude, longitude);
            }

            if (update.AreaOfInterestLongitude != null)
            {
                var latitude = group.areaOfInterestCoordinate.GetLatValue();
                group.areaOfInterestCoordinate.SetValue(latitude, update.AreaOfInterestLongitude);
            }

            if (update.Description != null)
                group.description.SetValue(update.Description);
            if (update.DescriptionComment != null)
                group.description.SetComment(update.DescriptionComment);
            if (update.DownstreamTieInComment != null)
                group.downstreamTieInCoordinate.SetComment(update.DownstreamTieInComment);
            if (update.DownstreamTieInLatitude != null)
            {
                var longitude = group.downstreamTieInCoordinate.GetLongValue();
                group.downstreamTieInCoordinate.SetValue(update.DownstreamTieInLatitude, longitude);
            }

            if (update.DownstreamTieInLongitude != null)
            {
                var latitude = group.downstreamTieInCoordinate.GetLatValue();
                group.downstreamTieInCoordinate.SetValue(latitude, update.DownstreamTieInLongitude);
            }

            if (update.PhotoComment != null)
                group.comment.SetValue(update.PhotoComment);
            if (update.UTHighMeasurement != null)
                group.utHighMeasurment.SetValue(update.UTHighMeasurement);
            if (update.UTHighMeasurementComment != null)
                group.utHighMeasurment.SetComment(update.UTHighMeasurementComment);
            if (update.UTLowMeasurement != null)
                group.utLowMeasurment.SetValue(update.UTLowMeasurement);
            if (update.UTLowMeasurementComment != null)
                group.utLowMeasurment.SetComment(update.UTLowMeasurementComment);
            if (update.UpstreamTieInComment != null)
                group.upstreamTieInCoordinate.SetComment(update.UpstreamTieInComment);
            if (update.UpstreamTieInLatitude != null)
            {
                var longitude = group.upstreamTieInCoordinate.GetLongValue();
                group.upstreamTieInCoordinate.SetValue(update.UpstreamTieInLatitude, longitude);
            }

            if (update.UpstreamTieInLongitude != null)
            {
                var latitude = group.upstreamTieInCoordinate.GetLatValue();
                group.upstreamTieInCoordinate.SetValue(latitude, update.UpstreamTieInLatitude);
            }
        }

        /// <summary>
        ///     Gets a single photo attribute base from a leak report
        /// </summary>
        /// <param name="report"></param>
        /// <param name="photoDelete"></param>
        /// <returns></returns>
        public static AttributeBase GetPhotoAttributeBase(this LeakReport report, LeakReportingPhotoDelete photoDelete)
        {
            return report
                .GetChildrenRecursive()
                .OfType<AttributeBase>()
                .Where(a => a.Photos != null && a.Photos.Length > 0)
                .FirstOrDefault(a => a.Photos.Select(p => p.DatabaseId).Contains(photoDelete.PhotoDatabaseID));
        }

        /// <summary>
        ///     Gets the media entry for a leak report photo
        /// </summary>
        /// <param name="report"></param>
        /// <param name="update"></param>
        /// <returns></returns>
        public static MediaEntry GetPhotoMediaEntry(this LeakReport report, LeakReportingPhotoDescriptionUpdate update)
        {
            return report
                .GetChildrenRecursive()
                .OfType<AttributeBase>()
                .Where(a => a.Photos != null && a.Photos.Length > 0)
                .SelectMany(a => a.Photos)
                .FirstOrDefault(p => p.DatabaseId == update.PhotoDatabaseId);
        }
    }
}