export class AssetManagementSite {
    clientid:number;
    locationid: number;
    clientname: string;
    locationname: string;
    /*rsitE_RID: number;
    rsitE_GROUP: string; 
    rsitE_NAME: string;*/

    constructor(options?: Partial<AssetManagementSite>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }
    }
}
