import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
    DxButtonModule,
    DxDataGridModule,
    DxLoadPanelModule,
    DxPopupModule,
    DxScrollViewModule
} from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { BehaviorSubject } from 'rxjs';
import { UserProfile } from '../../../profile/models';
import { HasRolePipe, HasRolesPipe } from '../../../shared/pipes';
import { UsersService } from '../../../shared/services';
import { ApmService } from '../../services';
import { ReportTabComponent } from './report-tab.component';

describe('ReportTabComponent', () => {
    let component: ReportTabComponent;
    let fixture: ComponentFixture<ReportTabComponent>;
    let userSubject = new BehaviorSubject<UserProfile>(new UserProfile());

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxButtonModule,
                DxDataGridModule,
                DxPopupModule,
                DxScrollViewModule,
                DxLoadPanelModule,
                HttpClientTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [ReportTabComponent, HasRolesPipe],
            providers: [
                HasRolePipe,
                { provide: ApmService, useValue: {} },
                {
                    provide: UsersService,
                    useValue: { currentProfile$: userSubject.asObservable() }
                }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ReportTabComponent);
        component = fixture.componentInstance;
        component.workOrderDetail = {
            id: '',
            location: '',
            projectId: '',
            assetDescription: '',
            assetID: '',
            assetCategory: '',
            client: '',
            status: '',
            fieldWorkCompleted: new Date(),
            publishTime: null,
            inspectionTypes: []
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should hide button if user does not have app:admin or apm:qaqc', () => {
        userSubject.next(new UserProfile({ roles: [] }));
        fixture.detectChanges();

        expect(document.getElementById('publish-button')).toBeNull();
    });

    it('should show button if user is app:admin', () => {
        userSubject.next(new UserProfile({ roles: ['app:admin'] }));
        fixture.detectChanges();

        expect(document.getElementById('publish-button')).toBeTruthy();
    });

    it('should show button if user is apm:qaqc', () => {
        userSubject.next(new UserProfile({ roles: ['apm:qaqc'] }));
        fixture.detectChanges();

        expect(document.getElementById('publish-button')).toBeTruthy();
    });
});
