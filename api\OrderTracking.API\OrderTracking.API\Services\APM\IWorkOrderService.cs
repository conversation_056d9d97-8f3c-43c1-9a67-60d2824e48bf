﻿using System.Collections.Generic;
using System.Threading.Tasks;
using APMWebDataInterface.ExampleDataModel;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Services.APM
{
    public interface IWorkOrderService
    {
        /// <summary>
        ///     Get Work Order by id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="projectId"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task<WorkOrder> Get(string id, string projectId, string email);

        /// <summary>
        ///     Get Work Orders from the same Asset by their ids
        /// </summary>
        /// <param name="asset"></param>
        /// <param name="workOrderIds"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task<IEnumerable<WorkOrder>> GetByAsset(Asset asset, IEnumerable<string> workOrderIds, string email);

        /// <summary>
        ///     Update a work order and relevant tasks in the work order from a <see cref="FiveTenAssetDetailsUpdate" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="assetDetails"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task Update(WorkOrder workOrder, FiveTenAssetDetailsUpdate assetDetails, string email);

        /// <summary>
        ///     Update a work order from a <see cref="WorkOrderTransportObject" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="update"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task Update(WorkOrder workOrder, WorkOrderTransportObject update, string email);

        /// <summary>
        ///     Update a work order from a <see cref="VisualInspection" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="inspection"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task Update(WorkOrder workOrder, VisualInspection inspection, string email);

        /// <summary>
        ///     Creates a new work order associated with a given asset and project from a <see cref="NewWorkOrder" />
        /// </summary>
        /// <param name="newWorkOrder"></param>
        /// <param name="asset"></param>
        /// <param name="project"></param>
        /// <param name="email"></param>
        /// <param name="businessUnitId"></param>
        /// <returns></returns>
        Task<WorkOrder> Create(NewWorkOrder newWorkOrder, Asset asset, Project project, string email, string businessUnitId);

        /// <summary>
        ///     Get all work orders
        /// </summary>
        /// <returns></returns>
        /// <param name="email"></param>
        Task<WorkOrder[]> Get(string email);

        /// <summary>
        ///     Get work orders associated with one or more projects (via project id)
        /// </summary>
        /// <param name="projectIds"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task<WorkOrder[]> GetByProject(string email, params string[] projectIds);

        /// <summary>
        ///     Get work order by id (takes longer than if you can provide a project id).  If
        ///     you have the project id, please use <see cref="Get(string,string)" /> with an id and projectId parameter.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task<WorkOrder> Get(string id, string email);

        /// <summary>
        ///     Creates a task on a work order from a <see cref="NewTask" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="newTask"></param>
        /// <param name="email"></param>
        /// <param name="businessUnitId"></param>
        /// <returns></returns>
        Task<APMTask> CreateTask(WorkOrder workOrder, NewTask newTask, string email, string businessUnitId);

        /// <summary>
        ///     Updates a work orders task from a <see cref="TaskUpdate" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="taskId"></param>
        /// <param name="taskUpdate"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task<APMTask> UpdateTask(WorkOrder workOrder, string taskId, TaskUpdate taskUpdate, string email);

        /// <summary>
        ///     Update a work order and relevant tasks in the work order from a <see cref="FiveSeventyWalkdown" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="assetDetails"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task Update(WorkOrder workOrder, FiveSeventyWalkdown assetDetails, string email);

        /// <summary>
        ///     Update a work order and relevant tasks in the work order from a <see cref="SixFiftyThreeWalkDown" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="assetDetails"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task Update(WorkOrder workOrder, SixFiftyThreeWalkDown assetDetails, string email);

        /// <summary>
        ///     Deletes a Visual Inspection Photo using a <see cref="VisualInspectionPhotoTransport" />
        /// </summary>
        /// <param name="visualInspectionPhoto"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task DeleteVisualInspectionPhoto(VisualInspectionPhotoTransport visualInspectionPhoto, string email);

        /// <summary>
        ///     Updates the description of a Visual Inspection Photo using a <see cref="VisualInspectionPhotoTransport" />
        /// </summary>
        /// <param name="visualInspectionPhoto"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task UpdateVisualInspectionPhotoDescription(VisualInspectionPhotoTransport visualInspectionPhoto, string email);

        /// <summary>
        ///     Updates the photo description of a photo in the asset details section of a work order (current walk down)
        /// </summary>
        /// <param name="photo"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task UpdateAssetDetailsPhotoDescription(AssetDetailsPhotoTransport photo, string email);

        /// <summary>
        ///     Deletes a photo in the asset details section of a work order (current walk down)
        /// </summary>
        /// <param name="photo"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task DeleteAssetDetailsPhoto(AssetDetailsPhotoTransport photo, string email);
        
        /// <summary>
        ///     Update the published state of a work order
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="update"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task<WorkOrder> UpdatePublishedState(WorkOrder workOrder, PublishUpdateObject update, string email);
        
        /// <summary>
        /// builds a vm for the work orders grid to make remote ops work w/ a calculated column
        /// </summary>
        /// <param name="workOrder"></param>
        /// <returns></returns>
        WorkOrderVM BuildWorkOrderVM(WorkOrder workOrder);
    }
}