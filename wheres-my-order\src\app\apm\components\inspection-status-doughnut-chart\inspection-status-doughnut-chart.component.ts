import { Component, Input, ViewChild } from '@angular/core';
import { DxPieChartComponent } from 'devextreme-angular/ui/pie-chart';
import dxPie<PERSON>hart from 'devextreme/viz/pie_chart';
import { AssetCategoriesSelectorComponent } from '../../components';
import { AssetCategoryAPICode, InspectionStatusBreakdown } from '../../models';
import { DashboardService } from '../../services';

@Component({
    selector: 'app-inspection-status-doughnut-chart',
    templateUrl: './inspection-status-doughnut-chart.component.html',
    styleUrls: ['./inspection-status-doughnut-chart.component.scss']
})
export class InspectionStatusDoughnutChartComponent {
    @Input() loading: boolean;
    @Input() inspectionStatusBreakdown: InspectionStatusBreakdown;

    @ViewChild(DxPieChartComponent) chart: DxPieChartComponent;
    @ViewChild(AssetCategoriesSelectorComponent)
    categoriesSelector: AssetCategoriesSelectorComponent;

    constructor(private readonly _dashboard: DashboardService) {}

    renderChart() {
        this.chart?.instance?.render({ force: true });
    }

    customizeLabel(arg: { valueText: string; percentText: string }) {
        return `${arg.valueText} (${arg.percentText})`;
    }

    onDrawn(e: { component: dxPieChart; element: HTMLElement }) {
        setTimeout(() => e.component.render({ animate: true }));
    }

    onCategoriesChanged(categories: AssetCategoryAPICode[]) {
        this._dashboard.inspectionStatusCategories.next(categories);
    }
}
