﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <UserSecretsId>7b587e00-44ee-4c30-9c99-3cbce3b6fc80</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <LangVersion>9</LangVersion>
  </PropertyGroup>

  <PropertyGroup>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <_WebToolingArtifacts Remove="Properties\launchSettings.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Properties\launchSettings.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Audit.NET" Version="19.1.4" />
    <PackageReference Include="Audit.NET.SqlServer" Version="19.1.4" />
    <!--<PackageReference Include="Azure.Storage.Blobs" Version="12.13.0" />-->
    <PackageReference Include="Dapper.SqlBuilder" Version="2.0.78" />
        <PackageReference Include="DevExpress.Document.Processor" Version="23.2.5" />
    <PackageReference Include="DevExtreme.AspNet.Data" Version="2.8.6" />
    <!-- Replaced Firebase with Azure AD B2C -->
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="6.0.5" />
    <!-- Replaced Google Cloud Storage with Azure Blob Storage -->
    <PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
    <!-- Replaced Google Cloud Firestore with Azure Cosmos DB -->
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.52.0" />
    <!-- Replaced Google Cloud Secret Manager with Azure Key Vault -->
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.5.0" />
    <PackageReference Include="Azure.Identity" Version="1.10.4" />
    <!-- Azure Application Insights for diagnostics -->
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.21.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.5" />
    <!-- Azure SignalR Service for real-time communication -->
    <PackageReference Include="Microsoft.Azure.SignalR" Version="1.21.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="6.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Dynamics.Sdk.Messages" Version="0.5.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.AzureAppServices" Version="6.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="6.0.0" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.56.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="1.25.0" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="1.25.0" />
    <PackageReference Include="Microsoft.PowerBI.Api" Version="4.6.0" />
    <PackageReference Include="Microsoft.PowerPlatform.Dataverse.Client" Version="1.0.1" />
    <PackageReference Include="Microsoft.PowerPlatform.Dataverse.Client.Dynamics" Version="1.0.1" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.16.1" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="6.0.5" />
    <PackageReference Include="SendGrid" Version="9.28.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.3.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.3.1" />
    <PackageReference Include="Z.Dapper.Plus" Version="4.0.29" />
    <PackageReference Include="Microsoft.AspNetCore.ResponseCompression" Version="2.2.0" />
    <PackageReference Include="TeamDigital.APM.WebDataInterface" Version="4.0.11" />
  </ItemGroup>

  <!--<ItemGroup>
	  <ProjectReference Include="..\AIMaaS\AIMaaS.csproj" />
  </ItemGroup>-->

  <ItemGroup>

	  <ProjectReference Include="..\AIMaaS\AIMaaS.csproj" />

	  <ProjectReference Include="..\ClientPortal.Shared\ClientPortal.Shared.csproj" />

  </ItemGroup>

  <ItemGroup>
    <None Update="Files\2021-1-12 - Client Portal Authorized User Terms of Use.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Files\OneInsight-Final.png">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
