import { IAttribute } from '../../../shared/models/attributes';
import { AttributeDate, AttributeName, AttributeSerialNumber } from './asset';

export interface WalkdownFiveSeventy {
    displayName: string;
    sectionIdentification: SectionIdentification;
    sectionGeneralInformation: SectionGeneralInformation;
    sectionOperatingDesignConditions: SectionOperatingDesignConditions;
    sectionComponents: SectionComponents;
}
export interface SectionIdentification {
    attributeEquipment_Description: IAttribute<string>;
    attributeName: AttributeNameOrAttributeNumberOrCircuitIDOrAttributeAssetTypeOrAttributeProductHandledOrAttributeLastKnownInspectionDateOrAttributeStartGISLocationOrAttributeLineToWhatEqiupmentIDOrAttributeEndGISLocationOrAttributeMFGDateOrAttributeLineNo;
    attributeNumber_or_Circuit_ID: AttributeNameOrAttributeNumberOrCircuitIDOrAttributeAssetTypeOrAttributeProductHandledOrAttributeLastKnownInspectionDateOrAttributeStartGISLocationOrAttributeLineToWhatEqiupmentIDOrAttributeEndGISLocationOrAttributeMFGDateOrAttributeLineNo;
    attributeAsset_Type: AttributeNameOrAttributeNumberOrCircuitIDOrAttributeAssetTypeOrAttributeProductHandledOrAttributeLastKnownInspectionDateOrAttributeStartGISLocationOrAttributeLineToWhatEqiupmentIDOrAttributeEndGISLocationOrAttributeMFGDateOrAttributeLineNo;
    attributeProduct_Handled: AttributeNameOrAttributeNumberOrCircuitIDOrAttributeAssetTypeOrAttributeProductHandledOrAttributeLastKnownInspectionDateOrAttributeStartGISLocationOrAttributeLineToWhatEqiupmentIDOrAttributeEndGISLocationOrAttributeMFGDateOrAttributeLineNo;
    attributeLast_known_inspection_date: AttributeNameOrAttributeNumberOrCircuitIDOrAttributeAssetTypeOrAttributeProductHandledOrAttributeLastKnownInspectionDateOrAttributeStartGISLocationOrAttributeLineToWhatEqiupmentIDOrAttributeEndGISLocationOrAttributeMFGDateOrAttributeLineNo;
    attributeLocation: AttributeLocation;
    attributeLine_from_what_equipment_ID: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributeStart_GIS_Location: AttributeNameOrAttributeNumberOrCircuitIDOrAttributeAssetTypeOrAttributeProductHandledOrAttributeLastKnownInspectionDateOrAttributeStartGISLocationOrAttributeLineToWhatEqiupmentIDOrAttributeEndGISLocationOrAttributeMFGDateOrAttributeLineNo;
    attributeLine_to_what_eqiupment_ID: AttributeNameOrAttributeNumberOrCircuitIDOrAttributeAssetTypeOrAttributeProductHandledOrAttributeLastKnownInspectionDateOrAttributeStartGISLocationOrAttributeLineToWhatEqiupmentIDOrAttributeEndGISLocationOrAttributeMFGDateOrAttributeLineNo;
    attributeEnd_GIS_Location: AttributeNameOrAttributeNumberOrCircuitIDOrAttributeAssetTypeOrAttributeProductHandledOrAttributeLastKnownInspectionDateOrAttributeStartGISLocationOrAttributeLineToWhatEqiupmentIDOrAttributeEndGISLocationOrAttributeMFGDateOrAttributeLineNo;
    displayName: string;
}
export interface AttributeNameOrAttributeNumberOrCircuitIDOrAttributeAssetTypeOrAttributeProductHandledOrAttributeLastKnownInspectionDateOrAttributeStartGISLocationOrAttributeLineToWhatEqiupmentIDOrAttributeEndGISLocationOrAttributeMFGDateOrAttributeLineNo {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue: string;
    pendingValue?: null;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLogOrCommentChangeLog;
    displayName: string;
    databaseName: string;
    photos?: null[] | null;
    photoChangeLog: PhotoChangeLogOrValueChangeLog;
    commentChangeLog: CommentChangeLogOrValueChangeLog;
    comment?: null;
}
export interface ValueChangeLogOrCommentChangeLog {
    entries?: EntriesEntity[] | null;
    pendingChange?: null;
    pendingChangeCopy?: null;
}
export interface EntriesEntity {
    key: string;
    value: string;
    timeChanged: string;
    userName: string;
}
export interface PhotoChangeLogOrValueChangeLog {
    entries?: null[] | null;
}
export interface CommentChangeLogOrValueChangeLog {
    entries?: null[] | null;
    pendingChange?: null;
    pendingChangeCopy?: null;
}
export interface AttributeLocation {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options?: OptionsEntity[] | null;
    currentValue: string;
    pendingValue?: null;
    currentPendingOrValue: string;
    valueChangeLog: ValueChangeLogOrCommentChangeLog;
    displayName: string;
    databaseName: string;
    photos?: null[] | null;
    photoChangeLog: PhotoChangeLogOrValueChangeLog;
    commentChangeLog: ValueChangeLogOrCommentChangeLog;
    comment: string;
}
export interface OptionsEntity {
    isCommentRequired: boolean;
    value: string;
    description?: null;
}
export interface AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue?: null;
    pendingValue?: null;
    currentPendingOrValue?: null;
    valueChangeLog: CommentChangeLogOrValueChangeLog;
    displayName: string;
    databaseName: string;
    photos?: null[] | null;
    photoChangeLog: PhotoChangeLogOrValueChangeLog;
    commentChangeLog: CommentChangeLogOrValueChangeLog;
    comment?: null;
}
export interface SectionGeneralInformation {
    attributePipe_Class: AttributePipeClassOrAttributeLowestFlangeRatingOrAttributePipeSizeOrAttributePipeScheduleOrAttributeOperatingStatus;
    attributeInstallation_Date: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributeIn_service_Date: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributePID_No_: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributeConstructionDesign_Drawing_Number: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributeLowest_Flange_Rating: AttributePipeClassOrAttributeLowestFlangeRatingOrAttributePipeSizeOrAttributePipeScheduleOrAttributeOperatingStatus;
    attributeConstruction_Method: AttributeConstructionMethod;
    attribute570AWQ149: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributePipe_Size: AttributePipeClassOrAttributeLowestFlangeRatingOrAttributePipeSizeOrAttributePipeScheduleOrAttributeOperatingStatus;
    attributePipe_Schedule: AttributePipeClassOrAttributeLowestFlangeRatingOrAttributePipeSizeOrAttributePipeScheduleOrAttributeOperatingStatus;
    displayName: string;
    sectionPhotos: SectionPhotos;
    sectionDesign: SectionDesign;
    sectionInspection: SectionInspection;
    sectionManufacturerFabricator: SectionManufacturerFabricator;
    sectionService: SectionService;
}
export interface AttributePipeClassOrAttributeLowestFlangeRatingOrAttributePipeSizeOrAttributePipeScheduleOrAttributeOperatingStatus {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options?: OptionsEntity[] | null;
    currentValue?: null;
    pendingValue?: null;
    currentPendingOrValue?: null;
    valueChangeLog: CommentChangeLogOrValueChangeLog;
    displayName: string;
    databaseName: string;
    photos?: null[] | null;
    photoChangeLog: PhotoChangeLogOrValueChangeLog;
    commentChangeLog: CommentChangeLogOrValueChangeLog;
    comment?: null;
}
export interface AttributeConstructionMethod {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options?: OptionsEntity[] | null;
    valueChangeLog: PhotoChangeLogOrValueChangeLog;
    currentValue?: null[] | null;
    pendingValue?: null[] | null;
    displayName: string;
    databaseName: string;
    photos?: null[] | null;
    photoChangeLog: PhotoChangeLogOrValueChangeLog;
    commentChangeLog: CommentChangeLogOrValueChangeLog;
    comment?: null;
}
export interface SectionPhotos {
    attributeFront: AttributeFrontOrAttributeBackOrAttributeLeftOrAttributeRight;
    attributeBack: AttributeFrontOrAttributeBackOrAttributeLeftOrAttributeRight;
    attributeLeft: AttributeFrontOrAttributeBackOrAttributeLeftOrAttributeRight;
    attributeRight: AttributeFrontOrAttributeBackOrAttributeLeftOrAttributeRight;
    displayName: string;
}
export interface AttributeFrontOrAttributeBackOrAttributeLeftOrAttributeRight {
    areCommentsRequired: boolean;
    attributeType: string;
    displayName: string;
    databaseName: string;
    photos?: null[] | null;
    photoChangeLog: PhotoChangeLogOrValueChangeLog;
    commentChangeLog: CommentChangeLogOrValueChangeLog;
    comment?: null;
}
export interface SectionDesign {
    attributeDesign_Code: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributeCode_Year: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributeAddendum: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    displayName: string;
}
export interface SectionInspection {
    attributeInspection_Code: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributeYear: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributeAddendum: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributeNDE_Examination_Methods: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    displayName: string;
}
export interface SectionManufacturerFabricator {
    attributeMFG_Name: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributeMFG_Date: AttributeNameOrAttributeNumberOrCircuitIDOrAttributeAssetTypeOrAttributeProductHandledOrAttributeLastKnownInspectionDateOrAttributeStartGISLocationOrAttributeLineToWhatEqiupmentIDOrAttributeEndGISLocationOrAttributeMFGDateOrAttributeLineNo;
    displayName: string;
    attributeName?: AttributeName;
    attributeDate?: AttributeDate;
    attributeSerial_Number?: AttributeSerialNumber;
}
export interface SectionService {
    attributeServiceProductContents: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributeSpecific_Gravity: AttributeSpecificGravityOrAttributeOperatingTemperatureOrAttributeJointEfficiency;
    displayName: string;
}
export interface AttributeSpecificGravityOrAttributeOperatingTemperatureOrAttributeJointEfficiency {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit?: null;
    currentValue?: null;
    pendingValue?: null;
    currentPendingOrValue?: null;
    valueChangeLog: CommentChangeLogOrValueChangeLog;
    displayName: string;
    databaseName: string;
    photos?: null[] | null;
    photoChangeLog: PhotoChangeLogOrValueChangeLog;
    commentChangeLog: CommentChangeLogOrValueChangeLog;
    comment?: null;
}
export interface SectionOperatingDesignConditions {
    attributeOperating_Temperature: AttributeSpecificGravityOrAttributeOperatingTemperatureOrAttributeJointEfficiency;
    attributeDesign_MAWP: AttributeDesignMAWPOrAttributeDesignTemperatureOrAttributeOperatingPressureOrAttributePRVSetPressureOrAttributeAllowableStressAtTemperatureOrAttributeNominalThicknessScheduleOrAttributeCorrosionAllowance;
    attributeDesign_Temperature: AttributeDesignMAWPOrAttributeDesignTemperatureOrAttributeOperatingPressureOrAttributePRVSetPressureOrAttributeAllowableStressAtTemperatureOrAttributeNominalThicknessScheduleOrAttributeCorrosionAllowance;
    attributeOperating_Pressure: AttributeDesignMAWPOrAttributeDesignTemperatureOrAttributeOperatingPressureOrAttributePRVSetPressureOrAttributeAllowableStressAtTemperatureOrAttributeNominalThicknessScheduleOrAttributeCorrosionAllowance;
    attributePRV_Set_Pressure: AttributeDesignMAWPOrAttributeDesignTemperatureOrAttributeOperatingPressureOrAttributePRVSetPressureOrAttributeAllowableStressAtTemperatureOrAttributeNominalThicknessScheduleOrAttributeCorrosionAllowance;
    attributeOperation_Status: AttributePipeClassOrAttributeLowestFlangeRatingOrAttributePipeSizeOrAttributePipeScheduleOrAttributeOperatingStatus;
    displayName: string;
}
export interface AttributeDesignMAWPOrAttributeDesignTemperatureOrAttributeOperatingPressureOrAttributePRVSetPressureOrAttributeAllowableStressAtTemperatureOrAttributeNominalThicknessScheduleOrAttributeCorrosionAllowance {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit: string;
    currentValue?: null;
    pendingValue?: null;
    currentPendingOrValue?: null;
    valueChangeLog: CommentChangeLogOrValueChangeLog;
    displayName: string;
    databaseName: string;
    photos?: null[] | null;
    photoChangeLog: PhotoChangeLogOrValueChangeLog;
    commentChangeLog: CommentChangeLogOrValueChangeLog;
    comment?: null;
}
export interface SectionComponents {
    displayName: string;
    sectionPipe: SectionPipe;
}
export interface SectionPipe {
    name: string;
    changeLog: ChangeLog;
    currentEntries?: CurrentEntriesEntityOrPendingEntriesEntity[] | null;
    pendingEntries?: CurrentEntriesEntityOrPendingEntriesEntity[] | null;
}
export interface ChangeLog {
    entries?: EntriesEntity1[] | null;
}
export interface EntriesEntity1 {
    action: string;
    key: string;
    value: string;
    timeChanged: string;
    userName: string;
}
export interface CurrentEntriesEntityOrPendingEntriesEntity {
    attributeLine_No_: AttributeLineNo;
    attributeMaterial_Spec_and_Grade: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    attributeAllowable_Stress_at_Temperature: AttributeDesignMAWPOrAttributeDesignTemperatureOrAttributeOperatingPressureOrAttributePRVSetPressureOrAttributeAllowableStressAtTemperatureOrAttributeNominalThicknessScheduleOrAttributeCorrosionAllowance;
    attributeNominal_Thickness_schedule: AttributeDesignMAWPOrAttributeDesignTemperatureOrAttributeOperatingPressureOrAttributePRVSetPressureOrAttributeAllowableStressAtTemperatureOrAttributeNominalThicknessScheduleOrAttributeCorrosionAllowance;
    attributeCorrosion_Allowance: AttributeDesignMAWPOrAttributeDesignTemperatureOrAttributeOperatingPressureOrAttributePRVSetPressureOrAttributeAllowableStressAtTemperatureOrAttributeNominalThicknessScheduleOrAttributeCorrosionAllowance;
    attributeJoint_Efficiency: AttributeSpecificGravityOrAttributeOperatingTemperatureOrAttributeJointEfficiency;
    attributePipe_Spec_Number: AttributeLineFromWhatEquipmentIDOrAttributeDesignCodeOrAttributeCodeYearOrAttributeAddendumOrAttributeInspectionCodeOrAttributeYearOrAttributeNDEExaminationMethodsOrAttributeMFGNameOrAttributeServiceProductContentsOrAttributeInstallationDateOrAttributeInServiceDateOrAttributePIDNoOrAttributeConstructionDesignDrawingNumberOrAttribute570AWQ149OrAttributeLineNoOrAttributeMaterialSpecAndGradeOrAttributePipeSpecNumber;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: string;
}
export interface AttributeLineNo {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue?: string | null;
    pendingValue?: null;
    currentPendingOrValue?: string | null;
    valueChangeLog: ValueChangeLog;
    displayName: string;
    databaseName: string;
    photos?: null[] | null;
    photoChangeLog: PhotoChangeLogOrValueChangeLog;
    commentChangeLog: CommentChangeLogOrValueChangeLog;
    comment?: null;
}
export interface ValueChangeLog {
    entries?: (EntriesEntity2 | null)[] | null;
    pendingChange?: null;
    pendingChangeCopy?: null;
}
export interface EntriesEntity2 {
    key: string;
    value: string;
    timeChanged: string;
    userName: string;
}
