﻿// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.

using System.Diagnostics.CodeAnalysis;
using System.Runtime.CompilerServices;

// JTR: asp.net core does not use synchronization context, so not necessary
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task")]

// JTR: overkill for this app.  Consider removing if we take French or other cultures with
//      numbers with "," and "." switched
[assembly: SuppressMessage("Globalization", "CA1305:Specify IFormatProvider")]
[assembly: SuppressMessage("Globalization", "CA1304:Specify CultureInfo")]

[assembly: InternalsVisibleTo("OrderTracking.API.Tests")]