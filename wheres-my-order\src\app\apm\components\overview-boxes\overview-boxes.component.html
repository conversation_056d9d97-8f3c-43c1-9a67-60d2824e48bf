<div class="dx-card responsive-paddings content-block"
     style="position: relative">
    <dx-load-indicator *ngIf="loading"
                       id="large-indicator"
                       style="position: absolute; left: calc(50% - 30px); top: calc(50% - 30px)"
                       height="60"
                       width="60"></dx-load-indicator>
    <app-overview-box [icon]="'fa fa-folder-open-o'"
                      [value]="summaryInfo?.fwUTCount"
                      [label]="'Total CMLs in system'"></app-overview-box>
    <app-overview-box [icon]="'fa fa-bullseye'"
                      [value]="summaryInfo?.targetReportTimeInDays"
                      [label]="'Target Report Turn Around Time'">
    </app-overview-box>
    <app-overview-box [style.color]="summaryInfo?.averageDaysToPublish > summaryInfo?.targetReportTimeInDays ? 'red' : null"
                      [icon]="summaryInfo?.averageDaysToPublish > summaryInfo?.targetReportTimeInDays ? 'fa fa-exclamation-triangle' : 'fa fa-check-square-o'"
                      [value]="summaryInfo?.averageDaysToPublish | number : '1.0-1'"
                      [label]="'Average Report Turn Around Time'">
    </app-overview-box>
</div>
