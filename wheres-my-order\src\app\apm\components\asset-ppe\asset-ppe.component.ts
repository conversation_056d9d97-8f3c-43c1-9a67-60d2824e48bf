import { Component, EventEmitter, Input, Output } from '@angular/core';
import cloneDeep from 'clone-deep';
import { DxFormComponent } from 'devextreme-angular';
import { AssetPPEViewModel } from '../../models';

@Component({
    selector: 'app-asset-ppe',
    templateUrl: './asset-ppe.component.html',
    styleUrls: ['./asset-ppe.component.scss']
})
export class AssetPpeComponent {
    private _assetPPE: AssetPPEViewModel | undefined;
    private _original: AssetPPEViewModel | undefined;
    private _changes: Partial<AssetPPEViewModel> = {};

    @Input() set assetPPE(value: AssetPPEViewModel) {
        this.isEditing = false;
        this._assetPPE = value;
        this._original = cloneDeep(this._assetPPE, true);
        this._changes = {};
    }

    get assetPPE(): AssetPPEViewModel {
        return this._assetPPE;
    }

    @Output() save = new EventEmitter<Partial<AssetPPEViewModel>>();
    @Input() allowEditing: boolean;
    isEditing = false;
    isSaving = false;

    constructor() {}

    onFieldDataChanged(e) {
        let value;
        if (e.value === true) value = 'Yes';
        else if (e.value === false) value = 'No';
        else value = e.value;
        this._changes[e.dataField] = value;
    }

    onEditClicked(e) {
        this.isEditing = true;
    }

    onSaveClicked(e) {
        this.save.next({ id: this._assetPPE.id, ...this._changes });
    }

    onCancelClicked(e, form: DxFormComponent) {
        this.isEditing = false;
        form.instance.option('formData', this._original);
        this._changes = {};
    }
}
