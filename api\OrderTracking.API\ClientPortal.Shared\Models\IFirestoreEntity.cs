﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ClientPortal.Shared.Models
{
    /// <summary>
    /// DEPRECATED: Interface for Firestore entities - replaced by ICosmosEntity
    /// This interface is kept for backward compatibility during migration
    /// </summary>
    [Obsolete("Use ICosmosEntity<TKey> instead. This interface will be removed after Azure migration is complete.")]
    public interface IFirestoreEntity<out TKey> : IEntity<TKey>
    {
    }
}
