﻿using System.ComponentModel.DataAnnotations.Schema;
using Dapper.Contrib.Extensions;
using Newtonsoft.Json;

namespace ClientPortal.Shared.Models
{
    [Dapper.Contrib.Extensions.Table("OrderFiles")]
    [System.ComponentModel.DataAnnotations.Schema.Table("OrderFiles")]
    public class OrderBlobFile : BlobFile
    {
        [JsonProperty(PropertyName = "id")]
        [ExplicitKey]
        public string Id { get; set; }

        [JsonProperty(PropertyName = "salesLineRecId")]
        [ForeignKey("Order")]
        public long? SalesLineRecId { get; set; }
        
        [Write(false)]
        [JsonIgnore]
        public Order Order { get; set; }

    }
}