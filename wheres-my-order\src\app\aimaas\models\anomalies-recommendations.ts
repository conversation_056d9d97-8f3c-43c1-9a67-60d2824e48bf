export class AnomaliesRecommendations {
    clientid: string;
    clientname: string;
    locationname: string;
    operationid: string;
    anomalypriority: string;
    serialnumber:number;
    anomalytype: string;
    anomalydescription: string;
    inspectionoperationinstance: string;
    resolutionstate: string;
    anomaly: string;
    locationid: string;
    detectiondate: string;
    operationdate: string;
    assetid: string;
    assetname : string;
    proposedrecommemendation: string;
    constructor(options?: Partial<AnomaliesRecommendations>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }
    }
}
