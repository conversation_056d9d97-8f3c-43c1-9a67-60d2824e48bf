import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrModule } from 'ngx-toastr';
import { PhotoSasPipe } from '../../pipes';
import { ApmService } from '../../services';
import { WorkDetailsTabComponent } from './work-details-tab.component';

describe('WorkDetailsTabComponent', () => {
    let component: WorkDetailsTabComponent;
    let fixture: ComponentFixture<WorkDetailsTabComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [HttpClientTestingModule, ToastrModule.forRoot()],
            providers: [{ provide: ApmService, useValue: {} }, PhotoSasPipe],
            declarations: [WorkDetailsTabComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(WorkDetailsTabComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
