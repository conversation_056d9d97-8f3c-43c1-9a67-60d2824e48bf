import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { DxButtonModule } from 'devextreme-angular';
import { DxDataGridModule } from 'devextreme-angular/ui/data-grid';
import { DxFormModule } from 'devextreme-angular/ui/form';
import { DxTabPanelModule } from 'devextreme-angular/ui/tab-panel';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import {
    AssetsTabComponent,
    DetailsTabComponent,
    LocationTabComponent,
    ProjectDetailsComponent,
    ProjectsGridComponent
} from '../../components';
import { ProjectDetailPipe } from '../../pipes/project-detail.pipe';
import { ApmService } from '../../services';
import { ProjectsComponent } from './projects.component';

describe('ProjectsComponent', () => {
    let component: ProjectsComponent;
    let fixture: ComponentFixture<ProjectsComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxDataGridModule,
                DxFormModule,
                DxTabPanelModule,
                DxButtonModule,
                HttpClientTestingModule,
                RouterTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [
                ProjectsComponent,
                ProjectsGridComponent,
                ProjectDetailsComponent,
                DetailsTabComponent,
                LocationTabComponent,
                AssetsTabComponent,
                ProjectDetailPipe
            ],
            providers: [
                {
                    provide: ApmService,
                    useValue: {
                        selectedBU$: of('123'),
                        buSelected$: of(false),
                        getUsers: () => {},
                        getLocations: () => {}
                    }
                }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ProjectsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
