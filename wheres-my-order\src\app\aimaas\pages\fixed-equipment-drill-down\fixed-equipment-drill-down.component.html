<div *ngIf="canShowEquipmentList">
    <app-breadcrumbs [crumbs]="crumbs"> </app-breadcrumbs>
    <dx-popup [(visible)]="popupVisible"
              [width]="1200"
              [height]="700"
              [showTitle]="true"
              [title]="equipmentPopupTitle"
              [dragEnabled]="false"
              [showCloseButton]="true"
              (onHiding)="closePopup()">

        <div class="tabs-demo">
            <div class="widget-container">
                <dx-tab-panel>

                    <dxi-item title="General Information">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=550>
                                <ng-container *ngIf="popupVisible">


                                    <app-asset-generalinformation [selectedAssetId]="currentAssetId"
                                                                  [asset]="currentAssetDetails">
                                    </app-asset-generalinformation>
                                </ng-container>
                            </dx-scroll-view>
                        </div>
                    </dxi-item>
                    <dxi-item title="Inspection Schedule">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=550>
                                <ng-container *ngIf="popupVisible">
                                    <app-inspection-schedule
                                                             [assetinspection]="inspectionSchedule">
                                    </app-inspection-schedule>
                                </ng-container>
                            </dx-scroll-view>
                        </div>

                    </dxi-item>

                    <dxi-item title="Component Details">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=540>
                                <app-asset-component-details
                                                             [assetComponent]="assetComponentMap">
                                </app-asset-component-details>
                            </dx-scroll-view>
                        </div>
                    </dxi-item>
                    <dxi-item title="General Analysis">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=600>
                                <ng-container *ngIf="popupVisible">
                                    <app-general-analysis
                                                          [assetId]="currentAssetId">
                                    </app-general-analysis>
                                </ng-container>
                            </dx-scroll-view>
                        </div>
                    </dxi-item>
                    <dxi-item title="Asset Attachments">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=600>
                                <ng-container *ngIf="popupVisible">
                                    <app-attachments
                                                     [selectedAssetId]="currentAssetId">
                                    </app-attachments>
                                </ng-container>
                            </dx-scroll-view>
                        </div>
                    </dxi-item>
                    <dxi-item title="Submissions">


                        <div *dxTemplate="let data">
                            <ng-container *ngIf="popupVisible">
                                <dx-scroll-view [height]=600>


                                    <app-submissions
                                                     [selectedAssetId]="currentAssetId">
                                    </app-submissions>
                                </dx-scroll-view>
                            </ng-container>
                        </div>
                    </dxi-item>
                </dx-tab-panel>

            </div>
        </div>
    </dx-popup>
    <dx-popup [(visible)]="submissionPopupVisible"
              [width]="1200"
              [height]="700"
              [showTitle]="true"
              [dragEnabled]="false"
              [showCloseButton]="true">
        <dxi-toolbar-item toolbar="top"
                          [text]="submissionPopupTitle"
                          location="center"
                          locateInMenu="always"></dxi-toolbar-item>

        <ng-container *ngIf="submissionPopupVisible">

            <app-client-data-submission [initialAnomaly]="initialAnomaly"
                                        (clientSubmissionTitleValueChange)="clientSubmissionTitleValueChange($event)"
                                        (formSubmittedValueChange)="clientDataFormSubmitted($event)">
            </app-client-data-submission>
        </ng-container>
    </dx-popup>

    <div class="dx-card content-block responsive-paddings"
         style="max-width: 94vw;">
        <div
             style="display: flex; justify-content: space-between; align-items: center;">
            <h3>Assets</h3>
            <div style="margin-left: auto;"
                 *ngIf="availableSites?.length>0">

                <!-- <dx-button [routerLink]="['../drilldown']"
             class="listpagebuttons">
      Equipment List
  </dx-button> -->
                <dx-button [routerLink]="['../inspection-drilldown']"
                           class="listpagebuttons">
                    Inspections List
                </dx-button>
                <dx-button [routerLink]="['../anomaly-drilldown']"
                           class="listpagebuttons">
                    Recommendations List
                </dx-button>

                <dx-button *ngIf="availableSites?.length>0 && (( 
   currentUser | hasRole: 'AIMaaS:Edit') ||( currentUser | hasRole: 'App:Admin') || 
   (currentUser | hasRole: 'AIMaaS:Admin')|| ( currentUser | hasRole: 'AIMaaS:Demo'))"
                           (onClick)="clientSubmitDataOnclick('frombuttonclick')"
                           class="listpagebuttons">
                    Action Center
                </dx-button>
            </div>
        </div>
        <dx-select-box *ngIf="availableSites?.length > 0"
                       #siteSelectionBox
                       id="selectBox"
                       style="width:370px; margin:2px"
                       [items]="availableSites"
                       [displayExpr]="customDisplayExpr"
                       [(value)]="selectedSite"
                       [hint]="selectedSite | siteLabel"
                       [showClearButton]="false"
                       [searchEnabled]="true"
                       itemTemplate="item"
                       stylingMode="filled"
                       (onSelectionChanged)="changeSite($event)">
            <dxo-drop-down-options container="#selectBox"
                                   [closeOnTargetScroll]="false"></dxo-drop-down-options>
            <div *dxTemplate="let data of 'item'">
                <div style="display:inline-block">{{data | siteLabel}}</div>
            </div>
        </dx-select-box>
        <div><dx-data-grid #grid
                          id="gridload"
                          class="gridload"
                          [dataSource]="assetDataSource"
                          [showBorders]="true"
                          [selectedRowKeys]="[]"
                          [filterValue]="currentFilter"
                          [allowColumnResizing]="true"
                          [filterSyncEnabled]="true"
                          [allowColumnReordering]="true"
                          (onCellPrepared)="onCellPrepared($event)"
                          (onRowExpanding)="onRowExpanding($event)"
                          (onRowClick)="assetSelectionChanged($event)"
                          (onExporting)="onExporting($event)">
                <dxo-load-panel [enabled]="true"></dxo-load-panel>

                <!-- Paging -->
                <dxo-paging [enabled]="true"
                            [pageSize]="10"></dxo-paging>
                <dxo-pager [showPageSizeSelector]="true"
                           [allowedPageSizes]="[5, 10, 25, 50]"></dxo-pager>

                <!-- Export -->
                <dxo-export [enabled]="true"></dxo-export>
                <dxo-toolbar>
                    <dxi-item name="groupPanel"></dxi-item>
                    <dxi-item widget="dxButton"
                              location="after"
                              [options]="{icon: 'fa fa-undo', hint: 'Restore Grid Defaults', onClick: restoreAssetsDefaultsClicked}">
                    </dxi-item>
                    <dxi-item name="columnChooserButton"></dxi-item>
                    <dxi-item name="exportButton"></dxi-item>
                </dxo-toolbar>
                <dxo-state-storing [enabled]="true"
                                   type="custom"
                                   storageKey="equipment"
                                   [customLoad]="loadState"
                                   [customSave]="saveState">
                </dxo-state-storing>
                <dxo-column-chooser [enabled]="true"
                                    mode="dragAndDrop"
                                    [width]="310"
                                    title="Column Chooser (Drag and Drop)">
                </dxo-column-chooser>
                <dxo-filter-row [visible]="true"></dxo-filter-row>
                <dxo-header-filter [visible]="true"></dxo-header-filter>
                <dxo-selection mode="single"></dxo-selection>
                <dxo-filter-panel [visible]="true"></dxo-filter-panel>

                <dxi-column caption="P-Unit"
                            dataField="areaname"></dxi-column>
                <dxi-column dataField="assetid"
                            caption="Asset Id"></dxi-column>
                <dxi-column dataField="description"
                            caption="Description"
                            dataType="string"
                            [width]="200"></dxi-column>
                <dxi-column caption="Asset Type "
                            dataField="assettype"></dxi-column>
                <dxi-column caption="Services/Product/Contents"
                            dataField="service"></dxi-column>

                <dxi-column dataField="serialnumber"
                            caption="Serial Number"
                            dataType="string"
                            [visible]="false"></dxi-column>

                <dxi-column dataField="pid"
                            caption="P&ID"
                            [calculateCellValue]="formatPID"
                            dataType="string"></dxi-column>
                <dxi-column dataField="orientation"
                            caption="Orientation"
                            dataType="string"
                            [visible]="false"></dxi-column>
                <dxi-column dataField="manufacturer"
                            caption="Manufacturer"
                            dataType="string"
                            [visible]="false"></dxi-column>

                <dxi-column dataField="localjuridictional"
                            caption="Local Juridictional"
                            [calculateCellValue]="formatLocalJudictional"
                            dataType="string"
                            [visible]="false"></dxi-column>
                <dxi-column dataField="
                    nationalboard"
                            caption="National Board"
                            dataType="string"
                            [visible]="false"></dxi-column>

                <dxi-column dataField="constructioncode"
                            caption="Design Code"
                            dataType="string"
                            [visible]="false"></dxi-column>

                <dxi-column dataField="inspectioncode"
                            caption="Inspection Code"
                            dataType="string"
                            [visible]="false"></dxi-column>
                <dxi-column dataField="equipcategory"
                            caption="Equipment Category"
                            dataType="string"
                            [visible]="false"></dxi-column>
                <dxi-column dataField="riskClass"
                            caption="Risk Class"
                            dataType="string"
                            [visible]="false"></dxi-column>

            </dx-data-grid>
            <div *ngIf="isLoading"
                 style="width: 100%; height: 100%;">
                <dx-load-panel #loadPanel
                               shadingColor="rgba(0,0,0,0.4)"
                               [position]="{ of: '.gridload' }"
                               [(visible)]="isLoading"
                               [showIndicator]="true"
                               [showPane]="true"
                               [shading]="true"
                               [hideOnOutsideClick]="false">
                </dx-load-panel>
            </div>
        </div>
    </div>
</div>
<div *ngIf="!canShowEquipmentList"
     class="center-text">
    <p>
        <strong> Asset Id is not found or you may not access to this
            Asset</strong>
    </p>

</div>