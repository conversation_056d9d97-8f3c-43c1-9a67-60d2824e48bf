const { authenticator } = require("otplib");
const username = Cypress.env("username_APM");
const password = Cypress.env("password_APM");
const otp = authenticator.generate(Cypress.env("secret_key"));
const today = new Date();
const day = today.getDate();
const month = today.getMonth() + 1;
const year = today.getFullYear();
const formatteddate = `${month}/${day}/${year}`;
const nextday = new Date();
const futuredate = new Date(nextday);
futuredate.setDate(nextday.getDate() + 3);
const day1 = ("0" + futuredate.getDate()).slice(-2);
const month1 = ("0" + (futuredate.getMonth() + 1)).slice(-2); // Adding leading zero if needed
const year1 = futuredate.getFullYear();
const formatteddate1 = `${month1}/${day1}/${year1}`;
const Teamprojectnumber = "Automateproject";
const Asset_category = "Vessel";
const Facitlityname = "Facilitytesting";
const Assignees = Cypress.env("Assigneesallocated");
const status = "Scheduled";
const clientworkordernumber = "ClientWO01";
const Client_Cost_Code = "Costalcode01";
const Purchase_Order_AFE = "PO 01";
const ClientWODescription = "This is generated for automation purpose";
const equipementDes = "checkingpurpose";
const addsitename = "Site123";
const value = "Value123";
const comment = "comment123";
const Lease = "Leasenew1";
const Equipment = "Equipmentnew1";
const desc = "Adding job description";
const Asset_total = " Total Tasks: 58 ";
const selectcategory = "510";
const Task_total = "Total Assets: 138";

class APM {
  elements = {
    Login: () => cy.xpath("//span[text()='Login']"),
    Teamlogin: () => cy.xpath("//button[@id='TeamActiveDirectory']"),
    usernameclick: () => cy.get('input[type="email"]'),
    submitusername: () => cy.get('input[type="submit"]'),
    Passwordclick: () => cy.get('input[type="password"]'),
    TOTP: () => cy.get("[id='idTxtBx_SAOTCC_OTC']"),
    TOTP_Submit: () => cy.get("#idSubmit_SAOTCC_Continue"),
    Finalsubmit: () => cy.get("#idSIButton9"),
    Workmanagement: () => cy.xpath("//span[text()='Work Management']"),
    Assetperformancemanagement: () =>
      cy.xpath("//span[text()='Asset Performance Management']"),
    Project: () => cy.xpath("//span[text()='Projects']"),
    create: () => cy.xpath("//span[text()='Create']"),
    Asset: () => cy.get("li[data-item-id='/apm/asset-management']"),
    Create_Asset: () => cy.xpath("(//span[text()='Create'])[1]"),
    box1: () => cy.xpath("(//input[@role='textbox'])[13]"),
    box2: () => cy.xpath("(//input[@role='combobox'])[9]"),
    box2a: () => cy.get("div[role='option']"),
    box3: () => cy.xpath("(//input[@role='textbox'])[15]"),
    box4: () => cy.xpath("(//input[@role='combobox'])[10]"),
    box5: () => cy.xpath("(//input[@role='combobox'])[11]"),
    Save_button: () => cy.xpath("//span[text()='Save']"),
    toasttitle: () =>
      cy.xpath("//div[@aria-label='Project successfully created']"),
    box6: () => cy.xpath("//input[@aria-label='Search in the data grid']"),
    box7: () => cy.xpath("//*[@role='gridcell' and @aria-colindex='4']/span"),
    Asset_box1: () => cy.xpath("(//input[@aria-autocomplete='list'])[2]"),
    Asset_box1a: () => cy.get("div[role='option']"),
    Asset_box2: () => cy.xpath("//input[@name='assetId']"),
    Asset_box3: () => cy.xpath("//input[@name='assetName']"),
    Asset_box4: () => cy.xpath("(//input[@aria-autocomplete='list'])[3]"),
    Asset_box4a: () => cy.get("div[role='option']"),
    Asset_box5: () => cy.xpath("(//input[@role='spinbutton'])[1]"),
    Asset_box6: () => cy.xpath("(//input[@role='spinbutton'])[2]"),
    Asset_box7: () => cy.xpath("//span[text()='Save and Create Work Order']"),
    Asset_box8: () => cy.xpath("//input[@name='facilityName']"),
    Asset_box9: () => cy.xpath("(//input[@aria-haspopup='listbox'])[4]"),
    Asset_box9a: () => cy.get("div[role='option']"),
    Asset_box10: () => cy.xpath("//span[text()='Save']"),
    Asset_box10a: () => cy.xpath("//span[text()='Save and Create Work Order']"),
    Asset_box11: () => cy.xpath("//span[text()='Create']"),
    Task_box1: () => cy.xpath("//span[text()='Work Orders']"),
    Task_box2: () =>
      cy.xpath("//*[@role='gridcell' and @aria-colindex='1']/span"),
    Task_box3: () => cy.xpath("(//div[@role='tab'])[3]"),
    Task_box4: () => cy.xpath("(//input[@role='combobox'])[15]"),
    Task_box4a: () => cy.get("div[role='option']"),
    Task_box5: () =>
      cy.xpath(
        "//div[@class='dx-texteditor-input-container dx-tag-container']"
      ),
    Task_box6: () => cy.xpath("(//input[@role='textbox'])[26]"),
    Task_box7: () => cy.xpath("(//input[@role='combobox'])[17]"),
    Task_box8: () => cy.xpath("(//input[@role='combobox'])[20]"),
    Task_box9: () => cy.xpath("(//input[@role='textbox'])[27]"),
    Task_box10: () => cy.xpath("(//input[@role='textbox'])[28]"),
    Task_box11: () => cy.xpath("(//input[@role='textbox'])[25]"),
    Task_Save: () => cy.xpath("(//span[text()='Save'])[2]"),
    Task_create: () => cy.xpath("(//span[text()='Create'])[2]"),
    Task_toastmessage: () => cy.get(".toast-message"),
    WOM: () => cy.get("li[data-item-id='/apm/work-orders']"),
    WO_Create: () => cy.get("div[aria-label='Create']"),
    WO_details_icon: () => cy.xpath("(//a[@title='Work Order Details'])[2]"),
    WO_box1: () => cy.xpath("(//input[@aria-autocomplete='list'])[10]"),
    WO_box2: () => cy.xpath("(//input[@aria-haspopup='listbox'])[6]"),
    WO_box3: () => cy.xpath("(//input[@role='textbox'])[29]"),
    WO_facility: () => cy.xpath("(//input[@role='textbox'])[14]"),
    WO_box4: () => cy.xpath("(//input[@role='combobox'])[28]"),

    WO_box5: () => cy.xpath("(//span[text()='Save'])[2]"),

    WO_box6: () => cy.xpath("(//input[@aria-haspopup='listbox'])[6]"),
    WO_BBox1: () => cy.xpath("(//input[@aria-autocomplete='list'])[2]"),
    WO_BBox2: () => cy.xpath("(//input[@aria-autocomplete='list'])[3]"),
    Task_page: () => cy.get("li[data-item-id='/apm/tasks']"),
    Edit: () => cy.xpath("//span[text()='Edit']"),
    Assetdetails: () => cy.xpath("//span[text()='Asset Details']"),
    Equipmentdescription: () => cy.xpath("(//input[@role='textbox'])[10]"),
    toasttitle2: () =>
      cy.xpath("//div[@aria-label='Asset task data updated successfully']"),
    Totaltitile_LR: () =>
      cy.xpath("//div[@aria-label='Leak Report successfully created']"),
    toasttitle_LR2: () =>
      cy.xpath(
        "//div[@aria-label='Successfully updated leak report work details']"
      ),
    toasttitle_LR3: () =>
      cy.xpath(
        "//div[@aria-label='Successfully updated leak report information']"
      ),
    Clientmanagement_tab: () =>
      cy.get("li[data-item-id='/apm/client-management']"),
    Add: () =>
      cy.xpath("//div[@aria-label='Add']//div[@class='dx-button-content']"),
    New_client: () => cy.get("div[title='New Client']"),
    NewBusiness_unit: () => cy.get("div[title='New Business Unit']"),
    clientname: () => cy.get("input[name='clientName']"),
    client_from_option: () => cy.xpath("(//input[@role='combobox'])[1]"),
    sitename: () => cy.get("input[name='siteName']"),
    users: () =>
      cy.xpath(
        "(//div[@class='dx-texteditor-input-container dx-tag-container'])[1]"
      ),
    Ok_button: () => cy.xpath("//span[text()='OK']"),
    toasttitle3: () =>
      cy.xpath("div[class='ng-tns-c16-1 toast-message ng-star-inserted']"),
    Leak_report: () => cy.get("li[data-item-id='/apm/leak-reporting']"),
    Area_LK: () => cy.xpath("(//input[@role='textbox'])[8]"),
    Lease_LK: () => cy.xpath("(//input[@role='textbox'])[9]"),
    EquipementID_LK: () => cy.xpath("(//input[@role='textbox'])[10]"),
    Jobdescription_LK: () => cy.xpath("(//input[@role='textbox'])[11]"),
    Activeradio_LK: () => cy.xpath("//div[text()='active']"),
    Select_Area: () =>
      cy.xpath("//td[@role='gridcell' and @aria-colindex='2']/span"),
    Work_details_LR: () => cy.xpath("//span[text()='Work Details']"),
    Edit_LR: () => cy.xpath("(//span[text()='Edit'])[1]"),
    Edit_LR2: () => cy.xpath("(//span[text()='Edit'])[2]"),
    client_value_LR: () => cy.xpath("//input[@name='client.value']"),
    client_comment_LR: () => cy.xpath("//input[@name='client.comment']"),
    Leakrepot_Tab: () => cy.xpath("//span[text()='Leak Report']"),
    EquipmentID_value: () =>
      cy.xpath("//input[@name='equipmentIDAtLineStart.value']"),
    EquipmentID_comment: () =>
      cy.xpath("//input[@name='equipmentIDAtLineStart.comment']"),
    LR_Save: () => cy.xpath("(//span[text()='Save'])[2]"),
    Facility_new: () => cy.xpath("//input[@name='facilityName']"),
    Dashboard_tab: () => cy.get("li[data-item-id='/apm/dashboard']"),
    SelectedProjects: () =>
      cy.xpath(
        '(//div[@class="dx-texteditor-input-container dx-tag-container"])[1]'
      ),
    SelectedProjectfromOptions: () =>
      cy.xpath("(//span[@class='dx-checkbox-icon'])[7]"),
    TotalAssets: () => cy.xpath("//p[normalize-space()='Total Assets: 138']"),
    SelectAssetCategories_1: () =>
      cy.xpath(
        '(//div[@class="dx-texteditor-input-container dx-tag-container"])[2]'
      ),
    TotalTask: () => cy.xpath("//p[normalize-space()='Total Tasks: 58']"),
    SelectAssetCategories_2: () =>
      cy.xpath(
        '(//div[@class="dx-texteditor-input-container dx-tag-container"])[3]'
      ),
    MonthlyTasktracker: () => cy.xpath('//*[text()="Monthly Task Tracker"]'),
    Checkingcategory: () => cy.xpath('(//*[text()="510"])[5]'),
    SelectAssetCategories_3: () =>
      cy.xpath(
        '(//div[@class="dx-texteditor-input-container dx-tag-container"])[4]'
      ),
    WeeklyTasktracker: () => cy.xpath('//*[text()="Weekly Task Tracker"]'),
    Equipmentbyarea: () => cy.xpath('//*[text()="Equipment by Area and Type"]'),
    InspectionWithoutDueDates: () =>
      cy.xpath('//*[text()="Inspections Without Due Dates"]'),
    asset: () => cy.get('li[data-item-id="21"]'),
  };

  LoginofAPM() {
    cy.fixture("example.json").then((urldata) => {
      const url = urldata.URL1;

      cy.visit(url);
    });
    cy.viewport(1920, 1080);
    this.elements.Login().click();
    cy.wait(3000);
    this.elements.Teamlogin().click();

    cy.origin(
      "https://login.microsoftonline.com/3cfc49f9-956e-4e4e-a1b6-e03368c2e448/oauth2",
      {
        args: {
          username,
        },
      },
      ({ username }) => {
        cy.wait(2000);
        cy.get('input[type="email"]').type(Cypress.env("username_APM"));
        cy.wait(1000);
        cy.get('input[type="submit"]').click();
      }
    );

    cy.origin(
      "https://login.microsoftonline.com/3cfc49f9-956e-4e4e-a1b6-e03368c2e448/oauth2/",
      {
        args: {
          password,
        },
      },
      ({ password }) => {
        cy.get('input[type="password"]').type(Cypress.env("password_APM"));
        cy.get('input[type="submit"]').click();
      }
    );

    this.elements.TOTP().type(otp);
    this.elements.TOTP_Submit().click();
    this.elements.Finalsubmit().click();
    cy.wait(5000);
  }

  Dashboard_page() {
    cy.wait(1000);

    this.elements.Workmanagement().click();
    this.elements.Assetperformancemanagement().click();
    this.elements.Dashboard_tab().click();

    cy.wait(5000);
    this.elements.SelectedProjects().click();
    cy.wait(5000);

    this.elements.SelectedProjectfromOptions().click({ force: true });
    cy.wait(3000);

    this.elements.Ok_button().click();
    cy.wait(3000);
    this.elements.TotalTask().should("have.text", Asset_total);
    this.elements.SelectAssetCategories_1().click();

    this.elements
      .SelectedProjectfromOptions()
      .each(function ($ele, index, list) {
        if ($ele.text() == selectcategory) {
          cy.wrap($ele).click();
        } else {
        }
      });
    this.elements.TotalAssets().should("have.text", Task_total);
    this.elements.SelectAssetCategories_2().click();
    this.elements
      .SelectedProjectfromOptions()
      .each(function ($ele, index, list) {
        if ($ele.text() == selectcategory) {
          cy.wrap($ele).click();
        } else {
        }
      });
    this.elements
      .MonthlyTasktracker()
      .should("have.text", "Monthly Task Tracker");
    this.elements.Checkingcategory().should("have.text", selectcategory);
    this.elements.SelectAssetCategories_3().click();
    this.elements
      .SelectedProjectfromOptions()
      .each(function ($ele, index, list) {
        if ($ele.text() == selectcategory) {
          cy.wrap($ele).click();
        } else {
        }
      });
    this.elements
      .WeeklyTasktracker()
      .should("have.text", "Weekly Task Tracker");
    this.elements
      .Equipmentbyarea()
      .should("have.text", "Equipment by Area and Type");
    this.elements
      .InspectionWithoutDueDates()
      .should("have.text", "Inspections Without Due Dates");
  }
  Project_pagecreation() {
    cy.fixture("example.json").then((Data) => {
      const project_name = Data.Projectname;
      this.elements.Workmanagement().click();
      this.elements.Assetperformancemanagement().click();
      this.elements.Project().click();
      cy.wait(4000);
      this.elements.create().click({ force: true });
      cy.wait(1000);
      this.elements.box1().type(Teamprojectnumber);
      this.elements.box2().click();
      this.elements.box2a().each(function ($ele, index, list) {
        if ($ele.text() == "12") {
          cy.wrap($ele).click();
        } else {
        }
      });
      this.elements.box3().type(project_name);
      this.elements.box4().type(formatteddate).type("{enter}");
      this.elements.box5().type(formatteddate1).type("{enter}");
      this.elements.Save_button().click();
      cy.window(3000);
      this.elements.toasttitle().should("be.visible");
      this.elements
        .toasttitle()
        .should("have.text", " Project successfully created ");
      this.elements.box6().type(project_name, { delay: 100 });
      cy.wait(1000);
      this.elements.box7().click({ force: true });
      cy.wait(5000);
    });
  }

  Asset_pagecreation() {
    cy.fixture("example.json").then((Data) => {
      const project_name = Data.Projectname;
      const Asset_ID = Data.AssetID;
      const Asset_name = Data.Assestname;
      this.elements.Workmanagement().click();
      this.elements.Assetperformancemanagement().click();
      this.elements.Asset().click();
      this.elements.Create_Asset().click();
      cy.wait(2000);
      this.elements.Asset_box1().click();
      cy.wait(2000);
      this.elements.Asset_box1a().each(function ($ele, index, list) {
        if ($ele.text() == project_name) {
          cy.wrap($ele).click();
        } else {
        }
      });
      this.elements.Asset_box2().type(Asset_ID);
      this.elements.Asset_box3().type(Asset_name);
      this.elements.Asset_box4().click();
      this.elements.Asset_box4a().each(function ($ele, index, list) {
        if ($ele.text() == Asset_category) {
          cy.wrap($ele).click();
        } else {
        }
      });
      this.elements.Asset_box5().type("-90");
      this.elements.Asset_box6().type("-180");
      this.elements.Asset_box10a().click();
      cy.wait(3000);
    });
  }

  Workorder_pagecreation() {
    this.elements.Facility_new().type(Facitlityname);
    this.elements.Asset_box10().click({ force: true }, { timeout: 2000 });
    cy.wait(6000);
  }

  Task_pagecreation() {
    y.fixture("example.json").then((Data) => {
      const project_name = Data.Projectname;
      const Task_type = Data.Tasktype;
      this.elements.Workmanagement().click();
      this.elements.Assetperformancemanagement().click();
      this.elements.WOM().click();
      this.elements.box6().type(project_name);
      cy.wait(6000);
      this.elements.Task_box2().click({ force: true });
      cy.wait(6000);
      this.elements.Task_box3().click({ force: true });
      cy.wait(2000);
      this.elements.Task_create().click();
      cy.wait(3000);
      this.elements.Task_box4().click({ force: true });
      this.elements.Task_box4a().each(function ($ele, index, list) {
        if ($ele.text() == Task_type) {
          cy.wrap($ele).click();
        } else {
        }
      });
      this.elements.Task_box5().click({ force: true });
      this.elements.Task_box4a.each(function ($ele, index, list) {
        if ($ele.text() == Assignees) {
          cy.wrap($ele).click();
        } else {
        }
      });
      this.elements.Task_box11.type(clientworkordernumber, { force: true });
      this.elements.Task_box7.click({ force: true });
      this.elements.Task_box4a.each(function ($ele, index, list) {
        if ($ele.text() == Assignees) {
          cy.wrap($ele).click({ force: true });
        } else {
        }
      });
      this.elements.Task_box8().click();
      this.elements.Task_box4a.each(function ($ele, index, list) {
        if ($ele.text() == Assignees) {
          cy.wrap($ele).click({ force: true });
        } else {
        }
      });
      this.elements.Task_box6.type(Client_Cost_Code);
      this.elements.Task_box9.type(Purchase_Order_AFE);
      this.elements.Task_box10.type(ClientWODescription);
      this.elements.Save_button().click();
      this.elements
        .Task_toastmessage()
        .should("have.text", " Task was added successfully ");
      cy.wait(1000);
      this.elements.WO_details_icon().click({ force: true });
      this.elements.Assetdetails().click();
      this.elements.Edit().click();
      this.elements.Equipmentdescription().type(equipementDes);
      this.elements.Asset_box10().click();
      this.elements
        .toasttitle2()
        .should("have.text", " Asset task data updated successfully ");
    });
  }

  Businessunit_creation() {
    this.elements.Workmanagement({ force: true }).click();
    this.elements.Assetperformancemanagement().click();
    this.elements.Clientmanagement_tab().click();
    this.elements.Add().click();
    this.elements.New_client().click();

    cy.fixture("example.json").then((clientname1) => {
      const Clienname_new = clientname1.addclientname;

      this.elements.clientname().type(Clienname_new);
      this.elements.Save_button().click();
      this.elements.Add().click();
      this.elements.NewBusiness_unit().click();
      this.elements.client_from_option().click();
      this.elements.Task_box4a().each(function ($ele, index, list) {
        if ($ele.text() == Clienname_new) {
          cy.wrap($ele).click({ force: true });
        } else {
        }
      });
      this.elements.users().click();
      this.elements.Task_box4a().each(function ($ele, index, list) {
        if ($ele.text() == Assignees) {
          cy.wrap($ele).click({ force: true });
        } else {
        }
      });
      this.elements.Ok_button().click();
      this.elements.Save_button().click();
      this.elements.Workmanagement({ force: true }).click();
      this.elements.Assetperformancemanagement().click();
      this.elements.Project().click();
      this.elements.client_from_option().click();
      this.elements.Task_box4a().each(function ($ele, index, list) {
        if ($ele.text() == Clienname_new) {
          cy.wrap($ele).should("have.text", Clienname_new);
        } else {
        }
      });
    });
  }

  Leakreporting_pagecreation() {
    cy.fixture("example.json").then((Data) => {
      const Area_1 = Data.Area;
      this.elements.Workmanagement({ force: true }).click();
      this.elements.Assetperformancemanagement().click();
      this.elements.Leak_report().click();
      this.elements.create().click();
      cy.wait(1500);
      this.elements.Area_LK().type(Area_1, { delay: 1000 });
      cy.wait(1500);
      this.elements.Lease_LK().type(Lease);
      cy.wait(1500);
      this.elements.EquipementID_LK().type(Equipment);
      cy.wait(1500);
      this.elements.Jobdescription_LK().type(desc);
      this.elements.Activeradio_LK().click();
      this.elements.Save_button().click();
      cy.wait(2000);
      this.elements
        .Totaltitile_LR()
        .should("have.text", " Leak Report successfully created ");
      this.elements.box6().type(Area);
      cy.wait(5000);
      this.elements.Select_Area().click();
      this.elements.Work_details_LR().click();
      this.elements.Edit_LR().click();
      this.elements.client_value_LR().type(value);
      this.elements.client_comment_LR().type(comment);
      this.elements.Task_Save().click();
      this.elements
        .toasttitle_LR2()
        .should("have.text", " Successfully updated leak report work details ");
      cy.wait(1000);
      this.elements.Leakrepot_Tab().click();
      cy.wait(2000);
      this.elements.Edit_LR2().click({ force: true });
      cy.wait(1000);
      this.elements.EquipmentID_value().type(value);
      this.elements.EquipmentID_comment().type(comment);
      this.elements.LR_Save().click();
      this.elements
        .toasttitle_LR3()
        .should("have.text", " Successfully updated leak report information ");
    });
  }
}
module.exports = new APM();
