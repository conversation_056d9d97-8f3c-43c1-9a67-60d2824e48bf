<dx-popup [showTitle]="true"
          [title]="title$ | async">
    <app-asset-creation *ngIf="(creationStep$ | async) === 'asset'">
    </app-asset-creation>
    <app-work-order-creation *ngIf="(creationStep$ | async) === 'workOrder'"
                             [assetId]="createdAssetId"
                             [projectId]="assetProjectId">
    </app-work-order-creation>
    <app-tasks-tab *ngIf="(creationStep$ | async) === 'tasks'"
                   [workOrder]="creationWorkOrder$ | async"
                   [availableUsers]="users$ | async | userEmails"
                   (newTask)="newTask($event)"
                   (taskUpdate)="taskUpdate($event)"></app-tasks-tab>

    <dxi-toolbar-item *ngIf="(creationStep$ | async) === 'asset'"
                      widget="dxButton"
                      location="after"
                      [options]="{text: 'Save and Create Work Order', stylingMode: 'text', type: 'default', onClick: onSaveAndCreateWorkOrderClicked, disabled: saving}"
                      [toolbar]="'bottom'"></dxi-toolbar-item>
    <dxi-toolbar-item *ngIf="(creationStep$ | async) === 'workOrder'"
                      widget="dxButton"
                      location="after"
                      [options]="{text: 'Save and Create Tasks', stylingMode: 'text', type: 'default', onClick: onSaveAndCreateTasksClicked, disabled: saving}"
                      [toolbar]="'bottom'"></dxi-toolbar-item>
    <dxi-toolbar-item *ngIf="(creationStep$ | async) !== 'tasks'"
                      widget="dxButton"
                      location="after"
                      [options]="{text: 'Save', stylingMode: 'text', type: 'default', onClick: onSaveCreationClicked, disabled: saving}"
                      [toolbar]="'bottom'">
    </dxi-toolbar-item>
    <dxi-toolbar-item widget="dxButton"
                      location="after"
                      [options]="{text: (creationStep$ | async) === 'tasks' ? 'Close' : 'Cancel', stylingMode: 'text', type: 'default', onClick: onCancelCreationClicked, disabled: saving}"
                      [toolbar]="'bottom'">
    </dxi-toolbar-item>
</dx-popup>
