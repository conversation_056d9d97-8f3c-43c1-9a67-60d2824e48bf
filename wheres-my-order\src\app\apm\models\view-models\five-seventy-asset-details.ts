import { Photo } from '../data';
import { AssetDetailsPhoto } from './asset-details-photo';

export interface FiveSeventyAssetDetails {
    workOrderId: string;
    projectId: string;
    assetCategory: string;
    identificationName: string;
    identificationNumber: string;
    assetType: string;
    assetDescription: string;
    productHandled: string;
    lastKnownInspectionDate: string;
    location: string;
    lineFromEquipmentId: string;
    startGisLocationLat: number;
    startGisLocationLong: number;
    lineToEquipmentId: string;
    endGisLocationLat: number;
    endGisLocationLong: number;
    backPhotos: Photo[];
    frontPhotos: Photo[];
    leftPhotos: Photo[];
    rightPhotos: Photo[];
    designCode: string;
    designYear: string;
    designAddendum: string;
    inspectionCode: string;
    ndeExaminationMethods: string;
    inspectionYear: string;
    inspectionAddendum: string;
    pipeClass: string;
    manufacturerName: string;
    manufacturerDate: string;
    installationDate: string;
    inServiceDate: string;
    pIDNumber: string;
    designDrawingNumber: string;
    lowestFlangeRating: string;
    typeOfConstruction: string[];
    threadedConnections: string;
    pipeSize: string[];
    pipeSchedule: string[];
    serviceProductContents: string;
    specificGravity: number;
    operatingTemperature: number;
    designMAWP: number;
    designTemperature: number;
    operatingPressure: number;
    pRVSetPressure: number;
    operationStatus: string;
    pipes: PipeComponent[];
    allPhotos?: AssetDetailsPhoto[];
}
export interface PipeComponent {
    databaseId: string;
    lineNumber: string;
    materialSpecAndGrade: string;
    allowableStressAtTemperature: number;
    nominalThickness: number;
    corrosionAllowance: number;
    jointEfficiency: number;
    pipeSpecNumber: string;
}
