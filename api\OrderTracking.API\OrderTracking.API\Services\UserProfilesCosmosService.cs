using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using OrderTracking.API.Models;
using OrderTracking.API.Repositories;

namespace OrderTracking.API.Services
{
    /// <summary>
    /// Azure Cosmos DB service for UserProfile operations (migrated from Firebase)
    /// </summary>
    public class UserProfilesCosmosService : UserProfileCosmosRepository, IUserProfilesService
    {
        private readonly IAuthHistoryService _authHistory;
        private readonly IHttpContextAccessor _httpContextAccessor;

        #region Constructors

        public UserProfilesCosmosService(IContainerFactory containerFactory, IServiceProvider serviceProvider, IConfiguration configuration) 
            : base(containerFactory, configuration)
        {
            _authHistory = (IAuthHistoryService) serviceProvider.GetService(typeof(IAuthHistoryService));
            _httpContextAccessor = (IHttpContextAccessor) serviceProvider.GetService(typeof(IHttpContextAccessor));
        }

        #endregion

        #region Interface Implementation

        public async Task<UserProfile> UpdateItemIncludingIdAsync(UserProfile userProfile, string originalId)
        {
            try
            {
                var updatedProfile = await UpdateAsync(userProfile, originalId);
                if (updatedProfile != null) await CreateChangeEventAsync(null, updatedProfile);
                return updatedProfile;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task<UserProfile> AddRoleToUser(string userId, string roleId)
        {
            try
            {
                var user = await GetAsync(userId);
                if (user == null) return null;

                if (user.Roles == null) user.Roles = new List<string>();
                if (!user.Roles.Contains(roleId))
                {
                    user.Roles.Add(roleId);
                    user = await UpdateAsync(user);
                    if (user != null) await CreateChangeEventAsync(null, user);
                }

                return user;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task<UserProfile> RemoveRoleFromUser(string userId, string roleId)
        {
            try
            {
                var user = await GetAsync(userId);
                if (user?.Roles == null) return user;

                if (user.Roles.Contains(roleId))
                {
                    user.Roles.Remove(roleId);
                    user = await UpdateAsync(user);
                    if (user != null) await CreateChangeEventAsync(null, user);
                }

                return user;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public new async Task<UserProfile> AddAsync(UserProfile userProfile)
        {
            try
            {
                userProfile = VerifyProperties(userProfile);
                var newProfile = await base.AddAsync(userProfile);
                if (newProfile != null) await CreateChangeEventAsync(null, newProfile);
                return newProfile;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public new async Task<UserProfile> UpdateAsync(UserProfile userProfile)
        {
            try
            {
                var originalProfile = await GetAsync(userProfile.Id);
                userProfile = VerifyProperties(userProfile);
                var updatedProfile = await base.UpdateAsync(userProfile);
                if (updatedProfile != null) await CreateChangeEventAsync(originalProfile, updatedProfile);
                return updatedProfile;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public new async Task RemoveAsync(string id)
        {
            try
            {
                var originalProfile = await GetAsync(id);
                await base.RemoveAsync(id);
                if (originalProfile != null) await CreateChangeEventAsync(originalProfile, null);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task DeleteUsersAsync(string[] ids)
        {
            try
            {
                foreach (var id in ids)
                {
                    await RemoveAsync(id);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        #endregion

        #region Private Methods

        private UserProfile VerifyProperties(UserProfile userProfile)
        {
            if (userProfile == null) return null;

            // Ensure required properties are set
            if (string.IsNullOrEmpty(userProfile.Id))
            {
                userProfile.Id = userProfile.Email?.ToLowerInvariant();
            }

            // UserProfile doesn't have CreatedDate/ModifiedDate properties
            // These would be handled by Azure Cosmos DB timestamps if needed

            return userProfile;
        }

        private async Task CreateChangeEventAsync(UserProfile originalProfile, UserProfile newProfile)
        {
            try
            {
                if (_authHistory == null || _httpContextAccessor?.HttpContext == null) return;

                var userEmail = _httpContextAccessor.HttpContext.User?.Identity?.Name;
                if (string.IsNullOrEmpty(userEmail)) return;

                var changeEvent = new ChangeEvent
                {
                    Id = Guid.NewGuid().ToString(),
                    Old = originalProfile,
                    New = newProfile,
                    CreatedAt = DateTime.UtcNow,
                    User = await GetCurrentUserAsync()
                };

                await _authHistory.AddItemAsync(changeEvent);
            }
            catch (Exception e)
            {
                // Log but don't throw - change events are not critical
                Console.WriteLine($"Failed to create change event: {e.Message}");
            }
        }

        public static RoleError CanRolesBeAdded(UserProfile userProfile, string[] roleIds, string[] removedRoleIds)
        {

            // this will be true if user is attempting to add AIMaaS:Demo role with any other AIMaaS role
            if (roleIds.Select(item => item.ToLower()).Contains("aimaas:demo") &&
                ((roleIds.Select(item => item.ToLower()).Contains("aimaas:admin")) ||
                roleIds.Select(item => item.ToLower()).Contains("aimaas:edit") ||
                roleIds.Select(item => item.ToLower()).Contains("aimaas:view")))
            {
                return new RoleError(){Error = true, Message = "Cannot add AIMaaS:Demo alongside other AIMaaS Roles" };
            }

            foreach (var roleId in roleIds)
            {

                // If trying to add AIMaaS:Demo, check to make sure that other AIMaaS Roles are not already assigned to user
                // Rest of code in this block is building a proper error message
                if (string.Equals(roleId.ToLower(), "aimaas:demo"))
                {
                    List<string> blockingRoles = new List<string>();
                    if (userProfile.Roles.Select(role => role.ToLower()).Contains("aimaas:admin") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:admin"))
                    {
                        blockingRoles.Add("AIMaaS:Admin");
                    }

                    if (userProfile.Roles.Select(role => role.ToLower()).Contains("aimaas:edit") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:edit"))
                    {
                        blockingRoles.Add("AIMaaS:Edit");
                    }

                    if (userProfile.Roles.Select(role => role.ToLower()).Contains("aimaas:view") &&!removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:view"))
                    {
                        blockingRoles.Add("AIMaaS:View");
                    }

                    if (blockingRoles.Count > 0)
                    {
                        string message = "AIMaaS:Demo could not be added because of existing role(s): ";
                        foreach (var role in blockingRoles)
                        {
                            if (role == blockingRoles.Last())
                            {
                                message += "and " + role + " ";
                            }
                            else
                            {
                                message += role + ",";
                            }
                        }

                        return new RoleError() {Error = true, Message = message};
                    }

                }

                // If adding AIMaaS:Admin, make sure we don't already have AIMaaS:Demo, if we do return error
                if (string.Equals(roleId.ToLower(), "aimaas:admin"))
                {
                    if (userProfile.Roles.Select(item => item.ToLower()).Contains("aimaas:demo") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:demo"))
                    {
                        return new RoleError()
                        {
                            Error = true,
                            Message =
                                "AIMaaS:Admin role could not be added because user already has AIMaaS:Demo role"
                        };
                    }
                }

                // If adding AIMaaS:Edit, make sure we don't already have AIMaaS:Demo, if we do return error
                if (string.Equals(roleId.ToLower(), "aimaas:edit"))
                {
                    if (userProfile.Roles.Select(item => item.ToLower()).Contains("aimaas:demo") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:demo"))
                    {
                        return new RoleError()
                        {
                            Error = true,
                            Message =
                                "AIMaaS:Edit role could not be added because user already has AIMaaS:Demo role"
                        };
                    }
                }

                // If adding AIMaaS:View, make sure we don't already have AIMaaS:Demo, if we do return error
                if (string.Equals(roleId.ToLower(), "aimaas:view"))
                {
                    if (userProfile.Roles.Select(item => item.ToLower()).Contains("aimaas:demo") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:demo"))
                    {
                        return new RoleError()
                        {
                            Error = true,
                            Message =
                                "AIMaaS:View role could not be added because user already has AIMaaS:Demo role"
                        };
                    }
                }
            }

            return new RoleError(){Error = false};
        }

        #endregion

        #region Private Methods

        private async Task<UserProfile> GetCurrentUserAsync() => await GetAsync(_httpContextAccessor.HttpContext.User.Identity.Name.ToLower());

        #endregion
    }
}
