import { Pipe, PipeTransform } from '@angular/core';
import { LeakReportPhotoEntries, PhotosEntity } from '../models';

@Pipe({
    name: 'leakReportingPhotoModalTitle'
})
export class LeakReportingPhotoModalTitlePipe implements PipeTransform {
    transform(photo: PhotosEntity, group: LeakReportPhotoEntries): string {
        if (!photo) return '';
        if (!group) return '';
        const index = group.photos.photos.indexOf(photo);
        const count = group.photos.photos.length;
        const title = group.description.currentValue ?? 'No description';
        return `${title} (Photo ${index + 1} of ${count})`;
    }
}
