import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DxDataGridComponent, DxSelectBoxComponent } from 'devextreme-angular';
import CustomStore from 'devextreme/data/custom_store';
import DataSource from 'devextreme/data/data_source';
import { ColumnButtonClickEvent, RowUpdatedEvent } from 'devextreme/ui/data_grid';
import { ToastrService } from 'ngx-toastr';
import { lastValueFrom } from 'rxjs';
import { APMStatus, APMTaskType, availableStatusesToChangeTo, TaskUpdate, TaskVM } from '../../models';
import { ApmService } from '../../services';

@Component({
  selector: 'app-manage-tasks',
  templateUrl: './manage-tasks.component.html',
  styleUrls: ['./manage-tasks.component.scss']
})
export class ManageTasksComponent implements OnInit {

  constructor(
    public _apm: ApmService, 
    private readonly _toasts: ToastrService,  
    private readonly _router: Router) { }

  // Views

  @ViewChild(DxSelectBoxComponent) assetSelectBox: DxSelectBoxComponent;
  @ViewChild(DxDataGridComponent) datagridView: DxDataGridComponent;

  // Collections

  assetTasks: Array<TaskVM> = []
  allTasks = []
  availableStatus = []
  users$ = this._apm.getUsers();

  // Other variables

  allowEditing$ = this._apm.allowEditing$;

  assetsDataSource = new DataSource({
    store: new CustomStore({
        key: 'id',
        byKey: (key: string) => lastValueFrom(this._apm.getAsset(key)),
        load: (options) => lastValueFrom(this._apm.loadAllAssets(options)),
        update: (key: string, values: any) =>
            lastValueFrom(this._apm.updateAsset(values)),
        useDefaultSearch: true
      })
  });

  ngOnInit(): void {
    var tDs = new CustomStore({
      key: 'id',
      byKey: (key: string) => this._apm.getTask(key).toPromise(),
      load: (options) => this._apm.loadTasks(options).toPromise()
  });  
    tDs.load().then(data => {
      this.allTasks = data["data"]
      if (this.assetSelectBox.value != null) {
        this.searchAsset()
      }
      this.datagridView.noDataText = "No tasks for the selected asset"
    })
  }

  searchAsset(){
    if (this.assetSelectBox.value == null) {
      this.assetTasks = []
      return
    }

    this.assetTasks = this.allTasks.filter(t => t["equipmentId"] == this.assetSelectBox.value)
    if(this.assetTasks.length == 0) {
      this.showNoTasks()
    }
  }

  showNoTasks(){
    this._toasts.info(
      'The asset ' +  this.assetSelectBox.value + ' does not contain any tasks',
      'No tasks',)
  }

  onValueChanged(e) {
    this.searchAsset()
  }

  updateValues(e) {
    this.availableStatus = []
    var workOrderId = e.row.data.workOrderId
    var workOrderRequest = lastValueFrom(this._apm.getWorkOrder(workOrderId))
    workOrderRequest.then(data => {
      this.availableStatus = availableStatusesToChangeTo(
        data,
        e.row.data.id,
        e.row.data.taskType as APMTaskType,
        e.row.data.status as APMStatus
      );
    }).catch(e=> {
      console.log(e)
    })
  }

  customizeText(cellInfo) {
    return cellInfo.value.toString().replaceAll(",", ", ")
  }

  tasksResultsClicked = (e: ColumnButtonClickEvent) => {
    this._router.navigate(['apm/task-details', e.row.data.workOrderId], {
        queryParams: { task_id: e.row.data.id }
    })
  }

  onRowUpdated(e: RowUpdatedEvent) {

    var update = {
      projectId: e.data?.projectId,
      workOrderId: e.data?.workOrderId,
      status: e.data?.status,
      taskAssignees: e.data?.assignedUsers,
      supervisor: e.data?.supervisor,
      databaseId: e.data?.id,
    } as TaskUpdate;

    this._apm.updateWorkOrderHeader(update).subscribe(
      data => {
        this._toasts.success(
          'Task was updated successfully',
          'Saved changes',
        )
      }
    )
  }
}