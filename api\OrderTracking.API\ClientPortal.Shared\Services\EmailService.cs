﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.Extensions.Options;
using OrderTracking.API.Models;
using SendGrid;
using SendGrid.Helpers.Mail;

namespace ClientPortal.Shared.Services
{
    public class EmailAttachment
    {
        public string FileName { get; set; }
        public Stream Stream { get; set; }
    }

    /// <summary>
    ///     Service for sending emails
    ///     For attachments, see:
    ///     https://docs.microsoft.com/en-us/azure/sendgrid-dotnet-how-to-send-email#how-to-add-an-attachment
    ///     For footer, see: https://docs.microsoft.com/en-us/azure/sendgrid-dotnet-how-to-send-email#footer-settings
    /// </summary>
    public class EmailService : IEmailService
    {
        private readonly SendGridClient _client;

        /// <summary>
        ///     Constructor (duh)
        /// </summary>
        /// <param name="options"></param>
        public EmailService(IOptions<Models.SendGrid> options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            _client = new SendGridClient(options.Value.APIKey);
        }

        /// <summary>
        ///     Send an email to 1 or more recipients with Priority set to urgent and Importance set to high.
        /// </summary>
        /// <param name="recipients"></param>
        /// <param name="subject"></param>
        /// <param name="htmlContent"></param>
        /// <returns></returns>
        public async Task<Response> SendUrgentEmail(IReadOnlyCollection<IEmailRecipient> recipients, string subject,
            string htmlContent)
        {
            var msg = CreateEmail(recipients, subject, htmlContent);
            msg.AddHeaders(new Dictionary<string, string>
            {
                {"X-Priority", "1"},
                {"Priority", "urgent"},
                {"Importance", "high"}
            });
            var response = await _client.SendEmailAsync(msg);
            return response;
        }

        /// <summary>
        ///     Send an email to 1 or more recipients
        /// </summary>
        /// <param name="emailData"></param>
        public async Task<Response> SendEmail(EmailData emailData)
        {
            var msg = CreateEmail(emailData.Recipients, emailData.Subject, emailData.HtmlContent);
            if (emailData.Attachments != null && emailData.Attachments.Count > 0)
                foreach (var attachment in emailData.Attachments)
                {
                    if (attachment.Stream.CanSeek)
                    {
                        // set steam to beginning of file so it is actually a openable file
                        attachment.Stream.Seek(0, SeekOrigin.Begin);
                    }
                    // If `attachment.Stream.CanRead` returns false, SendGrid silently ignores the attachment.
                    await msg.AddAttachmentAsync(attachment.FileName, attachment.Stream);
                }

            var response = await _client.SendEmailAsync(msg);
            return response;
        }

        private static SendGridMessage CreateEmail(IReadOnlyCollection<IEmailRecipient> recipients, string subject,
            string htmlContent)
        {
            if (recipients == null) throw new ArgumentNullException(nameof(recipients));
            if (recipients.Count < 1)
                throw new ArgumentException("Email must have at least 1 recipient", nameof(recipients));

            var msg = new SendGridMessage();

            msg.SetFrom(new EmailAddress("<EMAIL>", "TEAM OneInsight"));

            var recipientEmails = recipients.Select(recipient =>
                new EmailAddress(recipient.Id, $"{recipient.GivenName} {recipient.Surname}"));
            msg.AddTos(recipientEmails.ToList());

            msg.SetSubject(subject);

            msg.AddContent(MimeType.Html, htmlContent);

            return msg;
        }
    }
}