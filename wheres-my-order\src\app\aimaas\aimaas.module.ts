import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { DxChartModule } from 'devextreme-angular/ui/chart';
import { DxRangeSelectorModule } from 'devextreme-angular/ui/range-selector';
import { SharedModule } from '../shared';
import { AIMaaSRoutingModule } from './aimaas-routing.module';
import {
    AssetDetailsComponent,
    AttachmentsComponent,
    CalculationsViewComponent,
    EquipmentDashboardComponent,
    InspectionScheduleComponent,
    InspectionsDashboardComponent
} from './components';
import { AssetComponentDetailsComponent } from './components/asset-component-details/asset-component-details.component';
import { AssetGeneralinformationComponent } from './components/asset-generalinformation/asset-generalinformation.component';
import { CorrosionAnalysisComponent } from './components/corrosion-analysis/corrosion-analysis.component';
import { GeneralAnalysisComponent } from './components/general-analysis/general-analysis.component';
import { GeneralInspectionDetailsComponent } from './components/general-inspection-details/general-inspection-details.component';
import { InspectionAnomaliesComponent } from './components/inspection-anomalies/inspection-anomalies.component';
import { InspectionAttachmentsComponent } from './components/inspection-attachments/inspection-attachments.component';
//import { InspectionScheduleComponent } from './components/inspection-schedule/inspection-schedule.component';
import {
    DxDateBoxModule,
    DxListModule,
    DxTagBoxModule
} from 'devextreme-angular';
import { ClientDataSubmissionComponent } from './components/client-data-submission/client-data-submission.component';
import { DocumentUploadComponent } from './components/document-upload/document-upload.component';
import { RecommendationsDashboardComponent } from './components/recommendations-dashboard/recommendations-dashboard.component';
import { RequestServiceComponent } from './components/request-service/request-service.component';
import { SubmissionsComponent } from './components/submissions/submissions.component';
import { UpdateAnomalyComponent } from './components/update-anomaly/update-anomaly.component';
import {
    AIMaaSComponent,
    FixedEquipmentDrillDownComponent,
    FixedEquipmentInspectionDrilldownComponent
} from './pages';
import {
    CalcDisplayValuePipe,
    InspectionCountPipe,
    SiteLabelPipe
} from './pipes';
import { AnomalyDrillDownComponent } from './pages/anomaly-drill-down/anomaly-drill-down.component';

@NgModule({
    declarations: [
        AIMaaSComponent,
        FixedEquipmentDrillDownComponent,
        FixedEquipmentInspectionDrilldownComponent,
        InspectionCountPipe,
        AssetDetailsComponent,
        AttachmentsComponent,
        CalculationsViewComponent,
        CalcDisplayValuePipe,
        SiteLabelPipe,
        InspectionsDashboardComponent,
        EquipmentDashboardComponent,
        AssetGeneralinformationComponent,
        AssetComponentDetailsComponent,
        InspectionScheduleComponent,
        GeneralInspectionDetailsComponent,
        InspectionAnomaliesComponent,
        CorrosionAnalysisComponent,
        InspectionAttachmentsComponent,
        GeneralAnalysisComponent,
        RecommendationsDashboardComponent,
        SubmissionsComponent,
        UpdateAnomalyComponent,
        DocumentUploadComponent,
        RequestServiceComponent,
        ClientDataSubmissionComponent,
        AnomalyDrillDownComponent
    ],
    imports: [
        CommonModule,
        AIMaaSRoutingModule,
        DxChartModule,
        DxRangeSelectorModule,
        SharedModule,
        DxDateBoxModule,
        DxTagBoxModule,
        DxListModule
    ],
    providers: [SiteLabelPipe]
})
export class AIMaaSModule {}
