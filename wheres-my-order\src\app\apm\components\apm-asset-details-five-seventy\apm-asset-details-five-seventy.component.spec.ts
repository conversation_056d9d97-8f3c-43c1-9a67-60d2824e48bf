import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
    DxFormModule,
    DxGalleryModule,
    DxLoadIndicatorModule,
    DxPopupModule
} from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { PhotoSasPipe } from '../../pipes';
import { ApmService } from '../../services';
import { ApmAssetDetailsFiveSeventyComponent } from './apm-asset-details-five-seventy.component';

describe('ApmAssetDetailsFiveSeventyComponent', () => {
    let component: ApmAssetDetailsFiveSeventyComponent;
    let fixture: ComponentFixture<ApmAssetDetailsFiveSeventyComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxFormModule,
                DxPopupModule,
                DxGalleryModule,
                DxLoadIndicatorModule,
                HttpClientTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [ApmAssetDetailsFiveSeventyComponent],
            providers: [{ provide: ApmService, useValue: {} }, PhotoSasPipe]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ApmAssetDetailsFiveSeventyComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
