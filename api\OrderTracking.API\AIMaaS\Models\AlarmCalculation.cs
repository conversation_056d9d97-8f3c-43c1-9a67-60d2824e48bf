﻿using System;

// <PERSON><PERSON><PERSON><PERSON> disable InconsistentNaming (property names map to database columns, do not change unless column names change)
// We don't really know what some of these columns mean or represent.  If more work is to be done connecting CredoSoft, please attempt to provide more insight 
// <PERSON><PERSON><PERSON><PERSON> disable UnusedMember.Global (properties exist for serialization purposes.  Not used in source at this time)
// Re<PERSON><PERSON>per disable IdentifierTy<PERSON> (properties map to database columns, and the naming convention is not in our control)
#pragma warning disable 1591 

namespace AIMaaS.Models
{
#pragma warning disable CA1707 // Identifiers should not contain underscores
    /// <summary>
    ///     Result record for alarm calculations in CredoSoft
    /// </summary>
    // ReSharper disable once ClassNeverInstantiated.Global
    public class AlarmCalculation
    {
        public long OBJID { get; set; }
        public string OBJDESC_ { get; set; }
        public string OBJTYPECODE_ { get; set; }
        public string OBJSERVICE { get; set; }
        public string TM_PV_SPEC_GRAVITY_ { get; set; }
        public string EQDESIGNCODE_ { get; set; }
        public DateTime? EQMANUFDATE { get; set; }
        public DateTime? OBJCOMMISSION { get; set; }
        public string ALY_OM_VERT_HORIZ { get; set; }
        public string TM_PV_CONFIG_GEOM_ { get; set; }
        public decimal DIM_DIAMOUTSIDE_ { get; set; }
        public decimal DIM_DIAMINSIDE { get; set; }
        public string DIM_DIAMUNITS { get; set; }
        public decimal DIM_RADIUSOUTSIDE { get; set; }
        public decimal DIM_RADIUSINSIDE { get; set; }
        public decimal DIM_RADIUS_CROWN_INSIDE { get; set; }
        public decimal DIM_RADIUS_CROWN_OUTSIDE { get; set; }
        public string DIM_RADIUS_UNIT { get; set; }
        public decimal DIM_CONE_APEX_ANGLE { get; set; }
        public decimal DIM_TALARM_K { get; set; }
        public decimal DIM_TALARM_L { get; set; }
        public decimal DIM_TALARM_M { get; set; }
        public decimal DIM_LENGTH_ { get; set; }
        public string DIM_LENGTHUNIT_ { get; set; }
        public decimal DIM_TANK_FILL_HEIGHT_M { get; set; }
        public string MATSPEC_ { get; set; }
        public string MATGRADE_ { get; set; }
        public string RMAT_DAMAGE_GROUP_ { get; set; }
        public decimal DIM_JOINTQUAL { get; set; }
        public decimal DIM_TNOMINAL { get; set; }
        public string DIM_TNOMINAL_UNIT { get; set; }
        public decimal DIM_TCORRALLOW { get; set; }
        public string DIM_TCORRALLOW_UNIT { get; set; }
        public string PRESSUNITS_ { get; set; }
        public decimal PRESSDESMAX_ { get; set; }
        public decimal PRSSRELIEF_ { get; set; }
        public decimal PRESSOPERNORM_ { get; set; }
        public string TEMPUNITS_ { get; set; }
        public decimal TEMPDESMAX_ { get; set; }
        public decimal TEMPOPERNORM_ { get; set; }
        public string DIM_TALARM1_CALCCODE { get; set; }
        public decimal DIM_TALARM1_VAL { get; set; }
        public string DIM_TALARM2_CALCCODE { get; set; }
        public decimal DIM_TALARM2_VAL { get; set; }
        public string DIM_TALARM3_CALCCODE { get; set; }
        public decimal DIM_TALARM3_VAL { get; set; }
        public string DIM_TALARM4_CALCCODE { get; set; }
        public decimal DIM_TALARM4_VAL { get; set; }
        public string DIM_TALARM5_CALCCODE { get; set; }
        public decimal DIM_TALARM5_VAL { get; set; }
        public string DIM_TALARM_NOTE01 { get; set; }
        public decimal DIM_TALARM1_FACTORX { get; set; }
        public decimal DIM_TALARM2_FACTORX { get; set; }
        public decimal DIM_TALARM3_FACTORX { get; set; }
        public decimal DIM_TALARM5_FACTORX { get; set; }
        public decimal DIM_MAWT { get; set; }
        public string DIM_MAWT_UNIT { get; set; }
        public decimal DIM_MAWT_STRUCTURAL { get; set; }
        public string DIM_MAWT_STRUCTURAL_UNIT { get; set; }
        public decimal DIM_TTOLERANCEPERCENT { get; set; }
        public decimal DIM_DESIGNFACTOR { get; set; }
        public decimal DIM_TALARM_Y { get; set; }
        public long RSITE_RID { get; set; }
    }
#pragma warning restore CA1707 // Identifiers should not contain underscores
}