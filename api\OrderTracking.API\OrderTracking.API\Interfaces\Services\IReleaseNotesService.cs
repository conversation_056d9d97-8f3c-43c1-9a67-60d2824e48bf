using System.Collections.Generic;
using System.Threading.Tasks;
using OrderTracking.API.Models;

namespace OrderTracking.API.Interfaces.Services
{
    /// <summary>
    ///     Interface for CRUD actions on release notes container in CosmosDB.
    /// </summary>
    public interface IReleaseNotesService
    {
        /// <summary>
        ///     Get items ordered by createdAt, desc
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<ReleaseNotes>> GetItemsAsync();

        /// <summary>
        ///     Create a release notes document in the database.
        /// </summary>
        /// <param name="releaseNotes"></param>
        /// <returns></returns>
        Task<ReleaseNotes> AddItemAsync(ReleaseNotes releaseNotes);

        /// <summary>
        ///     Delete a release notes document from the database.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task DeleteItemAsync(string id);

        /// <summary>
        ///     Update an existing release notes document in the database.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="releaseNotes"></param>
        /// <returns></returns>
        Task UpdateItemAsync(string id, ReleaseNotes releaseNotes);

        /// <summary>
        ///     Get a single release notes document from the database.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<ReleaseNotes> GetItemAsync(string id);

        /// <summary>
        ///     Delete multiple release notes items from the database at once.
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task DeleteItemsAsync(string[] ids);
    }
}