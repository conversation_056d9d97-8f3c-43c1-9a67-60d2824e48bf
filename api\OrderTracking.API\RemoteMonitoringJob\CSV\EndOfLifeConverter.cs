﻿using System;
using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.TypeConversion;

namespace RemoteMonitoringJob.CSV
{
    // ReSharper disable once ClassNeverInstantiated.Global
    public class FloatingPointNumberConverter : DoubleConverter
    {
        public override object ConvertFromString(string text, IReaderRow row, MemberMapData memberMapData)
        {
            try
            {
                return text.ToLower() == "nan" ? null : base.ConvertFromString(text, row, memberMapData);
            }
            catch (Exception e) when (e is FormatException || e is ReaderException || e is TypeConverterException)
            {
                return null;
            }
        }
    }

    // ReSharper disable once ClassNeverInstantiated.Global
    public class EndOfLifeConverter : DateTimeConverter
    {
        public override object ConvertFromString(string text, IReaderRow row, MemberMapData memberMapData)
        {
            try
            {
                return base.ConvertFromString(text, row, memberMapData);
            }
            catch (Exception e) when (e is FormatException || e is ReaderException || e is TypeConverterException)
            {
                return null;
            }
        }
    }
}