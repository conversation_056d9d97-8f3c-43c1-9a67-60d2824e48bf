﻿using System;
using System.Reflection;

namespace ClientPortal.Shared.Models
{
    public static class FirestoreModelExtensions
    {
        /// <summary>
        /// Generates a document reference path for a particular property of a C# class.
        /// </summary>
        /// <param name="this">The instance of the class to act on.</param>
        /// <param name="collectionName">The name of the Firestore collection.</param>
        /// <param name="propertyName">The name of the property to use as a document ID.</param>
        /// <typeparam name="TEntity">The type of the entity.</typeparam>
        /// <returns>A string representing the path to a Firestore document.</returns>
        public static string GetDocumentPath<TEntity>(this TEntity @this, string collectionName, string propertyName)
        {
            // Directly retrieve the property value to avoid ambiguity.
            Type type = @this.GetType();
            PropertyInfo property = type.GetProperty(propertyName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static) ?? throw new ArgumentException($"Property '{propertyName}' not found on {@this.GetType().FullName}.");
            object propertyValue = property.GetValue(@this, null);
            return propertyValue == null
                ? throw new ArgumentException("Property value cannot be null.", nameof(propertyName))
                : $"{collectionName}/{propertyValue}";
        }
    }
}
