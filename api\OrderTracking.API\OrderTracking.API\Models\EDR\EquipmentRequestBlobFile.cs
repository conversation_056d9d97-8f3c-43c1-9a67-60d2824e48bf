﻿using ClientPortal.Shared.Models;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.EDR
{
    /// <summary>
    ///     Object representing information about files associated with equipment requests
    /// </summary>
    public class EquipmentRequestBlobFile : BlobFile
    {
        /// <summary>
        ///     Unique identifier
        /// </summary>
        [JsonProperty(PropertyName = "id")] public string Id { get; set; }

        /// <summary>
        ///     Unique identifier of the equipment request this file is associated with.
        /// </summary>
        [JsonProperty(PropertyName = "equipmentRequestId")]
        public string EquipmentRequestId { get; set; }

        /// <summary>
        ///     Whether the file consists of PJA information
        /// </summary>
        [JsonProperty(PropertyName = "hasPJA")]
        public bool HasPJA { get; set; }

        /// <summary>
        ///     Whether the file consists of a data sheet
        /// </summary>
        [JsonProperty(PropertyName = "hasDataSheet")]
        public bool HasDataSheet { get; set; }

        /// <summary>
        ///     Whether the file consists of shipping info
        /// </summary>
        [JsonProperty(PropertyName = "hasShippingInfo")]
        public bool HasShippingInfo { get; set; }

        /// <summary>
        ///     Whether the file consists of photos
        /// </summary>
        [JsonProperty(PropertyName = "hasPhoto")]
        public bool HasPhoto { get; set; }

        /// <summary>
        ///     Whether the file consists of information other than PJA, a data sheet, and/or shipping info
        /// </summary>
        [JsonProperty(PropertyName = "hasOther")]
        public bool HasOther { get; set; }
    }
}