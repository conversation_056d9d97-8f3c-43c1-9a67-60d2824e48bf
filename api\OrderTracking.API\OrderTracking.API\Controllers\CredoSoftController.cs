﻿//using System.Linq;
//using System.Threading.Tasks;
////using AIMaaS.Models;
////using AIMaaS.Services;
//using ClientPortal.Shared.Extensions;
//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.Extensions.Options;
//using OrderTracking.API.Services;

//namespace OrderTracking.API.Controllers
//{
//    /// <summary>
//    ///     Controller to handle requests for CredoSoft gathered data.
//    /// </summary>
//    [Route("api/[controller]")]
//    [ApiController]
//    [Authorize]
//    [Authorize("CREDO")]
//    public class CredoSoftController : ControllerBase
//    {
//        private readonly IOptions<CredoSoftData> _options;
//        private readonly ICredoSoftService _service;
//        private readonly IUserProfilesService _userProfiles;

//        /// <summary>
//        ///     Constructor.
//        /// </summary>
//        /// <param name="service"></param>
//        /// <param name="userProfiles"></param>
//        /// <param name="options"></param>
//        public CredoSoftController(
//            ICredoSoftService service,
//            IUserProfilesService userProfiles,
//            IOptions<CredoSoftData> options
//        )
//        {
//            _service = service;
//            _userProfiles = userProfiles;
//            _options = options;
//        }

//        /// <summary>
//        ///     Get the SAS key for authenticating with the associated file share in Azure.
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("SAS")]
//        public IActionResult GetSAS() => Ok(_options.Value.FileShareSAS);

//        /// <summary>
//        ///     Get assets from CredoSoft
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("Assets")]
//        public async Task<IActionResult> GetAssets()
//        {
//            var filteredAssets = await _service.GetAssetsAsync();
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);

//            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
//            var hasDemoRole = user.HasRole("aimaas:demo");

//            if (hasDemoRole)
//            {
//                var sites = await _service.GetAllAssetManagementSitesAsync();
//                var demoSite = sites.FirstOrDefault(site => site.RSITE_GROUP.Trim().ToLower().Contains("demo"));
//                if (demoSite == null) return NotFound();
//                return Ok(filteredAssets.Where(component => component.RSITE_RID == demoSite.RSITE_RID));
//            }

//            if (!hasAdminRole)
//                filteredAssets = filteredAssets.Where(asset => user.AssetManagementSiteIds.Contains(asset.RSITE_RID))
//                    .ToList();

//            return Ok(filteredAssets);
//        }

//        /// <summary>
//        ///     Get a specific asset from CredoSoft
//        /// </summary>
//        /// <param name="objId"></param>
//        /// <returns></returns>
//        [HttpGet("Assets/{objId}")]
//        public async Task<IActionResult> GetAsset(long objId)
//        {
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);
//            var asset = await _service.GetAssetAsync(objId);

//            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
//            var hasDemoRole = user.HasRole("aimaas:demo");

//            if (hasDemoRole)
//            {
//                var sites = await _service.GetAllAssetManagementSitesAsync();
//                var demoSite = sites.FirstOrDefault(site => site.RSITE_GROUP.Trim().ToLower().Contains("demo"));
//                if (demoSite == null || asset.RSITE_RID != demoSite.RSITE_RID) return NotFound();
//                return Ok(asset);
//            }

//            if (!hasAdminRole &&
//                !user.AssetManagementSiteIds.Contains(asset.RSITE_RID)) return Forbid();

//            return Ok(asset);
//        }

//        /// <summary>
//        ///     Get components (children of assets)
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("Components")]
//        public async Task<IActionResult> GetComponents()
//        {
//            var filteredComponents = await _service.GetComponentsAsync();
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);

//            var hasDemoRole = user.HasRole("aimaas:demo");
//            if (hasDemoRole)
//            {
//                var sites = await _service.GetAllAssetManagementSitesAsync();
//                var demoSite = sites.FirstOrDefault(site => site.RSITE_GROUP.Trim().ToLower().Contains("demo"));
//                if (demoSite == null) return NotFound();
//                return Ok(filteredComponents.Where(component => component.RSITE_RID == demoSite.RSITE_RID));
//            }

//            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
//            if (!hasAdminRole)
//                filteredComponents =
//                    filteredComponents.Where(component => user.AssetManagementSiteIds.Contains(component.RSITE_RID))
//                        .ToList();

//            return Ok(filteredComponents);
//        }

//        /// <summary>
//        ///     Get components that belong to a specific asset
//        /// </summary>
//        /// <param name="assetObjId"></param>
//        /// <returns></returns>
//        [HttpGet("Components/{assetObjId}")]
//        public async Task<IActionResult> GetComponentsForAsset(long assetObjId)
//        {
//            var asset = await _service.GetAssetAsync(assetObjId);
//            if (asset == null) return NotFound();

//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);

//            var hasDemoRole = user.HasRole("aimaas:demo");
//            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
//            var userHasSiteID = user.AssetManagementSiteIds.Contains(asset.RSITE_RID);
//            if (!hasAdminRole && !userHasSiteID && !hasDemoRole) return Forbid();

//            var filteredComponents = await _service.GetComponentsForAssetAsync(assetObjId);

//            if (hasDemoRole)
//            {
//                var sites = await _service.GetAllAssetManagementSitesAsync();
//                var demoSite = sites.FirstOrDefault(site => site.RSITE_GROUP.Trim().ToLower().Contains("demo"));
//                if (demoSite == null) return NotFound();
//                return Ok(filteredComponents.Where(component => component.RSITE_RID == demoSite.RSITE_RID));
//            }

//            if (!hasAdminRole)
//                filteredComponents =
//                    filteredComponents.Where(component => user.AssetManagementSiteIds.Contains(component.RSITE_RID))
//                        .ToList();

//            return Ok(filteredComponents);
//        }

//        /// <summary>
//        ///     Get alarm calcs
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("AlarmCalcs")]
//        public async Task<IActionResult> GetAlarmCalcs()
//        {
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);
//            var filteredAlarmCalcs = await _service.GetAllAlarmCalculations();

//            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
//            var hasDemoRole = user.HasRole("aimaas:demo");

//            if (hasDemoRole)
//            {
//                var sites = await _service.GetAllAssetManagementSitesAsync();
//                var demoSite = sites.FirstOrDefault(site => site.RSITE_GROUP.Trim().ToLower().Contains("demo"));
//                if (demoSite == null) return NotFound();
//                return Ok(filteredAlarmCalcs.Where(component => component.RSITE_RID == demoSite.RSITE_RID));
//            }

//            if (!hasAdminRole)
//                filteredAlarmCalcs = filteredAlarmCalcs
//                    .Where(alarmCalc => user.AssetManagementSiteIds.Contains(alarmCalc.RSITE_RID)).ToList();


//            return Ok(filteredAlarmCalcs);
//        }

//        /// <summary>
//        ///     Get alarm calcs for a specific component.
//        /// </summary>
//        /// <param name="componentObjId"></param>
//        /// <returns></returns>
//        [HttpGet("AlarmCalcs/{componentObjId}")]
//        public async Task<IActionResult> GetAlarmCalcForComponent(long componentObjId)
//        {
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);

//            var component = await _service.GetComponentAsync(componentObjId);
//            if (component == null) return NotFound();

//            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
//            var hasDemoRole = user.HasRole("aimaas:demo");

//            var alarmCalc = await _service.GetAlarmCalculationForComponentAsync(componentObjId);

//            if (hasDemoRole)
//            {
//                var sites = await _service.GetAllAssetManagementSitesAsync();
//                var demoSite = sites.FirstOrDefault(site => site.RSITE_GROUP.Trim().ToLower().Contains("demo"));
//                if (demoSite == null || alarmCalc.RSITE_RID != demoSite.RSITE_RID) return NotFound();
//                return Ok(alarmCalc);
//            }

//            if (!hasAdminRole && !user.AssetManagementSiteIds.Contains(alarmCalc.RSITE_RID)) return Forbid();

//            return Ok(alarmCalc);
//        }

//        /// <summary>
//        ///     Get inspections
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("Inspections")]
//        public async Task<IActionResult> GetInspections()
//        {
//            var filteredInspections = await _service.GetInspectionsAsync();
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);

//            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
//            var hasDemoRole = user.HasRole("aimaas:demo");

//            if (hasDemoRole)
//            {
//                var sites = await _service.GetAllAssetManagementSitesAsync();
//                var demoSite = sites.FirstOrDefault(site => site.RSITE_GROUP.Trim().ToLower().Contains("demo"));
//                if (demoSite == null) return NotFound();
//                return Ok(filteredInspections.Where(component => component.RSITE_RID == demoSite.RSITE_RID));
//            }

//            if (!hasAdminRole)
//                filteredInspections =
//                    filteredInspections.Where(inspection => user.AssetManagementSiteIds.Contains(inspection.RSITE_RID))
//                        .ToList();

//            return Ok(filteredInspections);
//        }

//        /// <summary>
//        ///     Get information about attachments that belong to assets
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("Assets/Attachments")]
//        public async Task<IActionResult> GetAllAssetAttachments()
//        {
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);

//            var filteredAssetAttachments = await _service.GetAllAssetAttachmentsAsync();

//            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
//            var hasDemoRole = user.HasRole("aimaas:demo");

//            if (hasDemoRole)
//            {
//                var sites = await _service.GetAllAssetManagementSitesAsync();
//                var demoSite = sites.FirstOrDefault(site => site.RSITE_GROUP.Trim().ToLower().Contains("demo"));
//                if (demoSite == null) return NotFound();
//                return Ok(filteredAssetAttachments.Where(component => component.RSITE_RID == demoSite.RSITE_RID));
//            }

//            if (!hasAdminRole)
//                filteredAssetAttachments = filteredAssetAttachments
//                    .Where(attachment => user.AssetManagementSiteIds.Contains(attachment.RSITE_RID)).ToList();

//            return Ok(filteredAssetAttachments);
//        }

//        /// <summary>
//        ///     Get information about attachments that belong to a specific asset
//        /// </summary>
//        /// <param name="objid"></param>
//        /// <returns></returns>
//        [HttpGet("Assets/Attachments/{objid}")]
//        public async Task<IActionResult> GetAssetAttachments(long objid)
//        {
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);

//            var filteredAssetAttachments = await _service.GetAssetAttachmentsAsync(objid);

//            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
//            var hasDemoRole = user.HasRole("aimaas:demo");

//            if (hasDemoRole)
//            {
//                var sites = await _service.GetAllAssetManagementSitesAsync();
//                var demoSite = sites.FirstOrDefault(site => site.RSITE_GROUP.Trim().ToLower().Contains("demo"));
//                if (demoSite == null) return NotFound();
//                return Ok(filteredAssetAttachments.Where(component => component.RSITE_RID == demoSite.RSITE_RID));
//            }

//            if (!hasAdminRole)
//                filteredAssetAttachments = filteredAssetAttachments
//                    .Where(attachment => user.AssetManagementSiteIds.Contains(attachment.RSITE_RID)).ToList();

//            return Ok(filteredAssetAttachments);
//        }

//        /// <summary>
//        ///     Get information about attachments that belong to inspections
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("Inspections/Attachments")]
//        public async Task<IActionResult> GetAllInspectionAttachments()
//        {
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);

//            var filteredInspectionAttachments = await _service.GetAllInspectionAttachmentsAsync();

//            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
//            var hasDemoRole = user.HasRole("aimaas:demo");

//            if (hasDemoRole)
//            {
//                var sites = await _service.GetAllAssetManagementSitesAsync();
//                var demoSite = sites.FirstOrDefault(site => site.RSITE_GROUP.Trim().ToLower().Contains("demo"));
//                if (demoSite == null) return NotFound();
//                return Ok(filteredInspectionAttachments.Where(component => component.RSITE_RID == demoSite.RSITE_RID));
//            }

//            if (!hasAdminRole)
//                filteredInspectionAttachments = filteredInspectionAttachments
//                    .Where(attachment => user.AssetManagementSiteIds.Contains(attachment.RSITE_RID)).ToList();

//            return Ok(filteredInspectionAttachments);
//        }

//        /// <summary>
//        ///     Get information about attachments that belong to a specific inspection
//        /// </summary>
//        /// <param name="emid"></param>
//        /// <returns></returns>
//        [HttpGet("Inspections/Attachments/{emid}")]
//        public async Task<IActionResult> GetInspectionAttachments(long emid)
//        {
//            var email = User?.Identity?.Name?.ToLower();
//            var user = await _userProfiles.GetAsync(email);

//            var filteredInspectionAttachments = await _service.GetInspectionAttachmentsAsync(emid);

//            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
//            var hasDemoRole = user.HasRole("aimaas:demo");

//            if (hasDemoRole)
//            {
//                var sites = await _service.GetAllAssetManagementSitesAsync();
//                var demoSite = sites.FirstOrDefault(site => site.RSITE_GROUP.Trim().ToLower().Contains("demo"));
//                if (demoSite == null) return NotFound();
//                return Ok(filteredInspectionAttachments.Where(component => component.RSITE_RID == demoSite.RSITE_RID));
//            }

//            if (!hasAdminRole)
//                filteredInspectionAttachments = filteredInspectionAttachments
//                    .Where(attachment => user.AssetManagementSiteIds.Contains(attachment.RSITE_RID)).ToList();

//            return Ok(filteredInspectionAttachments);
//        }

//        /// <summary>
//        ///     Get information about attachments that belong to assets
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("AssetManagementSites")]
//        public async Task<IActionResult> GetAllAssetManagementSites() =>
//            Ok(await _service.GetAllAssetManagementSitesAsync());
//    }
//}