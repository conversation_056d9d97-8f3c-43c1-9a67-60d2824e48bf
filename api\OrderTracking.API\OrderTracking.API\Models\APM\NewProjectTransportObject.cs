﻿using System;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class NewProjectTransportObject
    {
        [JsonProperty(PropertyName = "apmNumber")]
        public string ApmNumber { get; set; }

        [JsonProperty(PropertyName = "teamProjectNumber")]
        public string TeamProjectNumber { get; set; }

        [JsonProperty(PropertyName = "client")]
        public string Client { get; set; }

        [JsonProperty(PropertyName = "locationId")]
        public string LocationId { get; set; }

        [JsonProperty(PropertyName = "projectName")]
        public string ProjectName { get; set; }
        
        [JsonProperty(PropertyName = "plannedStart")]
        public DateTime? PlannedStart { get; set; }

        [JsonProperty(PropertyName = "plannedEnd")]
        public DateTime? PlannedEnd { get; set; }
    }
}
