﻿using System;
using Newtonsoft.Json;
// Migrated from Google Cloud Firestore to Azure Cosmos DB
// using Google.Cloud.Firestore;

namespace ClientPortal.Shared.Models
{
    public class ChangeEvent: ICosmosEntity<string>
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("old")]
        public object Old { get; set; }

        [JsonProperty("new")]
        public object New { get; set; }

        [JsonProperty("user")]
        public object User { get; set; }

        [JsonProperty("createdAt")]
        public DateTime CreatedAt { get; set; }
    }
}