﻿using System;
using System.Text;
using Newtonsoft.Json;

namespace OrderTracking.API.Models
{
    /// <summary>
    ///     Verification Token, used for verifying that users are in control and actively using their user profiles
    /// </summary>
    public class VerificationToken
    {
        /// <summary>
        ///     Constructs a VerificationToken, based on when the token should expire and what email the token is for
        /// </summary>
        /// <param name="verificationExpirationInSeconds"></param>
        /// <param name="email"></param>
        public VerificationToken(double verificationExpirationInSeconds, string email)
        {
            Email = email;
            VerificationExpirationInSeconds = verificationExpirationInSeconds;
        }

        /// <summary>
        ///     When the previous user verification is expired, requiring another verification to take place
        /// </summary>
        public DateTime VerificationExpirationDate => DateTime.UnixEpoch.AddSeconds(VerificationExpirationInSeconds);

        /// <summary>
        ///     The verification expiration in seconds
        /// </summary>
        public double VerificationExpirationInSeconds { get; set; }

        /// <summary>
        ///     The email the token is for
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        ///     Returns false if the <see cref="VerificationExpirationDate" /> is before now
        /// </summary>
        public bool IsExpired => DateTime.UtcNow > VerificationExpirationDate;

        /// <summary>
        ///     Create token that expires in 24 hours, with a user's email
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        public static VerificationToken Create24HourToken(string email)
        {
            var time = (int) DateTime.UtcNow.Subtract(new DateTime(1970, 1, 1)).TotalSeconds +
                       new TimeSpan(24, 0, 0).TotalSeconds;
            var token = new VerificationToken(time, email);
            return token;
        }

        /// <summary>
        ///     Deserialize a verification token
        /// </summary>
        /// <param name="serializedToken"></param>
        /// <returns></returns>
        public static VerificationToken DeserializeToken(string serializedToken)
        {
            var byteArray = Convert.FromBase64String(serializedToken);
            var tokenString = Encoding.UTF8.GetString(byteArray);
            var token = JsonConvert.DeserializeObject<VerificationToken>(tokenString);

            return token;
        }

        /// <summary>
        ///     Serialize the token
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public static string SerializeToken(VerificationToken token)
        {
            var jsonData = JsonConvert.SerializeObject(token);
            var byteArray = Encoding.UTF8.GetBytes(jsonData);
            var serializedToken = Convert.ToBase64String(byteArray);

            return serializedToken;
        }
    }
}