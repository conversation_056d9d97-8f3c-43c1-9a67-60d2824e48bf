<div class="dx-card responsive-paddings content-block pie-container"
     style="position: relative">
    <dx-load-indicator *ngIf="loading"
                       id="large-indicator"
                       style="position: absolute; left: calc(50% - 30px); top: calc(50% - 30px)"
                       height="60"
                       width="60"></dx-load-indicator>

    <div>
        <dx-chart [dataSource]="chartData"
                  [palette]="'Soft Pastel'">
            <dxo-zoom-and-pan [dragToZoom]="true"
                              panKey="ctrl"
                              argumentAxis="both"
                              valueAxis="both"
                              [allowMouseWheel]="false">
            </dxo-zoom-and-pan>
            <dxi-series valueField="vessel"
                        name="Vessel"></dxi-series>
            <dxi-series valueField="tank"
                        name="Tank"></dxi-series>
            <dxi-series valueField="piping"
                        name="Piping"></dxi-series>
            <dxo-common-series-settings argumentField="area"
                                        type="stackedBar">
            </dxo-common-series-settings>
            <dxo-legend verticalAlignment="bottom"
                        horizontalAlignment="center"
                        itemTextPosition="top">
            </dxo-legend>
            <dxo-title [text]="'Equipment by Area and Type'"
                       [horizontalAlignment]="'center'"
                       [verticalAlignment]="'top'"></dxo-title>
        </dx-chart>
        <dx-button style="float: right;"
                   text="Reset"
                   (onClick)="resetZoom(chart)">
        </dx-button>
    </div>
