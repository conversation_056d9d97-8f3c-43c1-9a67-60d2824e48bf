﻿using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Moq;
using NUnit.Framework;
using OrderTracking.API.Authorization.Handlers;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Tests.Authorization.Handlers
{
    [TestFixture]
    public class AssetManagementSiteAssignmentAuthorizationHandlerTests
    {
        [SetUp]
        public void SetUp()
        {
            _mockService = new Mock<IUserProfilesService>(MockBehavior.Strict);
            _handler = new AssetManagementSiteAssignmentAuthorizationHandler(_mockService.Object);
        }

        private Mock<IUserProfilesService> _mockService;
        private AssetManagementSiteAssignmentAuthorizationHandler _handler;
        private UserProfile _user;
        private AuthorizationHandlerContext _context;

        [Test]
        public async Task HandleAsync_AssetManagementSiteAssignment_ContextHasSucceeded()
        {
            _user = new UserProfile { Id = "<EMAIL>", Roles = { "AIMaaS:View" } };
            _mockService.Setup((service => service.GetAsync("<EMAIL>")))
                .ReturnsAsync(_user);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_user, _user,
                new[] { new AssetManagementSiteAssignmentRequirement() });
            await _handler.HandleAsync(_context);
            Assert.That(_context.HasSucceeded);
        }

        [Test]
        public async Task HandleAsync_AssetManagementSiteAssignment_ViewRole_ContextHasFailed()
        {
            _user = new UserProfile { Id = "<EMAIL>", Roles = { "NotAIMaaS:View" } };
            var _otherUser = new UserProfile{Id ="<EMAIL>"};
            _mockService.Setup((service => service.GetAsync("<EMAIL>")))
                .ReturnsAsync(_user);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_user, _otherUser,
                new[] { new AssetManagementSiteAssignmentRequirement() });
            await _handler.HandleAsync(_context);
            Assert.That(_context.HasFailed);
        }
    }
}
