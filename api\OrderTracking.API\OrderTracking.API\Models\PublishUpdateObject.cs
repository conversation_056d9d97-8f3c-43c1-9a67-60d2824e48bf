﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderTracking.API.Models
{
    public class PublishUpdateObject
    {
        [JsonProperty("projectId")]
        public string ProjectId { get; set; }

        [JsonProperty("workOrderId")]
        public string WorkOrderId { get; set; }

        [JsonProperty("isPublishing")]
        public bool IsPublishing { get; set; }
    }
}
