---
apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: run-job-remote-monitoring-stg-usc1
  annotations:
    run.googleapis.com/description: run-job-remote-monitoring-stg-usc1
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/vpc-access-connector: projects/vpc-host-nonprod-pj334-cv215/locations/us-central1/connectors/slcon-run-dev-usc1
        run.googleapis.com/vpc-access-egress: private-ranges-only
    spec:
      taskCount: 1
      template:
        spec:
          serviceAccountName: <EMAIL>
          containers:
          - image: us-central1-docker.pkg.dev/oneinsight-stage-051d/ar-dotnet/cpa-remote-monitoring:latest
            env:
            - name: GOOGLE_CLOUD_PROJECT
              value: oneinsight-stage-051d
            - name: Smartpims__Password
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: SmartpimsPassword
            - name: ConnectionStrings__RemoteMonitoring
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: ConnectionStrings__RemoteMonitoring
            - name: Connections__ProjectID
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: Connections__ProjectID
            - name: ZDapperPlus__LicenseKey
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: ZDapperPlus__LicenseKey
            resources:
              limits:
                cpu: 1000m
                memory: 512Mi
          maxRetries: 3
          timeoutSeconds: '600'
     
     