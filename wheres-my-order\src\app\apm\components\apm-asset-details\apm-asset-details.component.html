<div class="content-block responsive-paddings">

    <ng-container [ngSwitch]="workOrder?.asset?.walkDown?.displayName">
        <!-- Vessel -->
        <div *ngSwitchCase="'510 Asset Walkdown-Details-F'">
            <app-apm-asset-details-five-ten [assetDetails]="walkdown | assetDetailsFiveTen : assetId : projectId : workOrderId"
                                            (photoDescriptionUpdate)="onPhotoDescriptionUpdate($event)"
                                            (photoDelete)="onPhotoDelete($event)"
                                            [allowEditing]="allowEditing">
            </app-apm-asset-details-five-ten>
        </div>

        <!-- Piping -->
        <div *ngSwitchCase="'570 Asset Walkdown-Details-F'">
            <app-apm-asset-details-five-seventy [assetDetails]="walkdown | assetDetailsFiveSeventy : workOrderId : projectId"
                                                (photoDescriptionUpdate)="onPhotoDescriptionUpdate($event)"
                                                (photoDelete)="onPhotoDelete($event)"
                                                [allowEditing]="allowEditing">
            </app-apm-asset-details-five-seventy>
        </div>

        <!-- Tank -->
        <div *ngSwitchCase="'653 Asset Walkdown-Details-F'">
            <app-apm-asset-details-six-fifty-three [assetDetails]="walkdown | assetDetailsSixFiftyThree : workOrderId : projectId"
                                                   (photoDescriptionUpdate)="onPhotoDescriptionUpdate($event)"
                                                   (photoDelete)="onPhotoDelete($event)"
                                                   [allowEditing]="allowEditing">
            </app-apm-asset-details-six-fifty-three>
        </div>
    </ng-container>

</div>
