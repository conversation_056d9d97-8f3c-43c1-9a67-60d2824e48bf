import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Input,
    Output
} from '@angular/core';
import cloneDeep from 'clone-deep';
import { DxFormComponent } from 'devextreme-angular';
import CustomStore from 'devextreme/data/custom_store';
import { BehaviorSubject } from 'rxjs';
import { ProjectDetail, ProjectUpdate } from '../../models';
import { ApmService } from '../../services';

@Component({
    changeDetection: ChangeDetectionStrategy.OnPush,
    selector: 'app-details-tab',
    templateUrl: './details-tab.component.html',
    styleUrls: ['./details-tab.component.scss']
})
export class DetailsTabComponent {
    private _project: ProjectDetail | undefined;
    private _original: ProjectDetail | undefined;
    private _changes: Partial<ProjectDetail> = {};

    @Input()
    set project(proj: ProjectDetail) {
        this._project = proj;
        this._original = cloneDeep(this._project, true);
        this._changes = {};
        this.isEditing = false;
        this._cd.markForCheck();
    }
    get project(): ProjectDetail {
        return this._project;
    }

    @Output() projectUpdated = new EventEmitter<Partial<ProjectUpdate>>();

    @Input() allowEditing: boolean;
    isEditing = false;
    isSaving = new BehaviorSubject<boolean>(false);
    locationsDataSource = new CustomStore({
        key: 'id',
        byKey: (key: any) => this._apm.getLocation(key).toPromise(),
        load: (options) => this._apm.loadLocations(options).toPromise()
    });

    constructor(
        private readonly _apm: ApmService,
        private readonly _cd: ChangeDetectorRef
    ) {}

    onFieldDataChanged(e) {
        this._changes[e.dataField] = e.value;
    }

    onEditClicked(e) {
        this.isEditing = true;
    }

    onSaveClicked(e) {
        this.isSaving.next(true);
        this.projectUpdated.next({ id: this._project.id, ...this._changes });
    }

    onCancelClicked(e, form: DxFormComponent) {
        this.isEditing = false;
        form.instance.option('formData', this._original);
        this._changes = {};
    }
}
