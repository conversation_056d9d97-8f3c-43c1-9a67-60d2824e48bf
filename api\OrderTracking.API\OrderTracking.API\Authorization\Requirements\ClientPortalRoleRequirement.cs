﻿using System.Collections.Generic;
using Microsoft.AspNetCore.Authorization;

namespace OrderTracking.API.Authorization.Requirements
{
    /// <summary>
    ///     Class that represents a role requirement.  If a user has any of the roles
    ///     listed in the requirement, the requirement should succeed.
    /// </summary>
    public class ClientPortalRoleRequirement : IAuthorizationRequirement
    {
        /// <summary>
        ///     Constructor
        /// </summary>
        /// <param name="roles">The roles required to perform an action</param>
        public ClientPortalRoleRequirement(IReadOnlyCollection<string> roles)
        {
            Roles = roles;
        }

        /// <summary>
        ///     Roles required to perform an action.  If a user has any one or more of
        ///     the following roles, the policy requirement should succeed.
        /// </summary>
        public IReadOnlyCollection<string> Roles { get; }
    }
}