﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using OrderTracking.API.Models.EDR;

namespace OrderTracking.API.Hubs
{
    /// <summary>
    ///     EDRHub, where EDR specific real-time updates are handled.
    /// </summary>
    // TODO: Do we want a more specific authorization rule for this hub?
    [Authorize]
    // ReSharper disable once ClassNeverInstantiated.Global
    public class EDRHub : Hub
    {
        private readonly ILogger<EDRHub> _logger;

        /// <summary>
        ///     Constructor for EDRHub so we can inject a logger
        /// </summary>
        /// <param name="logger"></param>
        public EDRHub(ILogger<EDRHub> logger)
        {
            _logger = logger;
        }

        /// <inheritdoc cref="Hub.OnConnectedAsync"/>
        public override Task OnConnectedAsync()
        {
            _logger.LogDebug($"{Context.User.Identity.Name} connected to EDRHub");
            return Task.CompletedTask;
        }
        
        public override Task OnDisconnectedAsync(Exception exception)
        {
            _logger.LogError(message: exception?.Message);
            return base.OnDisconnectedAsync(exception);
        }

        /// <summary>
        ///     Broadcasts the <see cref="EquipmentRequest"/> to listeners to update their client UIs.
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public Task RequestUpdated(EquipmentRequest request) => Clients.All.SendAsync("requestUpdated", request);
    }
}