import { APMStatus } from '..';
import { APMTaskType } from '../apm-task-type';

export interface TaskVM {
    id: string;
    projectId: string;
    equipmentId: string;
    area: string;
    unit: string;
    equipmentType: string;
    equipmentDescription: string;
    dueDate: string;
    taskType: APMTaskType;
    assignedUsers: string[];
    taskUpdatedDate: string;
    afe: string;
    supervisor: string;
    plannedStart: string;
    plannedEnd: string;
    status: APMStatus;
    apmProjectNumber: string;
    apmWorkOrderNumber: string;
    projectName: string;
    teamProjectNumber: string;
    apmTaskNumber: string;
    client: string;
    workOrderId: string;
    clientWorkOrderNumber: string;
    businessUnitId: string;
    assetDatabaseId: string;
}
