import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { DxChartModule } from 'devextreme-angular/ui/chart';
import { DxPieChartModule } from 'devextreme-angular/ui/pie-chart';
import { DxRangeSelectorModule } from 'devextreme-angular/ui/range-selector';

import { AssetInspection } from '../../models';
import { InspectionCountPipe } from '../../pipes';
import {
    InspectionsDashboardComponent
} from './inspections-dashboard.component';

describe('InspectionsDashboardComponent', () => {
    let component: InspectionsDashboardComponent;
    let fixture: ComponentFixture<InspectionsDashboardComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                RouterTestingModule,
                DxPieChartModule,
                DxChartModule,
                DxRangeSelectorModule,
            ],
            declarations: [InspectionsDashboardComponent, InspectionCountPipe],
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(InspectionsDashboardComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should not throw error when rendering charts', () => {
        component.inspections = [new AssetInspection()];

        expect(() => component.renderCharts()).not.toThrowError();
    });
});
