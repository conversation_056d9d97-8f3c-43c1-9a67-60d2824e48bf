﻿//using Audit.Core;
//using Dapper;
//using Dapper.Contrib.Extensions;
//using Microsoft.Data.SqlClient;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.Logging;
//using OrderTracking.API.Interfaces.Services;
//using OrderTracking.API.Models.CRD;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;

//namespace OrderTracking.API.Services
//{
//    public class CRDService : ICRDService
//    {

//        private readonly IConfiguration _config;
//        private readonly ILogger<CRDService> _logger;

//        public CRDService(IConfiguration config, ILogger<CRDService> logger)
//        {
//            _config = config;
//            _logger = logger;
//        }

//        /// <summary>
//        /// 
//        /// </summary>
//        /// <param name="id"></param>
//        /// <returns></returns>
//        public async Task<CRDEntry> GetCRDEntry(int id)
//        {
//            var connectionString = _config.GetConnectionString("CRDDatabase");
//            var connection =
//               new SqlConnection(
//                  connectionString);
//            return await connection.QuerySingleAsync<CRDEntry>("SELECT * FROM CRDEntries WHERE ID = @ID", new { ID = id });
//        }

//        /// <summary>
//        /// </summary>
//        /// <returns></returns>
//        public async Task<IEnumerable<CRDEntry>> GetAllCRDEntries()
//        {
//            var connectionString = _config.GetConnectionString("CRDDatabase");
//            var connection =
//               new SqlConnection(
//                  connectionString);

//            return await connection.QueryAsync<CRDEntry>("SELECT * FROM CRDEntries");
//        }

//        /// <summary>
//        /// </summary>
//        /// <returns></returns>
//        public async Task<IEnumerable<CRDAuditEntry>> GetAllCRDAuditRecords()
//        {
//            var connectionString = _config.GetConnectionString("CRDDatabase");
//            var connection =
//               new SqlConnection(
//                  connectionString);
//            return await connection.QueryAsync<CRDAuditEntry>("SELECT * FROM AuditLog WHERE AuditLog.EventType LIKE 'CRDEntry%'");
//        }

//        /// <summary>
//        /// </summary>
//        /// <returns></returns>
//        public async Task<IEnumerable<CRDEntry>> GetCRDAuditRecordsById(int id)
//        {
//            var connectionString = _config.GetConnectionString("CRDDatabase");
//            var connection =
//               new SqlConnection(
//                  connectionString);
//            return await connection.QueryAsync<CRDEntry>("SELECT * FROM AuditLog WHERE AuditLog.EventType LIKE 'CRDEntry%' AND AuditLog.RecordId = @ID", new { ID = id });
//        }

//        /// <summary>
//        /// 
//        /// </summary>
//        /// <param name="record"></param>
//        /// <param name="user"></param>
//        /// <returns></returns>
//        public async Task<CRDEntry> InsertCRDEntry(CRDEntry record, string user)
//        {
//            var connectionString = _config.GetConnectionString("CRDDatabase");
//            var connection =
//               new SqlConnection(
//                  connectionString);

//            CRDEntry entry = null;
//            using (var scope = await AuditScope.CreateAsync("CRDEntry:Insert", () => entry))
//            {
//                var recordId = await connection.InsertAsync(record);
//                scope.SetCustomField("User", user);
//                scope.SetCustomField("RecordId", recordId);
//                entry = record;
//            }

//            return entry;
//        }

//        /// <summary>
//        /// 
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="crdEntry">Updated in-memory entity</param>
//        /// <param name="user"></param>
//        /// <returns></returns>
//        public async Task<CRDEntry> UpdateCRDEntry(int id, CRDEntry crdEntry, string user)
//        {
//            var connectionString = _config.GetConnectionString("CRDDatabase");
//            var connection =
//               new SqlConnection(
//                  connectionString);
//            var old = await connection.QuerySingleAsync<CRDEntry>("SELECT * FROM CRDEntries WHERE ID = @ID", new { ID = id });
//            using (var scope = await AuditScope.CreateAsync("CRDEntry:Update", () => old))
//            {
//                scope.SetCustomField("User", user);
//                scope.SetCustomField("RecordId", id);
//                var result = await connection.UpdateAsync(crdEntry);
//                old = crdEntry;
//            }

//            return crdEntry;
//        }

//        /// <summary>
//        /// 
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="user"></param>
//        /// <returns></returns>
//        public async Task<bool> DeleteCRDEntry(int id, string user)
//        {
//            var connectionString = _config.GetConnectionString("CRDDatabase");
//            var connection =
//               new SqlConnection(
//                  connectionString);

//            bool successful;
//            var crdEntry = await connection.QuerySingleAsync<CRDEntry>("SELECT * FROM CRDEntries WHERE ID = @ID", new { ID = id });
//            using (var scope = await AuditScope.CreateAsync("CRDEntry:Delete", () => crdEntry))
//            {
//                scope.SetCustomField("User", user);
//                scope.SetCustomField("RecordId", id);
//                successful = await connection.DeleteAsync<CRDEntry>(new CRDEntry { ID = id });
//                if (successful)
//                {
//                    crdEntry = null;
//                }
//            }

//            return successful;
//        }

//        private Exception Exception(string v)
//        {
//            throw new NotImplementedException();
//        }
//    }
//}
