UPDATE Orders
SET [STATUS] = CASE 
    WHEN LOWER(SALESSTATUS) = 'invoiced' THEN 'Delivered'
    WHEN DELIVERYDATE IS NOT NULL THEN 'Shipped'
    WHEN CREATEDDATETIME IS NOT NULL AND <PERSON><PERSON>IVERYDATE IS NULL THEN 'Production'
    WHEN NULLIF(LTRIM(RTRIM(JSSJOBID)), '') IS NOT NULL AND DELIVERYDATE IS NULL AND CREATEDDATETIME IS NULL THEN 'Engineering'
    WHEN NULLIF(LTRIM(RTRIM(INTERCOMPANYPURCHID)), '') IS NOT NULL THEN 'Ordered'
    ELSE NULL
END