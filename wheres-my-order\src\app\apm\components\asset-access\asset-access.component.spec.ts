import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxFormModule, DxLoadIndicatorModule } from 'devextreme-angular';
import { AssetAccessComponent } from './asset-access.component';

describe('AssetAccessComponent', () => {
    let component: AssetAccessComponent;
    let fixture: ComponentFixture<AssetAccessComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DxLoadIndicatorModule, DxFormModule],
            declarations: [AssetAccessComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(AssetAccessComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
