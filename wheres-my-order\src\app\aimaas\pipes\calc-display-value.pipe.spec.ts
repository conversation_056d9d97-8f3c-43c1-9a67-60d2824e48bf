import { CalcDisplayValuePipe } from './calc-display-value.pipe';
const pipe = new CalcDisplayValuePipe();
describe('CalcDisplayValuePipe', () => {
    it('create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should return null if value is 0', () => {
        expect(pipe.transform(0)).toBe(null);
    });

    it('should return value if value is not 0', () => {
        const value = 5;

        expect(pipe.transform(value)).toBe(value);
    });
});
