import { Component, Input } from '@angular/core';
import cloneDeep from 'clone-deep';
import { confirm } from 'devextreme/ui/dialog';
import { saveAs } from 'file-saver';
import { ToastrService } from 'ngx-toastr';
import { ReplaySubject, firstValueFrom } from 'rxjs';
import { CommaSeparatedStringToNumbers } from '../../../shared/helpers';
import {
    AssetPath,
    LeakRepairPhotoGroup,
    LeakReportPhotoEntries,
    PhotoGroupTransport,
    PhotosEntity
} from '../../models';
import { ApmService } from '../../services';

@Component({
    selector: 'app-leak-repair-photos',
    templateUrl: './leak-repair-photos.component.html',
    styleUrls: ['./leak-repair-photos.component.scss']
})
export class LeakRepairPhotosComponent {
    private _currentPhoto = new ReplaySubject<PhotosEntity>();
    private _currentGroup = new ReplaySubject<LeakRepairPhotoGroup>();

    private _photoGroups: LeakReportPhotoEntries[];
    private _allPhotos: PhotoGroupTransport[];
    private _changes: Partial<PhotoGroupTransport> = {};
    private _originalPhotoInfo: PhotoGroupTransport;

    assetPathLoadingCompleted = false;
    assetPathsArray: AssetPath = {
        allPhotos: {},
        leakRepairPhotos: {}
    };

    isSaving = false;
    popupGallerySelectedIndex = 0;
    isEditingPhotos = false;
    showPopup = false;
    currentPhotoInfo: PhotoGroupTransport;

    @Input() reportID: string;
    @Input() allowEditing: boolean;
    @Input()
    set photoGroups(photoGroups: LeakReportPhotoEntries[]) {
        this._photoGroups = photoGroups;
        this._allPhotos = this._photoGroups?.flatMap((g) =>
            g.photos.photos.map((p) => {
                this.assetPathsArray.leakRepairPhotos[p.blobName] = '';
                const areaOfInterest = CommaSeparatedStringToNumbers(
                    g.areaOfInterestCoordinate.currentValue
                );
                const upstreamTieIn = CommaSeparatedStringToNumbers(
                    g.upstreamTieInCoordinate.currentValue
                );
                const downstreamTieIn = CommaSeparatedStringToNumbers(
                    g.downstreamTieInCoordinate.currentValue
                );
                return {
                    photo: p,
                    group: g,
                    groupDatabaseID: g.databaseId,
                    description: g.description.currentValue,
                    descriptionComment: g.description.comment,
                    photoComment: g.comment.currentValue,
                    areaOfInterestLatitude:
                        areaOfInterest?.length >= 1 ? areaOfInterest[0] : null,
                    areaOfInterestLongitude:
                        areaOfInterest?.length >= 1 ? areaOfInterest[1] : null,
                    areaOfInterestComment: g.areaOfInterestCoordinate.comment,
                    upstreamTieInLatitude:
                        upstreamTieIn?.length >= 1 ? upstreamTieIn[0] : null,
                    upstreamTieInLongitude:
                        upstreamTieIn?.length >= 1 ? upstreamTieIn[1] : null,
                    upstreamTieInComment: g.upstreamTieInCoordinate.comment,
                    downstreamTieInLatitude:
                        downstreamTieIn?.length >= 1
                            ? downstreamTieIn[0]
                            : null,
                    downstreamTieInLongitude:
                        downstreamTieIn?.length >= 1
                            ? downstreamTieIn[1]
                            : null,
                    downstreamTieInComment: g.downstreamTieInCoordinate.comment,
                    utHighMeasurement: g.utHighMeasurment.currentValue,
                    utHighMeasurementComment: g.utHighMeasurment.comment,
                    utLowMeasurement: g.utLowMeasurment.currentValue,
                    utLowMeasurementComment: g.utLowMeasurment.comment
                };
            })
        );
        this.updateAssetPaths();
    }

    get photoGroups() {
        return this._photoGroups;
    }

    get allPhotos() {
        return this._allPhotos;
    }

    get photo$() {
        return this._currentPhoto.asObservable();
    }

    get photoGroup$() {
        return this._currentGroup.asObservable();
    }

    constructor(
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService
    ) {}

    imageClicked(e, group, photo, gallery) {
        this.popupGallerySelectedIndex = this.allPhotos.findIndex(
            (somePhoto) => somePhoto.photo.databaseId === photo.databaseId
        );
        this._currentGroup.next(group);
        this._currentPhoto.next(photo);

        this._originalPhotoInfo = this._allPhotos.find(
            (p) => p.photo.databaseId === photo.databaseId
        );
        this.currentPhotoInfo = cloneDeep(this._originalPhotoInfo);

        this.showPopup = true;
    }

    onSelectionChanged(e) {
        this._changes = {};
        this.isEditingPhotos = false;
        this._originalPhotoInfo = e.addedItems[0];
        this.currentPhotoInfo = cloneDeep(this._originalPhotoInfo);

        this._currentGroup.next(e.addedItems[0].group);
        this._currentPhoto.next(e.addedItems[0].photo);
    }

    onFieldDataChanged(e) {
        if (e.dataField === 'photo' || e.dataField === 'group') return;
        this._changes[e.dataField] = e.value;
    }

    async onDeletePhotoClicked(e, photoInfo) {
        const result = await confirm(
            'Are you sure you want to delete this photo?',
            'Are you sure?'
        );
        if (result) {
            this._apm
                .deleteLeakReportGroupPhoto(photoInfo.photo.databaseId, {
                    reportId: this.reportID,
                    photoGroupId: photoInfo.groupDatabaseID
                })
                .subscribe(() => {
                    const photoGroups = cloneDeep(this._photoGroups);
                    const photos = photoGroups.find(
                        (g) => g.databaseId === photoInfo.group.databaseId
                    ).photos.photos;
                    if (photos) {
                        const index = photos.findIndex(
                            (p) => p.databaseId === photoInfo.photo.databaseId
                        );
                        if (index >= 0) {
                            photos.splice(index, 1);
                        }
                    }
                    this.photoGroups = cloneDeep(photoGroups);
                    this._toasts.success(
                        'Successfully removed photo from photo group',
                        'Photo successfully deleted'
                    );
                });
        }
    }

    async onDownloadClicked(fileName: string) {
        const url = await firstValueFrom(this._apm.getSignedUrl(fileName));
        const blob = await this._apm.downloadFileFromUrl(url).toPromise();
        saveAs(blob, fileName);
    }

    onEditClicked(e) {
        this.isEditingPhotos = true;
    }

    async onPhotoGroupSaved(e, photoInfo: PhotoGroupTransport) {
        let shouldUpdate = true;

        if (photoInfo.group.photos.photos.length > 1) {
            shouldUpdate = await confirm(
                'Changes will be applied to all photos in the selected collection.  Would you like to proceed?',
                'Are you sure?'
            );
        }

        if (shouldUpdate) {
            this.isSaving = true;
            this._apm
                .updateLeakReportPhotoGroup(
                    this.reportID,
                    photoInfo.group.databaseId,
                    this.currentPhotoInfo
                )
                .subscribe(() => {
                    this.isEditingPhotos = false;
                    this._originalPhotoInfo = cloneDeep(this.currentPhotoInfo);
                    this._allPhotos
                        .filter(
                            (photoVM) =>
                                photoVM.groupDatabaseID ===
                                photoInfo.group.databaseId
                        )
                        .forEach((photoVM) => {
                            photoVM.description =
                                this.currentPhotoInfo.description;
                            photoVM.descriptionComment =
                                this.currentPhotoInfo.descriptionComment;
                            photoVM.photoComment =
                                this.currentPhotoInfo.photoComment;
                            photoVM.areaOfInterestLatitude =
                                this.currentPhotoInfo.areaOfInterestLatitude;
                            photoVM.areaOfInterestLongitude =
                                this.currentPhotoInfo.areaOfInterestLongitude;
                            photoVM.areaOfInterestComment =
                                this.currentPhotoInfo.areaOfInterestComment;
                            photoVM.upstreamTieInLatitude =
                                this.currentPhotoInfo.upstreamTieInLatitude;
                            photoVM.upstreamTieInLongitude =
                                this.currentPhotoInfo.upstreamTieInLongitude;
                            photoVM.upstreamTieInComment =
                                this.currentPhotoInfo.upstreamTieInComment;
                            photoVM.downstreamTieInLatitude =
                                this.currentPhotoInfo.downstreamTieInLatitude;
                            photoVM.downstreamTieInLongitude =
                                this.currentPhotoInfo.downstreamTieInLongitude;
                            photoVM.downstreamTieInComment =
                                this.currentPhotoInfo.downstreamTieInComment;
                            photoVM.utHighMeasurement =
                                this.currentPhotoInfo.utHighMeasurement;
                            photoVM.utHighMeasurementComment =
                                this.currentPhotoInfo.utHighMeasurementComment;
                            photoVM.utLowMeasurement =
                                this.currentPhotoInfo.utLowMeasurement;
                            photoVM.utLowMeasurementComment =
                                this.currentPhotoInfo.utLowMeasurementComment;
                        });

                    this._toasts.success(
                        'Successfully updated the photo group',
                        'Successfully updated'
                    );
                    this.isSaving = false;
                });
        }
    }

    onPhotoGroupCancel(e) {
        this.isEditingPhotos = false;
        this.currentPhotoInfo = cloneDeep(this._originalPhotoInfo);
    }

    getAssetImage(type, blobPath) {
        return this.assetPathsArray[type][blobPath]
            ? this.assetPathsArray[type][blobPath]
            : '';
    }

    async updateAssetPaths() {
        if (Object.keys(this.assetPathsArray.leakRepairPhotos).length) {
            Object.keys(this.assetPathsArray.leakRepairPhotos).forEach(
                async (blobName) => {
                    let assetPath;
                    try {
                        assetPath = await firstValueFrom(
                            this._apm.getSignedUrl(blobName)
                        );
                    } catch (error) {
                        assetPath = '';
                    }
                    this.assetPathsArray.leakRepairPhotos[blobName] = assetPath;
                    this.assetPathsArray.allPhotos[blobName] = assetPath;
                }
            );
        }
        this.assetPathLoadingCompleted = true;
    }
}
