<div *ngIf="!assetDetails">
    <dx-load-indicator style="position: relative; left: 50%;"
                       id="large-indicator"
                       height="60"
                       width="60"></dx-load-indicator>
</div>
<div *ngIf="assetDetails"
     class="content-block repsonse-paddings">
    <dx-form #form
             [formData]="assetDetails"
             [readOnly]="(isEditing$ | async) === false || !allowEditing"
             (onFieldDataChanged)="onFieldDataChanged($event)">
        <dxi-item itemType="group"
                  caption="Identification"
                  [colCount]="3">
            <dxi-item dataField="identificationName">
                <dxo-label text="Name"></dxo-label>
            </dxi-item>
            <dxi-item dataField="identificationNumber">
                <dxo-label text="Number or Circuit ID"></dxo-label>
            </dxi-item>
            <dxi-item dataField="assetType"></dxi-item>
            <dxi-item dataField="assetDescription">
                <dxo-label text="Asset Description"></dxo-label>
            </dxi-item>
            <dxi-item dataField="productHandled"></dxi-item>
            <dxi-item dataField="lastKnownInspectionDate"
                      editorType="dxDateBox"></dxi-item>
            <dxi-item dataField="location"
                      editorType="dxSelectBox"
                      [editorOptions]="{items:locations}"></dxi-item>
            <dxi-item dataField="lineFromEquipmentId">
                <dxo-label text="Line from what equipment ID?"></dxo-label>
            </dxi-item>
            <dxi-item dataField="lineToEquipmentId">
                <dxo-label text="Line to what equipment ID?"></dxo-label>
            </dxi-item>
            <dxi-item itemType="group"
                      caption="Start GIS Location"
                      [colCount]="2">
                <dxi-item dataField="startGisLocationLat"
                          editorType="dxNumberBox">
                    <dxo-label text="Latitude"></dxo-label>
                    <dxi-validation-rule type="custom"
                                         [reevaluate]="true"
                                         [validationCallback]="validateStartGIS"
                                         message="Both latitude and longitude must be provided">
                    </dxi-validation-rule>
                    <dxi-validation-rule type="compare"
                                         comparisonType="<="
                                         [comparisonTarget]="positiveLatitude"
                                         message="Latitude must be less than or equal to 90">
                    </dxi-validation-rule>
                    <dxi-validation-rule type="compare"
                                         comparisonType=">="
                                         [comparisonTarget]="negativeLatitude"
                                         message="Latitude must be greater than or equal to -90">
                    </dxi-validation-rule>
                </dxi-item>
                <dxi-item dataField="startGisLocationLong"
                          editorType="dxNumberBox">
                    <dxo-label text="Longitude"></dxo-label>
                    <dxi-validation-rule type="custom"
                                         [reevaluate]="true"
                                         [validationCallback]="validateStartGIS"
                                         message="Both latitude and longitude must be provided">
                    </dxi-validation-rule>
                    <dxi-validation-rule type="compare"
                                         comparisonType="<="
                                         [comparisonTarget]="positiveLongitude"
                                         message="Longitude must be less than or equal to 180">
                    </dxi-validation-rule>
                    <dxi-validation-rule type="compare"
                                         comparisonType=">="
                                         [comparisonTarget]="negativeLongitude"
                                         message="Longitude must be greater than or equal to -180">
                    </dxi-validation-rule>
                </dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      caption="End GIS Location"
                      [colCount]="2">
                <dxi-item dataField="endGisLocationLat"
                          editorType="dxNumberBox">
                    <dxo-label text="Latitude"></dxo-label>
                    <dxi-validation-rule type="custom"
                                         [reevaluate]="true"
                                         [validationCallback]="validateEndGIS"
                                         message="Both latitude and longitude must be provided">
                    </dxi-validation-rule>
                    <dxi-validation-rule type="compare"
                                         comparisonType="<="
                                         [comparisonTarget]="positiveLatitude"
                                         message="Latitude must be less than or equal to 90">
                    </dxi-validation-rule>
                    <dxi-validation-rule type="compare"
                                         comparisonType=">="
                                         [comparisonTarget]="negativeLatitude"
                                         message="Latitude must be greater than or equal to -90">
                    </dxi-validation-rule>
                </dxi-item>
                <dxi-item dataField="endGisLocationLong"
                          editorType="dxNumberBox">
                    <dxo-label text="Longitude"></dxo-label>
                    <dxi-validation-rule type="custom"
                                         [reevaluate]="true"
                                         [validationCallback]="validateEndGIS"
                                         message="Both latitude and longitude must be provided">
                    </dxi-validation-rule>
                    <dxi-validation-rule type="compare"
                                         comparisonType="<="
                                         [comparisonTarget]="positiveLongitude"
                                         message="Longitude must be less than or equal to 180">
                    </dxi-validation-rule>
                    <dxi-validation-rule type="compare"
                                         comparisonType=">="
                                         [comparisonTarget]="negativeLongitude"
                                         message="Longitude must be greater than or equal to -180">
                    </dxi-validation-rule>
                </dxi-item>
            </dxi-item>
        </dxi-item>
        <dxi-item itemType="group"
                  caption="General Information">
            <dxi-item itemType="group"
                      caption="Photos">
                <dxi-item template="frontPhotos">
                    <dxo-label text="Front"></dxo-label>
                    <div *dxTemplate>
                        <dx-tile-view [items]="assetDetails?.frontPhotos"
                                      [baseItemHeight]="120"
                                      [baseItemWidth]="185"
                                      [itemMargin]="10"
                                      [direction]="'horizontal'"
                                      [noDataText]="'No photos in this section'"
                                      [height]="(assetDetails?.frontPhotos?.length > 0 ? 180 : 40)"
                                      (onItemClick)="onItemClick($event)">
                            <div *dxTemplate="let galleryItem of 'item'">
                                <div *ngIf="galleryItem?.blobName"
                                     class="image-tile">
                                    <img class="tile-image"
                                         *ngIf="assetPathLoadingCompleted"
                                         [src]="getAssetImage('frontPhotos',galleryItem.blobName)"
                                         alt="">

                                </div>
                            </div>
                        </dx-tile-view>
                    </div>
                </dxi-item>
                <dxi-item template="backPhotos">
                    <dxo-label text="Back"></dxo-label>
                    <div *dxTemplate>
                        <dx-tile-view [items]="assetDetails?.backPhotos"
                                      [baseItemHeight]="120"
                                      [baseItemWidth]="185"
                                      [itemMargin]="10"
                                      [direction]="'horizontal'"
                                      [noDataText]="'No photos in this section'"
                                      [height]="(assetDetails.backPhotos?.length > 0 ? 180 : 40)"
                                      (onItemClick)="onItemClick($event)">
                            <div *dxTemplate="let galleryItem of 'item'">
                                <div *ngIf="galleryItem?.blobName"
                                     class="image-tile">
                                    <img class="tile-image"
                                         *ngIf="assetPathLoadingCompleted"
                                         [src]="getAssetImage('backPhotos',galleryItem.blobName)"
                                         alt="">
                                </div>
                            </div>
                        </dx-tile-view>
                    </div>
                </dxi-item>
                <dxi-item template="leftPhotos">
                    <dxo-label text="Left"></dxo-label>
                    <div *dxTemplate>
                        <dx-tile-view [items]="assetDetails?.leftPhotos"
                                      [baseItemHeight]="120"
                                      [baseItemWidth]="185"
                                      [itemMargin]="10"
                                      [direction]="'horizontal'"
                                      [noDataText]="'No photos in this section'"
                                      [height]="(assetDetails.leftPhotos?.length > 0 ? 180 : 40)"
                                      (onItemClick)="onItemClick($event)">
                            <div *dxTemplate="let galleryItem of 'item'">
                                <div *ngIf="galleryItem?.blobName"
                                     class="image-tile">
                                    <img class="tile-image"
                                         *ngIf="assetPathLoadingCompleted"
                                         [src]="getAssetImage('leftPhotos',galleryItem.blobName)"
                                         alt="">
                                </div>
                            </div>
                        </dx-tile-view>
                    </div>
                </dxi-item>
                <dxi-item template="rightPhotos">
                    <dxo-label text="Right"></dxo-label>
                    <div *dxTemplate>
                        <dx-tile-view [items]="assetDetails?.rightPhotos"
                                      [baseItemHeight]="120"
                                      [baseItemWidth]="185"
                                      [itemMargin]="10"
                                      [direction]="'horizontal'"
                                      [noDataText]="'No photos in this section'"
                                      [height]="(assetDetails.rightPhotos?.length > 0 ? 180 : 40)"
                                      (onItemClick)="onItemClick($event)">
                            <div *dxTemplate="let galleryItem of 'item'">
                                <div *ngIf="galleryItem?.blobName"
                                     class="image-tile">
                                    <img class="tile-image"
                                         *ngIf="assetPathLoadingCompleted"
                                         [src]="getAssetImage('rightPhotos',galleryItem.blobName)"
                                         alt="">
                                </div>
                            </div>
                        </dx-tile-view>
                    </div>
                </dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      caption="Design"
                      [colCount]="3">
                <dxi-item dataField="designCode">
                    <dxo-label text="Code"></dxo-label>
                </dxi-item>
                <dxi-item dataField="designYear">
                    <dxo-label text="Year"></dxo-label>
                </dxi-item>
                <dxi-item dataField="designAddendum">
                    <dxo-label text="Addendum"></dxo-label>
                </dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      caption="Inspection"
                      [colCount]="3">
                <dxi-item dataField="inspectionCode">
                    <dxo-label text="Code"></dxo-label>
                </dxi-item>
                <dxi-item dataField="inspectionYear">
                    <dxo-label text="Year"></dxo-label>
                </dxi-item>
                <dxi-item dataField="inspectionAddendum">
                    <dxo-label text="Addendum"></dxo-label>
                </dxi-item>
                <dxi-item dataField="ndeExaminationMethods">
                    <dxo-label text="NDE Examination Methods"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      [colCount]="3">
                <dxi-item dataField="pipeClass"
                          editorType="dxSelectBox"
                          [editorOptions]="{items:pipeClasses, acceptCustomValue: true}">
                </dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      [colCount]="3"
                      caption="Manufacturer (Fabricator)">
                <dxi-item dataField="manufacturerName">
                    <dxo-label text="MFG Name"></dxo-label>
                </dxi-item>
                <dxi-item dataField="manufacturerDate"
                          editorType="dxDateBox">
                    <dxo-label text="MFG Date"></dxo-label>
                </dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      [colCount]="3">
                <dxi-item editorType="dxDateBox"
                          dataField="installationDate">
                </dxi-item>
                <dxi-item editorType="dxDateBox"
                          dataField="inServiceDate">
                </dxi-item>
                <dxi-item dataField="pIDNumber">
                    <dxo-label text="P&ID NO.">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="designDrawingNumber">
                    <dxo-label text="Construction/Design Drawing Number">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="lowestFlangeRating"
                          editorType="dxSelectBox"
                          [editorOptions]="{items:flangeRatings}">
                </dxi-item>
                <dxi-item dataField="typeOfConstruction"
                          editorType="dxTagBox"
                          [editorOptions]="{items:constructionMethods}">
                </dxi-item>
                <dxi-item dataField="threadedConnections">
                    <dxo-label text="Does this line have threaded connections?
                        (Injection,
                        Drains, Vents, O-lets)">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="pipeSize"
                          editorType="dxTagBox"
                          [editorOptions]="{items:pipeSizes}">
                </dxi-item>
                <dxi-item dataField="pipeSchedule"
                          editorType="dxTagBox"
                          [editorOptions]="{items:pipeSchedule}">
                </dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      caption="Service"
                      [colCount]="3">
                <dxi-item dataField="serviceProductContents">
                    <dxo-label text="Service/Product/Contents">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="specificGravity">
                </dxi-item>
            </dxi-item>
        </dxi-item>
        <dxi-item itemType="group"
                  [colCount]="3"
                  caption="Operating/Design Conditions">

            <dxi-item dataField="operatingTemperature">
                <dxo-label text="Operating Temperature (&deg;F)">
                </dxo-label>
            </dxi-item>

            <dxi-item dataField="designMAWP">
                <dxo-label text="Design MAWP (psi)">
                </dxo-label>
            </dxi-item>
            <dxi-item dataField="designTemperature">
                <dxo-label text="Design Temperature (&deg;F)">
                </dxo-label>
            </dxi-item>
            <dxi-item dataField="operatingPressure">
                <dxo-label text="Operating Pressure (&deg;F)">
                </dxo-label>
            </dxi-item>
            <dxi-item dataField="pRVSetPressure">
                <dxo-label text="PRV Set Pressure (&deg;F)">
                </dxo-label>
            </dxi-item>
            <dxi-item dataField="operationStatus"
                      editorType="dxSelectBox"
                      [editorOptions]="{items:operatingStatus}">
            </dxi-item>
        </dxi-item>
        <dxi-item itemType="group"
                  caption="Components"
                  *ngIf="assetDetails?.pipes?.length > 0">
            <dxi-item itemType="group"
                      *ngFor="let pipes of assetDetails?.pipes; let i = index"
                      [colCount]="3"
                      caption="Pipe">
                <dxi-item [dataField]="'pipes['+ i +'].lineNumber'">
                    <dxo-label text="Line No.">
                    </dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'pipes['+ i +'].materialSpecAndGrade'">
                    <dxo-label text="Material Spec and Grade">
                    </dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'pipes['+ i +'].allowableStressAtTemperature'"
                          editorType="dxNumberBox">
                    <dxo-label text="Allowable Stress at Temperature">
                    </dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'pipes['+ i +'].nominalThickness'"
                          editorType="dxNumberBox">
                    <dxo-label text="Nominal Thickness (Schedule)">
                    </dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'pipes['+ i +'].corrosionAllowance'"
                          editorType="dxNumberBox">
                    <dxo-label text="Corrosion Allowance">
                    </dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'pipes['+ i +'].jointEfficiency'"
                          editorType="dxNumberBox">
                    <dxo-label text="Joint Efficiency">
                    </dxo-label>
                </dxi-item>
                <dxi-item [dataField]="'pipes['+ i +'].pipeSpecNumber'">
                    <dxo-label text="Pipe Spec Number">
                    </dxo-label>
                </dxi-item>
            </dxi-item>
        </dxi-item>
        <dxi-item [template]="'buttons'"></dxi-item>

        <div *dxTemplate="let data of 'buttons'"
             class="buttons">
            <dx-button *ngIf="(isEditing$ | async) === false; else saveAndCancel"
                       text="Edit"
                       type="default"
                       (onClick)="onEditClicked($event)"
                       [disabled]="!allowEditing"></dx-button>
            <ng-template #saveAndCancel>
                <dx-button text="Cancel"
                           type="danger"
                           [disabled]="isSaving$ | async"
                           (onClick)="onCancelClicked($event, form)">
                </dx-button>
                <dx-button text="Save"
                           type="success"
                           (onClick)="onSaveClicked($event)"
                           [disabled]="!allowEditing || (isSaving$ | async)">
                </dx-button>
            </ng-template>
        </div>
    </dx-form>
</div>

<dx-popup [(visible)]="showPhotoPopup"
          [showTitle]="true"
          [showCloseButton]="true"
          [hideOnOutsideClick]="true"
          #popup>
    <dx-gallery #gallery
                id="gallery"
                [dataSource]="allPhotos"
                [loop]="true"
                width="100%"
                height="100%"
                [showNavButtons]="true"
                [showIndicator]="true"
                [(selectedIndex)]="popupGallerySelectedIndex"
                (onContentReady)="onPopupGalleryContentReady($event, popup, gallery)"
                (onSelectionChanged)="onPopupGallerySelectionChanged($event, popup, gallery)">

        <div *dxTemplate="let photoInfo of 'item'"
             class="popup-template">

            <div *ngIf="photoInfo?.photo?.blobName"
                 class="image-container dx-swatch-additional">
                <img *ngIf="assetPathLoadingCompleted"
                     [src]="getAssetImage('allPhotos',photoInfo.photo.blobName)"
                     alt="">


            </div>
            <div class="info">
                <div class="text-content">
                    <h6>Description</h6>
                    <dx-text-area #descriptionEditor
                                  [readOnly]="(isEditingPhotoDescription$ | async) === false"
                                  [value]="photoInfo?.photo?.description?.currentValue"
                                  [autoResizeEnabled]="true">
                    </dx-text-area>
                    <div class="description-buttons">
                        <dx-button *ngIf="(isEditingPhotoDescription$ | async) === false; else saveCancelPhotoDescription"
                                   icon="edit"
                                   text="Edit"
                                   (onClick)="onEditDescriptionClicked($event, descriptionEditor.value)"
                                   [disabled]="!allowEditing">
                        </dx-button>
                        <ng-template #saveCancelPhotoDescription>
                            <dx-button icon="save"
                                       text="Save"
                                       type="success"
                                       (onClick)="onDescriptionSave($event, photoInfo, descriptionEditor.value)"
                                       [disabled]="!allowEditing">
                            </dx-button>
                            <dx-button text="Cancel"
                                       type="danger"
                                       (onClick)="onDescriptionCancel($event, descriptionEditor)">
                            </dx-button>
                        </ng-template>
                    </div>
                </div>
                <div class="buttons">

                    <dx-button icon="remove"
                               text="Delete"
                               type="danger"
                               [disabled]="!allowEditing"
                               (onClick)="onDeletePhotoClicked($event, photoInfo)">
                    </dx-button>

                    <dx-button icon="download"
                               text="Download"
                               type="default"
                               (onClick)="onDownloadClicked(photoInfo?.photo?.blobName)">
                    </dx-button>
                </div>
            </div>
        </div>
    </dx-gallery>
</dx-popup>