import { Component, OnInit, ViewChild } from '@angular/core';
import {
    DxFormComponent,
    DxListComponent,
    DxTagBoxComponent,
    DxTreeViewComponent
} from 'devextreme-angular';
import { ClickEvent } from 'devextreme/ui/button';
import { confirm } from 'devextreme/ui/dialog';
import { ItemClickEvent } from 'devextreme/ui/drop_down_button';
import { ContentReadyEvent, ItemDeletingEvent } from 'devextreme/ui/list';
import { SelectionChangedEvent } from 'devextreme/ui/tree_view';
import { ValidationCallbackData } from 'devextreme/ui/validation_rules';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { first, map, shareReplay, tap } from 'rxjs/operators';
import { District, DistrictService } from '../../../shared/services';
import {
    TreeViewBusinessUnit,
    TreeViewClient,
    TreeViewUser
} from '../../models';
import { ApmService, ClientManagementService } from '../../services';

type CardAction = 'NEW-CLIENT' | 'NEW-BU' | 'EDIT-CLIENT' | 'EDIT-BU';

const ALL_NEW_TYPES = ['New Client', 'New Business Unit'] as const;
type NewTypes = typeof ALL_NEW_TYPES;
type NewType = NewTypes[number];

@Component({
    selector: 'app-client-management',
    templateUrl: './client-management.component.html',
    styleUrls: ['./client-management.component.scss']
})
export class ClientManagementComponent implements OnInit {
    private _isEditing = new BehaviorSubject(false);
    private _buUsers = new BehaviorSubject([]);
    private _buDistricts = new BehaviorSubject([]);
    private _originalNode: TreeViewClient | TreeViewBusinessUnit;
    private _selectedNode: TreeViewClient | TreeViewBusinessUnit;
    private _currentCardAction: CardAction | undefined;
    private _isSaving = new BehaviorSubject(false);

    clients$ = this._clientManagement.clients$;
    isSaving$ = this._isSaving.asObservable();

    /** Selected Client or Business Unit */
    get selectedNode(): TreeViewClient | TreeViewBusinessUnit {
        return this._selectedNode;
    }

    /** Current Action being taken by the user */
    get currentCardAction(): CardAction | undefined {
        return this._currentCardAction;
    }

    /** If a client is current selected, returns this client */
    get selectedClient(): TreeViewClient {
        return this.selectedNode instanceof TreeViewClient
            ? this.selectedNode
            : undefined;
    }

    /** If a business unit is selected, returns this business unit */
    get selectedBusinessUnit(): TreeViewBusinessUnit {
        return this.selectedNode instanceof TreeViewBusinessUnit
            ? this.selectedNode
            : undefined;
    }

    readonly isEditing$ = this._isEditing.asObservable();

    @ViewChild(DxTreeViewComponent) treeView: DxTreeViewComponent;
    @ViewChild('businessUnitNewUsers') buNewUsersTagBox: DxTagBoxComponent;
    @ViewChild('businessUnitUsers') buUsersList: DxListComponent;
    @ViewChild('businessUnitNewDistricts')
    buNewDistrictsTagBox: DxTagBoxComponent;
    @ViewChild('businessUnitDistricts') buDistrictsList: DxListComponent;
    @ViewChild('newBUForm') newBUForm: DxFormComponent;
    @ViewChild('existingBUForm') existingBUForm: DxFormComponent;
    @ViewChild('existingClientForm') existingClientForm: DxFormComponent;

    /**
     * Users that are available to choose from when adding
     * users to a business unit
     */
    availableUsers$: Observable<TreeViewUser[]> = combineLatest([
        this._apm.getUsers(),
        this._buUsers
    ]).pipe(
        map(([allUsers, buUsers]) =>
            allUsers
                .filter(
                    (u) => !buUsers.map((buUser) => buUser.id).includes(u.id)
                )
                .map((u) => new TreeViewUser(u.id, u.name.currentValue))
        ),
        shareReplay()
    );

    /**
     * Districts that are available to choose from when adding
     * disticts to a business unit
     */
    availableDistricts$ = combineLatest([
        this._districts.getDistricts(),
        this._buDistricts
    ]).pipe(
        map(([allDistricts, buDistricts]) =>
            allDistricts.filter(
                (d) =>
                    !buDistricts
                        .map((buDistrict) => buDistrict.number)
                        .includes(d.number)
            )
        ),
        shareReplay()
    );

    /** Display expression for a APM user object */
    apmUserDisplayExpr = (e: any) => {
        return e?.name || e?.email || e?.id?.replace(/\|/g, '.');
    };

    /** Display expression for a District object */
    districtDisplayExpr = (e: District) => {
        return `${e.number} - ${e.name}`;
    };

    /** Called when attempting to delete a Client or a Business Unit */
    removeCurrentNode = async (e: ClickEvent) => {
        if (!this.selectedNode) return;

        const type =
            this.selectedNode instanceof TreeViewClient
                ? 'client'
                : 'business unit';
        const result = await confirm(
            `Are you sure that you want to remove the ${type} ${this.selectedNode.text}?`,
            'Are you sure?'
        );

        if (!result) return;

        const success = this._clientManagement.removeClientOrBusinessUnit(
            this.selectedNode
        );

        if (success) this.treeView.instance.unselectAll();
    };

    refresh = (e: ClickEvent) => {
        this._clientManagement.refresh();
    };

    /** Called when attempting to add a new Client or Business Unit */
    onAddNewClientOrSite = (e: ItemClickEvent) => {
        const newType = e.itemData as NewType;
        this.treeView.instance.unselectAll();
        this._currentCardAction =
            newType === 'New Client'
                ? 'NEW-CLIENT'
                : newType === 'New Business Unit'
                ? 'NEW-BU'
                : undefined;
    };

    uniqueBUNameNewBU = async (e: ValidationCallbackData) => {
        const success = await this.uniqueNameForBU(
            this.newBUForm.instance.getEditor('client').option('value').id,
            e.value as string,
            null
        );
        return success;
    };

    uniqueBUNameExistingBU = async (e: ValidationCallbackData) => {
        const success = await this.uniqueNameForBU(
            this.selectedBusinessUnit.parentId,
            e.value as string,
            this.selectedBusinessUnit.id
        );
        return success;
    };

    /** Button options for the save button of the New Client Form */
    newClientButtonOptions$ = this.isSaving$.pipe(
        map((isSaving) => ({
            text: 'Save',
            type: 'success',
            stylingMode: 'contained',
            disabled: isSaving,
            useSubmitBehavior: true
        }))
    );

    constructor(
        private readonly _apm: ApmService,
        private readonly _districts: DistrictService,
        private readonly _clientManagement: ClientManagementService
    ) {}

    ngOnInit(): void {
        this._clientManagement.refresh();
    }

    /**
     * Called when a user changes the selection to a different
     * Client or Business Unit
     */
    onSelectionChanged(e: SelectionChangedEvent) {
        this._isEditing.next(false);

        this._selectedNode = e.component
            .getSelectedNodes()
            .map((node) => node.itemData)[0] as
            | TreeViewClient
            | TreeViewBusinessUnit;

        if (this.selectedNode)
            this._originalNode = this.cloneNode(this.selectedNode);

        this._currentCardAction =
            this.selectedNode instanceof TreeViewClient
                ? 'EDIT-CLIENT'
                : this.selectedNode instanceof TreeViewBusinessUnit
                ? 'EDIT-BU'
                : undefined;
    }

    /** Called when the BU edit button is clicked */
    editBUClicked(e: ClickEvent) {
        this._originalNode = this.cloneNode(this.selectedNode);
        this._isEditing.next(true);
    }

    /** Called when the Client edit button is clicked */
    editClientClicked(e: ClickEvent) {
        this._originalNode = this.cloneNode(this.selectedNode);
        this._isEditing.next(true);
    }

    /** Called when the cancel button for the Existing Client Form is clicked */
    editClientCancelClicked(e: ClickEvent) {
        this.selectedClient.text = this._originalNode.text;
        this._isEditing.next(false);
    }

    /** Called when saving changes to an existing Client */
    saveExistingClientClicked(e: ClickEvent) {
        let result = this.existingClientForm.instance.validate();
        if (result.status === 'pending') {
            result.complete.then((r) => {
                if (r.isValid) {
                    this.updateClient();
                }
            });
        } else if (result.isValid) {
            this.updateClient();
        }
    }

    /** Called when clicking cancel on the Existing Business Unit Form */
    editBusinessUnitCancelClicked(e: ClickEvent) {
        this.selectedBusinessUnit.text = this._originalNode.text;
        this._isEditing.next(false);
    }

    /** Called when clicking cancel on the New Business Unit Form */
    newBusinessUnitCancelClicked(e: ClickEvent) {
        this.treeView.instance.unselectAll();
    }

    /** Called when saving changes to an existing Business Unit */
    saveExistingBusinessUnitClicked(e: ClickEvent) {
        let result = this.existingBUForm.instance.validate();
        if (result.status === 'pending') {
            result.complete.then((r) => {
                if (r.isValid) {
                    this.updateBusinessUnit();
                }
            });
        } else if (result.isValid) {
            this.updateBusinessUnit();
        }
    }

    /** Called when the content of the list component for business unit users is ready */
    onBUUsersContentReady(e: ContentReadyEvent) {
        this._buUsers.next(e.component.option('items'));
    }

    /** Called when the content of the list component for business unit districts is ready */
    onBUDistrictsContentReady(e: ContentReadyEvent) {
        this._buDistricts.next(e.component.option('items'));
    }

    /** Called when removing a user from the list of users on a business unit */
    onBUUserRemoving(e: ItemDeletingEvent) {
        const description = this.apmUserDisplayExpr(e.itemData);
        e.cancel = <PromiseLike<void>>(
            (<unknown>(
                confirm(
                    `Are you sure you want to remove access for ${description} to ${this.selectedBusinessUnit.text}?`,
                    'Are you sure?'
                ).then((r) => !r)
            ))
        );
    }

    /** Called when removing a district from the list of districts on a business unit */
    onBUDistrictRemoving(e: ItemDeletingEvent) {
        const description = this.districtDisplayExpr(e.itemData);
        e.cancel = <PromiseLike<void>>(
            (<unknown>(
                confirm(
                    `Are you sure you want to remove access for ${description} to ${this.selectedBusinessUnit.text}?`,
                    'Are you sure?'
                ).then((r) => !r)
            ))
        );
    }

    /** Called when submitting a new client */
    newClientSubmit(e: SubmitEvent, form: DxFormComponent) {
        let result = form.instance.validate();
        if (result.status === 'pending') {
            result.complete.then((r) => {
                if (r.isValid) {
                    this.createClient(form.formData.clientName);
                }
            });
        } else if (result.isValid) {
            this.createClient(form.formData.clientName);
        }
    }

    /** Called when submitting a new business unit for a client */
    newBUSubmit(e: SubmitEvent, form: DxFormComponent) {
        let result = form.instance.validate();
        if (result.status === 'pending') {
            result.complete.then((r) => {
                if (r.isValid) {
                    this.createBusinessUnit(
                        form.formData.client.id,
                        form.formData.siteName
                    );
                }
            });
        } else if (result.isValid) {
            this.createBusinessUnit(
                form.formData.client.id,
                form.formData.siteName
            );
        }
    }

    private createClient(clientName: string) {
        this._isSaving.next(true);
        this._clientManagement
            .createClient(clientName)
            .pipe(
                tap((newClient) => {
                    if (newClient) {
                        setTimeout(() =>
                            this.treeView.instance.selectItem(newClient)
                        );
                    }
                    this._isSaving.next(false);
                })
            )
            .subscribe();
    }

    private createBusinessUnit(clientId: string, siteName: string) {
        this._isSaving.next(true);
        this._clientManagement
            .addBusinessUnit(
                clientId,
                siteName,
                this.buNewUsersTagBox.value,
                this.buNewDistrictsTagBox.value
            )
            .pipe(
                tap((newBU) => {
                    if (newBU) {
                        setTimeout(() => {
                            this.treeView.instance.selectItem(newBU);
                            this.treeView.instance.expandItem(newBU);
                        });
                    }
                    this._isSaving.next(false);
                })
            )
            .subscribe();
    }

    /**
     * clones a TreeViewClient or TreeViewBusinessUnit.
     */
    private cloneNode(node: TreeViewClient | TreeViewBusinessUnit) {
        return node instanceof TreeViewClient
            ? TreeViewClient.clone(node)
            : TreeViewBusinessUnit.clone(node);
    }

    private uniqueNameForBU(
        clientId: string,
        value: string,
        existingBusinessUnitId: string
    ) {
        return this._clientManagement.clients$
            .pipe(
                // get the client related to the selected business unit
                map((clients) => clients.find((c) => c.id === clientId)),
                // get the business units for the client
                map((client) => client.items),
                // filter out the existingBusinessUnitId if not null
                map((businessUnits) =>
                    existingBusinessUnitId
                        ? businessUnits.filter(
                              (bu) => bu.id !== existingBusinessUnitId
                          )
                        : businessUnits
                ),
                // see if value is already used in existing BU
                map((businessUnits) =>
                    businessUnits.map((bu) => bu.text).includes(value)
                ),
                // fail validation if already included
                map((included) => !included),
                first()
            )
            .toPromise();
    }

    private updateClient() {
        this._isSaving.next(true);
        this._clientManagement
            .updateClient(this.selectedClient)
            .pipe(
                tap((successful) => {
                    if (successful) {
                        this._originalNode = this.cloneNode(
                            this.selectedClient
                        );
                        this._isEditing.next(false);
                        this._isSaving.next(false);
                    }
                })
            )
            .subscribe();
    }

    private updateBusinessUnit() {
        this._isSaving.next(true);
        if (this.buNewUsersTagBox?.value?.length > 0) {
            this.selectedBusinessUnit.users.push(
                ...this.buNewUsersTagBox.value
            );
            this._buUsers.next(this.buUsersList.items);
        }
        if (this.buNewDistrictsTagBox?.value?.length > 0) {
            this.selectedBusinessUnit.districts.push(
                ...this.buNewDistrictsTagBox.value
            );
            this._buDistricts.next(this.buDistrictsList.items);
        }
        this._clientManagement
            .updateBusinessUnit(this.selectedBusinessUnit)
            .pipe(
                tap((successful) => {
                    if (successful) {
                        this._originalNode = this.cloneNode(
                            this.selectedBusinessUnit
                        );
                        this._isEditing.next(false);
                    } else {
                        if (this.buNewUsersTagBox?.value?.length > 0) {
                            this.buNewUsersTagBox?.value?.forEach((u) =>
                                this.selectedBusinessUnit.users.pop()
                            );
                            this._buUsers.next(this.buUsersList.items);
                        }
                        if (this.buNewDistrictsTagBox?.value?.length > 0) {
                            this.buNewDistrictsTagBox?.value?.forEach((u) =>
                                this.selectedBusinessUnit.districts.pop()
                            );
                            this._buDistricts.next(this.buDistrictsList.items);
                        }
                    }
                    this._isSaving.next(false);
                })
            )
            .subscribe();
    }
}
