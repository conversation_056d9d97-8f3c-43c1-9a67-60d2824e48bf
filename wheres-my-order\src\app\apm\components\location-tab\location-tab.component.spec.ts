import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
    DxButtonModule,
    DxFormModule,
    DxListModule,
    DxPopupModule
} from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { ProjectLocationPipe } from '../../pipes';
import { ApmService } from '../../services';
import { LocationTabComponent } from './location-tab.component';

describe('LocationTabComponent', () => {
    let component: LocationTabComponent;
    let fixture: ComponentFixture<LocationTabComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxFormModule,
                DxButtonModule,
                DxPopupModule,
                DxListModule,
                HttpClientTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [LocationTabComponent],
            providers: [
                ProjectLocationPipe,
                { provide: ApmService, useValue: {} }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(LocationTabComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
