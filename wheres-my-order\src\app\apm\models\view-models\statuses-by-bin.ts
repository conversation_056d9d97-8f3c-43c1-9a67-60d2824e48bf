export class StatusesByBin {
    bin: string;

    pipingScheduledCount: number = 0;
    pipingInProgressCount: number = 0;
    pipingCompleteCount: number = 0;
    pipingNotStartedCount: number = 0;
    pipingPublishedCount: number = 0;
    pipingOnHoldCount: number = 0;
    pipingOverdueCount: number = 0;
    pipingCanceledCount: number = 0;
    pipingUnknownCount: number = 0;

    vesselScheduledCount: number = 0;
    vesselInProgressCount: number = 0;
    vesselCompleteCount: number = 0;
    vesselNotStartedCount: number = 0;
    vesselPublishedCount: number = 0;
    vesselOnHoldCount: number = 0;
    vesselOverdueCount: number = 0;
    vesselCanceledCount: number = 0;
    vesselUnknownCount: number = 0;

    tankScheduledCount: number = 0;
    tankInProgressCount: number = 0;
    tankCompleteCount: number = 0;
    tankNotStartedCount: number = 0;
    tankPublishedCount: number = 0;
    tankOnHoldCount: number = 0;
    tankOverdueCount: number = 0;
    tankCanceledCount: number = 0;
    tankUnknownCount: number = 0;

    constructor(key: string) {
        this.bin = key;
    }
}
