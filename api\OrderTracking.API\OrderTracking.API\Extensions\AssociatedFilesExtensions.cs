﻿using System;
using System.Collections.Generic;
using System.Linq;
using ClientPortal.Shared.Models;
using OrderTracking.API.Models.EDR;

namespace OrderTracking.API.Extensions
{
    /// <summary>
    ///     Extension methods for records associated with Orders and EquipmentRequests
    /// </summary>
    public static class AssociatedFilesExtensions
    {
        /// <summary>
        ///     Sets each file's Id to a new Guid.
        /// </summary>
        /// <param name="files"></param>
        /// <returns></returns>
        public static IEnumerable<OrderBlobFile> WithNewIds(this IEnumerable<OrderBlobFile> files)
        {
            return files.Select(file =>
            {
                file.Id = Guid.NewGuid().ToString();
                return file;
            });
        }

        /// <summary>
        ///     Sets each file's Id to a new Guid.
        /// </summary>
        /// <param name="files"></param>
        /// <returns></returns>
        public static IEnumerable<EquipmentRequestBlobFile> WithNewIds(this IEnumerable<EquipmentRequestBlobFile> files)
        {
            return files.Select(file =>
            {
                file.Id = Guid.NewGuid().ToString();
                return file;
            });
        }

        /// <summary>
        ///     Sets each order job's Id to a new Guid.
        /// </summary>
        /// <param name="jobs"></param>
        /// <returns></returns>
        public static IEnumerable<OrdersJob> WithNewIds(this IEnumerable<OrdersJob> jobs)
        {
            return jobs.Select(job =>
            {
                job.Id = Guid.NewGuid().ToString();
                return job;
            });
        }
    }
}