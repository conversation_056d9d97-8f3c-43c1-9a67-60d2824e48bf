import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { DxDataGridModule } from 'devextreme-angular/ui/data-grid';
import { of } from 'rxjs';

import { UserProfile } from '../../profile/models';
import { BreadcrumbsComponent } from '../../shared/components';
import { UsersService } from '../../shared/services';
import { UserAuditComponent } from './user-audit.component';

describe('UserAuditComponent', () => {
    let component: UserAuditComponent;
    let fixture: ComponentFixture<UserAuditComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [RouterTestingModule, DxDataGridModule],
            declarations: [UserAuditComponent, BreadcrumbsComponent],
            providers: [
                {
                    provide: UsersService,
                    useValue: {
                        getAll() {
                            return of([new UserProfile()]);
                        },
                    },
                },
            ],
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(UserAuditComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
