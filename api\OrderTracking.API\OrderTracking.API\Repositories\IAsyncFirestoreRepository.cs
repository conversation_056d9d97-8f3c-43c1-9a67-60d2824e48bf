﻿// DEPRECATED: This interface has been replaced by IAsyncCosmosRepository for Azure Cosmos DB
// Commented out during Firebase-to-Azure migration cleanup
/*
using System.Threading.Tasks;
using ClientPortal.Shared.Models;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    /// Standard CRUD methods for Firestore Entities where a Partition Id is needed.
    /// This interface is for methods that require PartitionId and the implementor
    /// knows how to assign this appropriately.
    /// </summary>
    public interface IAsyncFirestoreRepository<TEntity, in TKey>
        : IAsyncRepository<TEntity, TKey> where TEntity : ICosmosEntity<TKey>
    {
        #region Public Methods

        /// <summary>
        /// Get document with id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<TEntity> GetAsync(TKey id);

        /// <summary>
        /// Get document with id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<TEntity> GetAsync(TKey id , string partitionKey);


        /// <summary>
        /// Update document
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task<TEntity> UpdateAsync(TEntity entity);

        /// <summary>
        /// Remove document
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task RemoveAsync(TKey id);

        #endregion
    }
}
*/
