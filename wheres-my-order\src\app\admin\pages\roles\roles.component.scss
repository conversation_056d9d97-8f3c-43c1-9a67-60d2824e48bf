.side-by-side {
    display: flex;
    justify-content: flex-start;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: stretch;
    div.left {
        margin: 6.5px;
        flex-shrink: 1;
        width: 300px;
        min-width: 200px;
        max-width: 500px;
    }
    div.right {
        margin: 6.5px;
        flex-shrink: 1;
        width: 500px;
        min-width: 300px;
    }
}

div.header {
    display: flex;
    font-size: 2rem;
    align-content: flex-start;
    margin-bottom: 2rem;
    img.clip {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: lightgray;
    }
    span.name {
        margin-left: 1rem;
        display: block;
        line-height: normal;
    }
    span.description {
        margin-left: 1.1rem;
        font-size: initial;
        display: block;
        line-height: normal;
    }
}

::ng-deep tr.dx-row > td.dx-focused {
    color: #fff;
    background-color: #3364b8;
}
