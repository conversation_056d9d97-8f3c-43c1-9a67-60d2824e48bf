import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { DxLoadIndicatorModule } from 'devextreme-angular/ui/load-indicator';
import { DxSelectBoxModule } from 'devextreme-angular/ui/select-box';
import { of } from 'rxjs';
import { UserProfile } from '../../../profile/models';
import { UsersService } from '../../../shared/services';
import { SiteLabelPipe } from '../../pipes';
import { AIMaaSComponent } from './aimaas.component';

describe('AIMaaSComponent', () => {
    let component: AIMaaSComponent;
    let fixture: ComponentFixture<AIMaaSComponent>;

    beforeEach(
        waitForAsync(() => {
            TestBed.configureTestingModule({
                imports: [
                    HttpClientTestingModule,
                    RouterTestingModule,
                    DxSelectBoxModule,
                    DxLoadIndicatorModule
                ],
                declarations: [AIMaaSComponent],
                providers: [
                    {
                        provide: UsersService,
                        useValue: { currentProfile$: of(new UserProfile()) }
                    },
                    SiteLabelPipe
                ]
            }).compileComponents();
        })
    );

    beforeEach(() => {
        fixture = TestBed.createComponent(AIMaaSComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
