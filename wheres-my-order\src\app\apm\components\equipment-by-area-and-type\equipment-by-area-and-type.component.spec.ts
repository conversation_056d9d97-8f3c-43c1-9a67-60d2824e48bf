import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxLoadIndicatorModule } from 'devextreme-angular';
import { DxChartModule } from 'devextreme-angular/ui/chart';
import { EquipmentByAreaAndTypeComponent } from './equipment-by-area-and-type.component';

describe('EquipmentByAreaAndTypeComponent', () => {
    let component: EquipmentByAreaAndTypeComponent;
    let fixture: ComponentFixture<EquipmentByAreaAndTypeComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DxChartModule, DxLoadIndicatorModule],
            declarations: [EquipmentByAreaAndTypeComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(EquipmentByAreaAndTypeComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
