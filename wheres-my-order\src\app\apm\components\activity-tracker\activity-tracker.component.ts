import { Component, Input, ViewChild } from '@angular/core';
import { DxDataGridComponent } from 'devextreme-angular';
import {
    EditCanceledEvent,
    EditingStartEvent,
    InitNewRowEvent,
    SavedEvent,
    SavingEvent,
    ToolbarPreparingEvent
} from 'devextreme/ui/data_grid';
import { alert } from 'devextreme/ui/dialog';
import dxPopup, { ContentReadyEvent, Properties } from 'devextreme/ui/popup';
import { ToastrService } from 'ngx-toastr';
import { lastValueFrom } from 'rxjs';
import {
    ActivityUpdate,
    ProjectAndTaskActivities,
    ProjectVm,
    TrackedActivityVM
} from '../../models';
import { ApmService } from '../../services';

@Component({
    selector: 'app-activity-tracker',
    templateUrl: './activity-tracker.component.html',
    styleUrls: ['./activity-tracker.component.scss']
})
export class ActivityTrackerComponent {
    private _activities: ProjectAndTaskActivities;
    private _activitiesList: TrackedActivityVM[];
    popup: dxPopup<Properties>;

    @Input() set activities(value: ProjectAndTaskActivities) {
        this._activities = value;
        if (!this._activities) return;
        const projectActivities: TrackedActivityVM[] =
            this._activities.projectActivities.flatMap((activity) =>
                activity.activities
                    .flatMap((item) => ({
                        activityType: 'project' as 'project',
                        databaseId: activity.databaseId,
                        date: activity.date.currentValue,
                        clientWorkOrder: activity.workOrderNumber.currentValue,
                        technician: activity.user.currentValue,
                        taskType: item.name,
                        duration: item.duration.currentValue,
                        count: item.count.currentValue
                    }))
                    .filter(
                        (activity) =>
                            activity.duration !== null ||
                            activity.count !== null
                    )
            );

        const taskActivities: TrackedActivityVM[] =
            this._activities.taskActivities.flatMap((activity) =>
                activity.activities
                    .flatMap((item) => ({
                        activityType: 'task' as 'task',
                        databaseId: activity.databaseId,
                        date: activity.date.currentValue,
                        clientWorkOrder: activity.workOrderNumber.currentValue,
                        technician: activity.user.currentValue,
                        taskType: item.displayName,
                        duration: item.currentValue,
                        count: null,
                        taskId: activity.taskId
                    }))
                    .filter((activity) => activity.duration !== null)
            );

        const allActivities = [...projectActivities, ...taskActivities];

        this._activitiesList = allActivities;
        this.projectActivityWorkOrderNumbers = Array.from(
            new Set(allActivities.map((activity) => activity.clientWorkOrder))
        );
    }
    get activitiesList() {
        return this._activitiesList;
    }

    projectActivityWorkOrderNumbers: string[];

    @Input() allowEditing: boolean;
    @Input() selectedProject: ProjectVm;

    @Input() availableUsers: string[];

    @ViewChild(DxDataGridComponent) dataGrid: DxDataGridComponent;

    private readonly _projectActivityTypes = [
        'Permitting',
        'Job Setup',
        'Lunch',
        'Post CleanUp',
        'FW-RT',
        'FW-MT',
        'FW-PT',
        'FW-UT',
        'FW-VT',
        'FW-ML',
        'FW-GW',
        'FW-ET',
        'FW-LS',
        'FW-GPR',
        'FW-LT',
        'FW-IR',
        'FW-PMI',
        'FW-AE',
        'FW-VA',
        'FW-API 510',
        'FW-API 570',
        'FW-API 653',
        'FW-AWS CWI',
        'FW-NACE',
        'FW-Other'
    ];
    private readonly _taskActivityTypes = [
        'Permitting',
        'Job Setup',
        'Lunch',
        'Post CleanUp',
        'FW-VT',
        'FW-API 510',
        'FW-API 570',
        'FW-API 653',
        'FW-Other'
    ];

    isInserting: boolean = false;
    showCount = false;
    taskTypes: string[] | undefined;

    constructor(
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService
    ) {}

    onToolbarPreparing(e: ToolbarPreparingEvent) {
        const addBtn = e.toolbarOptions.items.find(
            (i) => i.name === 'addRowButton'
        );
        if (!addBtn) return;

        addBtn.showText = 'always';
        addBtn.options = {
            ...addBtn.options,
            icon: null,
            text: 'Add Activity',
            type: 'success',
            disabled: !this.allowEditing,
            stylingMode: 'contained'
        };
    }

    onPopupContentReady = (e: ContentReadyEvent) => {
        this.popup = e.component;
    };

    // can probably use one transport object here
    onSaving(e: SavingEvent) {
        const objUpdates = e.changes[0].data;
        var update: ActivityUpdate;
        var existingObj: TrackedActivityVM;
        var isNewObject = true;

        if (this.isInserting) {
            if (objUpdates.clientWorkOrder === undefined)
                objUpdates.clientWorkOrder = null;

            existingObj = this.activitiesList.find(
                (activity) =>
                    Date.parse(activity.date) === Date.parse(objUpdates.date) &&
                    activity.technician === objUpdates.technician &&
                    activity.clientWorkOrder === objUpdates.clientWorkOrder
            );
            if (existingObj) isNewObject = false;
        } else {
            isNewObject = false;
        }

        if (!this.isInserting && !isNewObject) {
            const row = e.changes[0].key;
            update = {
                clientWorkOrder: row.clientWorkOrder,
                databaseId: row.databaseId,
                date: row.date,
                taskType: row.taskType,
                previousTaskType: row.taskType,
                technician: row.technician,
                count: row.count,
                duration: row.duration,
                projectId: this.selectedProject.id,
                isInserting: this.isInserting,
                taskId: row.taskId,
                activityType: row.activityType
            };

            if (e.changes[0].type === 'remove') {
                update.count = null;
                update.duration = null;
            } else {
                for (let key in objUpdates) {
                    update[key] = objUpdates[key];
                }
            }
        } else if (this.isInserting && !isNewObject) {
            update = {
                clientWorkOrder: existingObj.clientWorkOrder,
                databaseId: existingObj.databaseId,
                date: existingObj.date,
                taskType: existingObj.taskType,
                previousTaskType: existingObj.taskType,
                technician: existingObj.technician,
                count: existingObj.count,
                duration: existingObj.duration,
                projectId: this.selectedProject.id,
                isInserting: this.isInserting,
                taskId: existingObj.taskId,
                activityType: existingObj.activityType
            };

            for (let key in objUpdates) {
                update[key] = objUpdates[key];
            }
        } else {
            update = {
                clientWorkOrder: null,
                databaseId: null,
                date: null,
                taskType: null,
                previousTaskType: null,
                technician: null,
                count: null,
                duration: null,
                projectId: this.selectedProject.id,
                isInserting: this.isInserting,
                taskId: null,
                activityType: 'project' // We only support adding project activities at this time.
            };

            for (let key in objUpdates) {
                update[key] = objUpdates[key];
            }
        }

        this.popup?.option('toolbarItems[0].disabled', true);
        this.popup?.option('toolbarItems[1].disabled', true);
        e.promise = lastValueFrom(this._apm.updateProjectActivity(update)).then(
            (project) => {
                this._toasts.success('Successfully saved changes to activity');
                this.isInserting = false;
            }
        );
    }

    onSaved(e: SavedEvent) {
        // We support adding activities to the project, not the tasks, at this time (3/16/2022).
        // the activityType property is client-side only and is how we are controlling the interface
        // for editing an existing activity, whether it is project or task based.  There is no
        // `count` on task activities, so we hide the count field on the form depending on what type
        // of activity this is.  This will need to change if and when we support adding new task activities.

        // The saved event has a list of changes that were made during the save operation.  This
        // is in case we are doing batch editing.  However, we are not using batch editing, so
        // we can assume there will only be one change here at this time (3/16/2022)
        const firstChange = e.changes[0];
        if (firstChange) {
            if (firstChange.type === 'insert') {
                firstChange.data.activityType = 'project';
            }
        }
        this.popup?.option('toolbarItems[0].disabled', false);
        this.popup?.option('toolbarItems[1].disabled', false);
    }

    onNewRowInit(e: InitNewRowEvent) {
        this.isInserting = true;
        this.showCount = true;
        this.taskTypes = this._projectActivityTypes;
        e.promise = alert(
            '<p>Activity creation is currently only supported for project-level activities.</p><p>To add a task-level activity, please use the mobile application.</p>',
            'Notice'
        );
    }

    onEditCanceled(e: EditCanceledEvent) {
        this.isInserting = false;
    }

    onEditingStart(e: EditingStartEvent) {
        this.showCount = e.data.activityType === 'project';
        this.taskTypes =
            e.data.activityType === 'project'
                ? this._projectActivityTypes
                : this._taskActivityTypes;
    }
}
