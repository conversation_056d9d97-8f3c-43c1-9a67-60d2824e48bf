import { PhotosEntity } from '..';

export interface LeakRepairPhotoGroup {
    databaseId: string;
    description: string;
    descriptionComment?: string;
    comment?: string;
    areaOfInterest?: GisLocation;
    upstreamTieIn?: GisLocation;
    downstreamTieIn?: GisLocation;
    utHigh?: string;
    utHighComment?: string;
    utLow?: string;
    utLowComment?: string;
    photos: PhotosEntity[];
}

export interface GisLocation {
    latitude: string;
    longitude: string;
    comment: string;
}
