using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
// Migrated from Google Cloud Firestore to Azure Cosmos DB
// using Google.Cloud.Firestore;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Options;
using OrderTracking.API.Extensions;
using OrderTracking.API.Interfaces;
using OrderTracking.API.Models;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     Service class providing access to notifications (migrated to Azure Cosmos DB)
    /// </summary>
    public class NotificationsService : INotificationsService
    {
        // Migrated from Firestore CollectionReference to Azure Cosmos DB Container
        private readonly Container _container;

        #region Constructors

        /// <summary>
        ///     Constructs a NotificationsService class, injecting connection configuration
        /// </summary>
        /// <param name="cosmosClientAdapter">Azure Cosmos DB client adapter</param>
        /// <param name="options">Connection options</param>
        public NotificationsService(ICosmosClientAdapter cosmosClientAdapter, IOptions<Connections> options)
        {
            if (cosmosClientAdapter == null) throw new ArgumentNullException(nameof(cosmosClientAdapter));
            if (options == null) throw new ArgumentNullException(nameof(options));

            // Get the container for notifications
            var cosmosClient = cosmosClientAdapter.GetClient();
            var database = cosmosClient.GetDatabase(options.Value.Database ?? "OrderTrackingDB");
            _container = database.GetContainer(options.Value.Notifications ?? "notifications");
        }

        #endregion

        #region Interface Implementation

        /// <summary>
        ///     Get notifications, dependent on the userId
        /// </summary>
        /// <param name="userId">User ID to filter notifications</param>
        /// <returns></returns>
        public async Task<IEnumerable<Notification>> GetItemByUserAsync(string userId)
        {
            // Migrated from Firestore to Azure Cosmos DB
            var query = "SELECT * FROM c WHERE c.recipient = @userId";
            var queryDefinition = new QueryDefinition(query).WithParameter("@userId", userId);
            var queryResultSetIterator = _container.GetItemQueryIterator<Notification>(queryDefinition);

            var results = new List<Notification>();
            while (queryResultSetIterator.HasMoreResults)
            {
                var currentResultSet = await queryResultSetIterator.ReadNextAsync();
                results.AddRange(currentResultSet);
            }

            return results;
        }

        /// <summary>
        ///     Update a notification
        /// </summary>
        /// <param name="id"></param>
        /// <param name="notification"></param>
        /// <returns></returns>
        public async Task UpdateItemAsync(string id, Notification notification)
        {
            if (notification == null) throw new ArgumentNullException(nameof(notification));

            // Migrated from Firestore to Azure Cosmos DB
            // Ensure the ID is set in the notification object
            notification.Id = id;
            await _container.UpsertItemAsync(notification, new PartitionKey(id));
        }

        #endregion
    }
}