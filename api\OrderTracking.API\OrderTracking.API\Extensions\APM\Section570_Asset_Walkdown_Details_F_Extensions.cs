using System.Linq;
using APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Extensions.APM
{
    /// <summary>
    ///     Extension methods to update a 570 Asset Walk down
    /// </summary>
    public static class Section570_Asset_Walkdown_Details_F_Extensions
    {
        /// <summary>
        ///     Updates a 570 Asset Walk down with a <see cref="FiveSeventyWalkdown" />
        /// </summary>
        /// <param name="details"></param>
        /// <param name="data"></param>
        public static void Update(this Section570_Asset_Walkdown_Details_F details, FiveSeventyWalkdown data)
        {
            if (data.IdentificationName != null)
                details.sectionIdentification.attributeName.SetValue(data.IdentificationName.Value);
            if (data.IdentificationNumber != null)
                details.sectionIdentification.attributeNumber_or_Circuit_ID.SetValue(data.IdentificationNumber.Value);
            if (data.AssetType != null)
                details.sectionIdentification.attributeAsset_Type.SetValue(data.AssetType.Value);
            if (data.AssetDescription != null)
                details.sectionIdentification.attributeEquipment_Description.SetValue(data.AssetDescription.Value);
            if (data.ProductHandled != null)
                details.sectionIdentification.attributeProduct_Handled.SetValue(data.ProductHandled.Value);
            if (data.LastKnownInspectionDate != null)
                details.sectionIdentification.attributeLast_known_inspection_date
                    .SetValue(data.LastKnownInspectionDate.Value);
            if (data.Location != null)
                details.sectionIdentification.attributeLocation.SetValue(data.Location.Value);
            if (data.LineFromEquipmentId != null)
                details.sectionIdentification.attributeLine_from_what_equipment_ID.SetValue(data.LineFromEquipmentId
                    .Value);
            if (data.LineToEquipmentId != null)
                details.sectionIdentification.attributeLine_to_what_eqiupment_ID.SetValue(data.LineToEquipmentId.Value);

            // Potentially throws an InvalidLocationException
            details.sectionIdentification.attributeStart_GIS_Location.SetLocation(
                data.StartGisLocationLat,
                data.StartGisLocationLong);
            details.sectionIdentification.attributeEnd_GIS_Location.SetLocation(
                data.EndGisLocationLat,
                data.EndGisLocationLong);

            if (data.DesignCode != null)
                details.sectionGeneralInformation.sectionDesign.attributeDesign_Code.SetValue(data.DesignCode.Value);
            if (data.DesignYear != null)
                details.sectionGeneralInformation.sectionDesign.attributeCode_Year.SetValue(data.DesignYear.Value);
            if (data.DesignAddendum != null)
                details.sectionGeneralInformation.sectionDesign.attributeAddendum.SetValue(data.DesignAddendum.Value);

            if (data.InspectionCode != null)
                details.sectionGeneralInformation.sectionInspection.attributeInspection_Code.SetValue(
                    data.InspectionCode.Value);
            if (data.NDEExaminationMethods != null)
                details.sectionGeneralInformation.sectionInspection.attributeNDE_Examination_Methods.SetValue(
                    data.NDEExaminationMethods.Value);
            if (data.InspectionYear != null)
                details.sectionGeneralInformation.sectionInspection.attributeYear.SetValue(data.InspectionYear.Value);
            if (data.InspectionAddendum != null)
                details.sectionGeneralInformation.sectionInspection.attributeAddendum.SetValue(data.InspectionAddendum
                    .Value);
            if (data.PipeClass != null)
                details.sectionGeneralInformation.attributePipe_Class.SetValue(data.PipeClass.Value);
            if (data.ManufacturerName != null)
                details.sectionGeneralInformation.sectionManufacturerFabricator.attributeMFG_Name.SetValue(
                    data.ManufacturerName.Value);
            if (data.ManufacturerDate != null)
                details.sectionGeneralInformation.sectionManufacturerFabricator.attributeMFG_Date.SetValue(
                    data.ManufacturerDate.Value);
            if (data.InstallationDate != null)
                details.sectionGeneralInformation.attributeInstallation_Date.SetValue(data.InstallationDate.Value);
            if (data.InServiceDate != null)
                details.sectionGeneralInformation.attributeIn_service_Date.SetValue(data.InServiceDate.Value);
            if (data.PIdNumber != null)
                details.sectionGeneralInformation.attributePID_No_.SetValue(data.PIdNumber.Value);
            if (data.DesignDrawingNumber != null)
                details.sectionGeneralInformation.attributeConstructionDesign_Drawing_Number.SetValue(
                    data.DesignDrawingNumber.Value);
            if (data.LowestFlangeRating != null)
                details.sectionGeneralInformation.attributeLowest_Flange_Rating.SetValue(data.LowestFlangeRating.Value);
            if (data.TypeOfConstruction != null)
                details.sectionGeneralInformation.attributeConstruction_Method.SetValue(data.TypeOfConstruction.Value);
            if (data.ThreadedConnections != null)
                details.sectionGeneralInformation.attribute570AWQ149.SetValue(data.ThreadedConnections.Value);
            if (data.PipeSize != null)
                details.sectionGeneralInformation.attributePipe_Size.SetValue(data.PipeSize.Value);
            if (data.PipeSchedule != null)
                details.sectionGeneralInformation.attributePipe_Schedule.SetValue(data.PipeSchedule.Value);
            if (data.ServiceProductContents != null)
                details.sectionGeneralInformation.sectionService.attributeServiceProductContents.SetValue(
                    data.ServiceProductContents.Value);
            if (data.SpecificGravity != null)
                details.sectionGeneralInformation.sectionService.attributeSpecific_Gravity.SetValue(data.SpecificGravity
                    .Value);
            if (data.OperatingTemperature != null)
                details.sectionOperatingDesignConditions.attributeOperating_Temperature.SetValue(
                    data.OperatingTemperature.Value);
            if (data.DesignMawp != null)
                details.sectionOperatingDesignConditions.attributeDesign_MAWP.SetValue(data.DesignMawp.Value);
            if (data.DesignTemperature != null)
                details.sectionOperatingDesignConditions.attributeDesign_Temperature.SetValue(data.DesignTemperature
                    .Value);
            if (data.OperatingPressure != null)
                details.sectionOperatingDesignConditions.attributeOperating_Pressure.SetValue(data.OperatingPressure
                    .Value);
            if (data.PRvSetPressure != null)
                details.sectionOperatingDesignConditions.attributePRV_Set_Pressure.SetValue(data.PRvSetPressure.Value);
            if (data.OperationStatus != null)
                details.sectionOperatingDesignConditions.attributeOperation_Status.SetValue(data.OperationStatus.Value);
            if (data.Pipes != null && data.Pipes.Length > 0)
                foreach (var pipe in data.Pipes.Where(p => p != null))
                {
                    var dbPipe = details.sectionComponents.sectionPipe.CurrentEntries
                        .Find(o => o.DatabaseId == pipe.DatabaseId);
                    if (pipe.LineNumber != null)
                        dbPipe.attributeLine_No_.SetValue(pipe.LineNumber.Value);
                    if (pipe.MaterialSpecAndGrade != null)
                        dbPipe.attributeMaterial_Spec_and_Grade.SetValue(pipe.MaterialSpecAndGrade.Value);
                    if (pipe.NominalThickness != null)
                        dbPipe.attributeNominal_Thickness_schedule.SetValue(pipe.NominalThickness.Value);
                    if (pipe.CorrosionAllowance != null)
                        dbPipe.attributeCorrosion_Allowance.SetValue(pipe.CorrosionAllowance.Value);
                    if (pipe.JointEfficiency != null)
                        dbPipe.attributeJoint_Efficiency.SetValue(pipe.JointEfficiency.Value);
                    if (pipe.PipeSpecNumber != null)
                        dbPipe.attributePipe_Spec_Number.SetValue(pipe.PipeSpecNumber.Value);
                    if (pipe.AllowableStressAtTemperature != null)
                        dbPipe.attributeAllowable_Stress_at_Temperature.SetValue(
                            pipe.AllowableStressAtTemperature.Value);
                }
        }
    }
}