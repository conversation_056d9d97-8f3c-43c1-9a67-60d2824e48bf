﻿using System;

// (these properties map to database columns and we are not in control of the naming convention)
// <PERSON><PERSON><PERSON><PERSON> disable InconsistentNaming
// <PERSON><PERSON><PERSON><PERSON> disable UnusedMember.Global
// ReSharper disable IdentifierTypo
#pragma warning disable 1591

namespace AIMaaS.Models
{
    /// <summary>
    ///     Inspection object from CredoSoft
    /// </summary>
    // ReSharper disable once ClassNeverInstantiated.Global (this class is used for serialization/deserialization)
    public class Inspection
    {
#pragma warning disable CA1707 // Identifiers should not contain underscores
        public string RSITE_NAME { get; set; }
        public string OBJGROUPID { get; set; }
        public string OBJNAME { get; set; }
        public string OBJDESC { get; set; }
        public string OBJTYPECODE { get; set; }
        public string OBJSERVICE { get; set; }
        public string OBJCOMMENT { get; set; }
        public string EQDESIGNCODE { get; set; }
        public string EQINSPCODE { get; set; }
        public string TM_PV_ORIENTATION { get; set; }
        public decimal DIM_DIAMOUTSIDE { get; set; }
        public string DIM_DIAMUNITS { get; set; }
        public decimal DIM_LENGTH { get; set; }
        public string DIM_LENGTHUNIT { get; set; }
        public string PRESSUNITS { get; set; }
        public decimal PRESSDESMAX { get; set; }
        public decimal TM_PV_RLF_DEV_PRESS { get; set; }
        public decimal PRESSOPERNORM { get; set; }
        public string TEMPUNITS { get; set; }
        public decimal TEMPDESMAX { get; set; }
        public decimal TEMPOPERNORM { get; set; }
        public int EQ_IS_PWHT { get; set; }
        public string OBJCORRCIRC { get; set; }
        public string EQLININGEXT { get; set; }
        public string EQLININGINT { get; set; }
        public string OBJRISKCODE { get; set; }
        public string OBJSTATUS { get; set; }
        public decimal ALY_OM_APRX_VOL_FEET { get; set; }
        public string ALY_OM_PV_CAP { get; set; }
        public string ALY_OM_BLR_1_CAP { get; set; }
        public string ALY_OM_BLR_2_CAP { get; set; }
        public string ALY_OM_BLR_3_CAP { get; set; }
        public string ALY_OM_RISK_NOTES { get; set; }
        public string ALY_OM_ACC_TYPE { get; set; }
        public string ALY_OM_INT_TIER { get; set; }
        public string ALY_OM_EXT_TIER { get; set; }
        public string ALY_OM_INT_SCAFF { get; set; }
        public string ALY_OM_EXT_SCAFF { get; set; }
        public string ALY_OM_INT_INS_RMV { get; set; }
        public string ALY_OM_EXT_INS_RMV { get; set; }
        public string ALY_OM_CONF_ENT { get; set; }
        public string ALY_OM_MANWAY_DET { get; set; }
        public string ALY_OM_INT_ACC_NOTES { get; set; }
        public string ALY_OM_EXT_ACC_NOTES { get; set; }
        public string TM_PV_ROPE_ACCESS { get; set; }
        public string TM_PV_RMT_VIS_INSP_TYP { get; set; }
        public string TM_PV_SPEC_PPE_REQ { get; set; }
        public string ALY_OM_REDUNANCY { get; set; }
        public string ALY_OM_EXPOSURE { get; set; }
        public string ALY_OM_EST_CONS { get; set; }
        public string ALY_OM_CRITICALITY { get; set; }
        public string ALY_OM_EST_INTERR { get; set; }
        public string ALY_OM_APSC_AREA_ID { get; set; }
        public string ALY_OM_APSC_REGION { get; set; }
        public string ALY_OM_SPEC_LOC { get; set; }
        public string ALY_OM_LOC_DESC { get; set; }
        public string ALY_OM_BUILDING { get; set; }
        public string ALY_OM_APSC_JURIS_NO { get; set; }
        public string ALY_OM_STATE_LOCID { get; set; }
        public DateTime? ALY_OM_STATE_CRT_EXP { get; set; }
        public int ALY_OM_STATE_CRT_INT { get; set; }
        public DateTime? ALY_OM_NDE_INSP_DUE { get; set; }
        public DateTime? ALY_OM_NDE_INSP_LAST { get; set; }
        public string OBJUNIQUEID { get; set; }
        public long OBJID { get; set; }
        public long RSITE_RID { get; set; }
        public DateTime? EMDATERAISED { get; set; }
        public DateTime? EMDATEPLANDUE { get; set; }
        public DateTime? EMDATECOMPLETE { get; set; }
        public string EMTYPECODE { get; set; }
        public string EVENTPRIORITY { get; set; }
        public string EMSTATUS { get; set; }
        public string EVENTNAME { get; set; }
        public string EVENTDESCRIPTION { get; set; }
        public string XEMPLAYER { get; set; }
        public string EVENTCOMMENT { get; set; }
        public string EHINSTRUCTION1 { get; set; }
        public string EHINSTRUCTION2 { get; set; }
        public string EHOBSERVATION1 { get; set; }
        public string EHOBSERVATION2 { get; set; }
        public string EHRECOMMENDATION1 { get; set; }
        public string EHRECOMMENDATION2 { get; set; }
        public string EHACTIONTAKEN01 { get; set; }
        public string EHACTIONTAKEN02 { get; set; }
        public long EMID { get; set; }
        public string RSTREAM_NAME { get; set; }
        public string ALY_OM_APSC_BOARD_NO { get; set; }
        public string EQSERIALNUM { get; set; }
        public DateTime? TM_PV_DATE_REPAIRED { get; set; }
        public DateTime? TM_PV_DATE_ALTERED { get; set; }
        public string TM_PV_R_CERT_NO { get; set; }
        public string TM_PV_CERT_HOLDER { get; set; }
        public string EQMANUFACTURER { get; set; }
        public DateTime? EQMANUFDATE { get; set; }
        public DateTime? OBJCOMMISSION { get; set; }
        public string ALY_OM_VERT_HORIZ { get; set; }
        public string OBJSYS_SORT_1 { get; set; }
        public string OBJSYS_SORT_2 { get; set; }
#pragma warning restore CA1707 // Identifiers should not contain underscores
    }
}