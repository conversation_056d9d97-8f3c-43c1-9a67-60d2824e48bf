﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using OrderTracking.API.Controllers;
using OrderTracking.API.Services;

namespace OrderTracking.API.Tests
{
    [TestFixture]
    public class RolesControllerTests
    {
        [SetUp]
        public void SetUp()
        {
            //Arrange - Common tests can use the same Mock
            _usersService = new Mock<IUserProfilesService>(MockBehavior.Strict);
            _rolesService = new Mock<IRolesService>(MockBehavior.Strict);
            _mockLogger = new Mock<ILoggerFactory>();
            _adminRole = new Role("app:admin");
            _httpContext = new Mock<HttpContext>(MockBehavior.Loose);
            _role1 = new Role("Portal:view");
            _role2 = new Role("Portal:edit");
            _role3 = new Role("Portal:foo");
            _roles = new List<Role> {_role1, _role2};
            
            _user1 = new UserProfile {Id = "<EMAIL>", Roles = {_role1.Id, _role2.Id,_adminRole.Id}};
            _user2 = new UserProfile {Id = "<EMAIL>", Roles = {_role1.Id}};
            _usersBoth = new List<UserProfile> {_user1, _user2};

            _groupsExpected = new List<string> {"Portal"};

            //Setup the Roles Service
            _rolesService.Setup(service => service.GetAsync(_role1.Id, _role1.Id))
                .ReturnsAsync(_role1);

            _rolesService.Setup(service => service.GetAsync(_role1.Id))
                .ReturnsAsync(_role1);

            _rolesService.Setup(service => service.GetAllAsync())
                .ReturnsAsync(_roles);

            string s = null;
            _rolesService.Setup(service => service.UpdateAsync(_role1, s))
                .ReturnsAsync(_role1);

            _rolesService.Setup(service => service.UpdateAsync(_role3, s))
                .ReturnsAsync(value: null);

            _rolesService.Setup(service => service.UpdateAsync(_role3, _role2.Id))
                .ReturnsAsync(_role3);

            _rolesService.Setup(service => service.GetGroupsAsync())
                .ReturnsAsync(_roles.Select(r => r.Group).Distinct().ToList());

            _rolesService.Setup(service => service.AddAsync(_role3))
                .ReturnsAsync(_role3);

            _rolesService.Setup(service => service.AddAsync(_role1))
                .ReturnsAsync(value: null);

            _rolesService.SetupAsync(service => service.RemoveAsync(_role3.Id));

            _rolesService.SetupAsync(service => service.RemoveAsync(_role1.Id));

            _usersService.SetupAsync(service => service.GetUsersForRoleAsync(_role1.Id)).Returns(_usersBoth);

            _usersService.SetupAsync(service => service.GetUsersForRoleAsync(_role3.Id))
                .Returns(new List<UserProfile>());

            _usersService.SetupAsync(service => service.GetUsersForGroupAsync(_role2.Group)).Returns(_usersBoth);

            _usersService.Setup(service => service.UpdateAsync(_user1)).Returns(value: null);

            _usersService.Setup(service => service.UpdateAsync(_user2)).Returns(value: null);

            _usersService.Setup(service => service.UpdateAsync(_user2)).Returns(value: null);

            _usersService.SetupAsync(service => service.UpdateAsync(_user1));

            _usersService.Setup(service => service.GetAsync(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(_user1);
            _usersService.Setup(service => service.GetAsync(It.IsAny<string>())).ReturnsAsync(_user1);
            _usersService.SetupAsync(service => service.AddRoleToUser(_user1.Id, _role3.RoleKey));

            //Common controller
            _rolesController = new RolesController(_rolesService.Object, _usersService.Object, _mockLogger.Object);
            // set up http context 
            var claims = new List<Claim>()
            {
                new Claim("emails", "TestEmail")
            };
            var identity = new ClaimsIdentity(claims,"testAuth");
            var claimsPrinciple = new ClaimsPrincipal(identity);
            _httpContext.SetupGet(context => context.User).Returns(claimsPrinciple);
            _rolesController.ControllerContext = new ControllerContext { HttpContext = _httpContext.Object };
        }

        private Mock<IUserProfilesService> _usersService;
        private Mock<IRolesService> _rolesService;
        private Mock<ILoggerFactory> _mockLogger;
        private Role _role1;
        private Role _role2;
        private List<Role> _roles;
        private Role _role3;
        private RolesController _rolesController;
        private List<string> _groupsExpected;
        private List<UserProfile> _usersBoth;
        private UserProfile _user1;
        private UserProfile _user2;
        private Role _adminRole;
        private Mock<HttpContext> _httpContext;


        [TestCase(":")]
        [TestCase(".")]
        public void ConstructRole_FromString_CreatesRole(string separator)
        {
            //Arrange
            var role = $"WMO{separator}admin";
            var roleExpected = "WMO:admin";

            //Act
            var r = new Role(role);

            //Assert
            Assert.That(r.Group, Is.EqualTo("WMO"));
            Assert.That(r.RoleName, Is.EqualTo("admin"));
            Assert.That(r.RoleKey, Is.EqualTo(roleExpected));
        }

        [Test]
        public async Task AddUser_ToRoleThatExists_ShouldReturnNoContent()
        {
            //Act
            var response = await _rolesController.AddUserToRole(_role3.Id, _user1.Id);

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("AddUserToRole", StatusCodes.Status204NoContent));
            Assert.That(response, Is.TypeOf<NoContentResult>());
        }

        [Test]
        public void ConstructRole_FromParts_CreatesRole()
        {
            //Arrange
            var group = "Portal";
            var roleName = "test";
            var description = "do i work?";
            var expectedKey = $"{group}:{roleName}";

            //Act
            var r = new Role(group, roleName, description);

            //Assert
            Assert.That(r.RoleKey, Is.EqualTo(expectedKey));
            Assert.That(r.Group, Is.EqualTo(group));
            Assert.That(r.RoleName, Is.EqualTo(roleName));
            Assert.That(r.Description, Is.EqualTo(description));
            Assert.That(r.Id, Is.EqualTo(r.RoleKey));

        }

        [Test]
        public void ConstructRole_FromVariousConstructors_CreateSameRoles()
        {
            //Arrange
            var group = "Portal";
            var roleName = "test";
            var description = "do i work?";
            var expectedKey = $"{group}:{roleName}";
            var expectedHash = HashCode.Combine(description, expectedKey);

            //Act
            var rKey = new Role(expectedKey) {Description = description};
            var rPieces = new Role(group, roleName, description);
            var rAssign = new Role(expectedKey, description);
            rAssign.Group = group;

            //Assert
            Assert.That(rKey, Is.EqualTo(rPieces));
            Assert.That(rKey, Is.EqualTo(rAssign));
            Assert.That(rAssign, Is.EqualTo(rPieces)); // a.k.a, the transitive property works
            Assert.That(rKey.GetHashCode(), Is.EqualTo(expectedHash));
            Assert.That(rAssign.GetHashCode(), Is.EqualTo(expectedHash));
            Assert.That(rPieces.GetHashCode(), Is.EqualTo(expectedHash));
        }

        [Test]
        public void ConstructRole_WithNullKey_ThrowsArgumentException()
        {
            //Arrange
            string key = null;

            //Act & Assert
            Assert.That(() => new Role(key),
                Throws.ArgumentNullException.With.Property("ParamName").EqualTo("roleKey"));
        }

        [Test]
        public void ConstructRole_WithBadKey_ThrowsInvalidOperationException()
        {
            //Arrange 
            var key = "foo/bar";

            //Act & Assert
            Assert.That(() => new Role(key),
                Throws.InvalidOperationException);
        }

        [Test]
        public async Task DeleteRole_ThatDoesNotExists_ShouldReturnNoContent()
        {
            //Act
            var response = await _rolesController.Delete(_role3.Id);

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("Delete", StatusCodes.Status204NoContent));
            Assert.That(response, Is.TypeOf<NoContentResult>());
        }

        [Test]
        public async Task DeleteRole_ThatExistsWithUsers_ShouldReturnError()
        {
            // Act
            var response = await _rolesController.Delete(_role1.Id);

            // Assert
            Assert.That(response, Is.TypeOf(typeof(BadRequestObjectResult)));
            Assert.That((response as BadRequestObjectResult).Value, Is.EqualTo($"{_role1.RoleKey} role still has users associated with it.  Cannot delete."));
        }

        [Test]
        public async Task DeleteRole_ThatExists_ShouldReturnNoContent()
        {
            //Act
            var response = await _rolesController.Delete(_role3.Id);

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("Delete", StatusCodes.Status204NoContent));
            Assert.That(response, Is.TypeOf<NoContentResult>());
        }

        [Test]
        public async Task GetGroups_ShouldReturnOkResultWithGroups()
        {
            //Act
            var response = await _rolesController.GetGroups();

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("GetGroups", StatusCodes.Status200OK));
            Assert.That(response.Value, Is.EquivalentTo(_groupsExpected));
        }

        [Test]
        public async Task GetRole_ById_ShouldReturnOkResultWithRole()
        {
            //Act
            var response = await _rolesController.Get(_role1.Id);

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("Get", StatusCodes.Status200OK));
            Assert.That(response.Value, Is.EqualTo(_role1));
        }


        [Test]
        public async Task GetRoles_ShouldReturnOkResultWithRoles()
        {
            //Act
            var response = await _rolesController.Get();

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("Get", StatusCodes.Status200OK));
            Assert.That(response.Value, Is.AssignableTo<IEnumerable<Role>>());
        }

        [Test]
        public async Task GetUsersByGroup_ShouldReturnUsers()
        {
            //Act
            var response = await _rolesController.GetUsersFromGroup(_role2.Group);

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("Get", StatusCodes.Status200OK));
            Assert.That(response, Is.EquivalentTo(_usersBoth));
        }

        [Test]
        public async Task GetUsersByRole_ShouldReturnUsers()
        {
            //Act
            var response = await _rolesController.GetUsers(_role1.Id);

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("Get", StatusCodes.Status200OK));
            Assert.That(response, Is.EquivalentTo(_usersBoth));
        }

        [Test]
        public async Task GetUsersByRole_ThatDoesntExist_ShouldReturnEmptyUserList()
        {
            //Act
            var response = await _rolesController.GetUsers(_role3.Id);

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("Get", StatusCodes.Status200OK));
            Assert.That(response, Is.Empty);
        }

        [Test]
        public async Task PostRole_ThatAlreadyExists_ShouldReturnConflict()
        {
            //Act
            var response = await _rolesController.Post(_role1);

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("Post", StatusCodes.Status409Conflict));
            Assert.That(response.Result, Is.TypeOf<ConflictResult>());
        }

        [Test]
        public async Task PostRole_ThatIsNew_ShouldReturnPostedResultOk()
        {
            //Act
            var response = await _rolesController.Post(_role3);
            var result = response.Result as CreatedAtActionResult;

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("Post", StatusCodes.Status201Created));
            Assert.That(response.Result, Is.TypeOf<CreatedAtActionResult>());
            Assert.That(result.Value, Is.EqualTo(_role3));
        }

        [Test]
        public async Task RemoveUser_FromRoleThatExists_ShouldReturnNoContent()
        {
            //Act
            var response = await _rolesController.RemoveUserFromRole(_role1.Id, _user1.Id);

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("RemoveUserFromRole", StatusCodes.Status204NoContent));
            Assert.That(response, Is.TypeOf<NoContentResult>());
        }

        [Test]
        public void Role_Overrides_WorkAsExpected()
        {
            //Arrange
            var group = "Portal";
            var roleName = "test";
            var description = "do i work?";
            var expectedKey = $"{group}:{roleName}";
            var expectedHash = HashCode.Combine(description, expectedKey);

            //Act
            var role1 = new Role(group, roleName, description);
            Role role2Null = null;
            var role3 = role1;
            object role4obj = null;
            object role1obj = role1;

            //Assert
            Assert.That(role1, Is.EqualTo(role3));
            Assert.That(role1.Equals(role1), Is.True);
            Assert.That(role1.Equals(role1obj), Is.True);
            Assert.That(role1.Equals(new object()), Is.False);
            Assert.That(role1.Equals(role2Null), Is.False);
            Assert.That(role1.Equals(role4obj), Is.False);
            Assert.That(role1.ToString(), Is.EqualTo(expectedKey));
            Assert.That(role1.GetHashCode(), Is.EqualTo(expectedHash));
        }

        [Test]
        public async Task UpdateRole_ThatDoesNotExist_ShouldReturnNotFound()
        {
            //Act
            var response = await _rolesController.Put(_role3);

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("Put", StatusCodes.Status400BadRequest));
            Assert.That(response.Result, Is.TypeOf<BadRequestResult>());
        }

        [Test]
        public async Task UpdateRole_ThatExists_ShouldReturnOk()
        {
            //Act
            var response = await _rolesController.Put(_role1);

            //Assert
            Assert.That(_rolesController.MethodHasStatusCode("Put", StatusCodes.Status200OK));
            Assert.That(response.Value, Is.EqualTo(_role1));
        }
    }
}

public class TestPrincipal : ClaimsPrincipal
{
    public TestPrincipal(params Claim[] claims) : base(new TestIdentity(claims))
    {
    }
}

public class TestIdentity : ClaimsIdentity
{
    public TestIdentity(params Claim[] claims) : base(claims)
    {
    }
}