.container {
    display: flex;
    flex-direction: column;
    grid-template-columns: 13% 87%;
    height: auto;
}
.client-submission-selectionbox {
    padding: 0%;
}
.content-block {
    margin-left: 10px;
}
.anomaly-container {
    display: grid;
    grid-template-columns: 50% 50%;
    height: 250px;
}
.left-anomaly {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 7px 7px;
    align-items: center;
    margin: 5px;
}
.right-anomaly {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 5px 5px;
    align-items: center;
    // padding-bottom: 60px;
    // padding-top: 6px;
    margin: 5px;
}
.inner-content {
    display: grid;
    grid-template-columns: 50% 50%;
    height: 300px;
}
.comment-maincontainer {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 7px 7px;
    align-items: center;
    margin: 5px;
}


.field-group {
    display: contents;
}

label {
    text-align: left;
}
dx-text-box,
dx-text-area,
dx-select-box,
dx-tag-box {
    width: 100%;
}
.required {
    color: red;
}
.documents-container dx-scroll-view {
    flex-grow: 1; /* Allows the scroll view to grow and fill remaining space */
}
