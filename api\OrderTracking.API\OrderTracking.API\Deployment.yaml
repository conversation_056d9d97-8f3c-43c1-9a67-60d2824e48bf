apiVersion: apps/v1
kind: Deployment
metadata:
  name: ordertrackingapi-deployment
  labels:
    app: ordertrackingapiDeployment
spec:
  replicas: 1
  selector:
    matchLabels:
      run: ordertrackingapi-pod
  template:
    metadata:
      labels:
        run: ordertrackingapi-pod
    spec:
      containers:
        - name: ordertrackingapi-container
          image: krakendev.azurecr.io/ordertrackingapi:1.0.5
          imagePullPolicy: Always
          ports:
            - containerPort: 80

---
apiVersion: v1
kind: Service
metadata:
  name: ordertrackingapi-service
  labels:
    name: ordertrackingapiService
spec:
  ports:
    - port: 80
      targetPort: 80
  selector:
    run: ordertrackingapi-pod
