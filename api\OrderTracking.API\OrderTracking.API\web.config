﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>

  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\OrderTracking.API.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
      <security>
        <requestFiltering>
          <requestLimits maxQueryString="8192"/>
        </requestFiltering>
      </security>
    </system.webServer>
  </location>

</configuration>