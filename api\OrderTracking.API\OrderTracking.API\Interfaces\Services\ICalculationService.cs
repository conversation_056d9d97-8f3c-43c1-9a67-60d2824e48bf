﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using OrderTracking.API.Models;

namespace OrderTracking.API.Interfaces
{
    /// <summary>
    ///     Interface for EquipmentRequestService
    /// </summary>
    public interface ICalculationService
    {
        /// <summary>
        ///     Add a Calculation
        /// </summary>
        /// <param name="equipmentRequest"></param>
        /// <returns"></returns>
        Task<string> AddItemAsync(JObject calculation);

        /// <summary>
        ///     Delete a Calculation.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="pk"></param>
        /// <returns></returns>
        Task DeleteItemAsync(string id, string pk);

        /// <summary>
        ///     Get an Calculation.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<dynamic> GetItemAsync(string id);

        /// <summary>
        ///     Get all Calculations.
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<dynamic>> GetItemsAsync();

        /// <summary>
        ///     Update an Calculation.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="equipmentRequest"></param>
        /// <returns></returns>
        Task UpdateItemAsync(string id, JObject equipmentRequest);

        /// <summary>
        ///     Delete multiple calculations
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task DeleteItemsAsync(string[] ids);
    }
}
