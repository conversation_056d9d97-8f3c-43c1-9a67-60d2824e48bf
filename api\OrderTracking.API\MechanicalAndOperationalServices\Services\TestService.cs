
﻿using System.Linq;
using ClientPortal.Shared.Models.MOS;
using Microsoft.Extensions.Logging;

namespace MechanicalAndOnStreamServices.Services
{
    public class TestService : ITestService
    {
        private readonly MOSContext _db;
        private readonly ILogger<TestService> _logger;

        public TestService(ILogger<TestService> logger, MOSContext db)
        {
            _logger = logger;
            _db = db;
        }

        public void DisplaySummary()
        {
            _logger.LogInformation($"Data sheets: {_db.DataSheets.Count()}");
            _logger.LogInformation($"Data sheet packages: {_db.DataSheetPackages.Count()}");
        }
    }

    public interface ITestService
    {
        void DisplaySummary();
    }
}