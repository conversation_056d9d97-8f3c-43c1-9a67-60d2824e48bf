<app-work-orders-grid [allowEditing]="allowEditing$ | async"
                      (selectionChanged)="onSelectionChanged($event)">
</app-work-orders-grid>
<app-assets-details [workOrder]="selectedWorkOrder$ | async"
                    [project]="project$ | async"
                    [location]="location$ | async"
                    [users]="users$ | async"
                    [loading]="loading$ | async"
                    (woDetailsSave)="onWODetailsSave($event)"
                    (addTask)="taskAdded($event)"
                    (updateTask)="taskUpdated($event)"
                    (projectSave)="onProjectSave($event)">
</app-assets-details>
