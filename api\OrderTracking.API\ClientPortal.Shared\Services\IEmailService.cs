﻿using System.Collections.Generic;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using OrderTracking.API.Models;
using SendGrid;

namespace ClientPortal.Shared.Services
{
    /// <summary>
    ///     Service for sending emails
    /// </summary>
    public interface IEmailService
    {
        /// <summary>
        ///     Send an email to 1 or more recipients
        /// </summary>
        /// <param name="emailData"></param>
        Task<Response> SendEmail(EmailData emailData);

        /// <summary>
        ///     Send an email to 1 or more recipients with Priority set to urgent and Importance set to high.
        /// </summary>
        /// <param name="recipients"></param>
        /// <param name="subject"></param>
        /// <param name="htmlContent"></param>
        /// <returns></returns>
        Task<Response> SendUrgentEmail(IReadOnlyCollection<IEmailRecipient> recipients, string subject,
            string htmlContent);
    }
}