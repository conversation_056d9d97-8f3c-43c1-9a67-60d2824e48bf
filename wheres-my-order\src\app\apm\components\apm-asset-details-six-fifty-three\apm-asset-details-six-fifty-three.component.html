<div *ngIf="!assetDetails">
    <dx-load-indicator style="position: relative; left: 50%;"
                       id="large-indicator"
                       height="60"
                       width="60"></dx-load-indicator>
</div>
<div *ngIf="assetDetails"
     class="content-block repsonse-paddings">
    <dx-form #form
             [formData]="assetDetails"
             [readOnly]="(isEditing$ | async) === false || !allowEditing"
             (onFieldDataChanged)="onFieldDataChanged($event)">
        <dxi-item itemType="group"
                  caption="Identification"
                  [colCount]="3">
            <dxi-item dataField="name"></dxi-item>
            <dxi-item dataField="numberOrId">
                <dxo-label text="Number or ID"></dxo-label>
            </dxi-item>
            <dxi-item dataField="assetType"></dxi-item>
            <dxi-item dataField="equipmentDescription"></dxi-item>
            <dxi-item dataField="lastKnownInspectionDate"
                      editorType="dxDateBox"></dxi-item>
            <dxi-item dataField="location"
                      editorType="dxSelectBox"
                      [editorOptions]="{items: overallLocationOptions}">
            </dxi-item>
            <dxi-item dataField="latitude"
                      editorType="dxNumberBox">
                <dxi-validation-rule type="custom"
                                     [reevaluate]="true"
                                     [validationCallback]="validateGIS"
                                     message="Both latitude and longitude must be provided">
                </dxi-validation-rule>
                <dxi-validation-rule type="compare"
                                     comparisonType="<="
                                     [comparisonTarget]="positiveLatitude"
                                     message="Latitude must be less than or equal to 90">
                </dxi-validation-rule>
                <dxi-validation-rule type="compare"
                                     comparisonType=">="
                                     [comparisonTarget]="negativeLatitude"
                                     message="Latitude must be greater than or equal to -90">
                </dxi-validation-rule>
            </dxi-item>
            <dxi-item dataField="longitude"
                      editorType="dxNumberBox">
                <dxi-validation-rule type="custom"
                                     [reevaluate]="true"
                                     [validationCallback]="validateGIS"
                                     message="Both latitude and longitude must be provided">
                </dxi-validation-rule>
                <dxi-validation-rule type="compare"
                                     comparisonType="<="
                                     [comparisonTarget]="positiveLongitude"
                                     message="Longitude must be less than or equal to 180">
                </dxi-validation-rule>
                <dxi-validation-rule type="compare"
                                     comparisonType=">="
                                     [comparisonTarget]="negativeLongitude"
                                     message="Longitude must be greater than or equal to -180">
                </dxi-validation-rule>
            </dxi-item>
        </dxi-item>
        <dxi-item itemType="group"
                  caption="General Information">
            <dxi-item itemType="group"
                      caption="Photos">
                <dxi-item>
                    <dxo-label text="Front"></dxo-label>
                    <div *dxTemplate>
                        <dx-tile-view [items]="assetDetails?.frontPhotos"
                                      [baseItemHeight]="120"
                                      [baseItemWidth]="120"
                                      [itemMargin]="10"
                                      [direction]="'horizontal'"
                                      [noDataText]="'No photos in this section'"
                                      [height]="(assetDetails?.frontPhotos?.length > 0 ? 180 : 40)"
                                      (onItemClick)="onItemClick($event)">
                            <div *dxTemplate="let galleryItem of 'item'">
                                <div *ngIf="galleryItem?.blobName"
                                     class="image-tile">
                                    <img class="tile-image"
                                         *ngIf="assetPathLoadingCompleted"
                                         [src]="getAssetImage('frontPhotos',galleryItem.blobName)">
                                </div>
                            </div>
                        </dx-tile-view>
                    </div>
                </dxi-item>
                <dxi-item>
                    <dxo-label text="Back"></dxo-label>
                    <div *dxTemplate>
                        <dx-tile-view [items]="assetDetails?.backPhotos"
                                      [baseItemHeight]="120"
                                      [baseItemWidth]="120"
                                      [itemMargin]="10"
                                      [direction]="'horizontal'"
                                      [noDataText]="'No photos in this section'"
                                      [height]="(assetDetails.backPhotos?.length > 0 ? 180 : 40)"
                                      (onItemClick)="onItemClick($event)">
                            <div *dxTemplate="let galleryItem of 'item'">
                                <div *ngIf="galleryItem?.blobName"
                                     class="image-tile">
                                    <img [height]="150"
                                         class="tile-image"
                                         *ngIf="assetPathLoadingCompleted"
                                         [src]="getAssetImage('backPhotos',galleryItem.blobName)">
                                </div>
                            </div>
                        </dx-tile-view>
                    </div>
                </dxi-item>
                <dxi-item>
                    <dxo-label text="Left"></dxo-label>
                    <div *dxTemplate>
                        <dx-tile-view #leftPhotos
                                      [items]="assetDetails?.leftPhotos"
                                      [baseItemHeight]="120"
                                      [baseItemWidth]="120"
                                      [itemMargin]="10"
                                      [direction]="'horizontal'"
                                      [noDataText]="'No photos in this section'"
                                      [height]="(assetDetails.leftPhotos?.length > 0 ? 180 : 40)"
                                      (onItemClick)="onItemClick($event)">
                            <div *dxTemplate="let galleryItem of 'item'">
                                <div *ngIf="galleryItem?.blobName"
                                     class="image-tile">
                                    <img class="tile-image"
                                         *ngIf="assetPathLoadingCompleted"
                                         [src]="getAssetImage('leftPhotos',galleryItem.blobName)">
                                </div>
                            </div>
                        </dx-tile-view>
                    </div>
                </dxi-item>
                <dxi-item>
                    <dxo-label text="RIght"></dxo-label>
                    <div *dxTemplate>
                        <dx-tile-view [items]="assetDetails?.rightPhotos"
                                      [baseItemHeight]="120"
                                      [baseItemWidth]="120"
                                      [itemMargin]="10"
                                      [direction]="'horizontal'"
                                      [noDataText]="'No photos in this section'"
                                      [height]="(assetDetails.rightPhotos?.length > 0 ? 180 : 40)"
                                      (onItemClick)="onItemClick($event)">
                            <div *dxTemplate="let galleryItem of 'item'">
                                <div *ngIf="galleryItem?.blobName"
                                     class="image-tile">
                                    <img [height]="150"
                                         class="tile-image"
                                         *ngIf="assetPathLoadingCompleted"
                                         [src]="getAssetImage('rightPhotos',galleryItem.blobName)">
                                </div>
                            </div>
                        </dx-tile-view>
                    </div>
                </dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      caption="Design"
                      [colCount]="3">
                <dxi-item dataField="designCode"></dxi-item>
                <dxi-item dataField="codeYear"></dxi-item>
                <dxi-item dataField="addendum"></dxi-item>
                <dxi-item dataField="maximumFillHeight">
                    <dxo-label text="Maximum Fill Height (ft)"></dxo-label>
                </dxi-item>
                <dxi-item dataField="diameter">
                    <dxo-label text="Diameter (ft)"></dxo-label>
                </dxi-item>
                <dxi-item dataField="height">
                    <dxo-label text="Height (ft)"></dxo-label>
                </dxi-item>
                <dxi-item dataField="tankVolumeInBBL">
                    <dxo-label text="Tank Volumne (BBL)"></dxo-label>
                </dxi-item>
                <dxi-item dataField="constructionMethod"
                          editorType="dxTagBox"
                          [editorOptions]="{items: constructionMethods, acceptCustomValue: true}">
                </dxi-item>
                <dxi-item dataField="orientation"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: pos}"></dxi-item>
                <dxi-item dataField="rt"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: rtOptions}">
                    <dxo-label text="RT"></dxo-label>
                </dxi-item>
                <dxi-item dataField="installationDate"
                          editorType="dxDateBox"></dxi-item>
                <dxi-item dataField="inServiceDate"
                          editorType="dxDateBox"></dxi-item>
                <dxi-item dataField="pAndIdNumber">
                    <dxo-label text="P&ID Number"></dxo-label>
                </dxi-item>
                <dxi-item dataField="constructionDesignDrawingNumber">
                </dxi-item>
                <dxi-item dataField="lowestFlangeRating"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: lowestFlangeItems}">
                </dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      caption="Service"
                      [colCount]="3">
                <dxi-item dataField="serviceProductContents">
                    <dxo-label text="Service/Product/Contents"></dxo-label>
                </dxi-item>
                <dxi-item dataField="specificGravity"></dxi-item>
                <dxi-item dataField="intendedService"></dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      caption="Inspection Openings">
                <dxi-item itemType="group"
                          *ngFor="let opening of assetDetails.inspectionOpenings; let i = index"
                          [colCount]="3">
                    <dxi-item
                              [dataField]="'inspectionOpenings[' + i + '].type'">
                        <dxo-label text="Type"></dxo-label>
                    </dxi-item>
                    <dxi-item
                              [dataField]="'inspectionOpenings[' + i + '].number'">
                        <dxo-label text="Number"></dxo-label>
                    </dxi-item>
                    <dxi-item
                              [dataField]="'inspectionOpenings[' + i + '].size'">
                        <dxo-label text="Size"></dxo-label>
                    </dxi-item>
                </dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      caption="Inspection"
                      [colCount]="3">
                <dxi-item dataField="inspectionCode"></dxi-item>
                <dxi-item dataField="inspectionYear"></dxi-item>
                <dxi-item dataField="inspectionAddendum"></dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      caption="Data Plate"
                      [colCount]="3">
                <dxi-item dataField="dataPlateAttached"
                          editorType="dxCheckBox"></dxi-item>
                <dxi-item dataField="dataPlateLegible"
                          editorType="dxCheckBox"></dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      caption="Manufacturer"
                      [colCount]="3">
                <dxi-item dataField="manufacturerName">
                    <dxo-label text="Name"></dxo-label>
                </dxi-item>
                <dxi-item dataField="manufacturerDate"
                          editorType="dxDateBox">
                    <dxo-label text="Date"></dxo-label>
                </dxi-item>
                <dxi-item dataField="manufacturerSerialNumber">
                    <dxo-label text="Serial Number"></dxo-label>
                </dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      caption="Repairs and Alterations"
                      [colCount]="3">
                <dxi-item dataField="repairOrAlterationPlateLegible"
                          editorType="dxCheckBox"></dxi-item>
                <dxi-item dataField="hasRepairOrAlterationPlate"
                          editorType="dxCheckBox"></dxi-item>
            </dxi-item>

            <dxi-item itemType="group"
                      caption="Repairs">
                <dxi-item itemType="group"
                          *ngFor="let repair of assetDetails.repairs; let i = index">
                    <dxi-item [dataField]="'repairs[' + i + '].dateRepairedOrAltered'"
                              editorType="dxDateBox">
                        <dxo-label text="Date Repaired or Altered"></dxo-label>
                    </dxi-item>
                    <dxi-item
                              [dataField]="'repairs[' + i + '].repairAlterationOrganization'">
                        <dxo-label text="Repair Alteration/Organization">
                        </dxo-label>
                    </dxi-item>
                    <dxi-item
                              [dataField]="'repairs[' + i + '].purposeOfRepairAlteration'">
                        <dxo-label text="Purpose of Repair Alteration">
                        </dxo-label>
                    </dxi-item>
                </dxi-item>
            </dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  caption="Operating/Design Conditions">
            <dxi-item itemType="group"
                      [colCount]="3">
                <dxi-item dataField="currentService"></dxi-item>
                <dxi-item dataField="designTemperature"></dxi-item>
                <dxi-item dataField="currentOperatingTemperature"></dxi-item>
                <dxi-item dataField="currentFillLevel">
                    <dxo-label text="Current Fill Level if available">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="operationStatus"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: operationStatus}"></dxi-item>
                <dxi-item dataField="tankEquippedWithVRU"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: yesNoUnknownItems}">
                </dxi-item>
                <dxi-item dataField="tankEquippedWithLeakDetection"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: yesNoNaItems}"></dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      caption="Tank Out-of-Service Requirements"
                      [colCount]="3">
                <dxi-item dataField="tankOutOfService"
                          editorType="dxSelectBox"
                          [editorOptions]="{items: yesNoNaItems}">
                    <dxo-label
                               text="Is the tank Out-Of-Service In accordance with Jurisdictional requirements?">
                    </dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      caption="Regulatory Requirements">
                <dxi-item itemType="group"
                          *ngFor="let requirement of assetDetails.regulatoryRequirements; let i = index"
                          [colCount]="3">
                    <dxi-item
                              [dataField]="'regulatoryRequirements[' + i + '].jurisdictionRegulatoryAgency'">
                        <dxo-label text="Jurisdiction-Regulatory agency">
                        </dxo-label>
                    </dxi-item>
                </dxi-item>
            </dxi-item>
        </dxi-item>
        <dxi-item itemType="group"
                  caption="Components">
            <dxi-item itemType="group"
                      caption="Shell Courses">
                <dxi-item itemType="group"
                          *ngFor="let shellCourse of assetDetails.shellCourses; let i = index"
                          [colCount]="3">
                    <dxi-item [dataField]="'shellCourses[' + i + '].number'"
                              editorType="dxTextBox">
                        <dxo-label text="Number"></dxo-label>
                    </dxi-item>
                    <dxi-item
                              [dataField]="'shellCourses[' + i + '].materialSpecAndGrade'">
                        <dxo-label text="Material Spec and Grade"></dxo-label>
                    </dxi-item>
                    <dxi-item [dataField]="'shellCourses[' + i + '].allowableStressAtTemp'"
                              editorType="dxNumberBox">
                        <dxo-label text="Allowable Stress at Temperature (psi)">
                        </dxo-label>
                    </dxi-item>
                    <dxi-item [dataField]="'shellCourses[' + i + '].nominalThickness'"
                              editorType="dxNumberBox">
                        <dxo-label text="Nominal Thickness (in.)"></dxo-label>
                    </dxi-item>
                    <dxi-item [dataField]="'shellCourses[' + i + '].corrosionAllowance'"
                              editorType="dxNumberBox">
                        <dxo-label text="Corrosion Allowance (in.)"></dxo-label>
                    </dxi-item>
                    <dxi-item [dataField]="'shellCourses[' + i + '].lengthOrHeight'"
                              editorType="dxNumberBox">
                        <dxo-label text="Length or Height (ft)"></dxo-label>
                    </dxi-item>
                    <dxi-item [dataField]="'shellCourses[' + i + '].jointEfficiency'"
                              editorType="dxNumberBox">
                        <dxo-label text="Joint Efficiency"></dxo-label>
                    </dxi-item>
                </dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      caption="Tank Bottom (Floor)"
                      [colCount]="3">
                <dxi-item dataField="tankFloorType"></dxi-item>
                <dxi-item dataField="tankFloorMaterialSpecAndGrade"></dxi-item>
                <dxi-item dataField="tankFloorAnnularRingNominalThickness"
                          editorType="dxNumberBox">
                    <dxo-label text="Nominal Thickness (Annular Ring, in.)">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="tankFloorSketchPlatesNominalThickness"
                          editorType="dxNumberBox">
                    <dxo-label text="Nominal Thickness (Sketch Plates, in.)">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="tankFloorInnerPlatesNominalThickness"
                          editorType="dxNumberBox">
                    <dxo-label text="Nominal Thickness (Inner Plates, in.)">
                    </dxo-label>
                </dxi-item>
                <dxi-item dataField="tankFloorCorrosionAllowance"
                          editorType="dxNumberBox">
                    <dxo-label text="Corrosion Allowance (in.)"></dxo-label>
                </dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      caption="Tank Roofs">
                <dxi-item itemType="group"
                          *ngFor="let roof of assetDetails.tankRoofs; let i = index"
                          [colCount]="3">
                    <dxi-item [dataField]="'tankRoofs[' + i + '].type'"
                              editorType="dxTagBox"
                              [editorOptions]="{items: tankTypes}">
                        <dxo-label text="Type"></dxo-label>
                    </dxi-item>
                    <dxi-item
                              [dataField]="'tankRoofs[' + i + '].materialSpecAndGrade'">
                        <dxo-label text="Material Spec and Grade"></dxo-label>
                    </dxi-item>
                    <dxi-item [dataField]="'tankRoofs[' + i + '].nominalThickness'"
                              editorType="dxNumberBox">
                        <dxo-label text="Nominal Thickness (Roof, in.)">
                        </dxo-label>
                    </dxi-item>
                    <dxi-item [dataField]="'tankRoofs[' + i + '].corrosionAllowance'"
                              editorType="dxNumberBox">
                        <dxo-label text="Corrosion Allowance (in.)"></dxo-label>
                    </dxi-item>
                </dxi-item>
            </dxi-item>
            <dxi-item itemType="group"
                      caption="Nozzles">
                <dxi-item itemType="group"
                          *ngFor="let nozzle of assetDetails.nozzles; let i = index"
                          [colCount]="3">
                    <dxi-item [dataField]="'nozzles[' + i + '].number'"
                              editorType="dxTextBox">
                        <dxo-label text="Number"></dxo-label>
                    </dxi-item>
                    <dxi-item [dataField]="'nozzles[' + i + '].type'">
                        <dxo-label text="Type"></dxo-label>
                    </dxi-item>
                    <dxi-item
                              [dataField]="'nozzles[' + i + '].materialSpecAndGrade'">
                        <dxo-label text="Material Spec and Grade"></dxo-label>
                    </dxi-item>
                    <dxi-item [dataField]="'nozzles[' + i + '].pipeSize'"
                              editorType="dxTagBox"
                              [editorOptions]="{items: pipeSizeItems}">
                        <dxo-label text="Pipe Size (in.)"></dxo-label>
                    </dxi-item>
                    <dxi-item [dataField]="'nozzles[' + i + '].pipeSchedule'"
                              editorType="dxTagBox"
                              [editorOptions]="{items: pipeScheduleItems}">
                        <dxo-label text="Pipe Schedule"></dxo-label>
                    </dxi-item>
                    <dxi-item [dataField]="'nozzles[' + i + '].flangeRating'"
                              editorType="dxTagBox"
                              [editorOptions]="{items: flangeRatingItems}">
                        <dxo-label text="Flange Rating"></dxo-label>
                    </dxi-item>
                    <dxi-item
                              [dataField]="'nozzles[' + i + '].reinforcementPadType'">
                        <dxo-label text="Reinforcement Pad Type"></dxo-label>
                    </dxi-item>
                    <dxi-item
                              [dataField]="'nozzles[' + i + '].reinforcementPadDimensions'">
                        <dxo-label text="Reinforcement Pad Dimensions">
                        </dxo-label>
                    </dxi-item>
                    <dxi-item [dataField]="'nozzles[' + i + '].reinforcementPadThickness'"
                              editorType="dxNumberBox">
                        <dxo-label text="Reinforcement Pad Thickness (in.)">
                        </dxo-label>
                    </dxi-item>
                </dxi-item>
            </dxi-item>
        </dxi-item>

        <dxi-item [template]="'buttons'"></dxi-item>

        <div *dxTemplate="let data of 'buttons'"
             class="buttons">
            <dx-button *ngIf="(isEditing$ | async) === false; else saveAndCancel"
                       text="Edit"
                       type="default"
                       [disabled]="!allowEditing"
                       (onClick)="onEditClicked($event)"></dx-button>
            <ng-template #saveAndCancel>
                <dx-button text="Cancel"
                           type="danger"
                           [disabled]="isSaving$ | async"
                           (onClick)="onCancelClicked($event, form)">
                </dx-button>
                <dx-button text="Save"
                           type="success"
                           [disabled]="!allowEditing || (isSaving$ | async)"
                           (onClick)="onSaveClicked($event)"></dx-button>
            </ng-template>
        </div>

    </dx-form>
</div>

<dx-popup [(visible)]="showPhotoPopup"
          [showTitle]="true"
          [showCloseButton]="true"
          [hideOnOutsideClick]="true"
          #popup>
    <dx-gallery #gallery
                id="gallery"
                [dataSource]="allPhotos"
                [loop]="true"
                width="100%"
                height="100%"
                [showNavButtons]="true"
                [showIndicator]="true"
                [(selectedIndex)]="popupGallerySelectedIndex"
                (onContentReady)="onPopupGalleryContentReady($event, popup, gallery)"
                (onSelectionChanged)="onPopupGallerySelectionChanged($event, popup, gallery)">

        <div *dxTemplate="let photoInfo of 'item'"
             class="popup-template">

            <div *ngIf="photoInfo?.photo?.blobName"
                 class="image-container dx-swatch-additional">
                <img *ngIf="assetPathLoadingCompleted"
                     [src]="getAssetImage('rightPhotos',photoInfo.photo.blobName)"
                     alt="">

            </div>
            <div class="info">
                <div class="text-content">
                    <h6>Description</h6>
                    <dx-text-area #descriptionEditor
                                  [readOnly]="(isEditingPhotoDescription$ | async) === false"
                                  [value]="photoInfo?.photo?.description?.currentValue"
                                  [autoResizeEnabled]="true"
                                  [disabled]="!allowEditing">
                    </dx-text-area>
                    <div class="description-buttons">
                        <dx-button *ngIf="(isEditingPhotoDescription$ | async) === false; else saveCancelPhotoDescription"
                                   icon="edit"
                                   text="Edit"
                                   [disabled]="!allowEditing"
                                   (onClick)="onEditDescriptionClicked($event, descriptionEditor.value)">
                        </dx-button>
                        <ng-template #saveCancelPhotoDescription>
                            <dx-button icon="save"
                                       text="Save"
                                       type="success"
                                       [disabled]="!allowEditing"
                                       (onClick)="onDescriptionSave($event, photoInfo, descriptionEditor.value)">
                            </dx-button>
                            <dx-button text="Cancel"
                                       type="danger"
                                       (onClick)="onDescriptionCancel($event, descriptionEditor)">
                            </dx-button>
                        </ng-template>
                    </div>
                </div>
                <div class="buttons">

                    <dx-button icon="remove"
                               text="Delete"
                               type="danger"
                               [disabled]="!allowEditing"
                               (onClick)="onDeletePhotoClicked($event, photoInfo)">
                    </dx-button>

                    <dx-button icon="download"
                               text="Download"
                               type="default"
                               (onClick)="onDownloadClicked(photoInfo?.photo?.blobName)">
                    </dx-button>
                </div>
            </div>
        </div>
    </dx-gallery>
</dx-popup>