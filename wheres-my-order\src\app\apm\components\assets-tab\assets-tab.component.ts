import { Component, Input, ViewChild } from '@angular/core';
import { DxDataGridComponent } from 'devextreme-angular';
import ArrayStore from 'devextreme/data/array_store';
import dxButton, { ClickEvent } from 'devextreme/ui/button';
import {
    EditorPreparingEvent,
    RowInsertedEvent
} from 'devextreme/ui/data_grid';
import { confirm } from 'devextreme/ui/dialog';
import dxForm, { InitializedEvent } from 'devextreme/ui/form';
import { ValueChangedEvent as NumberBoxValueChangedEvent } from 'devextreme/ui/number_box';
import { ValueChangedEvent as SelectBoxValueChangedEvent } from 'devextreme/ui/select_box';
import { ValidationCallbackData } from 'devextreme/ui/validation_rules';
import { ToastrService } from 'ngx-toastr';
import { map } from 'rxjs/operators';
import { isNullOrUndefined } from '../../../shared/helpers';
import { DataGridService } from '../../../shared/services';
import {
    Location,
    ProjectAsset,
    ProjectVm,
    SupportedAssetCategories
} from '../../models';
import { NewAsset } from '../../models/transport-objects/new-asset';
import { ApmService } from '../../services';

@Component({
    selector: 'app-assets-tab',
    templateUrl: './assets-tab.component.html',
    styleUrls: ['./assets-tab.component.scss']
})
export class AssetsTabComponent {
    private _editForm: dxForm;
    private _projectAssets: ProjectAsset[];

    @Input()
    set projectAssets(value: ProjectAsset[]) {
        this._projectAssets = value;
        this.assetsDataSource = new ArrayStore({
            key: 'id',
            data: value
        });
    }
    get projectAssets() {
        return this._projectAssets;
    }

    @Input() allowEditing: boolean;
    @Input() assets: any;
    @Input() selectedProject: ProjectVm;
    @Input() locations: Location[];
    @Input() availableAssets: ProjectAsset[];

    @ViewChild('assetsGrid') assetsGrid: DxDataGridComponent;
    @ViewChild('availableAssetsGrid') availableAssetsGrid: DxDataGridComponent;

    assetsDataSource: ArrayStore;
    removeAssetsButton: dxButton | undefined;
    addAssetPopupVisible = false;
    isPiping = false;
    isAssetAtLocation = false;
    selectedRowKeys: Array<any>;
    createNewAssetDisabled$ = this._apm.selectedBU$.pipe(
        map((bu) => bu === null || bu === undefined)
    );

    readonly assetCategories = SupportedAssetCategories;

    constructor(
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService,
        private readonly _grid: DataGridService
    ) {}

    startLatitudeValidation = (e: ValidationCallbackData) =>
        e.data.startLatitude === undefined
            ? true
            : e.data.startLatitude >= -90 && e.data.startLatitude <= 90;

    startLongitudeValidation = (e: ValidationCallbackData) =>
        e.data.startLongitude === undefined
            ? true
            : e.data.startLongitude >= -180 && e.data.startLongitude <= 180;

    endLatitudeValidation = (e: ValidationCallbackData) =>
        e.data.endLatitude === undefined
            ? true
            : e.data.endLatitude >= -90 && e.data.endLatitude <= 90;

    endLongitudeValidation = (e: ValidationCallbackData) =>
        e.data.endLongitude === undefined
            ? true
            : e.data.endLongitude >= -180 && e.data.endLongitude <= 180;

    validateStartGIS = (e: ValidationCallbackData) =>
        this.validationCallback(e.data.startLatitude, e.data.startLongitude);

    validateEndGIS = (e: ValidationCallbackData) =>
        this.validationCallback(e.data.endLatitude, e.data.endLongitude);

    onEditFormInitialized = (e: InitializedEvent) => {
        this._editForm = e.component;
    };

    createNewAssetClicked = (e: ClickEvent) => {
        this.isAssetAtLocation = false;
        this.assetsGrid.instance.addRow();
    };

    createNewAssetAtLocationClicked = (e: ClickEvent) => {
        this.isAssetAtLocation = true;
        this.assetsGrid.instance.addRow();
    };

    addAssetClicked = (e: ClickEvent) => {
        this.addAssetPopupVisible = true;
    };

    restoreDefaultsClicked = async (e: ClickEvent) => {
        const result = await this._grid.resetGridState(this.assetsGrid);
        if (result) localStorage.removeItem('apmProjectAssetsGridState');
    };

    onEditorPreparing(e: EditorPreparingEvent) {
        if (e.parentType == 'dataRow') {
            if (
                e.dataField === 'startLatitude' ||
                e.dataField === 'startLongitude' ||
                e.dataField === 'endLatitude' ||
                e.dataField === 'endLongitude'
            ) {
                e.editorOptions.onValueChanged = (
                    args: NumberBoxValueChangedEvent
                ) => {
                    // Supposedly necessary to give the form enough time to set values
                    // before validating the whole form again.  We are doing this because
                    // our validation depends on validating multiple fields at once, so
                    // I want to make sure that all the values I expect to be there during
                    // validation are indeed there.
                    setTimeout(() => this._editForm.validate());
                    e.setValue(args.value);
                };
            }
            if (e.dataField == 'category') {
                e.editorOptions.onValueChanged = (
                    args: SelectBoxValueChangedEvent
                ) => {
                    this.isPiping = args?.value === 'Piping';
                    e.setValue(args?.value);
                };
            }
        }
    }

    editCancelled(e) {
        this.isAssetAtLocation = false;
    }

    onRowInserted(e: RowInsertedEvent) {
        let payload: NewAsset;

        payload = {
            assetId: e.data.assetId,
            assetName: e.data.assetName,
            category: e.data.category,
            startLat: e.data.startLatitude,
            startLong: e.data.startLongitude,
            endLat: e.data.endLatitude,
            endLong: e.data.endLongitude,
            locationId: e.data.locationId,
            projectId: this.selectedProject.id,
            id: ''
        };

        this._apm.postAsset(payload).subscribe(() => {
            this.isAssetAtLocation = false;
            this._toasts.success(
                'Asset successfully saved',
                'Save successful!'
            );
            this.assetsGrid.instance.refresh();
        });
    }

    addSelectedAssetsClicked(e) {
        const keysToAdd = this.availableAssetsGrid.instance
            .getSelectedRowKeys()
            .map((asset) => asset.id);
        this._apm
            .addRemoveProjectAsset(
                {
                    assetsToAdd: keysToAdd,
                    assetsToRemove: [],
                    projectId: this.selectedProject.id
                },
                this.selectedProject.locationId
            )
            .subscribe();

        this.availableAssetsGrid.instance.deselectAll();
        this.addAssetPopupVisible = false;
    }

    cancelAddAssetClicked(e) {
        this.availableAssetsGrid.instance.deselectAll();
        this.addAssetPopupVisible = false;
    }

    displayPipingGIS(asset: ProjectAsset) {
        return `${asset?.startLatitude}, ${asset?.startLongitude} : ${asset?.endLatitude}, ${asset?.endLongitude}`;
    }

    displayGIS(asset: ProjectAsset) {
        return `${asset?.startLatitude}, ${asset?.startLongitude}`;
    }

    removeAssetsFromProject = async (e: ClickEvent) => {
        const response = await confirm(
            'Are you sure you want to remove the selected assets from the project?',
            'Are you sure?'
        );
        if (response) {
            const keysToRemove = this.assetsGrid.instance.getSelectedRowKeys();
            keysToRemove.forEach((key) => {
                this.assetsDataSource.remove(key);
            });

            this._apm
                .addRemoveProjectAsset(
                    {
                        assetsToAdd: [],
                        assetsToRemove: keysToRemove,
                        projectId: this.selectedProject.id
                    },
                    this.selectedProject.locationId
                )
                .subscribe(() => {
                    this._toasts.success(
                        `Asset successfully removed from project ${this.selectedProject.name}`,
                        'Asset Removed'
                    );
                    this.assetsGrid.instance.refresh();
                });
        }
    };

    private validationCallback(latitude: number, longitude: number) {
        if (isNullOrUndefined(latitude) && isNullOrUndefined(longitude))
            return true;
        if (isNullOrUndefined(latitude) && !isNullOrUndefined(longitude))
            return false;
        if (!isNullOrUndefined(latitude) && isNullOrUndefined(longitude))
            return false;
        return true;
    }
}
