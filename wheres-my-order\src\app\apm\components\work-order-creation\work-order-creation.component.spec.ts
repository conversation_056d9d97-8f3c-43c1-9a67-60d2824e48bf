import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxFormModule } from 'devextreme-angular';
import { ApmService } from '../../services';
import { WorkOrderCreationComponent } from './work-order-creation.component';

describe('WorkOrderCreationComponent', () => {
    let component: WorkOrderCreationComponent;
    let fixture: ComponentFixture<WorkOrderCreationComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DxFormModule],
            declarations: [WorkOrderCreationComponent],
            providers: [{ provide: ApmService, useValue: {} }]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(WorkOrderCreationComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
