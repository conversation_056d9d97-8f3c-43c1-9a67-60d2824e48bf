{"ConnectionStrings": {"Source": "Server=.;Initial Catalog=orders-dump;Integrated Security=True", "Target": "Server=.;Initial Catalog=orders-devteststg;Integrated Security=True"}, "ApplicationInsights": {"InstrumentationKey": "95205a62-1384-45d2-bc18-d68987f479af"}, "ZDapperPlus": {"LicenseName": "4647;701-teaminc.com", "LicenseKey": "***REMOVED***"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "SendGrid": {"APIKey": "***REMOVED***"}, "Emails": {"Support": [{"Email": "<EMAIL>", "GivenName": "<PERSON>", "Surname": "<PERSON><PERSON><PERSON><PERSON>"}]}}