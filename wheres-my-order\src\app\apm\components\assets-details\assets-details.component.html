<div class="dx-card responsive-paddings content-block">
    <div *ngIf="loading">
        <dx-load-indicator style="position: relative; left: 50%;"
                           id="large-indicator"
                           height="60"
                           width="60"></dx-load-indicator>
    </div>
    <dx-tab-panel [items]="tabs"
                  [disabled]="!workOrder">
        <div class="responsive-paddings"
             *dxTemplate="let data of 'project-details'">
            <app-details-tab [project]="project | projectDetail : location"
                             [allowEditing]="allowEditing$ | async"
                             (projectUpdated)="onProjectUpdated($event)">
            </app-details-tab>
        </div>
        <div class="responsive-paddings"
             *dxTemplate="let data of 'details'">
            <app-wo-details-tab [woDetail]="workOrder | woDetail : project"
                                (save)="onWODetailsSave($event)"
                                [allowEditing]="allowEditing$ | async">
            </app-wo-details-tab>
        </div>
        <div class="responsive-paddings"
             *dxTemplate="let data of 'tasks'">
            <app-tasks-tab [workOrder]="workOrder"
                           [availableUsers]="users | userEmails"
                           (newTask)="newTask($event)"
                           (taskUpdate)="taskUpdate($event)">
            </app-tasks-tab>
        </div>
    </dx-tab-panel>
</div>
