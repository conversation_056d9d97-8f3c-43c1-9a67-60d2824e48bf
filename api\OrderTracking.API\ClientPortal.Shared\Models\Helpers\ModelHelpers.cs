﻿using System.Linq;
using System.Reflection;
using Newtonsoft.Json;

namespace ClientPortal.Shared.Models.Helpers
{
    public static class ModelHelpers
    {
        /// <summary>
        /// Retrieves the name given to the <see cref="JsonPropertyAttribute"/> for a class'
        /// property.  Pass the property name of the class like:
        /// <code>ModelHelpers.JsonPropertyName&lt;MyModel&gt;(nameof(MyModel.PropertyOne))</code>
        /// </summary>
        /// <param name="propertyName"></param>
        /// <returns>Name of JsonPropertyAttribute's PropertyName, or null if it doesn't exist</returns>
        public static string JsonPropertyName<T>(string propertyName)
        {
            var jsonProperty = typeof(T)
                .GetProperties()
                .First(p => p.Name == propertyName)
                .GetCustomAttribute<JsonPropertyAttribute>();
            return jsonProperty != null
                ? jsonProperty.PropertyName
                : propertyName;
        }
    }
}