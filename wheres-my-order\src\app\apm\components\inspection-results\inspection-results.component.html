<div class="responsive-paddings content-block">
    <div *ngIf="!inspectionResult">
        <dx-load-indicator style="position: relative; left: 50%;"
                           id="large-indicator"
                           height="60"
                           width="60"></dx-load-indicator>
    </div>
    <div *ngIf="inspectionResult">

        <div class="button-container">
            <dx-button #expandCollapseButton
                       [stylingMode]="'text'"
                       [text]="'Collapse All'"
                       (onClick)="toggleExpandCollapseAll($event, accordion)">
            </dx-button>
        </div>

        <!-- Accordion with changes for 653 inspection forms -->
        <dx-accordion #accordion
                      [items]="sections"
                      [multiple]="true"
                      [collapsible]="true"
                      (onOptionChanged)="onOptionChanged($event)"
                      (onSelectionChanged)="onSelectionChanged(accordion, expandCollapseButton)">

            <div *dxTemplate="let section of 'title'"
                 style="font-size: inherit">
                {{section.disabled ? section.title + ' - Not Applicable' :
                section.title}}
            </div>

            <div *dxTemplate="let section of 'item'">
                <div *ngFor="let question of section.questions; let i = index">
                    <div class="question">
                        <div class="question-content">
                            <strong>{{question.text}}</strong>
                            <span class="response"
                                  *ngIf="question.attributeType === 'PredefinedValue' || question.attributeType === 'Boolean'">
                                <span>Response:</span>
                                <dx-select-box [items]="question.options"
                                               displayExpr="value"
                                               valueExpr="value"
                                               [(value)]="question.response"
                                               [readOnly]="!isEditing || !allowEditing">
                                </dx-select-box>
                            </span>
                            <span class="response"
                                  *ngIf="question.attributeType === 'MultiPredefinedValue'">
                                <span>Response:</span>
                                <dx-tag-box [items]="question.options"
                                            [showSelectionControls]="true"
                                            applyValueMode="useButtons"
                                            displayExpr="value"
                                            valueExpr="value"
                                            [(value)]="question.response"
                                            [readOnly]="!isEditing || !allowEditing">
                                </dx-tag-box>
                            </span>
                            <span class="comments"
                                  *ngIf="question.hasOtherOption && question.response?.includes('Other')">
                                <span>Other:</span>
                                <dx-text-box dataField="other"
                                             [(value)]="question.other"
                                             [readOnly]="!isEditing || !allowEditing"></dx-text-box>
                            </span>
                            <span class="comments">
                                <span>Comments:</span>
                                <dx-text-area [(value)]="question.comments"
                                              [readOnly]="!isEditing || !allowEditing">
                                </dx-text-area>
                            </span>
                        </div>

                        <dx-gallery #thumbnailGallery
                                    [dataSource]="question.photos"
                                    [showNavButtons]="true"
                                    [height]="150"
                                    noDataText="No Photos"
                                    (dblclick)="thumbnailDoubleClicked($event, section, question, thumbnailGallery)">
                            <div *dxTemplate="let galleryItem of 'item'">
                                <div *ngIf="galleryItem.blobName">
                                    <img [height]="150"
                                         *ngIf="assetPathLoadingCompleted"
                                         [src]="getAssetImage('questionPhotos',galleryItem.blobName)">
                                </div>
                            </div>
                        </dx-gallery>
                    </div>
                </div>

                <div *ngIf="section.subSections?.length !== 0">
                    <dx-accordion #accordion
                                  [items]="section.subSections"
                                  [multiple]="true"
                                  [collapsible]="true"
                                  (onOptionChanged)="onOptionChanged($event)"
                                  (onSelectionChanged)="onSelectionChanged(accordion, expandCollapseButton)">

                        <div *dxTemplate="let subSection of 'title'"
                             style="font-size: inherit">
                            {{subSection.disabled ? subSection.title + '
                            -NotApplicable':subSection.title}}
                        </div>

                        <div *dxTemplate="let subSection of 'item'">
                            <div
                                 *ngFor="let question of subSection.questions; let i = index">
                                <div class="question">
                                    <div class="question-content">
                                        <strong>{{question.text}}</strong>

                                        <div class="gallery-wrapper"
                                             *ngIf="question.attributeType === 'Photo'">
                                            <dx-gallery #thumbnailGallery
                                                        [dataSource]="question.photos"
                                                        [showNavButtons]="true"
                                                        [height]="150"
                                                        noDataText="No Photos"
                                                        (dblclick)="thumbnailDoubleClicked($event, section, question, thumbnailGallery)">
                                                <div
                                                     *dxTemplate="let galleryItem of 'item'">
                                                    <div
                                                         *ngIf="galleryItem.blobName">
                                                        <img [height]="150"
                                                             *ngIf="assetPathLoadingCompleted"
                                                             [src]="getAssetImage('subSectionQuestionPhotos',galleryItem.blobName)">
                                                    </div>
                                                </div>
                                            </dx-gallery>
                                        </div>

                                        <div
                                             *ngIf="question.attributeType === 'Coordinate'">
                                            <span class="comments">
                                                <span>Latitude:</span>
                                                <dx-number-box dataField="latitude"
                                                               [(value)]="question.latitude"
                                                               [readOnly]="!isEditing || !allowEditing"></dx-number-box>
                                            </span>
                                            <span class="comments">
                                                <span>Longitude:</span>
                                                <dx-number-box dataField="longitude"
                                                               [(value)]="question.longitude"
                                                               [readOnly]="!isEditing || !allowEditing"></dx-number-box>
                                            </span>
                                        </div>

                                        <span class="response"
                                              *ngIf="question.attributeType === 'PredefinedValue' || question.attributeType === 'Boolean'">
                                            <span>Response:</span>
                                            <dx-select-box [items]="question.options"
                                                           displayExpr="value"
                                                           valueExpr="value"
                                                           [(value)]="question.response"
                                                           [readOnly]="!isEditing || !allowEditing">
                                            </dx-select-box>
                                        </span>
                                        <span class="response"
                                              *ngIf="question.attributeType === 'MultiPredefinedValue'">
                                            <span>Response:</span>
                                            <dx-tag-box [items]="question.options"
                                                        [showSelectionControls]="true"
                                                        applyValueMode="useButtons"
                                                        displayExpr="value"
                                                        valueExpr="value"
                                                        [(value)]="question.response"
                                                        [readOnly]="!isEditing || !allowEditing">
                                            </dx-tag-box>
                                        </span>
                                        <span class="comments"
                                              *ngIf="question.hasOtherOption && question.response?.includes('Other')">
                                            <span>Other:</span>
                                            <dx-text-box dataField="other"
                                                         [(value)]="question.other"
                                                         [readOnly]="!isEditing || !allowEditing"></dx-text-box>
                                        </span>
                                        <span class="comments">
                                            <span>Comments:</span>
                                            <dx-text-area [(value)]="question.comments"
                                                          [readOnly]="!isEditing || !allowEditing">
                                            </dx-text-area>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </dx-accordion>
                </div>
            </div>
        </dx-accordion>

        <div class="button-container"
             *ngIf="inspectionResult.sections?.length > 0">
            <dx-button *ngIf="isEditing"
                       [type]="'success'"
                       [text]="'Save'"
                       [disabled]="!allowEditing || isSaving"
                       (onClick)="onSaveClicked()"></dx-button>
            <dx-button *ngIf="isEditing"
                       [type]="'error'"
                       [text]="'Cancel'"
                       [disabled]="isSaving"
                       (onClick)="onCancelClicked()"></dx-button>
            <dx-button *ngIf="!isEditing"
                       [type]="'default'"
                       [text]="'Edit'"
                       [disabled]="!allowEditing"
                       (onClick)="onEditClicked()"></dx-button>
        </div>
    </div>

</div>

<dx-popup [(visible)]="showPhotoPopup"
          [showTitle]="true"
          [showCloseButton]="true"
          [hideOnOutsideClick]="true"
          #popup>
    <dx-gallery #gallery
                id="gallery"
                [dataSource]="allPhotos"
                [loop]="true"
                width="100%"
                height="100%"
                [showNavButtons]="true"
                [showIndicator]="true"
                [(selectedIndex)]="popupGallerySelectedIndex"
                (onContentReady)="onPopupGalleryContentReady($event, popup, gallery)"
                (onSelectionChanged)="onPopupGallerySelectionChanged($event, popup, gallery)">

        <div *dxTemplate="let photoInfo of 'item'"
             class="popup-template">

            <div *ngIf="photoInfo?.photo?.blobName"
                 class="image-container">
                <img *ngIf="assetPathLoadingCompleted"
                     [src]="getAssetImage('allPhotos',photoInfo.photo.blobName)"
                     alt="">

            </div>
            <div class="info">
                <div class="text-content">
                    <p class="question">{{photoInfo.question}}</p>
                    <p class="response">Response: {{photoInfo.response}}</p>
                    <p class="comments">Comments: {{photoInfo.comments}}</p>
                    <h6>Description</h6>
                    <dx-text-area #descriptionEditor
                                  [readOnly]="!isEditingPhotoDescription || !allowEditing"
                                  [value]="photoInfo?.photo?.description?.currentValue"
                                  [autoResizeEnabled]="true">
                    </dx-text-area>
                    <div class="description-buttons">
                        <dx-button *ngIf="!isEditingPhotoDescription; else saveCancelPhotoDescription"
                                   icon="edit"
                                   text="Edit"
                                   [disabled]="!allowEditing"
                                   (onClick)="onEditDescriptionClicked($event, photoInfo?.photo?.description?.currentValue)">
                        </dx-button>
                        <ng-template #saveCancelPhotoDescription>
                            <dx-button icon="save"
                                       text="Save"
                                       type="success"
                                       [disabled]="!allowEditing"
                                       (onClick)="onDescriptionSave($event, photoInfo, descriptionEditor.value)">
                            </dx-button>
                            <dx-button text="Cancel"
                                       type="danger"
                                       (onClick)="onDescriptionCancel($event, descriptionEditor)">
                            </dx-button>
                        </ng-template>
                    </div>
                </div>
                <div class="buttons">

                    <dx-button icon="remove"
                               text="Delete"
                               type="danger"
                               (onClick)="onDeletePhotoClicked($event, photoInfo)">
                    </dx-button>

                    <dx-button *ngIf="assetPathLoadingCompleted"
                               icon="download"
                               text="Download"
                               type="default"
                               (onClick)="onDownloadClicked(photoInfo?.photo?.blobName)">
                    </dx-button>
                </div>
            </div>
        </div>
    </dx-gallery>
</dx-popup>