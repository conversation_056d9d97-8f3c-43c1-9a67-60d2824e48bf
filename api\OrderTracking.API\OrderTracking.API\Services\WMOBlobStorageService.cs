﻿//using System;
//using Microsoft.Extensions.Logging;
//using Microsoft.Extensions.Options;
//using OrderTracking.API.Interfaces;
//using OrderTracking.API.Models;

//namespace OrderTracking.API.Services
//{
//    /// <summary>
//    ///     Service class that provides access to files stored in Azure Blob Storage, configured for WMO
//    /// </summary>
//    public class WMOBlobStorageService : BlobStorageService, IWMOBlobStorageService
//    {
//        /// <summary>
//        ///     Constructs a WMOBlobStorageService, injecting a logger and blob storage configuration
//        /// </summary>
//        /// <param name="logger"></param>
//        /// <param name="options"></param>
//        // ReSharper disable once SuggestBaseTypeForParameter
//        public WMOBlobStorageService(ILogger<WMOBlobStorageService> logger, IOptions<BlobStorage> options) : base(options, logger)
//        {
//            if (options == null) throw new ArgumentNullException(nameof(options));

//            ContainerClient = ServiceClient.GetBlobContainerClient(options.Value.WMOContainer) ??
//                              ServiceClient.CreateBlobContainer(options.Value.WMOContainer);

//            logger.LogInformation("Created ContainerClient for WMO");
//        }
//    }
//}