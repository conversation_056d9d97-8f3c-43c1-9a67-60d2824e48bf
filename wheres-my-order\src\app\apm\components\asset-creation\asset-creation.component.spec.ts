import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxFormModule } from 'devextreme-angular';
import { ApmService } from '../../services';
import { AssetCreationComponent } from './asset-creation.component';

describe('AssetCreationComponent', () => {
    let component: AssetCreationComponent;
    let fixture: ComponentFixture<AssetCreationComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DxFormModule],
            declarations: [AssetCreationComponent],
            providers: [{ provide: ApmService, useValue: {} }]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(AssetCreationComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
