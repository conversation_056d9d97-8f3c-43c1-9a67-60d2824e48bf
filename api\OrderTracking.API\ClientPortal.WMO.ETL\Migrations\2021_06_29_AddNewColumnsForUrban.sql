ALTER TABLE dbo.Orders ADD
[TIS_JSSROUTING]            nvarchar (60)    NULL,
[JSSDATESTAMPED]            datetime         NULL,
[J<PERSON><PERSON><PERSON>DQUOTEREQUESTED]    nvarchar (10)    NULL,
[J<PERSON><PERSON><PERSON>DQUOTEAPPLICABLE]   nvarchar (10)    NULL,
[<PERSON><PERSON><PERSON><PERSON><PERSON>QUOTEDATE]         datetime         NULL,
[J<PERSON><PERSON><PERSON>ONENTID]            nvarchar (60)    NULL,
[OLDJ<PERSON>VERIFIER]            nvarchar (100)   NULL,
[JSSPROFESSIONALENGINEER]   nvarchar (100)   NULL,
[JSSLANE]                   nvarchar (10)    NULL,
[JSSP<PERSON><PERSON>MP]                nvarchar (10)    NULL,
[<PERSON><PERSON><PERSON><PERSON><PERSON>RRENTHOLDER]       nvarchar (100)   NULL,
[JSSDATECUSTOMERETA]        datetime         NULL,
[J<PERSON><PERSON>SCCOMPONENT]          nvarchar (250)   NULL,
[JSSJOBPRIORITY]            nvarchar (30)    NULL,
[JSSDA<PERSON>ENTERED]            datetime         NULL,
[J<PERSON>DATECOMPLETED]          datetime         NULL,
[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ERIAL]      nvarchar (30)    NULL,
[<PERSON><PERSON><PERSON><PERSON><PERSON>IZ<PERSON>]               nvarchar (30)    NULL,
[JSSLINEMATERIAL]           nvarchar (30)    NULL,
[JSSLEAKTYPE]               nvarchar (30)    NULL,
[JSSOD]                     numeric (32, 16) NULL,
[JSSDESIGNPRESSURE]         numeric (32, 16) NULL,
[JSSDESIGNTEMPERATURE]      numeric (32, 16) NULL,
[JSSENCLOSURESEAL]          nvarchar (30)    NULL,
[JSSBRANCH]                 nvarchar (4)     NULL,
[CUSTNAME]                  nvarchar (60)    NULL,
[PRODServiceLineGroup]      nvarchar (10)    NULL,
[JSSENGINEERINGPRIORITY]    nvarchar (30)    NULL,
[JSSJobsTableRepairType]    nvarchar (30)    NULL,
[JSSSpecTableRepairType]    nvarchar (30)    NULL,
[JSSJOBSTATUS]              nvarchar (10)    NULL,
[JSSMODIFIEDDATETIME]       datetime         NULL,
[JSSMODIFIEDBY]             nvarchar (100)   NULL,
[OldJSSDesigner]            nvarchar (100)   NULL,
[DataTaker]                 nvarchar (60)    NULL,
[JSSTier]                   nvarchar (10)    NULL

GO;

ALTER TABLE dbo.OrdersDump ADD
[TIS_JSSROUTING]            nvarchar (60)    NULL,
[JSSDATESTAMPED]            datetime         NULL,
[JSSRAPIDQUOTEREQUESTED]    nvarchar (10)    NULL,
[JSSRAPIDQUOTEAPPLICABLE]   nvarchar (10)    NULL,
[JSSRAPIDQUOTEDATE]         datetime         NULL,
[JSSCOMPONENTID]            nvarchar (60)    NULL,
[OLDJSSVERIFIER]            nvarchar (100)   NULL,
[JSSPROFESSIONALENGINEER]   nvarchar (100)   NULL,
[JSSLANE]                   nvarchar (10)    NULL,
[JSSPESTAMP]                nvarchar (10)    NULL,
[OLDJSSCURRENTHOLDER]       nvarchar (100)   NULL,
[JSSDATECUSTOMERETA]        datetime         NULL,
[JSSDESCCOMPONENT]          nvarchar (250)   NULL,
[JSSJOBPRIORITY]            nvarchar (30)    NULL,
[JSSDATEENTERED]            datetime         NULL,
[JSSDATECOMPLETED]          datetime         NULL,
[JSSENCLOSUREMATERIAL]      nvarchar (30)    NULL,
[JSSLINESIZE]               nvarchar (30)    NULL,
[JSSLINEMATERIAL]           nvarchar (30)    NULL,
[JSSLEAKTYPE]               nvarchar (30)    NULL,
[JSSOD]                     numeric (32, 16) NULL,
[JSSDESIGNPRESSURE]         numeric (32, 16) NULL,
[JSSDESIGNTEMPERATURE]      numeric (32, 16) NULL,
[JSSENCLOSURESEAL]          nvarchar (30)    NULL,
[JSSBRANCH]                 nvarchar (4)     NULL,
[CUSTNAME]                  nvarchar (60)    NULL,
[PRODServiceLineGroup]      nvarchar (10)    NULL,
[JSSENGINEERINGPRIORITY]    nvarchar (30)    NULL,
[JSSJobsTableRepairType]    nvarchar (30)    NULL,
[JSSSpecTableRepairType]    nvarchar (30)    NULL,
[JSSJOBSTATUS]              nvarchar (10)    NULL,
[JSSMODIFIEDDATETIME]       datetime         NULL,
[JSSMODIFIEDBY]             nvarchar (100)   NULL,
[OldJSSDesigner]            nvarchar (100)   NULL,
[DataTaker]                 nvarchar (60)    NULL,
[JSSTier]                   nvarchar (10)    NULL