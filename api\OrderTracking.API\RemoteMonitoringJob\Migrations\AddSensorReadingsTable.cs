﻿using ClientPortal.Shared.Models.RemoteMonitoring;
using FluentMigrator;

namespace RemoteMonitoringJob.Migrations
{
    /// <summary>
    ///     Initial migration for RemoteMonitoring database
    /// </summary>
    [Migration(202105120123)]
    public class AddSensorReadingsTable : Migration
    {
        public override void Up()
        {
            Create.Table("SensorReadings")
                .WithColumn(nameof(SensorReading.DateTimeUTC)).AsDateTime2().PrimaryKey()
                .WithColumn(nameof(SensorReading.ThicknessAlarmState)).AsString().Nullable()
                .WithColumn(nameof(SensorReading.CorrosionAlarmState)).AsString().Nullable()
                .WithColumn(nameof(SensorReading.Thickness)).AsDouble().Nullable()
                .WithColumn(nameof(SensorReading.TempCompensatedThickness)).AsDouble().Nullable()
                .WithColumn(nameof(SensorReading.MaterialTemperature)).AsDouble().Nullable()
                .WithColumn(nameof(SensorReading.DSITemperature)).AsDouble().Nullable()
                .WithColumn(nameof(SensorReading.ReferenceVelocity)).AsDouble().Nullable()
                .WithColumn(nameof(SensorReading.TempCompensatedVelocity)).AsDouble().Nullable()
                .WithColumn(nameof(SensorReading.CorrosionRateShortTerm)).AsDouble().Nullable()
                .WithColumn(nameof(SensorReading.EndOfLifeShortTerm)).AsDateTime2().Nullable()
                .WithColumn(nameof(SensorReading.CorrosionRateLongTerm)).AsDouble().Nullable()
                .WithColumn(nameof(SensorReading.EndOfLifeLongTerm)).AsDateTime2().Nullable()
                .WithColumn(nameof(SensorReading.ThicknessAlarmThreshold)).AsDouble().Nullable()
                .WithColumn(nameof(SensorReading.ThicknessWarningThreshold)).AsDouble().Nullable()
                .WithColumn(nameof(SensorReading.CorrosionAlarmThreshold)).AsDouble().Nullable()
                .WithColumn(nameof(SensorReading.CorrosionWarningThreshold)).AsDouble().Nullable()
                .WithColumn(nameof(SensorReading.Company)).AsString().Nullable()
                .WithColumn(nameof(SensorReading.Site)).AsString().Nullable()
                .WithColumn(nameof(SensorReading.Plant)).AsString().Nullable()
                .WithColumn(nameof(SensorReading.Asset)).AsString().Nullable()
                .WithColumn(nameof(SensorReading.CollectionPoint)).AsString().Nullable()
                .WithColumn(nameof(SensorReading.TML)).AsString().Nullable().PrimaryKey();
        }

        public override void Down()
        {
            Delete.Table("SensorReadings");
        }
    }
}