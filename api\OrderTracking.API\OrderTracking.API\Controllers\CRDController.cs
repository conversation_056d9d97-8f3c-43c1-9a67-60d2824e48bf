﻿//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Mvc;
//using OrderTracking.API.Interfaces.Services;
//using OrderTracking.API.Models.CRD;
//using System.Threading.Tasks;
//using OrderTracking.API.Services;

//namespace OrderTracking.API.Controllers
//{
//    [Route("api/[controller]")]
//    [ApiController]
//    [Authorize]
//    public class CRDController : ControllerBase
//    {

//        private readonly ICRDService _crdService;
//        private readonly IUserProfilesService _userProfiles;

//        /// <summary>
//        ///    CRD constructor
//        /// </summary>
//        /// <param name="crdService"></param>
//        /// <param name="userProfiles"></param>
//        public CRDController(ICRDService crdService, IUserProfilesService userProfiles)
//        {
//            _crdService = crdService;
//            _userProfiles = userProfiles;
//        }

//        /// <summary>
//        ///     GET endpoint
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet]
//        public async Task<IActionResult> Get()
//        {
//            var email = User.Identity.Name.ToLower();
//            var user = await _userProfiles.GetAsync(email, email);
//            if (user == null) return Unauthorized();
//            return Ok(await _crdService.GetAllCRDEntries());
//        }

//        /// <summary>
//        ///     GET a specific entry
//        /// </summary>
//        /// <param name="id"></param>
//        /// <returns></returns>
//        [HttpGet("{id}")]
//        public async Task<IActionResult> Get(int id)
//        {
//           var email = User.Identity.Name.ToLower();
//           var user = await _userProfiles.GetAsync(email, email);
//           if (user == null) return Unauthorized();
//            return Ok(await _crdService.GetCRDEntry(id));
//        }

//        /// <summary>
//        ///     Post/Insert a new entry
//        /// </summary>
//        /// <param name="entry"></param>
//        /// <returns></returns>
//        [HttpPost]
//        [Authorize(Policy = "CRD:Admin")]
//        public async Task<IActionResult> Post(CRDEntry entry)
//        {
//            var email = User.Identity.Name.ToLower();
//            var user = await _userProfiles.GetAsync(email, email);
//            if (user == null) return Unauthorized();

//            return Ok(await _crdService.InsertCRDEntry(entry, email));
//        }

//        /// <summary>
//        ///     Put/Update an entry
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="entry"></param>
//        /// <returns></returns>
//        [HttpPut("{id}")]
//        [Authorize(Policy = "CRD:Admin")]
//        public async Task<IActionResult> Put(int id, CRDEntry entry)
//        {
//            var email = User.Identity.Name.ToLower();
//            var user = await _userProfiles.GetAsync(email, email);
//            if (user == null) return Unauthorized();

//            return Ok(await _crdService.UpdateCRDEntry(id, entry, email));
//        }

//        /// <summary> 
//        ///     Delete CRD Entry 
//        /// </summary> 
//        /// <param name="id"></param> 
//        /// <returns></returns> 
//        [HttpDelete("{id}")]
//        [Authorize(Policy = "CRD:Admin")]
//        public async Task<IActionResult> DeleteCalculations(int id)
//        {
//            var email = User.Identity.Name.ToLower();
//            var user = await _userProfiles.GetAsync(email, email);
//            if (user == null) return Unauthorized();

//            return Ok(await _crdService.DeleteCRDEntry(id, email));
//        }

//        /// <summary>
//        ///     GET endpoint
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet("AuditEntries")]
//        public async Task<IActionResult> GetAuditEntries()
//        {
//            var email = User.Identity.Name.ToLower();
//            var user = await _userProfiles.GetAsync(email, email);
//            if (user == null) return Unauthorized();
//            return Ok(await _crdService.GetAllCRDAuditRecords());
//        }



//    }
//}
