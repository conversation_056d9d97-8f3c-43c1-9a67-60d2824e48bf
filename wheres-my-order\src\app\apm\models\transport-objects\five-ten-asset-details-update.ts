import { ValueChangedTransport } from './value-changed-transport';

export interface FiveTenAssetDetailsUpdate {
    taskId?: string;
    projectId: string;
    assetId: string;
    workOrderId: string;
    assetCategory: ValueChangedTransport<string>;
    identificationName: ValueChangedTransport<string>;
    identificationNumber: ValueChangedTransport<string>;
    assetType: ValueChangedTransport<string>;
    equipmentDescription: ValueChangedTransport<string>;
    lastKnownInspectionDate: ValueChangedTransport<Date>;
    location: ValueChangedTransport<string>;
    gisLocationLat: ValueChangedTransport<number>;
    gisLocationLong: ValueChangedTransport<number>;
    designCode: ValueChangedTransport<string>;
    designYear: ValueChangedTransport<string>;
    designAddendum: ValueChangedTransport<string>;
    inspectionCode: ValueChangedTransport<string>;
    inspectionYear: ValueChangedTransport<string>;
    inspectionAddendum: ValueChangedTransport<string>;
    dataPlateAttached: ValueChangedTransport<boolean>;
    dataPlateLegible: ValueChangedTransport<boolean>;
    manufacturerName: ValueChangedTransport<string>;
    manufacturerDate: ValueChangedTransport<Date>;
    manufacturerSerialNumber: ValueChangedTransport<string>;
    nationalBoardNumber: ValueChangedTransport<string>;
    rtNumber: ValueChangedTransport<string>;
    serviceProductContents: ValueChangedTransport<string>;
    specificGravity: ValueChangedTransport<number>;
    orientation: ValueChangedTransport<string>;
    rt: ValueChangedTransport<string>;
    installationDate: ValueChangedTransport<Date>;
    inServiceDate: ValueChangedTransport<Date>;
    pIDNumber: ValueChangedTransport<string>;
    designDrawingNumber: ValueChangedTransport<string>;
    lowestFlangeRating: ValueChangedTransport<string>;
    hydroTestPressure: ValueChangedTransport<number>;
    typeOfConstruction: ValueChangedTransport<string>;
    pwht: ValueChangedTransport<string>;
    ratingChanged: ValueChangedTransport<string>;
    isFiredPressureVessel: ValueChangedTransport<string>;
    hasRepairOrAlterationPlate: ValueChangedTransport<boolean>;
    repairOrAlterationPlateLegible: ValueChangedTransport<boolean>;
    designConditionsOperatingTemp: ValueChangedTransport<number>;
    shellSideDesignMAWP: ValueChangedTransport<number>;
    shellSideDesignTemp: ValueChangedTransport<number>;
    shellSideOperatingTemp: ValueChangedTransport<number>;
    shellSideOperatingPressure: ValueChangedTransport<number>;
    shellSideSetPressure: ValueChangedTransport<number>;
    tubeSideDesignMAWP: ValueChangedTransport<number>;
    tubeSideDesignTemp: ValueChangedTransport<number>;
    tubeSideOperatingTemp: ValueChangedTransport<number>;
    tubeSideOperatingPressure: ValueChangedTransport<number>;
    tubeSideSetPressure: ValueChangedTransport<number>;
    diameterMeasurement: ValueChangedTransport<string>;
    diameter: ValueChangedTransport<number>;
    hasMultipleDiameters: ValueChangedTransport<boolean>;
    diameterComments: ValueChangedTransport<string>;
    overallLengthHeight: ValueChangedTransport<number>;
    hasToriconicalSections: ValueChangedTransport<boolean>;
    operationStatus: ValueChangedTransport<string>;
    shellCourses: ShellCourseUpdate[];
    channels: ChannelUpdate[];
    heads: HeadUpdate[];
    nozzles: NozzleUpdate[];
    inspectionOpenings: InspectionOpeningUpdate[];
    repairs: RepairUpdate[];
}

export interface RepairUpdate {
    databaseId: string;
    dateRepairedOrAltered: ValueChangedTransport<string>;
    repairAlterationOrganization: ValueChangedTransport<string>;
    purposeOfRepairAlteration: ValueChangedTransport<string>;
    isNBFormR1Available: ValueChangedTransport<boolean>;
    isNBFormR2Available: ValueChangedTransport<boolean>;
    nbRCertificateNumber: ValueChangedTransport<string>;
}

export interface InspectionOpeningUpdate {
    type: ValueChangedTransport<string>;
    number: ValueChangedTransport<number>;
    size: ValueChangedTransport<string>;
    displayName: ValueChangedTransport<string>;
    databaseId: string;
}

export interface ShellCourseUpdate {
    databaseId: string;
    number: ValueChangedTransport<string>;
    materialSpecAndGrade: ValueChangedTransport<string>;
    allowableStressAtTemp: ValueChangedTransport<number>;
    nominalThickness: ValueChangedTransport<number>;
    corrosionAllowance: ValueChangedTransport<number>;
    lengthOrHeight: ValueChangedTransport<number>;
    jointEfficiency: ValueChangedTransport<number>;
}

export interface ChannelUpdate {
    databaseId: string;
    number: ValueChangedTransport<string>;
    location: ValueChangedTransport<string>;
    materialSpecAndGrade: ValueChangedTransport<string>;
    allowableStressAtTemp: ValueChangedTransport<number>;
    nominalThickness: ValueChangedTransport<number>;
    corrosionAllowance: ValueChangedTransport<number>;
    length: ValueChangedTransport<number>;
    jointEfficiency: ValueChangedTransport<number>;
}

export interface HeadUpdate {
    databaseId: string;
    number: ValueChangedTransport<string>;
    location: ValueChangedTransport<string>;
    geometry: ValueChangedTransport<string>;
    geometryComments: ValueChangedTransport<string>;
    materialSpecAndGrade: ValueChangedTransport<string>;
    allowableStressAtTemp: ValueChangedTransport<number>;
    nominalThickness: ValueChangedTransport<number>;
    corrosionAllowance: ValueChangedTransport<number>;
    jointEfficiency: ValueChangedTransport<number>;
}

export interface NozzleUpdate {
    databaseId: string;
    number: ValueChangedTransport<string>;
    type: ValueChangedTransport<string>;
    materialSpecAndGrade: ValueChangedTransport<string>;
    pipeSize: ValueChangedTransport<string[]>;
    pipeSchedule: ValueChangedTransport<string[]>;
    flangeRating: ValueChangedTransport<string[]>;
    reinforcementPadType: ValueChangedTransport<string>;
    reinforcementPadDimensions: ValueChangedTransport<string>;
    reinforcementPadThickness: ValueChangedTransport<number>;
}
