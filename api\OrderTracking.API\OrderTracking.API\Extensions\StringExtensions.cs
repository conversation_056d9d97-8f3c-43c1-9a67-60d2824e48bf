﻿using System;
using System.Text.RegularExpressions;

namespace OrderTracking.API.Extensions
{
    public static class StringExtensions
    {
        public static string PascalToKebabPlural(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return value;

            return Regex.Replace(
                value,
                "(?<!^)([A-Z][a-z]|(?<=[a-z])[A-Z0-9])",
                "-$1",
                RegexOptions.Compiled)
                .Trim()
                .ToLower()+"s";
        }
    }
}

