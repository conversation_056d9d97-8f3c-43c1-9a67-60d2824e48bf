import { Pipe, PipeTransform } from '@angular/core';
import { CommaSeparatedStringToNumbers } from '../../shared/helpers/comma-separated-string-to-numbers';
import { ProjectVm, WorkOrder, WorkOrderDetailTabInfo } from '../models';

@Pipe({
    name: 'woDetail'
})
export class WoDetailPipe implements PipeTransform {
    transform(
        workOrder: WorkOrder,
        project: ProjectVm
    ): WorkOrderDetailTabInfo {
        if (!workOrder) return null;

        const gisLocationArray = CommaSeparatedStringToNumbers(
            workOrder?.gisLocation?.currentValue
        );
        let lat: number;
        let long: number;
        if (gisLocationArray) {
            if (gisLocationArray.length > 0) {
                lat = gisLocationArray[0];
            }

            if (gisLocationArray.length > 1) {
                long = gisLocationArray[1];
            }
        }

        return {
            projectId: project?.id,
            id: workOrder.id,
            apmWorkOrderNumber: workOrder.apmWorkOrderNumber.currentValue,
            apmProjectNumber: project?.apmProjectNumber,
            facility: workOrder.facilityName.currentValue,
            assetCategory: workOrder.asset.assetCategory,
            status: workOrder.status.currentValue,
            plannedEndDate: workOrder.plannedEnd.currentValue,
            plannedStartDate: workOrder.plannedStart.currentValue,
            dueDate: workOrder.dueDate.currentValue,
            latitude: lat,
            longitude: long,
            primaryContactName: workOrder.primaryContactName.currentValue,
            primaryContactPhone: workOrder.primaryContactPhone.currentValue,
            teamProjectNumber: project?.teamProjectNumber,
            availableStatuses: workOrder.status.options
                .map((o) => o.value)
                .filter((value) => value.toLowerCase() != 'published')
        };
    }
}
