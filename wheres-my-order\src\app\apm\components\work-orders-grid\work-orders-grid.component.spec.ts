import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { DxDataGridModule } from 'devextreme-angular/ui/data-grid';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import { ApmService } from '../../services';
import { WorkOrdersGridComponent } from './work-orders-grid.component';

describe('WorkOrdersGridComponent', () => {
    let component: WorkOrdersGridComponent;
    let fixture: ComponentFixture<WorkOrdersGridComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxDataGridModule,
                RouterTestingModule,
                HttpClientTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [WorkOrdersGridComponent],
            providers: [
                {
                    provide: ApmService,
                    useValue: { selectedBU$: of('123'), buSelected$: of(false) }
                }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(WorkOrdersGridComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
