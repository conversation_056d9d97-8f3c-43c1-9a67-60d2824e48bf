#region Copyright Quest Integrity Group, LLC 2020
// www.QuestIntegrity.com
// +1-303-415-1475
// 
// All rights are reserved. Reproduction or transmission in whole or in part, in
// any form or by any means, electronic, mechanical or otherwise, is prohibited
// without the prior written consent of the copyright owner.
// 
// File Created: 2020-01-15 1:51 PM
// Updated:      2020-01-15 1:51 PM
// Created by:   <PERSON><PERSON><PERSON><PERSON>, Sean
#endregion

using Newtonsoft.Json;

namespace OrderTracking.API.Models.EDR
{
    /// <summary>
    ///     Address object for equipment demand requests
    /// </summary>
    public class Address
    {
        /// <summary>
        ///     First line of address
        /// </summary>
        [JsonProperty(PropertyName = "line1")]
        public string Line1 { get; set; }

        /// <summary>
        ///     Second line of address
        /// </summary>
        [JsonProperty(PropertyName = "line2")]
        public string Line2 { get; set; }

        /// <summary>
        ///     City of address
        /// </summary>
        [JsonProperty(PropertyName = "city")]
        public string City { get; set; }

        /// <summary>
        ///     State of address
        /// </summary>
        [JsonProperty(PropertyName = "state")]
        public string State { get; set; }

        /// <summary>
        ///  Zip Code of address
        /// </summary>
        [JsonProperty(PropertyName = "zipCode")]
        public string ZipCode { get; set; }
    }
}
