﻿using NUnit.Framework;
using OrderTracking.API.Helpers;

namespace OrderTracking.API.Tests
{
    [TestFixture]
    public class BlobServiceHelpersTest
    {
        [Test]
        [TestCase("98d613c8-390a-475f-85f1-be41d1106076/Blank Document.pdf", "98d613c8-390a-475f-85f1-be41d1106076%2FBlank%20Document.pdf")]
        [TestCase("98d613c8-390a-475f-85f1-be41d1106076%2Flog.txt", "98d613c8-390a-475f-85f1-be41d1106076%2Flog.txt")]
        public void TestUriEncodingHelper(string uri, string expectedEncodedUri)
        {
            // Arrange/Act
            string encodedUri = BlobServiceHelpers.EncodeUri(uri);

            // Assert
            Assert.AreEqual(expectedEncodedUri, encodedUri);
        }

        [Test]
        [TestCase("98d613c8-390a-475f-85f1-be41d1106076", "Blank Document.pdf", "98d613c8-390a-475f-85f1-be41d1106076/Blank%20Document.pdf")]
        [TestCase("98d613c8-390a-475f-85f1-be41d1106076", "log.txt", "98d613c8-390a-475f-85f1-be41d1106076/log.txt")]
        public void TestFullBlobStringCreator(string id, string fileName, string expectedFullBlobStringEncoded)
        {
            // Arrange/Act
            string fullBlobStringEncoded = BlobServiceHelpers.FullBlobStringEncoded(id, fileName);

            // Assert
            Assert.AreEqual(expectedFullBlobStringEncoded, fullBlobStringEncoded);
        }
    }
}
