<div *ngIf="!photoGroups">
    <dx-load-indicator style="position: relative; left: 50%;"
                       id="large-indicator"
                       height="60"
                       width="60"></dx-load-indicator>
</div>
<div class="responsive-paddings content-block">
    <div *ngIf="photoGroups"
         class="responsive-paddings content-block">
        <div *ngFor="let photoGroup of photoGroups">
            <h6>{{photoGroup.description.currentValue ?? 'No description'}}</h6>
            <dx-tile-view [items]="photoGroup.photos.photos"
                          [baseItemHeight]="120"
                          [baseItemWidth]="185"
                          [itemMargin]="10"
                          [direction]="'horizontal'"
                          [noDataText]="'No photos in this section'"
                          [height]="
                              (photoGroup.photos.photos?.length> 0) ? 180 : 40">
                <div *dxTemplate="let photo of 'item'"
                     (dblclick)="imageClicked($event, photoGroup, photo, gallery)">
                    <div *ngIf="photo?.blobName"
                         class="image-tile">
                        <img class="tile-image"
                             *ngIf="assetPathLoadingCompleted"
                             [src]="getAssetImage('leakRepairPhotos',photo.blobName)"
                             alt="">
                    </div>
                </div>
            </dx-tile-view>
        </div>
    </div>
</div>

<dx-popup #popup
          [(visible)]="showPopup"
          [showCloseButton]="true"
          [hideOnOutsideClick]="true"
          *ngIf="(photo$ | async) && (photoGroup$ | async)">
    <dxi-toolbar-item [text]="(photo$ | async) | leakReportingPhotoModalTitle : (photoGroup$ | async)"
                      location="before">
    </dxi-toolbar-item>
    <dx-gallery #gallery
                id="gallery"
                [dataSource]="allPhotos"
                [loop]="true"
                width="100%"
                height="100%"
                [showNavButtons]="true"
                [showIndicator]="true"
                [(selectedIndex)]="popupGallerySelectedIndex"
                (onSelectionChanged)="onSelectionChanged($event)">

        <div *dxTemplate="let photoInfo of 'item'"
             class="popup-template">

            <div *ngIf="photoInfo?.photo?.blobName"
                 class="image-container">
                <img *ngIf="assetPathLoadingCompleted"
                     [src]="getAssetImage('allPhotos',photoInfo.photo.blobName)"
                     alt="">

            </div>

            <div class="info">
                <dx-scroll-view>
                    <dx-form [formData]="currentPhotoInfo"
                             [colCount]="2"
                             [readOnly]="!isEditingPhotos || !allowEditing"
                             (onFieldDataChanged)="onFieldDataChanged($event)">
                        <dxi-item itemType="group"
                                  caption="Description">
                            <dxi-item dataField="description">
                                <dxo-label text="Value"></dxo-label>
                            </dxi-item>
                            <dxi-item dataField="descriptionComment">
                                <dxo-label text="Comment"></dxo-label>
                            </dxi-item>
                        </dxi-item>
                        <dxi-item itemType="group"
                                  caption="Photo Collection Comment">
                            <dxi-item dataField="photoComment">
                                <dxo-label text="Value"></dxo-label>
                            </dxi-item>
                        </dxi-item>
                        <dxi-item itemType="group"
                                  caption="Area of Interest">
                            <dxi-item dataField="areaOfInterestLatitude"
                                      editorType="dxNumberBox">
                                <dxo-label text="Latitude"></dxo-label>
                            </dxi-item>
                            <dxi-item dataField="areaOfInterestLongitude"
                                      editorType="dxNumberBox">
                                <dxo-label text="Longitude"></dxo-label>
                            </dxi-item>
                            <dxi-item dataField="areaOfInterestComment">
                                <dxo-label text="Comment"></dxo-label>
                            </dxi-item>
                        </dxi-item>
                        <dxi-item itemType="group"
                                  caption="Upstream Tie-in">
                            <dxi-item dataField="upstreamTieInLatitude"
                                      editorType="dxNumberBox">
                                <dxo-label text="Latitude"></dxo-label>
                            </dxi-item>
                            <dxi-item dataField="upstreamTieInLongitude"
                                      editorType="dxNumberBox">
                                <dxo-label text="Longitude"></dxo-label>
                            </dxi-item>
                            <dxi-item dataField="upstreamTieInComment">
                                <dxo-label text="Comment"></dxo-label>
                            </dxi-item>
                        </dxi-item>
                        <dxi-item itemType="group"
                                  caption="Downstream Tie-in">
                            <dxi-item dataField="downstreamTieInLatitude"
                                      editorType="dxNumberBox">
                                <dxo-label text="Latitude"></dxo-label>
                            </dxi-item>
                            <dxi-item dataField="downstreamTieInLongitude"
                                      editorType="dxNumberBox">
                                <dxo-label text="Longitude"></dxo-label>
                            </dxi-item>
                            <dxi-item dataField="downstreamTieInComment">
                                <dxo-label text="Comment"></dxo-label>
                            </dxi-item>
                        </dxi-item>
                        <dxi-item itemType="group"
                                  caption="UT High Measurement">
                            <dxi-item dataField="utHighMeasurement"
                                      editorType="dxNumberBox">
                                <dxo-label text="Value"></dxo-label>
                            </dxi-item>
                            <dxi-item dataField="utHighMeasurementComment">
                                <dxo-label text="Comment"></dxo-label>
                            </dxi-item>
                        </dxi-item>
                        <dxi-item itemType="group"
                                  caption="UT Low Measurement">
                            <dxi-item dataField="utLowMeasurement"
                                      editorType="dxNumberBox">
                                <dxo-label text="Value"></dxo-label>
                            </dxi-item>
                            <dxi-item dataField="utLowMeasurementComment">
                                <dxo-label text="Comment"></dxo-label>
                            </dxi-item>
                        </dxi-item>
                    </dx-form>
                </dx-scroll-view>
                <div class="buttons">

                    <dx-button icon="remove"
                               text="Delete"
                               type="danger"
                               [disabled]="!allowEditing"
                               (onClick)="onDeletePhotoClicked($event, photoInfo)">
                    </dx-button>

                    <dx-button *ngIf="assetPathLoadingCompleted"
                               icon="download"
                               text="Download"
                               type="default"
                               (onClick)="onDownloadClicked(photoInfo?.photo?.blobName)">
                    </dx-button>

                    <dx-button *ngIf="!isEditingPhotos; else saveCancelPhotoDescription"
                               icon="edit"
                               text="Edit"
                               [disabled]="!allowEditing"
                               (onClick)="onEditClicked($event, photoInfo?.photo?.description?.currentValue)">
                    </dx-button>
                    <ng-template #saveCancelPhotoDescription>
                        <dx-button icon="save"
                                   text="Save"
                                   type="success"
                                   [disabled]="!allowEditing || isSaving"
                                   (onClick)="onPhotoGroupSaved($event, photoInfo)">
                        </dx-button>
                        <dx-button text="Cancel"
                                   type="danger"
                                   [disabled]="!allowEditing"
                                   (onClick)="onPhotoGroupCancel($event)">
                        </dx-button>
                    </ng-template>
                </div>
            </div>
        </div>
    </dx-gallery>
</dx-popup>