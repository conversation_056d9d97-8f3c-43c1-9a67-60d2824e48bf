import { Component, Input } from '@angular/core';
import { AppointmentFormOpeningEvent } from 'devextreme/ui/scheduler';
import {
    APMStatus,
    APMTaskType,
    ProjectVm,
    statusColor,
    TaskVM
} from '../../models';

@Component({
    selector: 'app-timeline',
    templateUrl: './timeline.component.html',
    styleUrls: ['./timeline.component.scss']
})
export class TimelineComponent {
    private _currentDate = new Date();

    @Input() loading: boolean;
    @Input() tasks: TaskVM[];
    @Input() projects: ProjectVm[];

    get currentDate(): Date {
        return this._currentDate;
    }

    readonly equipmentTypeResources = [
        { id: 'Vessel', text: 'Vessel' },
        { id: 'Piping', text: 'Piping' },
        { id: 'Tank', text: 'Tank' }
    ];

    readonly statuses = [
        { id: 'Scheduled', text: 'Scheduled', color: statusColor('Scheduled') },
        {
            id: 'In Progress',
            text: 'In Progress',
            color: statusColor('In Progress')
        },
        { id: 'Completed', text: 'Completed', color: statusColor('Completed') },
        {
            id: 'Not Started',
            text: 'Not Started',
            color: statusColor('Not Started')
        },
        { id: 'Published', text: 'Published', color: statusColor('Published') },
        { id: 'On Hold', text: 'On Hold', color: statusColor('On Hold') },
        { id: 'Overdue', text: 'Overdue', color: statusColor('Overdue') },
        { id: 'Unknown', text: 'Unknown', color: statusColor('Unknown') }
    ];

    readonly taskTypes: { id: APMTaskType; text: APMTaskType }[] = [
        { id: 'Full', text: 'Full' },
        { id: 'Internal Visual', text: 'Internal Visual' },
        { id: 'External Visual', text: 'External Visual' },
        { id: 'Asset Walkdown', text: 'Asset Walkdown' }
    ];

    getStatusColor(status: APMStatus) {
        return this.statuses.find((s) => s.id === status)?.color;
    }

    onAppointmentFormOpening(e: AppointmentFormOpeningEvent) {
        e.form.option('items', [
            { label: { text: 'Project Name' }, dataField: 'projectName' },
            { label: { text: 'Asset ID' }, dataField: 'equipmentId' },
            {
                label: { text: 'APM Task Number' },
                dataField: 'apmTaskNumber'
            },
            { label: { text: 'Task Type' }, dataField: 'taskType' },
            { label: { text: 'Status' }, dataField: 'status' }
        ]);
    }
}
