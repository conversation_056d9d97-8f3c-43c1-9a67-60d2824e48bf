<dx-data-grid [dataSource]="submissions"
              [wordWrapEnabled]="true"
              (onRowRemoved)="submissionDeleted($event)"
              [showBorders]="true">
    <dxo-header-filter [visible]="true"></dxo-header-filter>
    <dxo-filter-row [visible]="true"></dxo-filter-row>
    <dxo-search-panel [visible]="true"
                      [highlightCaseSensitive]="true">
    </dxo-search-panel>
    <dxo-filter-panel [visible]="true"></dxo-filter-panel>

    <dxi-column dataField="submissionType"
                caption="Submission Type"></dxi-column>
    <dxi-column dataField="serviceType"
                caption="Type"></dxi-column>
    <dxi-column dataField="comment"
                caption="Comment"
                [calculateCellValue]="commentCellTemplate"></dxi-column>
    <dxi-column dataField="createdDate"
                caption="Date & Time"></dxi-column>
    <dxi-column dataField="submittedUser"
                caption="User Submitted"></dxi-column>
    <dxo-editing mode="row"
                 [allowDeleting]="( currentUser | hasRole: 'App:Admin') || 
                 (currentUser | hasRole: 'AIMaaS:Admin')">
    </dxo-editing>
</dx-data-grid>