using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Security.Cryptography.X509Certificates;

namespace ClientPortal.Shared.Extensions
{
    public static class GenericExtensions
    {
        public static IEnumerable<Variance> DetailedPropertyCompare<T>(this T obj1, T obj2)
        {
            var properties = obj1.GetType().GetProperties();
            return properties
                .Select(f => new Variance
                {
                    PropertyName = f.Name,
                    OldValue = f.GetValue(obj1),
                    NewValue = f.GetValue(obj2)
                })
                .Where(v =>
                {
                    if (v.OldValue == null && v.NewValue == null) return false;
                    if (v.OldValue == null && v.NewValue != null) return true;
                    if (v.OldValue != null && v.NewValue == null) return true;
                    if (v.OldValue is IEnumerable<object> && v.NewValue is not IEnumerable<object>) return true;
                    if (v.OldValue is not IEnumerable<object> && v.NewValue is IEnumerable<object>) return true;
                    if (v.OldValue is IEnumerable<object> oldArray && v.NewValue is IEnumerable<object> newArray)
                    {
                        if (oldArray.Count() != newArray.Count()) return true;
                        if (!oldArray.All(x => newArray.Any(y => y.Equals(x)))) return true;
                    }
                    else
                    {
                        if (v.OldValue != null && !v.OldValue.Equals(v.NewValue)) return true;
                    }

                    return false;
                })
                .ToList();
        }
    }

    public class Variance
    {
        public string PropertyName { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }
    }
}