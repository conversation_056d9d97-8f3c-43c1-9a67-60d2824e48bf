﻿using System.Collections.Generic;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    ///     Standard CRUD methods for Entities
    /// </summary>
    public interface IAsyncRepository<TEntity, in TKey> where TEntity : IEntity<TKey>
    {
        #region Public Methods

        /// <summary>
        ///     Get all entities
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<TEntity>> GetAllAsync();

        /// <summary>
        ///     Get entity
        /// </summary>
        /// <param name="entityId"></param>
        /// <returns></returns>
        Task<TEntity> GetAsync(TKey entityId);
        
        /// <summary>
        ///     Add entity
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task<TEntity> AddAsync(TEntity entity);
        
        /// <summary>
        ///     Update entity
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task<TEntity> UpdateAsync(TEntity entity);
        
        /// <summary>
        ///     Update entity
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="originalEntity"></param>
        /// <returns></returns>
        Task<TEntity> UpdateAsync(TEntity entity, TEntity originalEntity);
        
        /// <summary>
        ///     Update entity
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="originalId"></param>
        /// <returns></returns>
        Task<TEntity> UpdateAsync(TEntity entity, TKey originalId);
        
        /// <summary>
        ///     Remove entity
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task RemoveAsync(TKey id);
        
        /// <summary>
        ///     Remove entity
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task RemoveAsync(TEntity entity);

        #endregion
    }
}