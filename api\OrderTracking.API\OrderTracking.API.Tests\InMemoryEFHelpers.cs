using System.Data.Common;
using Microsoft.Data.Sqlite;
using Microsoft.Extensions.Configuration;
using Moq;

namespace OrderTracking.API.Tests
{
    public static class InMemoryEFHelpers
    {
        public static DbConnection CreateInMemoryDatabase()
        {
            var connection = new SqliteConnection("Filename=:memory:");

            connection.Open();

            return connection;
        }

        public static Mock<IConfiguration> MockIConfigurationForOrdersConnection()
        {
            var config = new Mock<IConfiguration>();
            var sqlConnections = new Mock<IConfigurationSection>();
            // As long as we don't create a connection with this connection string,
            // the tests in here, testing Entity Framework Core, will not error out.
            sqlConnections.Setup(s => s["Orders"]).Returns("Server=.");
            config.Setup(c => c.GetSection("SQLConnections")).Returns(sqlConnections.Object);
            return config;
        }
    }
}