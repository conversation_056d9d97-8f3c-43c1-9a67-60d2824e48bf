﻿using System;
using APMWebDataInterface.ExampleDataModel;
using APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Extensions.APM
{
    /// <summary>
    ///     Extension methods to update APM Asset type
    /// </summary>
    public static class AssetExtensions
    {
        /// <summary>
        ///     Update an asset from a <see cref="AssetVM" />
        /// </summary>
        /// <param name="asset"></param>
        /// <param name="vm"></param>
        public static void Update(this Asset asset, AssetVM vm)
        {
            var walkDown = asset.walkDown;
            switch (walkDown)
            {
                case Section510_Asset_Walkdown_Details_F fiveTenWalkDown:
                {
                    var identification = fiveTenWalkDown.sectionIdentification;
                    if (identification.attributeNumber_or_ID.CurrentValue != vm.EquipmentId)
                        identification.attributeNumber_or_ID.SetValue(vm.EquipmentId);
                    if (identification.attributeEquipment_Description.CurrentValue != vm.AssetDescription)
                        identification.attributeEquipment_Description.SetValue(vm.AssetDescription);
                    if (identification.attributeAsset_Type.CurrentValue != vm.AssetType)
                        identification.attributeAsset_Type.SetValue(vm.AssetType);
                    break;
                }
                case Section570_Asset_Walkdown_Details_F fiveSeventyWalkDown:
                {
                    var identification = fiveSeventyWalkDown.sectionIdentification;
                    if (identification.attributeNumber_or_Circuit_ID.CurrentValue != vm.EquipmentId)
                        identification.attributeNumber_or_Circuit_ID.SetValue(vm.EquipmentId);
                    if (identification.attributeEquipment_Description.CurrentValue != vm.AssetDescription)
                        identification.attributeEquipment_Description.SetValue(vm.AssetDescription);
                    if (identification.attributeAsset_Type.CurrentValue != vm.AssetType)
                        identification.attributeAsset_Type.SetValue(vm.AssetType);
                    break;
                }
                case Section653_Asset_Walkdown_Details_F sixFiftyThreeWalkDown:
                {
                    var identification = sixFiftyThreeWalkDown.sectionIdentification;
                    if (identification.attributeNumber_or_ID.CurrentValue != vm.EquipmentId)
                        identification.attributeNumber_or_ID.SetValue(vm.EquipmentId);
                    if (identification.attributeEquipment_Description.CurrentValue != vm.AssetDescription)
                        identification.attributeEquipment_Description.SetValue(vm.AssetDescription);
                    if (identification.attributeAsset_Type.CurrentValue != vm.AssetType)
                        identification.attributeAsset_Type.SetValue(vm.AssetType);
                    break;
                }
                default:
                    throw new ArgumentException("Invalid walk down type", nameof(asset));
            }

            if (asset.area.CurrentValue != vm.Area)
                asset.area.SetValue(vm.Area);
            if (asset.unit.CurrentValue != vm.Unit)
                asset.unit.SetValue(vm.Unit);
        }

        /// <summary>
        ///     Update an asset from a <see cref="AssetAccessUpdate" />
        /// </summary>
        /// <param name="asset"></param>
        /// <param name="assetUpdate"></param>
        public static void Update(this Asset asset, AssetAccessUpdate assetUpdate)
        {
            var insulation = asset.assetAccess.sectionInspectionAccessConditions.sectionInsulation;
            var inspectionPorts = asset.assetAccess.sectionInspectionAccessConditions.sectionInspectionPorts;
            var coating = asset.assetAccess.sectionExternalSurfaceConditions.sectionCoating;
            var corrosion = asset.assetAccess.sectionExternalSurfaceConditions.sectionCorrosion;
            var accessibility = asset.assetAccess.sectionAccessibility;
            var arialLift = accessibility.sectionAerialLiftRequirements;
            var internalAccess = asset.assetAccess.sectionInternalAccessConditions;
            var internalAccessRequirements = internalAccess.sectionInternalAccessRequirements;
            var cleaning = internalAccess.sectionCleaningRequirements;
            var internalCoating = internalAccess.sectionInternalCoatingLiner;

            if (assetUpdate.AccessForAerialLiftForAllLocationsAtHeight != arialLift.attributeAWAQ222.CurrentValue)
                arialLift.attributeAWAQ222.SetValue(assetUpdate.AccessForAerialLiftForAllLocationsAtHeight);

            if (assetUpdate.AdditionalPortsNeeded != inspectionPorts.attributeAdditional_ports_needed.CurrentValue)
                inspectionPorts.attributeAdditional_ports_needed.SetValue(assetUpdate.AdditionalPortsNeeded);

            if (assetUpdate.AdditionalPortsNeededComment != inspectionPorts.attributeAdditional_ports_needed.Comment)
                inspectionPorts.attributeAdditional_ports_needed.SetComment(assetUpdate.AdditionalPortsNeededComment);

            if (assetUpdate.AerialLiftNeeded != arialLift.attributeAerial_Lift_Needed.CurrentValue)
                arialLift.attributeAerial_Lift_Needed.SetValue(assetUpdate.AerialLiftNeeded);

            if (assetUpdate.AerialLiftNeededComment != arialLift.attributeAerial_Lift_Needed.Comment)
                arialLift.attributeAerial_Lift_Needed.SetComment(assetUpdate.AerialLiftNeededComment);

            if (assetUpdate.AllComponentsUnder4FeetInHeight !=
                accessibility.attributeAll_components_under_4_ft_in_height.CurrentValue)
                accessibility.attributeAll_components_under_4_ft_in_height.SetValue(assetUpdate
                    .AllComponentsUnder4FeetInHeight);

            if (assetUpdate.AllComponentsUnder4FeetInHeightComment !=
                accessibility.attributeAll_components_under_4_ft_in_height.Comment)
                accessibility.attributeAll_components_under_4_ft_in_height.SetComment(assetUpdate
                    .AllComponentsUnder4FeetInHeightComment);


            if (assetUpdate.AssetOutOfService != internalAccess.attributeIs_the_asset_out_of_service.CurrentValue)
                internalAccess.attributeIs_the_asset_out_of_service.SetValue(assetUpdate.AssetOutOfService);


            if (assetUpdate.BatteryPoweredPermitted != arialLift.attributeBattery_Powered_Permitted.CurrentValue)
                arialLift.attributeBattery_Powered_Permitted.SetValue(assetUpdate.BatteryPoweredPermitted);


            cleaning.attributeCleaning_recommendations.SetValue(assetUpdate.CleaningRecommendations);

            if (assetUpdate.CleaningRecommendationsComment != cleaning.attributeCleaning_recommendations.Comment)
                cleaning.attributeCleaning_recommendations.SetComment(assetUpdate.CleaningRecommendationsComment);

            if (assetUpdate.CleaningServiceReview != cleaning.attributeCleaning_service_review.CurrentValue)
                cleaning.attributeCleaning_service_review.SetValue(assetUpdate.CleaningServiceReview);

            if (assetUpdate.CleaningServiceReviewComment != cleaning.attributeCleaning_service_review.Comment)
                cleaning.attributeCleaning_service_review.SetComment(assetUpdate.CleaningRecommendationsComment);

            if (assetUpdate.ClientRequiredProofOfTraining !=
                arialLift.attributeClient_required_proof_of_training.CurrentValue)
                arialLift.attributeClient_required_proof_of_training.SetValue(assetUpdate
                    .ClientRequiredProofOfTraining);

            if (assetUpdate.ClientProvidedOperator != arialLift.attributeClient_provided_operator.CurrentValue)
                arialLift.attributeClient_provided_operator.SetValue(assetUpdate.ClientProvidedOperator);

            coating.attributeCoating_Condition.SetValue(assetUpdate.CoatingCondition);

            coating.attributeCoating_Conditions_Observed.SetValue(assetUpdate.CoatingConditionsObserved);

            if (assetUpdate.CoatingConditionsObservedComment != coating.attributeCoating_Conditions_Observed.Comment)
                coating.attributeCoating_Conditions_Observed.SetComment(assetUpdate.CoatingConditionsObservedComment);

            internalCoating.attributeCoatingLiner_Condition.SetValue(assetUpdate.CoatingLinerConditions);

            internalCoating.attributeCoatingLiner_Conditions_Observed.SetValue(assetUpdate
                .CoatingLinerConditionsObserved);

            if (assetUpdate.CoatingLinerConditionsObservedComment !=
                internalCoating.attributeCoatingLiner_Conditions_Observed.Comment)
                internalCoating.attributeCoatingLiner_Conditions_Observed.SetComment(assetUpdate
                    .CoatingLinerConditionsObservedComment);

            internalCoating.attributeCoatingLiner_Type.SetValue(assetUpdate.CoatingLinerType);

            if (assetUpdate.CoatingLinerTypeComment != internalCoating.attributeCoatingLiner_Type.Comment)
                internalCoating.attributeCoatingLiner_Type.SetComment(assetUpdate.CoatingLinerTypeComment);

            if (assetUpdate.CoatingRemovalRequired != coating.attributeCoating_Removal_Required.CurrentValue)
                coating.attributeCoating_Removal_Required.SetValue(assetUpdate.CoatingRemovalRequired);

            if (assetUpdate.CoatingRemovalRequiredComment != coating.attributeCoating_Removal_Required.Comment)
                coating.attributeCoating_Removal_Required.SetComment(assetUpdate.CoatingRemovalRequiredComment);

            coating.attributeCoating_Type.SetValue(assetUpdate.CoatingType);

            if (assetUpdate.CoatingTypeComment != coating.attributeCoating_Type.Comment)
                coating.attributeCoating_Type.SetComment(assetUpdate.CoatingTypeComment);

            corrosion.attributeCorrosion_identified.SetValue(assetUpdate.CorrosionIdentified);

            if (assetUpdate.CorrosionIdentifiedComment != corrosion.attributeCorrosion_identified.Comment)
                corrosion.attributeCorrosion_identified.SetComment(assetUpdate.CorrosionIdentifiedComment);

            corrosion.attributeCorrosion_removal_recommendation.SetValue(assetUpdate.CorrrosionRemovalRecommendation);

            if (assetUpdate.CorrosionRemovalRecommendationComment !=
                corrosion.attributeCorrosion_removal_recommendation.Comment)
                corrosion.attributeCorrosion_removal_recommendation.SetComment(assetUpdate
                    .CorrosionRemovalRecommendationComment);

            if (assetUpdate.EstimatedDistanceToAnyLiveElectricalOverheadLines != arialLift
                    .attributeEstimated_distance_to_any_live_electrical_overhead_lines.CurrentValue)
                arialLift.attributeEstimated_distance_to_any_live_electrical_overhead_lines.SetValue(assetUpdate
                    .EstimatedDistanceToAnyLiveElectricalOverheadLines);


            if (assetUpdate.ExistingInspectionPorts != inspectionPorts.attributeExisting_inspection_ports.CurrentValue)
                inspectionPorts.attributeExisting_inspection_ports.SetValue(assetUpdate.ExistingInspectionPorts);

            if (assetUpdate.FixedEquipmentLaddersStairwaysPlatformsInstalled != accessibility
                    .attributeFixed_Equipment_LaddersStairwaysPlatforms_Installed.CurrentValue)
                accessibility.attributeFixed_Equipment_LaddersStairwaysPlatforms_Installed.SetValue(assetUpdate
                    .FixedEquipmentLaddersStairwaysPlatformsInstalled);

            if (assetUpdate.FixedEquipmentLaddersStairwaysPlatformsInstalledComment !=
                accessibility.attributeFixed_Equipment_LaddersStairwaysPlatforms_Installed.Comment)
                accessibility.attributeFixed_Equipment_LaddersStairwaysPlatforms_Installed.SetComment(assetUpdate
                    .FixedEquipmentLaddersStairwaysPlatformsInstalledComment);

            if (assetUpdate.GasPoweredPermitted != arialLift.attributeGas_Powered_Permitted.CurrentValue)
                arialLift.attributeGas_Powered_Permitted.SetValue(assetUpdate.GasPoweredPermitted);

            if (assetUpdate.HasInsulation != insulation.attributeDoes_the_asset_have_insulation.CurrentValue)
                insulation.attributeDoes_the_asset_have_insulation.SetValue(assetUpdate.HasInsulation);

            insulation.attributeHeat_tracing.SetValue(assetUpdate.HeatTracing);

            if (assetUpdate.HeatTracingComment != insulation.attributeHeat_tracing.Comment)
                insulation.attributeHeat_tracing.SetComment(assetUpdate.HeatTracingComment);

            internalAccessRequirements.attributeInspection_opening_Types.SetValue(assetUpdate.InspectionOpeningTypes);

            if (assetUpdate.InspectionOpeningTypesComment !=
                internalAccessRequirements.attributeInspection_opening_Types.Comment)
                internalAccessRequirements.attributeInspection_opening_Types.SetComment(assetUpdate
                    .InspectionOpeningTypesComment);

            if (assetUpdate.InspectionOpeningsPresent !=
                internalAccessRequirements.attributeAre_there_inspection_openings.CurrentValue)
                internalAccessRequirements.attributeAre_there_inspection_openings.SetValue(assetUpdate
                    .InspectionOpeningsPresent);

            if (assetUpdate.InsulationPlugsMissing != inspectionPorts.attributeInsulation_plugs_missing.CurrentValue)
                inspectionPorts.attributeInsulation_plugs_missing.SetValue(assetUpdate.InsulationPlugsMissing);

            if (assetUpdate.InsulationPlugsMissingComment != inspectionPorts.attributeInsulation_plugs_missing.Comment)
                inspectionPorts.attributeInsulation_plugs_missing.SetComment(assetUpdate.InsulationPlugsMissingComment);

            if (assetUpdate.InsulationRemovalRequired != insulation.attributeInsulation_removal_required.CurrentValue)
                insulation.attributeInsulation_removal_required.SetValue(assetUpdate.InsulationRemovalRequired);

            if (assetUpdate.InsulationRemovalRequiredComment != insulation.attributeInsulation_removal_required.Comment)
                insulation.attributeInsulation_removal_required.SetComment(assetUpdate
                    .InsulationRemovalRequiredComment);

            insulation.attributeInsulation_type.SetValue(assetUpdate.InsulationType);

            if (assetUpdate.InsulationTypeComment != insulation.attributeInsulation_type.Comment)
                insulation.attributeInsulation_type.SetComment(assetUpdate.InsulationTypeComment);

            insulation.attributeJacketing_type.SetValue(assetUpdate.JacketingType);

            accessibility.attributeLadder_Requirements.SetValue(assetUpdate.LadderRequirements);

            if (assetUpdate.LadderRequirementsComment != accessibility.attributeLadder_Requirements.Comment)
                accessibility.attributeLadder_Requirements.SetComment(assetUpdate.LadderRequirementsComment);

            if (assetUpdate.PossibleAsbestos != insulation.attributePossible_asbestos.CurrentValue)
                insulation.attributePossible_asbestos.SetValue(assetUpdate.PossibleAsbestos);

            if (assetUpdate.PossibleAsbestosComment != insulation.attributePossible_asbestos.Comment)
                insulation.attributePossible_asbestos.SetComment(assetUpdate.PossibleAsbestosComment);

            if (assetUpdate.RopeAccessRequired != accessibility.attributeRope_access_required.CurrentValue)
                accessibility.attributeRope_access_required.SetValue(assetUpdate.RopeAccessRequired);

            if (assetUpdate.RopeAccessRequiredComment != accessibility.attributeRope_access_required.Comment)
                accessibility.attributeRope_access_required.SetComment(assetUpdate.RopeAccessRequiredComment);

            if (assetUpdate.ScaffoldingRequired != accessibility.attributeScaffolding_required.CurrentValue)
                accessibility.attributeScaffolding_required.SetValue(assetUpdate.ScaffoldingRequired);

            if (assetUpdate.ScaffoldingRequiredComment != accessibility.attributeScaffolding_required.Comment)
                accessibility.attributeScaffolding_required.SetComment(assetUpdate.ScaffoldingRequiredComment);

            if (assetUpdate.SizeOfAllAccessOpenings !=
                internalAccessRequirements.attributeSize_of_all_accessible_openings.CurrentValue)
                internalAccessRequirements.attributeSize_of_all_accessible_openings.SetValue(assetUpdate
                    .SizeOfAllAccessOpenings);

            internalAccessRequirements.attributeVentilation_requirements.SetValue(assetUpdate.VentilationRequirements);

            if (assetUpdate.VentilationRequirementsComment !=
                internalAccessRequirements.attributeVentilation_requirements.Comment)
                internalAccessRequirements.attributeVentilation_requirements.SetComment(assetUpdate
                    .VentilationRequirementsComment);
        }

        /// <summary>
        ///     Updates an asset from a <see cref="AssetPPEUpdate" />
        /// </summary>
        /// <param name="asset"></param>
        /// <param name="assetPPEUpdate"></param>
        public static void Update(this Asset asset, AssetPPEUpdate assetPPEUpdate)
        {
            if (assetPPEUpdate.AbatementRequired != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPersonnelAccessConditions.sectionOvergrownvegetation
                    .attributeAbatement_required.SetValue(assetPPEUpdate.AbatementRequired);
            if (assetPPEUpdate.Atmosphere != null)
                asset.assetPPE.sectionPPERequirements.attributeAtmosphere.SetValue(assetPPEUpdate.Atmosphere);
            if (assetPPEUpdate.BreathingProtection != null)
                asset.assetPPE.sectionPPERequirements.attributeBreathing_Protection.SetValue(assetPPEUpdate
                    .BreathingProtection);
            if (assetPPEUpdate.ChemicalSuit != null)
                asset.assetPPE.sectionPPERequirements.attributeChemical_Suit.SetValue(assetPPEUpdate.ChemicalSuit);
            if (assetPPEUpdate.ControlAreaPermit != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPermittingRequired.attributeControl_Area_of_Permit
                    .SetValue(
                        assetPPEUpdate.ControlAreaPermit);
            if (assetPPEUpdate.DrainageNeeded != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPersonnelAccessConditions.sectionStandingWater
                    .attributeDrainage_needed.SetValue(assetPPEUpdate.DrainageNeeded);
            if (assetPPEUpdate.EarProtection != null)
                asset.assetPPE.sectionPPERequirements.attributeHearing_Protection
                    .SetValue(assetPPEUpdate.EarProtection);
            if (assetPPEUpdate.EyeProtection != null)
                asset.assetPPE.sectionPPERequirements.attributeEye_Protection.SetValue(assetPPEUpdate.EyeProtection);
            if (assetPPEUpdate.FallProtection != null)
                asset.assetPPE.sectionPPERequirements.attributeFall_Protection.SetValue(assetPPEUpdate.FallProtection);
            if (assetPPEUpdate.FireRetardantClothing != null)
                asset.assetPPE.sectionPPERequirements.attributeFire_Retardant_Clothing.SetValue(assetPPEUpdate
                    .FireRetardantClothing);
            if (assetPPEUpdate.FootProtection != null)
                asset.assetPPE.sectionPPERequirements.attributeFoot_Protection.SetValue(assetPPEUpdate.FootProtection);
            if (assetPPEUpdate.GeneralHotWork != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPermittingRequired.attributeGeneral_Hot_Work
                    .SetValue(assetPPEUpdate.GeneralHotWork);
            if (assetPPEUpdate.GeneralWork != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPermittingRequired.attributeGeneral_Work.SetValue(
                    assetPPEUpdate.GeneralWork);
            if (assetPPEUpdate.HardHatRequired != null)
                asset.assetPPE.sectionPPERequirements.attributeHard_Hat.SetValue(assetPPEUpdate.HardHatRequired);
            if (assetPPEUpdate.HazardousAreaPermit != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPermittingRequired.attributeHazardous_Area_Permit
                    .SetValue(assetPPEUpdate.HazardousAreaPermit);
            if (assetPPEUpdate.HoleWatchNeeded != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPermittingRequired.sectionConfinedSpaceRequirements
                    .attributeHole_watch_needed.SetValue(assetPPEUpdate.HoleWatchNeeded);
            if (assetPPEUpdate.OnSiteLeaks != null)
                asset.assetPPE.sectionGeneralSiteConditions.attributeAre_there_any_on_site_leaks.SetValue(assetPPEUpdate
                    .OnSiteLeaks);
            if (assetPPEUpdate.OnSiteLeaksComments != null)
                asset.assetPPE.sectionGeneralSiteConditions.attributeAre_there_any_on_site_leaks.SetComment(
                    assetPPEUpdate.OnSiteLeaksComments);
            if (assetPPEUpdate.OpenFlameHotWork != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPermittingRequired.attributeOpen_Flame_Hot_Work
                    .SetValue(assetPPEUpdate.OpenFlameHotWork);
            if (assetPPEUpdate.OvergrownVegetation != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPersonnelAccessConditions.sectionOvergrownvegetation
                    .attributeIs_there_overgrown_vegation.SetValue(assetPPEUpdate.OvergrownVegetation);
            if (assetPPEUpdate.PermitRequired != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPermittingRequired.sectionConfinedSpaceRequirements
                    .attributePermit_required.SetValue(assetPPEUpdate.PermitRequired);
            if (assetPPEUpdate.PersonnelAccessConditionNotes != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPersonnelAccessConditions
                    .attributeConditions_observed_on_site.SetComment(assetPPEUpdate.PersonnelAccessConditionNotes);
            if (assetPPEUpdate.PersonnelAccessConditions != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPersonnelAccessConditions
                    .attributeConditions_observed_on_site.SetValue(assetPPEUpdate.PersonnelAccessConditions);
            if (assetPPEUpdate.PowerAvailable != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPersonnelAccessConditions.attributePower_available
                    .SetValue(assetPPEUpdate.PowerAvailable);
            if (assetPPEUpdate.SafetyGloves != null)
                asset.assetPPE.sectionPPERequirements.attributeSafety_Gloves.SetValue(assetPPEUpdate.SafetyGloves);
            if (assetPPEUpdate.SnakeChapsRequired != null)
                asset.assetPPE.sectionPPERequirements.attributeSnake_Chaps.SetValue(assetPPEUpdate.SnakeChapsRequired);
            if (assetPPEUpdate.StandingWater != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPersonnelAccessConditions.sectionStandingWater
                    .attributeIs_there_standing_water.SetValue(assetPPEUpdate.StandingWater);
            if (assetPPEUpdate.VehicleAccessibility != null)
                asset.assetPPE.sectionGeneralSiteConditions.attributeVehicle_Accessibility.SetValue(assetPPEUpdate
                    .VehicleAccessibility);
            if (assetPPEUpdate.VehicleAccessibilityComments != null)
                asset.assetPPE.sectionGeneralSiteConditions.attributeVehicle_Accessibility.SetComment(assetPPEUpdate
                    .VehicleAccessibilityComments);
            if (assetPPEUpdate.WaterAvailable != null)
                asset.assetPPE.sectionGeneralSiteConditions.sectionPersonnelAccessConditions.attributeWater_available
                    .SetValue(assetPPEUpdate.WaterAvailable);
        }
    }
}