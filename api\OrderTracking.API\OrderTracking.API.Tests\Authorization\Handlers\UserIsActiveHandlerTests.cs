﻿using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Moq;
using NUnit.Framework;
using OrderTracking.API.AuthHandlers;
using OrderTracking.API.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Tests.Authorization.Handlers
{
    [TestFixture]
    public class UserIsActiveHandlerTests
    {
        [SetUp]
        public void SetUp()
        {
            _mockService = new Mock<IUserProfilesService>(MockBehavior.Strict);
            _handler = new UserIsActiveHandler(_mockService.Object);
        }

        private Mock<IUserProfilesService> _mockService;
        private UserIsActiveHandler _handler;
        private UserProfile _user;
        private AuthorizationHandlerContext _context;

        [Test]
        public async Task HandleAsync_UserIsActive_ContextHasSucceeded()
        {
            _user = new UserProfile { Id="<EMAIL>"};
            _mockService.Setup((service => service.GetAsync("<EMAIL>")))
                .ReturnsAsync(_user);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_user, _user,
                new[] {new UserIsActiveRequirement()});
            await _handler.HandleAsync(_context);
            Assert.That(_context.HasSucceeded);
        }

        [Test]
        public async Task HandleAsync_UserIsActive_NotAuthenticated_ContextHasFailed()
        {
            _user = new UserProfile { Id = "<EMAIL>" };
            _mockService.Setup((service => service.GetAsync("<EMAIL>")))
                .ReturnsAsync(_user);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContextNotAuthenticated(_user, _user,
                new[] { new UserIsActiveRequirement() });
            await _handler.HandleAsync(_context);
            Assert.That(_context.HasFailed);
        }


        [Test]
        public async Task HandleAsync_UserIsActive_UserIsNull_ContextHasFailed()
        {
            _user = new UserProfile { Id="<EMAIL>" };
             _mockService.Setup((service => service.GetAsync( "<EMAIL>"))).ReturnsAsync((UserProfile)null);
            _context = AuthorizationTestHelpers.CreateAuthorizationHandlerContext(_user, _user,
                new[] { new UserIsActiveRequirement() });
            await _handler.HandleAsync(_context);
            Assert.That(_context.HasFailed);
        }
    }
}
