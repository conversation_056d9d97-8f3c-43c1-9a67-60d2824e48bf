using System;

namespace ClientPortal.Shared.Models
{
    public class EquipmentCenter : IEmailRecipient
    {
        public string Id { get; set; }
        public string GivenName { get; set; }
        public string Surname { get; set; }


        public static IEmailRecipient CreateRecipient(string equipmentCenter)
        {
            var recipient = new EquipmentCenter();
            switch (equipmentCenter)
            {
                case "Joliet (1490 HTS)":
                    recipient.Id = "<EMAIL>";
                    recipient.GivenName = "Joliet";
                    recipient.Surname = "1490 HTS";
                    break;
                case "Alvin (1441 HTS)":
                    recipient.Id = "<EMAIL>";
                    recipient.GivenName = "Alvin";
                    recipient.Surname = "1441 HTS";
                    break;
                case "Geismar (1414 HTS)":
                    recipient.Id = "<EMAIL>";
                    recipient.GivenName = "Geismar";
                    recipient.Surname = "1414 HTS";
                    break;
                case "Charlotte (1415 HTS)":
                    recipient.Id = "<EMAIL>";
                    recipient.GivenName = "Charlotte";
                    recipient.Surname = "1415 HTS";
                    break;
                case "Harbor City (1439 HTS)":
                    recipient.Id = "<EMAIL>";
                    recipient.GivenName = "Harbor City";
                    recipient.Surname = "1439 HTS";
                    break;
                case "Swedesboro (1406 MBI)":
                    recipient.Id = "<EMAIL>";
                    recipient.GivenName = "Swedesboro";
                    recipient.Surname = "1406 MBI";
                    break;
                case "Joliet (1423 MBI)":
                    recipient.Id = "<EMAIL>";
                    recipient.GivenName = "Joliet";
                    recipient.Surname = "1423 MBI";
                    break;
                case "Pasadena (1432 MBI)":
                    recipient.Id = "<EMAIL>";
                    recipient.GivenName = "Pasadena";
                    recipient.Surname = "1432 MBI";
                    break;
                case "Geismar (1488 MBI)":
                    recipient.Id = "<EMAIL>";
                    recipient.GivenName = "Geismar";
                    recipient.Surname = "1488 MBI";
                    break;
                case "Charlotte (1489 MBI)":
                    recipient.Id = "<EMAIL>";
                    recipient.GivenName = "Charlotte";
                    recipient.Surname = "1489 MBI";
                    break;
                case "Benicia (1491 MBI)":
                    recipient.Id = "<EMAIL>";
                    recipient.GivenName = "Benicia";
                    recipient.Surname = "1491 MBI";
                    break;
                default:
                    throw new ArgumentException($"Invalid equipment center: {equipmentCenter}", nameof(equipmentCenter));
            }

            return recipient;
        }

    }
}