﻿//using System;
//using System.Data.Common;
//using System.Linq;
//using System.Threading.Tasks;
//using ClientPortal.Shared.Models;
//using DevExtreme.AspNet.Data.ResponseModel;
//using Microsoft.AspNetCore.Http;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.EntityFrameworkCore;
//using Microsoft.EntityFrameworkCore.Infrastructure;
//using Microsoft.Extensions.Logging;
//using Moq;
//using NUnit.Framework;
//using OrderTracking.API.Controllers;
//using OrderTracking.API.Interfaces;
//using OrderTracking.API.Interfaces.Services;
//using OrderTracking.API.Models;
//using OrderTracking.API.Services;
//using DataSourceLoadOptions = OrderTracking.API.Models.DataSourceLoadOptions;

//namespace OrderTracking.API.Tests
//{
//    [TestFixture]
//    public class OrdersControllerTests : IDisposable
//    {
//        private DbConnection _connection;

//        [Test]
//        public async Task Get_EmptyId_ForbidResult()
//        {
//            // Arrange
//            var mockLogger = new Mock<ILogger<OrdersController>>();
//            var mockLoader = new Mock<IClientPortalResultsLoader>();
//            var userProfiles = new Mock<IUserProfilesService>(MockBehavior.Loose);
//            userProfiles.Setup(service => service.GetAsync("<EMAIL>"))
//                .ReturnsAsync(new UserProfile());
//            var ordersService = new Mock<IOrdersService>(MockBehavior.Loose);
//            var httpContext = new Mock<HttpContext>(MockBehavior.Loose);
//            httpContext.SetupGet(context => context.User.Identity.Name).Returns("<EMAIL>");
//            var ordersController = new OrdersController(mockLogger.Object, userProfiles.Object, ordersService.Object,
//                mockLoader.Object);
//            ordersController.ControllerContext = new ControllerContext {HttpContext = httpContext.Object};

//            // Act
//            var response = await ordersController.Get(string.Empty);

//            // Assert
//            Assert.That(response is ForbidResult);
//        }

//        [Test]
//        public async Task TestSuccessIfCustomerAccountAndId()
//        {
//            // Arrange
//            var mockLogger = new Mock<ILogger<OrdersController>>();
//            var mockLoader = new Mock<IClientPortalResultsLoader>(MockBehavior.Loose);
//            mockLoader.Setup(service =>
//                    service.LoadResult(It.IsAny<DataSourceLoadOptions>(), It.IsAny<IQueryable<Order>>()))
//                .ReturnsAsync(new LoadResult());
//            var userProfiles = new Mock<IUserProfilesService>(MockBehavior.Loose);
//            userProfiles.Setup(service => service.GetAsync("<EMAIL>"))
//                .ReturnsAsync(new UserProfile {CustomerAccounts = {"200685"}, DistrictIds = {"1461"}});
//            var ordersService = new Mock<IOrdersService>(MockBehavior.Loose);
//            var blobStorageService = new Mock<IBlobStorageService>(MockBehavior.Loose);
//            var httpContext = new Mock<HttpContext>(MockBehavior.Loose);
//            httpContext.SetupGet(context => context.User.Identity.Name).Returns("<EMAIL>");
//            var ordersController = new OrdersController(mockLogger.Object, userProfiles.Object, ordersService.Object,
//                mockLoader.Object);
//            ordersController.ControllerContext = new ControllerContext {HttpContext = httpContext.Object};

//            // Act
//            var response = await ordersController.Get(new DataSourceLoadOptions());

//            // Assert
//            Assert.That(response is OkObjectResult);
//        }

//        [Test]
//        public async Task Test()
//        {
//            // Arrange
//            const string email = "<EMAIL>";

//            // Mock Http Context
//            var httpContext = new Mock<HttpContext>(MockBehavior.Loose);
//            httpContext.SetupGet(c => c.User.Identity.Name).Returns(email);

//            // Setup In-Memory EF
//            var contextOptions = new DbContextOptionsBuilder<OrderContext>()
//                .UseSqlite(InMemoryEFHelpers.CreateInMemoryDatabase())
//                .Options;
//            _connection = RelationalOptionsExtension.Extract(contextOptions).Connection;
//            await using var context = new OrderContext(contextOptions);
//            await context.Database.EnsureCreatedAsync();

//            // Mock User Profiles Service
//            var userProfiles = new Mock<IUserProfilesService>(MockBehavior.Loose);
//            userProfiles.Setup(u => u.GetAsync(It.Is<string>(x => x == email)))
//                .ReturnsAsync(new UserProfile {Email = email});

//            // Mock FormCollection
//            var formCollection = new Mock<IFormCollection>(MockBehavior.Loose);
//            formCollection.Setup(f => f[It.Is<string>(x => x == "salesLineRecId")]).Returns("1");

//            // Mock Orders Service
//            var ordersService = new Mock<IOrdersService>(MockBehavior.Loose);
//            ordersService.Setup(o => o.GetBySalesLineRecIdAsync(It.Is<long>(x => x == 1L)))
//                //.Returns(context.Orders.Where(o => o.SALESLINE_RECID == 1L));
//                .Returns(Queryable.Where(context.Orders, o => o.SALESLINE_RECID == 1L));

//            // Create the controller
//            var controller = new OrdersController(
//                new Mock<ILogger<OrdersController>>(MockBehavior.Loose).Object,
//                userProfiles.Object,
//                ordersService.Object,
//                new Mock<IClientPortalResultsLoader>(MockBehavior.Loose).Object)
//            {
//                ControllerContext = new ControllerContext {HttpContext = httpContext.Object}
//            };

//            // Act / Assert
//            Assert.DoesNotThrowAsync(async () => await controller.UploadFiles(formCollection.Object));
//        }

//        public void Dispose()
//        {
//            _connection?.Dispose();
//        }
//    }
//}