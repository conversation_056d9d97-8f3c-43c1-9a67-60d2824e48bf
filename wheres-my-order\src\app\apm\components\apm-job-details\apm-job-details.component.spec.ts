import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxButtonModule } from 'devextreme-angular/ui/button';
import { DxDateBoxModule } from 'devextreme-angular/ui/date-box';
import { DxTextBoxModule } from 'devextreme-angular/ui/text-box';

import { ApmJobDetailsComponent } from './apm-job-details.component';

describe('ApmJobDetailsComponent', () => {
    let component: ApmJobDetailsComponent;
    let fixture: ComponentFixture<ApmJobDetailsComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DxTextBoxModule, DxButtonModule, DxDateBoxModule],
            declarations: [ApmJobDetailsComponent],
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ApmJobDetailsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
