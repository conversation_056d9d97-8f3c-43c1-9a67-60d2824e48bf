﻿using System;
using ClientPortal.Shared.Models.Helpers;
using Dapper.Contrib.Extensions;
using Newtonsoft.Json;

namespace ClientPortal.Shared.Models
{
    [Table("OrdersJobs")]
    public class OrdersJob
    {
        [JsonProperty(PropertyName = "id")]
        [ExplicitKey]
        public string Id { get; set; }

        [JsonProperty(PropertyName = "status")]
        public string Status { get; set; }

        [JsonProperty(PropertyName = "startedUTC")]
        [JsonConverter(typeof(AsUTCDateTimeConverter))]
        public DateTime StartedUTC { get; set; }

        [JsonProperty(PropertyName = "endedUTC")]
        [JsonConverter(typeof(AsUTCDateTimeConverter))]
        public DateTime EndedUTC { get; set; }

        [JsonProperty(PropertyName = "inserted")]
        public int Inserted { get; set; }

        [JsonProperty(PropertyName = "updated")]
        public int Updated { get; set; }

        [JsonProperty(PropertyName = "deleted")]
        public int Deleted { get; set; }

        public static OrdersJob CreateSkippedJob() =>
            new OrdersJob
            {
                Id = Guid.NewGuid().ToString(),
                StartedUTC = DateTime.UtcNow,
                EndedUTC = DateTime.UtcNow,
                Status = "Skipped"
            };

        public static OrdersJob CreateNewJob() => new OrdersJob
            {Id = Guid.NewGuid().ToString(), StartedUTC = DateTime.UtcNow};
    }
}