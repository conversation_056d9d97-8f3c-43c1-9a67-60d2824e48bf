import dxDataGrid from 'devextreme/ui/data_grid';

export class RowExpandingHandler {
    static handle(e: {
        cancel: boolean;
        component: dxDataGrid;
        element: HTMLElement;
        key: any;
        model: any;
    }) {
        // Only allow one row to be expanded at a time.  As long
        // as e.cancel remains false, the row that should expand
        // will expand after the execution of this method finishes.
        e.component.collapseAll(-1);

        // In addition to expanding the row, select the row that
        // was requested to be expanded.
        e.component.selectRows([e.key], false);
    }
}
