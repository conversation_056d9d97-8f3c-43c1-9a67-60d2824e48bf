import { AssetManagementSite } from '../models';
import { SiteLabelPipe } from './site-label.pipe';

describe('SiteLabelPipe', () => {
    const pipe = new SiteLabelPipe();
    it('create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should show the correct site label', () => {
        const site = new AssetManagementSite({
            rsitE_GROUP: 'Site Group',
            rsitE_NAME: 'Site Name',
        });

        expect(pipe.transform(site)).toBe('Site Group - Site Name');
    });
});
