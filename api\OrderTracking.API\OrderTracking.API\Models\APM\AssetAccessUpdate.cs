﻿using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class AssetAccessUpdate
    {
        [JsonProperty(PropertyName = "id")] public string? Id { get; set; }

        [JsonProperty(PropertyName = "hasInsulation")]
        public string HasInsulation { get; set; }

        [JsonProperty(PropertyName = "possibleAsbestos")]
        public string PossibleAsbestos { get; set; }

        [JsonProperty(PropertyName = "possibleAsbestosComment")]
        public string PossibleAsbestosComment { get; set; }

        [JsonProperty(PropertyName = "jacketingType")]
        public string[] JacketingType { get; set; }

        [JsonProperty(PropertyName = "insulationType")]
        public string[] InsulationType { get; set; }

        [JsonProperty(PropertyName = "insulationTypeComment")]
        public string InsulationTypeComment { get; set; }

        [JsonProperty(PropertyName = "insulationRemovalRequired")]
        public string InsulationRemovalRequired { get; set; }

        [JsonProperty(PropertyName = "insulationRemovalRequiredComment")]
        public string InsulationRemovalRequiredComment { get; set; }

        [JsonProperty(PropertyName = "heatTracing")]
        public string[] HeatTracing { get; set; }

        [JsonProperty(PropertyName = "heatTracingComment")]
        public string HeatTracingComment { get; set; }

        [JsonProperty(PropertyName = "existingInspectionPorts")]
        public string ExistingInspectionPorts { get; set; }

        [JsonProperty(PropertyName = "insulationPlugsMissing")]
        public string InsulationPlugsMissing { get; set; }

        [JsonProperty(PropertyName = "insulationPlugsMissingComment")]
        public string InsulationPlugsMissingComment { get; set; }

        [JsonProperty(PropertyName = "additionalPortsNeeded")]
        public string AdditionalPortsNeeded { get; set; }

        [JsonProperty(PropertyName = "additionalPortsNeededComment")]
        public string AdditionalPortsNeededComment { get; set; }

        [JsonProperty(PropertyName = "coatingType")]
        public string[] CoatingType { get; set; }

        [JsonProperty(PropertyName = "coatingTypeComment")]
        public string CoatingTypeComment { get; set; }

        [JsonProperty(PropertyName = "coatingCondition")]
        public string[] CoatingCondition { get; set; }

        [JsonProperty(PropertyName = "coatingConditionsObserved")]
        public string[] CoatingConditionsObserved { get; set; }

        [JsonProperty(PropertyName = "coatingConditionsObservedComment")]
        public string CoatingConditionsObservedComment { get; set; }

        [JsonProperty(PropertyName = "coatingRemovalRequired")]
        public string CoatingRemovalRequired { get; set; }

        [JsonProperty(PropertyName = "coatingRemovalRequiredComment")]
        public string CoatingRemovalRequiredComment { get; set; }

        [JsonProperty(PropertyName = "corrosionIdentified")]
        public string[] CorrosionIdentified { get; set; }

        [JsonProperty(PropertyName = "corrosionIdentifiedComment")]
        public string CorrosionIdentifiedComment { get; set; }

        [JsonProperty(PropertyName = "corrosionRemovalRecommendation")]
        public string[] CorrrosionRemovalRecommendation { get; set; }

        [JsonProperty(PropertyName = "corrosionRemovalRecommendationComment")]
        public string CorrosionRemovalRecommendationComment { get; set; }

        [JsonProperty(PropertyName = "fixedEquipmentLaddersStairwaysPlatformsInstalled")]
        public string FixedEquipmentLaddersStairwaysPlatformsInstalled { get; set; }

        [JsonProperty(PropertyName = "fixedEquipmentLaddersStairwaysPlatformsInstalledComment")]
        public string FixedEquipmentLaddersStairwaysPlatformsInstalledComment { get; set; }

        [JsonProperty(PropertyName = "allComponentsUnder4FeetInHeight")]
        public string AllComponentsUnder4FeetInHeight { get; set; }

        [JsonProperty(PropertyName = "allComponentsUnder4FeetInHeightComment")]
        public string AllComponentsUnder4FeetInHeightComment { get; set; }

        [JsonProperty(PropertyName = "ladderRequirements")]
        public string[] LadderRequirements { get; set; }

        [JsonProperty(PropertyName = "ladderRequirementsComment")]
        public string LadderRequirementsComment { get; set; }

        [JsonProperty(PropertyName = "aerialLiftNeeded")]
        public string AerialLiftNeeded { get; set; }

        [JsonProperty(PropertyName = "aerialLiftNeededComment")]
        public string AerialLiftNeededComment { get; set; }

        [JsonProperty(PropertyName = "accessForAerialLiftForAllLocationsAtHeight")]
        public string AccessForAerialLiftForAllLocationsAtHeight { get; set; }

        [JsonProperty(PropertyName = "gasPoweredPermitted")]
        public string GasPoweredPermitted { get; set; }

        [JsonProperty(PropertyName = "batteryPoweredPermitted")]
        public string BatteryPoweredPermitted { get; set; }

        [JsonProperty(PropertyName = "clientRequiredProofOfTraining")]
        public string ClientRequiredProofOfTraining { get; set; }

        [JsonProperty(PropertyName = "clientProvidedOperator")]
        public string ClientProvidedOperator { get; set; }

        [JsonProperty(PropertyName = "estimatedDistanceToAnyLiveElectricalOverheadLines")]
        public int? EstimatedDistanceToAnyLiveElectricalOverheadLines { get; set; }

        [JsonProperty(PropertyName = "scaffoldingRequired")]
        public string ScaffoldingRequired { get; set; }

        [JsonProperty(PropertyName = "scaffoldingRequiredComment")]
        public string ScaffoldingRequiredComment { get; set; }

        [JsonProperty(PropertyName = "ropeAccessRequired")]
        public string RopeAccessRequired { get; set; }

        [JsonProperty(PropertyName = "ropeAccessRequiredComment")]
        public string RopeAccessRequiredComment { get; set; }

        [JsonProperty(PropertyName = "assetOutOfService")]
        public string AssetOutOfService { get; set; }

        [JsonProperty(PropertyName = "inspectionOpeningsPresent")]
        public string InspectionOpeningsPresent { get; set; }

        [JsonProperty(PropertyName = "inspectionOpeningTypes")]
        public string[] InspectionOpeningTypes { get; set; }

        [JsonProperty(PropertyName = "inspectionOpeningTypesComment")]
        public string InspectionOpeningTypesComment { get; set; }

        [JsonProperty(PropertyName = "sizeOfAllAccessOpenings")]
        public string SizeOfAllAccessOpenings { get; set; }

        [JsonProperty(PropertyName = "ventilationRequirements")]
        public string[] VentilationRequirements { get; set; }

        [JsonProperty(PropertyName = "ventilationRequirementsComment")]
        public string VentilationRequirementsComment { get; set; }

        [JsonProperty(PropertyName = "cleaningRecommendations")]
        public string[] CleaningRecommendations { get; set; }

        [JsonProperty(PropertyName = "cleaningRecommendationsComment")]
        public string CleaningRecommendationsComment { get; set; }

        [JsonProperty(PropertyName = "cleaningServiceReview")]
        public string CleaningServiceReview { get; set; }

        [JsonProperty(PropertyName = "cleaningServiceReviewComment")]
        public string CleaningServiceReviewComment { get; set; }

        [JsonProperty(PropertyName = "coatingLinerType")]
        public string[] CoatingLinerType { get; set; }

        [JsonProperty(PropertyName = "coatingLinerTypeComment")]
        public string CoatingLinerTypeComment { get; set; }

        [JsonProperty(PropertyName = "coatingLinerConditions")]
        public string[] CoatingLinerConditions { get; set; }

        [JsonProperty(PropertyName = "coatingLinerConditionsObserved")]
        public string[] CoatingLinerConditionsObserved { get; set; }

        [JsonProperty(PropertyName = "coatingLinerConditionsObservedComment")]
        public string CoatingLinerConditionsObservedComment { get; set; }
    }
}