<div class="dx-card responsive-paddings content-block"
     style="position: relative">
    <dx-load-indicator *ngIf="loading"
                       style="position: absolute; left: calc(50% - 30px); top: calc(50% - 30px)"
                       height="60"
                       width="60"></dx-load-indicator>

    <app-asset-categories-selector (categories)="onCategoriesChanged($event)">
    </app-asset-categories-selector>

    <dx-pie-chart [dataSource]="inspectionStatusBreakdown?.inspectionStatuses"
                  [type]="'doughnut'"
                  [centerTemplate]="'centerTemplate'"
                  [palette]="'Soft Pastel'"
                  (onDrawn)="onDrawn($event)"
                  [resolveLabelOverlapping]="'shift'">

        <dxi-series [argumentField]="'status'"
                    [valueField]="'count'">
            <dxo-label [visible]="true"
                       [customizeText]="customizeLabel">
                <dxo-connector [visible]="true"></dxo-connector>
            </dxo-label>
        </dxi-series>

        <dxo-legend [horizontalAlignment]="'center'"
                    [verticalAlignment]="'bottom'"></dxo-legend>

        <svg *dxTemplate="let pieChart of 'centerTemplate'">
            <text *ngIf="!loading"
                  text-anchor="middle"
                  style="font-size: 0.5rem;"
                  x="100"
                  y="120"
                  fill="#494949">
                <tspan style="font-size: 1.5rem"
                       x="100"
                       dy="20px">Inspection</tspan>
                <tspan style="font-size: 1.5rem"
                       x="100"
                       dy="20px">Status</tspan>
            </text>
        </svg>
    </dx-pie-chart>

    <p style="display: block; text-align: center;">
        Total Tasks: {{inspectionStatusBreakdown?.taskCount ?? 0}}
    </p>
</div>
