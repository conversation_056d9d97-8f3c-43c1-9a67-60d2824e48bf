import {
    AfterViewInit,
    Component,
    Input,
    OnDestroy,
    ViewChild
} from '@angular/core';
import { DxFormComponent } from 'devextreme-angular';
import { FocusOutEvent } from 'devextreme/ui/number_box';
import { ValidationCallbackData } from 'devextreme/ui/validation_rules';
import { isNullOrUndefined } from '../../../shared/helpers';
import { AssetCategory, SupportedAssetCategories } from '../../models';
import { ApmService } from '../../services';

export type NewAssetTemp = {
    assetId: string;
    assetName: string;
    category: AssetCategory;
    startLat: number;
    startLong: number;
    endLat: number;
    endLong: number;
    locationId: string;
    projectId: string;
};

@Component({
    selector: 'app-asset-creation',
    templateUrl: './asset-creation.component.html',
    styleUrls: ['./asset-creation.component.scss']
})
export class AssetCreationComponent implements AfterViewInit, OnDestroy {
    readonly formData: NewAssetTemp = {
        assetId: undefined,
        assetName: undefined,
        category: undefined,
        endLat: null,
        endLong: null,
        locationId: undefined,
        projectId: undefined,
        startLat: null,
        startLong: null
    };
    readonly assetCategories = SupportedAssetCategories;
    projectsDataSource = this._apm.projectDataSource;

    @ViewChild(DxFormComponent) form: DxFormComponent;

    @Input() projectId: string;

    constructor(private readonly _apm: ApmService) {}

    ngAfterViewInit(): void {
        if (this.projectId) this.formData.projectId = this.projectId;
    }

    ngOnDestroy(): void {
        this.projectsDataSource?.filter(null);
    }

    validate(): boolean {
        return this.form.instance.validate().isValid;
    }

    validateStartGIS = (e: ValidationCallbackData) => {
        return this.validateGISPair('startLat', 'startLong');
    };

    validateEndGIS = (e: ValidationCallbackData) => {
        return this.validateGISPair('endLat', 'endLong');
    };

    startLongFocusOut = (e: FocusOutEvent) => {
        this.form.instance.validate();
    };

    private validateGISPair(
        latName: keyof Pick<NewAssetTemp, 'startLat' | 'endLat'>,
        longName: keyof Pick<NewAssetTemp, 'startLong' | 'endLong'>
    ) {
        const lat = this.form.instance.getEditor(latName).option('value');
        const long = this.form.instance.getEditor(longName).option('value');
        if (isNullOrUndefined(lat) && isNullOrUndefined(long)) return true;
        if (isNullOrUndefined(lat) && !isNullOrUndefined(long)) return false;
        if (!isNullOrUndefined(lat) && isNullOrUndefined(long)) return false;
        return true;
    }
}
