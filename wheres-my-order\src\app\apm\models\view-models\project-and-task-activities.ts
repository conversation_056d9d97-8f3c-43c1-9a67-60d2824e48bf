export type TrackedActivityVM = {
    activityType: 'project' | 'task';
    databaseId: string;
    date: string;
    clientWorkOrder: string;
    technician: string;
    taskType: string;
    duration: number;
    count?: number;
    taskId?: string;
};

export type ProjectAndTaskActivities = {
    projectActivities: {
        activities: ProjectActivityItem[];
        createdBy: string;
        createdTime: string;
        databaseId: string;
        date: DateAttribute;
        displayName: string;
        user: UserAttribute;
        workOrderNumber: WorkOrderNumberAttribute;
    }[];
    taskActivities: {
        activities: TaskActivityItem[];
        createdBy: string;
        createdTime: string;
        databaseId: string;
        date: DateAttribute;
        displayName: string;
        user: UserAttribute;
        workOrderNumber: WorkOrderNumberAttribute;
        taskId: string;
    }[];
};

type ProjectActivityItem = {
    count: CountAttribute;
    displayName: string;
    duration: DurationAttribute;
    name: string;
};

type TaskActivityItem = {
    allowNegatives: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    comment: unknown; // TODO:
    commentChangeLog: unknown; // TODO:
    currentPendingOrValue: number;
    currentValue: number;
    databaseName: unknown; // TODO:
    displayName: string;
    isQueryable: boolean;
    pendingValue: unknown; // TODO:
    photoChangeLog: unknown; // TODO:
    photos: unknown[]; // TODO:
    previewText: string;
    unit: unknown; // TODO:
    valueChangeLog: unknown; // TODO:
};

type DateAttribute = {
    areCommentsRequired: boolean;
    attributeType: 'string';
    comment: unknown; // TODO:
    commentChangeLog: unknown; // TODO:
    currentPendingOrValue: string;
    currentValue: string;
    currentValueDateTime: string;
    databaseName: unknown; // TODO:
    displayName: string;
    isQueryable: boolean;
    pendingValue: unknown; // TODO:
    photoChangeLog: unknown; // TODO:
    photos: unknown[]; // TODO:
    previewText: string;
    valueChangeLog: unknown; // TODO:
};

type UserAttribute = {
    areCommentsRequired: boolean;
    attributeType: string;
    comment: unknown; // TODO:
    commentChangeLog: unknown; // TODO:
    currentPendingOrValue: unknown; // TODO:
    currentValue: string;
    databaseName: unknown; // TODO:
    displayName: string;
    isQueryable: boolean;
    pendingValue: unknown; // TODO:
    photoChangeLog: unknown; // TODO:
    photos: unknown[]; // TODO:
    previewText: string;
    queryableValue: unknown; // TODO:
    valueChangeLog: unknown; // TODO:
};

type WorkOrderNumberAttribute = {
    areCommentsRequired: boolean;
    attributeType: string;
    comment: unknown; // TODO:
    commentChangeLog: unknown; // TODO:
    currentPendingOrValue: string;
    currentValue: string;
    databaseName: string;
    displayName: string;
    isQueryable: boolean;
    pendingValue: unknown; // TODO:
    photoChangeLog: unknown; // TODO:
    photos: unknown[]; // TODO:
    previewText: string;
    queryableValue: unknown; // TODO:
    valueChangeLog: unknown; // TODO:
};

type CountAttribute = {
    allowNegatives: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    comment: unknown; // TODO:
    commentChangeLog: unknown; // TODO:
    currentPendingOrValue: unknown; // TODO:
    currentValue: number;
    databaseName: unknown; // TODO:
    displayName: string;
    isQueryable: boolean;
    pendingValue: unknown; // TODO:
    photoChangeLog: unknown; // TODO:
    photos: unknown[]; // TODO:
    previewText: string;
    unit: unknown; // TODO:
    valueChangeLog: unknown; // TODO:
};

type DurationAttribute = {
    allowNegatives: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    comment: unknown; // TODO:
    commentChangeLog: unknown; // TODO:
    currentPendingOrValue: unknown; // TODO:
    currentValue: number;
    databaseName: unknown; // TODO:
    displayName: string;
    isQueryable: boolean;
    pendingValue: unknown; // TODO:
    photoChangeLog: unknown; // TODO:
    photos: unknown[]; // TODO:
    previewText: string;
    unit: unknown; // TODO:
    valueChangeLog: unknown; // TODO:
};
