using System;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;

namespace OrderTracking.API.Services
{
    public interface IUserAgreementService
    {
        /// <summary>
        ///     Email a PDF copy of the user agreement to the recipient
        /// </summary>
        /// <param name="recipient"></param>
        /// <param name="clientName"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        Task EmailPDFAsync(UserProfile recipient, string clientName);
    }
}