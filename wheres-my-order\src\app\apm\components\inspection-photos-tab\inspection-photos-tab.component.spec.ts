import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxButtonModule } from 'devextreme-angular/ui/button';
import { DxGalleryModule } from 'devextreme-angular/ui/gallery';
import { DxPopupModule } from 'devextreme-angular/ui/popup';
import { DxTileViewModule } from 'devextreme-angular/ui/tile-view';

import { SafePipe } from '../../../shared/pipes';
import {
    InspectionPhotosTabComponent
} from './inspection-photos-tab.component';

describe('InspectionPhotosTabComponent', () => {
    let component: InspectionPhotosTabComponent;
    let fixture: ComponentFixture<InspectionPhotosTabComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxPopupModule,
                DxTileViewModule,
                DxGalleryModule,
                DxButtonModule,
            ],
            declarations: [InspectionPhotosTabComponent, SafePipe],
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(InspectionPhotosTabComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
