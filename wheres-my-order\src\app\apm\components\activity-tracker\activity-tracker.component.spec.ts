import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxDataGridModule } from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { ApmService } from '../../services';
import { ActivityTrackerComponent } from './activity-tracker.component';

describe('ActivityTrackerComponent', () => {
    let component: ActivityTrackerComponent;
    let fixture: ComponentFixture<ActivityTrackerComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxDataGridModule,
                HttpClientTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [ActivityTrackerComponent],
            providers: [{ provide: ApmService, useValue: {} }]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ActivityTrackerComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
