﻿using System;
using System.Collections.Generic;
using System.Linq;
using ClientPortal.Shared.Extensions;
using ClientPortal.Shared.Models;
using NUnit.Framework;

namespace OrderTracking.API.Tests
{
    [TestFixture]
    public class UserProfileExtensionsTests
    {
        [Test]
        public void HasWorkEmail_HasOutlookEmail_ReturnsFalse()
        {
            // Arrange
            var user = new UserProfile {Id = "<EMAIL>"};

            // Act
            var hasWorkEmail = user.HasWorkEmail();

            // Assert
            Assert.That(hasWorkEmail, Is.False);
        }

        [Test]
        public void HasWorkEmail_HasGmailEmail_ReturnsFalse()
        {
            // Arrange
            var user = new UserProfile {Id = "<EMAIL>"};

            // Act
            var hasWorkEmail = user.HasWorkEmail();

            // Assert
            Assert.That(hasWorkEmail, Is.False);
        }

        [Test]
        public void HasWorkEmail_HasYahooEmail_ReturnsFalse()
        {
            // Arrange
            var user = new UserProfile {Id = "<EMAIL>"};

            // Act
            var hasWorkEmail = user.HasWorkEmail();

            // Assert
            Assert.That(hasWorkEmail, Is.False);
        }

        [Test]
        public void HasWorkEmail_HasInboxEmail_ReturnsFalse()
        {
            // Arrange
            var user = new UserProfile {Id = "<EMAIL>"};

            // Act
            var hasWorkEmail = user.HasWorkEmail();

            // Assert
            Assert.That(hasWorkEmail, Is.False);
        }

        [Test]
        public void HasWorkEmail_HasICloudEmail_ReturnsFalse()
        {
            // Arrange
            var user = new UserProfile {Id = "<EMAIL>"};

            // Act
            var hasWorkEmail = user.HasWorkEmail();

            // Assert
            Assert.That(hasWorkEmail, Is.False);
        }

        [Test]
        public void HasWorkEmail_HasMailEmail_ReturnsFalse()
        {
            // Arrange
            var user = new UserProfile {Id = "<EMAIL>"};

            // Act
            var hasWorkEmail = user.HasWorkEmail();

            // Assert
            Assert.That(hasWorkEmail, Is.False);
        }

        [Test]
        public void HasWorkEmail_HasAOLEmail_ReturnsFalse()
        {
            // Arrange
            var user = new UserProfile {Id = "<EMAIL>"};

            // Act
            var hasWorkEmail = user.HasWorkEmail();

            // Assert
            Assert.That(hasWorkEmail, Is.False);
        }

        [Test]
        public void HasWorkEmail_HasZohoEmail_ReturnsFalse()
        {
            // Arrange
            var user = new UserProfile {Id = "<EMAIL>"};

            // Act
            var hasWorkEmail = user.HasWorkEmail();

            // Assert
            Assert.That(hasWorkEmail, Is.False);
        }

        [Test]
        public void HasWorkEmail_HasYandexEmail_ReturnsFalse()
        {
            // Arrange
            var user = new UserProfile {Id = "<EMAIL>"};

            // Act
            var hasWorkEmail = user.HasWorkEmail();

            // Assert
            Assert.That(hasWorkEmail, Is.False);
        }

        [Test]
        public void HasWorkEmail_HasComcastEmail_ReturnsFalse()
        {
            // Arrange
            var user = new UserProfile {Id = "<EMAIL>"};

            // Act
            var hasWorkEmail = user.HasWorkEmail();

            // Assert
            Assert.That(hasWorkEmail, Is.False);
        }

        [Test]
        public void HasWorkEmail_HasTeamIncEmail_ReturnsTrue()
        {
            // Arrange
            var user = new UserProfile {Id = "<EMAIL>"};

            // Act
            var hasWorkEmail = user.HasWorkEmail();

            // Assert
            Assert.That(hasWorkEmail, Is.True);
        }

        [Test]
        public void HasWorkEmail_HasNullEmail_ThrowsArgumentNullException()
        {
            // Arrange
            var user = new UserProfile {Id = null};

            // Act
            void Act()
            {
                user.HasWorkEmail();
            }

            // Assert
            Assert.Throws<ArgumentNullException>(Act);
        }


        [Test]
        public void UserProfileExtensions_assignableRoles_returnsAllForAppAdmin()
        {
            // Arrange
            var roles = CreateRoleList();
            var user = CreateUser("app:admin");
            // Act / Assert
            Assert.That(user.AssignableRoles(roles).Count(), Is.EqualTo(roles.Count()));
        }

        [Test]
        public void UserProfileExtensions_assignableRoles_returnsZeroForNonAdmin()
        {
            // Arrange
            var roles = CreateRoleList();
            var user = CreateUser("wmo:something");
            // Act / Assert
            Assert.That(user.AssignableRoles(roles).Count(), Is.EqualTo(0));
        }

        [Test]
        public void UserProfileExtensions_assignableRoles_returnsWMOForWMOAdmin()
        {
            // Arrange
            var roles = CreateRoleList();
            var user = CreateUser("wmo:admin");
            // Act / Assert
            // the counts that I am asserting are based on the number of WMO roles created in
            // the CreateRoleList method
            Assert.That(user.AssignableRoles(roles).Count(), Is.EqualTo(2));
        }

        [Test]
        public void UserProfileExtensions_assignableRoles_returnsPortalForPortalAdmin()
        {
            // Arrange
            var roles = CreateRoleList();
            var user = CreateUser("portal:admin");
            // Act / Assert
            // the counts that I am asserting are based on the number of WMO roles created in
            // the CreateRoleList method
            Assert.That(user.AssignableRoles(roles).Count(), Is.EqualTo(2));
        }


        private static UserProfile CreateUser(string roleName) =>
            new UserProfile
            {
                Name = "Admin",
                Roles = {roleName}
            };

        private static IEnumerable<Role> CreateRoleList() =>
            new List<Role>
            {
                new Role("app:admin"),
                new Role("WMo:admin"),
                new Role("eDR:reader"),
                new Role("WmO:testUser"),
                new Role("EdR:user"),
                new Role("Test:admin"),
                new Role("portal:admin"),
                new Role("Portal:user")
            };
    }
}