using System;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderTracking.API.Interfaces.Services;
using OrderTracking.API.Models;

namespace OrderTracking.API.Services.APM
{
    /// <summary>
    /// 
    /// </summary>
    public class APMBlobStorageService : BlobStorageService, IAPMBlobStorageService
    {
        //private readonly string _apiKey;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="options"></param>
        /// <param name="logger"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public APMBlobStorageService(ILogger<APMBlobStorageService> logger, IOptions<BlobStorage> options) : base(options, logger)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            //ContainerClient = ServiceClient.GetBlobContainerClient(options.Value.APMContainer) ??
            //                  ServiceClient.CreateBlobContainer(options.Value.APMContainer);
            //_apiKey = options.Value.APMKey;
            logger.LogInformation("Created container client for APM");
        }

        /// <summary>
        /// generates a sas token that will grant read privilege for one hour
        /// </summary>
        /// <returns></returns>
        //public string GetSasToken()
        //{
        //    // Create a SAS token that's valid for one hour.
        //    AccountSasBuilder sasBuilder = new AccountSasBuilder()
        //    {
        //        Services = AccountSasServices.Blobs,
        //        ResourceTypes = AccountSasResourceTypes.Object,
        //        ExpiresOn = DateTimeOffset.UtcNow.AddHours(1),
        //        Protocol = SasProtocol.Https
        //    };

        //    sasBuilder.SetPermissions(AccountSasPermissions.Read);
        //    var key = new StorageSharedKeyCredential("aznascpiadevsa",
        //        _apiKey);
        //    // Use the key to get the SAS token.
        //    string sasToken = sasBuilder.ToSasQueryParameters(key).ToString();

        //    return sasToken;
        //}
    }

    //public class APMReportingBlobStorageService : BlobStorageService, IAPMReportingBlobStorageService
    //{
    //    private readonly string _apiKey;

    //    public APMReportingBlobStorageService(ILogger<APMReportingBlobStorageService> logger, IOptions<BlobStorage> options) : base(options, logger)
    //    {
    //        if (options == null) throw new ArgumentNullException(nameof(options));

    //        ContainerClient = ServiceClient.GetBlobContainerClient(options.Value.APMReportingContainer) ??
    //                          ServiceClient.CreateBlobContainer(options.Value.APMReportingContainer,
    //                              PublicAccessType.Blob);
    //        _apiKey = options.Value.APMReportingKey;
    //        logger.LogInformation("Created container client for APM Reporting");
    //    }

    //    /// <summary>
    //    /// generates a sas token that will grant read privilege for one hour
    //    /// </summary>
    //    /// 
    //    /// <returns></returns>
    //    public string GetSasToken()
    //    {
    //        // Create a SAS token that's valid for one hour.
    //        AccountSasBuilder sasBuilder = new AccountSasBuilder()
    //        {
    //            Services = AccountSasServices.Blobs,
    //            ResourceTypes = AccountSasResourceTypes.Object,
    //            ExpiresOn = DateTimeOffset.UtcNow.AddHours(1),
    //            Protocol = SasProtocol.Https
    //        };
    //        sasBuilder.SetPermissions(AccountSasPermissions.Read);

    //        var key = new StorageSharedKeyCredential("tdclientportalfiles",
    //            _apiKey);
    //        // Use the key to get the SAS token.
    //        string sasToken = sasBuilder.ToSasQueryParameters(key).ToString();

    //        return sasToken;
    //    }
    //}

    //public interface IAPMReportingBlobStorageService : IBlobStorageService
    //{
    //    /// <summary>
    //    ///     generates a short term sas token for blob access
    //    /// </summary>
    //    /// <returns></returns>
    //    string GetSasToken();
    //}
}