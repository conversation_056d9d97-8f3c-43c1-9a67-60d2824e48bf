<div id="assetsDoughnut"
     style="position: relative"
     class="dx-card responsive-paddings content-block pie-container">
    <dx-load-indicator *ngIf="loading"
                       id="large-indicator"
                       style="position: absolute; left: calc(50% - 30px); top: calc(50% - 30px)"
                       height="60"
                       width="60"></dx-load-indicator>
    <dx-pie-chart [type]="'doughnut'"
                  [dataSource]="chartData"
                  [centerTemplate]="'centerTemplate'"
                  [palette]="'Soft Pastel'"
                  (onDrawn)="onDrawn($event)"
                  [resolveLabelOverlapping]="'shift'">

        <dxi-series [argumentField]="'type'"
                    [valueField]="'count'">
            <dxo-label [visible]="true"
                       [customizeText]="customizeLabel">
                <dxo-connector [visible]="true"></dxo-connector>
            </dxo-label>
        </dxi-series>

        <dxo-legend [horizontalAlignment]="'center'"
                    [verticalAlignment]="'bottom'"></dxo-legend>

        <svg *dxTemplate="let pieChart of 'centerTemplate'">
            <text *ngIf="!loading"
                  text-anchor="middle"
                  style="font-size: 1rem"
                  x="100"
                  y="120"
                  fill="#494949">
                <tspan style="font-size: 2rem;"
                       x="100"
                       dy="20px">Assets</tspan>
            </text>
        </svg>
    </dx-pie-chart>
    <div>
        <p style="display: block; text-align: center;">Total Assets:
            {{totalAssets ?? 0}}</p>
    </div>
</div>