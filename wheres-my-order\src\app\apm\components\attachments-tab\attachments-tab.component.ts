import { Component, Input } from '@angular/core';
import { ToolbarPreparingEvent } from 'devextreme/ui/data_grid';
import { saveAs } from 'file-saver';
import { ToastrService } from 'ngx-toastr';
import { EMPTY, Observable, ReplaySubject } from 'rxjs';
import { catchError, map, shareReplay, switchMap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { AuthService, DataGridService } from '../../../shared/services';
import { WorkOrder } from '../../models';
import { ApmService } from '../../services';

@Component({
    selector: 'app-attachments-tab',
    templateUrl: './attachments-tab.component.html',
    styleUrls: ['./attachments-tab.component.scss']
})
export class AttachmentsTabComponent {
    private _workOrder: WorkOrder;
    private workOrderId = new ReplaySubject<string>();

    /** List of blobs uploaded and associated with the work order of this component */
    blobs$: Observable<any[]> = this.workOrderId.asObservable().pipe(
        switchMap((id) => this._apm.getWorkOrderFiles(id)),
        shareReplay()
    );

    /** Work Order associated with this component */
    @Input() set workOrder(value: WorkOrder) {
        this._workOrder = value;
        if (this._workOrder) this.workOrderId.next(this._workOrder.id);
    }
    get workOrder(): WorkOrder {
        return this._workOrder;
    }

    @Input() allowEditing: boolean;

    /** Show upload popup or not */
    showUploadPopup = false;

    /** Upload URL for the dx-file-uploader in component template */
    get uploadUrl(): string | null {
        if (this.workOrder?.id)
            return `${environment.api.url}/APM/InspectionFiles/${this.workOrder.id}`;
        else return null;
    }

    /** Headers to send with http call for file operations (authorization) */
    uploadHeaders$ = this._auth.acquireTokenSuccess$.pipe(
        map((payload) => ({
            Authorization: `Bearer ${payload.accessToken}`
        }))
    );

    constructor(
        private readonly _auth: AuthService,
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService,
        private readonly _grid: DataGridService
    ) {}

    onToolbarPreparing(e: ToolbarPreparingEvent) {
        e.toolbarOptions.items.unshift({
            widget: 'dxButton',
            options: {
                icon: 'fa fa-undo',
                hint: 'Restore Grid Defaults',
                onClick: () => this._grid.resetGridState(e.component)
            },
            location: 'after'
        });
        // Add upload button to header of data grid
        e.toolbarOptions.items.unshift({
            location: 'after',
            widget: 'dxButton',
            options: {
                text: 'Upload',
                stylingMode: 'contained',
                type: 'success',
                disabled: !this.allowEditing,
                onClick: (e) => {
                    this.showUploadPopup = true;
                }
            }
        });
    }

    fileName = (cellInfo) => {
        // file name includes work Order Id followed by a slash followed by file name.
        // Just show file name in cell in UI
        return decodeURI(cellInfo.value.split('/')[1]);
    };

    onFilesUploaded(e) {
        this._toasts.success(
            'Files were uploaded successfully',
            'Files uploaded'
        );

        // Hopefully make blobs refresh
        this.workOrderId.next(this._workOrder.id);

        // Close the popup
        this.showUploadPopup = false;
    }

    download = (e) => {
        const [workOrderId, fileName] = e.row.data.name.split('/');
        this._apm
            .downloadInspectionFile(workOrderId, fileName)
            .subscribe((file) => {
                // Save the file for the user using their browser file downloads
                saveAs(file, decodeURI(fileName));

                this._toasts.success(
                    'File downloaded successful.  Please check your browser downloads',
                    'Downloaded successfully'
                );
            });
    };

    onRowRemoving(e) {
        const [workOrderId, fileName] = e.data.name.split('/');
        this._apm
            .deleteInspectionFile(workOrderId, fileName)
            .pipe(
                catchError((error) => {
                    e.cancel = true;
                    return EMPTY;
                })
            )
            .subscribe(() =>
                this._toasts.success(
                    'File removed successfully',
                    'File deleted'
                )
            );
    }
}
