const { defineConfig } = require("cypress");

module.exports = defineConfig({
  projectId: "rb2z36",
  watchForFileChanges: true,
  chromeWebSecurity: false,
  video: true,
  e2e: {
    env: {},
    setupNodeEvents(on, config) {
      // implement node event listeners here
    },
    experimentalStudio: true,
    experimentalModifyObstructiveThirdPartyCode: true,
    // viewportWidth: 1000,
    // viewportHeight: 600,
  },
});
