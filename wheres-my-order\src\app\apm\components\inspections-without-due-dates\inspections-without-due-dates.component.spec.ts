import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxLoadIndicatorModule, DxPieChartModule } from 'devextreme-angular';
import { InspectionsWithoutDueDatesComponent } from './inspections-without-due-dates.component';

describe('InspectionsWithoutDueDatesComponent', () => {
    let component: InspectionsWithoutDueDatesComponent;
    let fixture: ComponentFixture<InspectionsWithoutDueDatesComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DxPieChartModule, DxLoadIndicatorModule],
            declarations: [InspectionsWithoutDueDatesComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(InspectionsWithoutDueDatesComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
