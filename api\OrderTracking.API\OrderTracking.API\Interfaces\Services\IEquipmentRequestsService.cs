//using System.Collections.Generic;
//using System.Threading.Tasks;
//using Azure.Storage.Blobs.Models;
//using Microsoft.AspNetCore.Http;
//using OrderTracking.API.Models.EDR;

//namespace OrderTracking.API.Interfaces
//{
//    /// <summary>
//    ///     Interface for EquipmentRequestService
//    /// </summary>
//    public interface IEquipmentRequestsService
//    {
//        /// <summary>
//        ///     Add an EquipmentRequest
//        /// </summary>
//        /// <param name="equipmentRequest"></param>
//        /// <returns></returns>
//        Task AddItemAsync(EquipmentRequest equipmentRequest);

//        /// <summary>
//        ///     Delete an EquipmentRequest.
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="pk"></param>
//        /// <returns></returns>
//        Task DeleteItemAsync(string id, string pk);

//        /// <summary>
//        ///     Get an EquipmentRequest.
//        /// </summary>
//        /// <param name="id"></param>
//        /// <returns></returns>
//        Task<EquipmentRequest> GetItemAsync(string id);

//        /// <summary>
//        ///     Get multiple EquipmentRequests based on a query string.
//        /// </summary>
//        /// <param name="queryString"></param>
//        /// <returns></returns>
//        Task<IEnumerable<EquipmentRequest>> GetItemsAsync(string queryString);

//        /// <summary>
//        ///     Update an EquipmentRequest.
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="equipmentRequest"></param>
//        /// <returns></returns>
//        Task UpdateItemAsync(string id, EquipmentRequest equipmentRequest);

//        /// <summary>
//        ///     Upload 1 or more files to an EquipmentRequest.
//        /// </summary>
//        /// <param name="equipmentRequest"></param>
//        /// <param name="files"></param>
//        /// <param name="email"></param>
//        /// <returns></returns>
//        Task UploadFilesAsync(EquipmentRequest equipmentRequest, IFormFileCollection files, string email);

//        /// <summary>
//        ///     Delete a blob files associated with an EquipmentRequest and remove the association.
//        /// </summary>
//        /// <param name="equipmentRequestId"></param>
//        /// <param name="fileId"></param>
//        /// <returns></returns>
//        Task DeleteFile(string equipmentRequestId, string fileId);

//        /// <summary>
//        ///     Download a blob file associated with an EquipmentRequest and send file to client.
//        /// </summary>
//        /// <param name="routeId"></param>
//        /// <param name="blobName"></param>
//        /// <returns></returns>
//        Task<BlobDownloadInfo> DownloadBlobAsync(string routeId, string blobName);

//        /// <summary>
//        ///     Delete multiple equipment demand requests
//        /// </summary>
//        /// <param name="ids"></param>
//        /// <returns></returns>
//        Task DeleteItemsAsync(string[] ids);
//    }
//}