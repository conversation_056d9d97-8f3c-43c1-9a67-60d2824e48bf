import {
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Input,
    Output,
    ViewChild
} from '@angular/core';
import cloneDeep from 'clone-deep';
import {
    DxGalleryComponent,
    DxPopupComponent,
    DxTextAreaComponent
} from 'devextreme-angular';
import { DxAccordionComponent } from 'devextreme-angular/ui/accordion';
import { DxButtonComponent } from 'devextreme-angular/ui/button';
import dxButton from 'devextreme/ui/button';
import { confirm } from 'devextreme/ui/dialog';
import { saveAs } from 'file-saver';
import { ToastrService } from 'ngx-toastr';
import { firstValueFrom } from 'rxjs';
import { tap } from 'rxjs/operators';
import {
    AssetPath,
    InspectionResult,
    InspectionResultsQuestion,
    InspectionResultsSection,
    Photo,
    VisualInspectionPhotoTransport
} from '../../models';
import { ApmService } from '../../services';

const collapseButtonText = 'Collapse All';
const expandButtonText = 'Expand All';

type PhotoInfo = {
    section: string;
    sectionKey: string;
    question: string;
    questionKey: string;
    response: string;
    comments: string;
    taskKey: string;
    photo: Photo;
};

@Component({
    selector: 'app-inspection-results',
    templateUrl: './inspection-results.component.html',
    styleUrls: ['./inspection-results.component.scss']
})
export class InspectionResultsComponent {
    private _originalPhotoDescription: string | undefined;
    private _inspectionResult: InspectionResult | undefined;

    private _initialSections: InspectionResultsSection[];

    assetPathLoadingCompleted = false;
    assetPathsArray: AssetPath = {
        allPhotos: {},
        questionPhotos: {},
        subSectionQuestionPhotos: {}
    };

    @Output() photoDelete = new EventEmitter<VisualInspectionPhotoTransport>();
    @Output() photoDescriptionUpdate =
        new EventEmitter<VisualInspectionPhotoTransport>();
    @Input() photoSas: string;
    @Input() set inspectionResult(value: InspectionResult) {
        if (!value) return;
        this._inspectionResult = value;
        this._sections = value?.sections;
        this._initialSections = cloneDeep(this._sections, true);

        let sectionPhotos = this._sections.flatMap((section) =>
            section.questions.flatMap((question) =>
                question.photos.map((p) => {
                    this.assetPathsArray.questionPhotos[p.blobName] = '';
                    return {
                        section: section.title,
                        sectionKey: section.databaseName,
                        question: question.text,
                        questionKey: question.key,
                        response: question.response,
                        comments: question.comments,
                        taskKey: this._inspectionResult.taskDatabaseId,
                        photo: p
                    };
                })
            )
        );

        let subsectionPhotos = this._sections.flatMap((section) =>
            section.subSections.flatMap((subsections) =>
                subsections.questions.flatMap((question) =>
                    question.photos.map((p) => {
                        this.assetPathsArray.subSectionQuestionPhotos[
                            p.blobName
                        ] = '';
                        return {
                            section: section.title,
                            sectionKey: section.databaseName,
                            question: question.text,
                            questionKey: question.key,
                            response: question.response,
                            comments: question.comments,
                            taskKey: this._inspectionResult.taskDatabaseId,
                            photo: p
                        };
                    })
                )
            )
        );
        this.updateAssetPaths();
        this.allPhotos = [...sectionPhotos, ...subsectionPhotos];
    }
    @Input() allowEditing: boolean;
    @ViewChild(DxAccordionComponent) accordion: DxAccordionComponent;

    get inspectionResult(): InspectionResult {
        return this._inspectionResult;
    }

    get sections(): InspectionResultsSection[] {
        return this._sections;
    }

    isEditing = false;
    isSaving = false;
    isEditingPhotoDescription = false;
    showPhotoPopup = false;
    allPhotos: PhotoInfo[] | undefined;

    popupGallerySelectedIndex = 0;
    private _sections: InspectionResultsSection[] | undefined;
    constructor(
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService,
        private readonly _cd: ChangeDetectorRef
    ) {}

    restoreDefaultCollapseState() {
        if (this.accordion)
            this.accordion.selectedItem = this.accordion.items[0];
    }

    toggleExpandCollapseAll(
        e: { component: dxButton },
        accordion: DxAccordionComponent
    ) {
        const isCollapsing = e.component.option('text') === collapseButtonText;

        // Toggle the button text
        if (isCollapsing) {
            e.component.option('text', expandButtonText);
        } else {
            e.component.option('text', collapseButtonText);
        }

        // Expand or collapse sections based on whether the user is expanding all or collapsing all
        accordion.items.forEach(
            (item: InspectionResultsSection, index: number) => {
                if (isCollapsing) accordion.instance.collapseItem(index);
                else if (!item.disabled) accordion.instance.expandItem(index);
            }
        );
    }

    onOptionChanged(e) {
        if (e.name === 'items') {
            this.accordion.instance.expandItem(0);
        }
    }

    onSelectionChanged(
        accordion: DxAccordionComponent,
        expandCollapseButton: DxButtonComponent
    ) {
        // When a section is expanded or collapsed, the selected items change.
        // `selectedItems` equals the sections that are expanded.  Change the
        // button text based on whether any sections are expanded or not.
        if (accordion.selectedItems.length <= 0) {
            expandCollapseButton.text = expandButtonText;
        } else {
            expandCollapseButton.text = collapseButtonText;
        }
    }

    onEditClicked() {
        this.isEditing = true;
    }

    onCancelClicked() {
        this.isEditing = false;
        this._sections = cloneDeep(this._initialSections, true);
    }

    onSaveClicked(e) {
        let result = this.setupInspectionResultPayload();
        this.isSaving = true;
        this._apm
            .putWorkOrderTask(result)
            .pipe(
                tap(() => {
                    this.isEditing = false;
                    this.isSaving = false;
                    this._cd.detectChanges();
                }),
                tap(() =>
                    this._toasts.success(
                        'Changes were saved successfully',
                        'Save Successful'
                    )
                ),
                tap(
                    () =>
                        (this._initialSections = cloneDeep(
                            this._sections,
                            true
                        ))
                )
            )
            .subscribe();
    }

    thumbnailDoubleClicked(
        e: MouseEvent,
        section: InspectionResultsSection,
        question: InspectionResultsQuestion,
        gallery: DxGalleryComponent
    ) {
        const index = this.allPhotos.findIndex(
            (p) =>
                p.section === section.title &&
                p.question === question.text &&
                p.photo === gallery.selectedItem
        );

        this.showPhotoPopup = !this.showPhotoPopup;

        this.popupGallerySelectedIndex = index >= 0 ? index : 0;
    }

    onPopupGalleryContentReady(
        e,
        popup: DxPopupComponent,
        gallery: DxGalleryComponent
    ) {
        const title = gallery.selectedItem?.section;
        if (title && popup.title !== title) {
            popup.instance.option('title', title);
        }
    }

    onPopupGallerySelectionChanged(
        e,
        popup: DxPopupComponent,
        gallery: DxGalleryComponent
    ) {
        const title = gallery.selectedItem?.section;
        if (title && popup.title !== title) {
            popup.instance.option('title', title);
        }
    }

    onEditDescriptionClicked(e, currentValue: string) {
        this.isEditingPhotoDescription = true;
        this._originalPhotoDescription = currentValue;
    }

    onDescriptionSave(e, photoInfo: PhotoInfo, description: string) {
        const update: VisualInspectionPhotoTransport = {
            projectId: this._inspectionResult.projectId,
            workOrderId: this._inspectionResult.workOrderId,
            photoDatabaseId: photoInfo.photo.databaseId,
            questionDatabaseName: photoInfo.questionKey,
            sectionDatabaseName: photoInfo.sectionKey,
            taskDatabaseId: photoInfo.taskKey,
            description
        };
        this.photoDescriptionUpdate.next(update);
        this.isEditingPhotoDescription = false;
    }

    onDescriptionCancel(e, editor: DxTextAreaComponent) {
        this.isEditingPhotoDescription = false;
        editor.instance.option('value', this._originalPhotoDescription);
    }

    async onDeletePhotoClicked(e, photoInfo: PhotoInfo) {
        const result = await confirm(
            'Are you sure you want to delete this photo?',
            'Are you sure?'
        );
        if (!result) return;
        const photoTransport: VisualInspectionPhotoTransport = {
            projectId: this._inspectionResult.projectId,
            workOrderId: this._inspectionResult.workOrderId,
            photoDatabaseId: photoInfo.photo.databaseId,
            questionDatabaseName: photoInfo.questionKey,
            sectionDatabaseName: photoInfo.sectionKey,
            taskDatabaseId: photoInfo.taskKey
        };
        this.photoDelete.next(photoTransport);
    }

    async onDownloadClicked(fileName: string) {
        const url = await firstValueFrom(this._apm.getSignedUrl(fileName));
        const blob = await this._apm.downloadFileFromUrl(url).toPromise();
        saveAs(blob, fileName);
    }

    setupInspectionResultPayload(): InspectionResult {
        let result = structuredClone(this._inspectionResult);
        this.processSectionsData(result.sections);
        return result;
    }

    processSectionsData(sections: InspectionResultsSection[]) {
        for (let section of sections) {
            for (let question of section.questions) {
                if (question.attributeType === 'MultiPredefinedValue') {
                    let values = question.response ?? [];
                    let response =
                        values.length !== 0 ? String(question.response) : null;
                    question.response = response;
                }

                if (question.attributeType === 'Coordinate') {
                    question.response = `${question.latitude ?? ''},${
                        question.longitude ?? ''
                    }`;
                }

                if (question.hasOtherOption && question.other) {
                    question.response = question.response?.replace(
                        'Other',
                        question.other
                    );
                }
            }

            if (section.subSections.length > 0) {
                this.processSectionsData(section.subSections);
            }
        }
    }
    getAssetImage(type, blobPath) {
        return this.assetPathsArray[type][blobPath]
            ? this.assetPathsArray[type][blobPath]
            : '';
    }
    async updateAssetPaths() {
        if (Object.keys(this.assetPathsArray.questionPhotos).length) {
            Object.keys(this.assetPathsArray.questionPhotos).forEach(
                async (blobName) => {
                    let assetPath;
                    try {
                        assetPath = await firstValueFrom(
                            this._apm.getSignedUrl(blobName)
                        );
                    } catch (error) {
                        assetPath = '';
                    }
                    this.assetPathsArray.questionPhotos[blobName] = assetPath;
                    this.assetPathsArray.allPhotos[blobName] = assetPath;
                }
            );
        }
        if (Object.keys(this.assetPathsArray.subSectionQuestionPhotos).length) {
            Object.keys(this.assetPathsArray.subSectionQuestionPhotos).forEach(
                async (blobName) => {
                    let assetPath;
                    try {
                        assetPath = await firstValueFrom(
                            this._apm.getSignedUrl(blobName)
                        );
                    } catch (error) {
                        assetPath = '';
                    }
                    this.assetPathsArray.subSectionQuestionPhotos[blobName] =
                        assetPath;
                    this.assetPathsArray.allPhotos[blobName] = assetPath;
                }
            );
        }
        this.assetPathLoadingCompleted = true;
    }
}
