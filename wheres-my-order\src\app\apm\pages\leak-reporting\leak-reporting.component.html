<app-leak-reporting-grid [rows]="reports$ | async | leakReportGridRows"
                         (addingLeakReport)="onAddingLeakReport($event)"
                         (selectedReportID)="onReportSelected($event)">
</app-leak-reporting-grid>
<app-leak-reporting-details [report]="selectedReport$ | async"
                            (statusChanged)="onStatusChanged($event)"
                            (leakReportInfoSaving)="onLeakReportInfoSaving($event)"
                            (leakReportWorkDetailsSaving)="onLeakReportWorkDetailsSaving($event)"
                            (photoDescriptionUpdate)="onPhotoDescriptionUpdate($event)"
                            (photoDelete)="onPhotoDelete($event)">
</app-leak-reporting-details>
