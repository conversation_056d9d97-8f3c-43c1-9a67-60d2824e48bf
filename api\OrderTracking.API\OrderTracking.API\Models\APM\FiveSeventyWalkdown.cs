﻿using System;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class FiveSeventyWalkdown
    {
        [JsonProperty("ndeExaminationMethods")]
        public ValueChangedTransport<string> NDEExaminationMethods { get; set; }

        [JsonProperty("workOrderId")] public string WorkOrderId { get; set; }
        [JsonProperty("projectId")] public string ProjectId { get; set; }
        [JsonProperty("assetCategory")] public ValueChangedTransport<string> AssetCategory { get; set; }
        [JsonProperty("identificationName")] public ValueChangedTransport<string> IdentificationName { get; set; }
        [JsonProperty("identificationNumber")] public ValueChangedTransport<string> IdentificationNumber { get; set; }
        [JsonProperty("assetType")] public ValueChangedTransport<string> AssetType { get; set; }
        [JsonProperty("assetDescription")] public ValueChangedTransport<string> AssetDescription { get; set; }
        [JsonProperty("productHandled")] public ValueChangedTransport<string> ProductHandled { get; set; }
        [JsonProperty("lastKnownInspectionDate")]
        public ValueChangedTransport<DateTime?> LastKnownInspectionDate { get; set; }
        [JsonProperty("location")] public ValueChangedTransport<string> Location { get; set; }
        [JsonProperty("lineFromEquipmentId")] public ValueChangedTransport<string> LineFromEquipmentId { get; set; }
        [JsonProperty("startGisLocationLat")] public ValueChangedTransport<double?> StartGisLocationLat { get; set; }
        [JsonProperty("startGisLocationLong")] public ValueChangedTransport<double?> StartGisLocationLong { get; set; }
        [JsonProperty("lineToEquipmentId")] public ValueChangedTransport<string> LineToEquipmentId { get; set; }
        [JsonProperty("endGisLocationLat")] public ValueChangedTransport<double?> EndGisLocationLat { get; set; }
        [JsonProperty("endGisLocationLong")] public ValueChangedTransport<double?> EndGisLocationLong { get; set; }
        [JsonProperty("designCode")] public ValueChangedTransport<string> DesignCode { get; set; }
        [JsonProperty("designYear")] public ValueChangedTransport<string> DesignYear { get; set; }
        [JsonProperty("designAddendum")] public ValueChangedTransport<string> DesignAddendum { get; set; }
        [JsonProperty("inspectionCode")] public ValueChangedTransport<string> InspectionCode { get; set; }
        [JsonProperty("inspectionYear")] public ValueChangedTransport<string> InspectionYear { get; set; }
        [JsonProperty("inspectionAddendum")] public ValueChangedTransport<string> InspectionAddendum { get; set; }
        [JsonProperty("pipeClass")] public ValueChangedTransport<string> PipeClass { get; set; }
        [JsonProperty("manufacturerName")] public ValueChangedTransport<string> ManufacturerName { get; set; }
        [JsonProperty("manufacturerDate")] public ValueChangedTransport<DateTime?> ManufacturerDate { get; set; }
        [JsonProperty("installationDate")] public ValueChangedTransport<DateTime?> InstallationDate { get; set; }
        [JsonProperty("inServiceDate")] public ValueChangedTransport<DateTime?> InServiceDate { get; set; }
        [JsonProperty("pIDNumber")] public ValueChangedTransport<string> PIdNumber { get; set; }
        [JsonProperty("designDrawingNumber")] public ValueChangedTransport<string> DesignDrawingNumber { get; set; }
        [JsonProperty("lowestFlangeRating")] public ValueChangedTransport<string> LowestFlangeRating { get; set; }
        [JsonProperty("typeOfConstruction")] public ValueChangedTransport<string[]> TypeOfConstruction { get; set; }
        [JsonProperty("threadedConnections")] public ValueChangedTransport<string> ThreadedConnections { get; set; }
        [JsonProperty("pipeSize")] public ValueChangedTransport<string[]> PipeSize { get; set; }
        [JsonProperty("pipeSchedule")] public ValueChangedTransport<string[]> PipeSchedule { get; set; }

        [JsonProperty("serviceProductContents")]
        public ValueChangedTransport<string> ServiceProductContents { get; set; }

        [JsonProperty("specificGravity")] public ValueChangedTransport<double?> SpecificGravity { get; set; }
        [JsonProperty("operatingTemperature")] public ValueChangedTransport<int?> OperatingTemperature { get; set; }
        [JsonProperty("designMAWP")] public ValueChangedTransport<double?> DesignMawp { get; set; }
        [JsonProperty("designTemperature")] public ValueChangedTransport<double?> DesignTemperature { get; set; }
        [JsonProperty("operatingPressure")] public ValueChangedTransport<double?> OperatingPressure { get; set; }
        [JsonProperty("pRVSetPressure")] public ValueChangedTransport<double?> PRvSetPressure { get; set; }
        [JsonProperty("operationStatus")] public ValueChangedTransport<string> OperationStatus { get; set; }
        [JsonProperty("pipes")] public PipeComponent[] Pipes { get; set; }
    }

    public class PipeComponent
    {
        [JsonProperty("databaseId")] public string DatabaseId { get; set; }
        [JsonProperty("lineNumber")] public ValueChangedTransport<string> LineNumber { get; set; }
        [JsonProperty("materialSpecAndGrade")] public ValueChangedTransport<string> MaterialSpecAndGrade { get; set; }

        [JsonProperty("allowableStressAtTemperature")]
        public ValueChangedTransport<double?> AllowableStressAtTemperature { get; set; }

        [JsonProperty("nominalThickness")] public ValueChangedTransport<double?> NominalThickness { get; set; }
        [JsonProperty("corrosionAllowance")] public ValueChangedTransport<double?> CorrosionAllowance { get; set; }
        [JsonProperty("jointEfficiency")] public ValueChangedTransport<double?> JointEfficiency { get; set; }
        [JsonProperty("pipeSpecNumber")] public ValueChangedTransport<string> PipeSpecNumber { get; set; }
    }
}