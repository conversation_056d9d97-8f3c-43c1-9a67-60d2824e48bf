FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

RUN apt-get update
RUN apt-get install -y libc6 libicu-dev libfontconfig1

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src
COPY . .

 ARG FEED_ACCESSTOKEN
#ARG GITLAB_DEPLOY_USERNAME
#ARG GITLAB_DEPLOY_TOKEN

#RUN dotnet nuget add source "https://pkgs.dev.azure.com/teaminc/debad0dc-be5c-449f-9cbd-c546461d5b75/_packaging/packageManager/nuget/v3/index.json" --name "NuGet" --username ${GITLAB_DEPLOY_USERNAME} --password ${GITLAB_DEPLOY_TOKEN} --store-password-in-clear-text

#RUN sed 's/GITLAB_DEPLOY_USERNAME/'"${GIT<PERSON>B_DEPLOY_USERNAME}"'/g; s/GIT<PERSON>B_DEPLOY_TOKEN/'"${GITLAB_DEPLOY_TOKEN}"'/g' ./api/OrderTracking.API/NuGet.Config > ./OrderTracking.API/NuGet.Config
 

ENV VSS_NUGET_EXTERNAL_FEED_ENDPOINTS="{\"endpointCredentials\": [{\"endpoint\":\"https://pkgs.dev.azure.com/teaminc/debad0dc-be5c-449f-9cbd-c546461d5b75/_packaging/packageManager/nuget/v3/index.json\", \"username\":\"docker\", \"password\":\"${FEED_ACCESSTOKEN}\"}]}"


RUN dotnet restore "./api/TeamDigital.Authorization.Web/TeamDigital.Authorization.Web/TeamDigital.Authorization.Web.csproj" --configfile ./api/OrderTracking.API/NuGet.Config
RUN dotnet restore "./api/OrderTracking.API/OrderTracking.API/OrderTracking.API.csproj" --configfile ./api/OrderTracking.API/NuGet.Config
RUN dotnet restore "./api/OrderTracking.API/ClientPortal.Shared/ClientPortal.Shared.csproj" --configfile ./api/OrderTracking.API/NuGet.Config

RUN dotnet build "./api/OrderTracking.API/ClientPortal.Shared/ClientPortal.Shared.csproj" -c Release -o /app/build
RUN dotnet build "./api/TeamDigital.Authorization.Web/TeamDigital.Authorization.Web/TeamDigital.Authorization.Web.csproj" -c Release -o /app/build
RUN dotnet build "./api/OrderTracking.API/OrderTracking.API/OrderTracking.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "./api/OrderTracking.API/OrderTracking.API/OrderTracking.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "OrderTracking.API.dll"]