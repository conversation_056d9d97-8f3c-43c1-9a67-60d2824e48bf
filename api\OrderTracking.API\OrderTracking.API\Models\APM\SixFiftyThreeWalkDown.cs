﻿using System;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class SixFiftyThreeInspectionOpening
    {
        [JsonProperty("type")] public ValueChangedTransport<string> Type { get; set; }
        
        [JsonProperty("number")] public ValueChangedTransport<int?> Number { get; set; }

        [JsonProperty("size")] public ValueChangedTransport<string> Size { get; set; }

        [JsonProperty("displayName")] public ValueChangedTransport<string> DisplayName { get; set; }

        [JsonProperty("databaseId")] public string DatabaseId { get; set; }
    }

    public class SixFiftyThreeRepair
    {
        [JsonProperty("databaseId")] public string DatabaseId { get; set; }

        [JsonProperty("dateRepairedOrAltered")]
        public ValueChangedTransport<DateTime?> DateRepairedOrAltered { get; set; }

        [JsonProperty("repairAlterationOrganization")]
        public ValueChangedTransport<string> RepairAlterationOrganization { get; set; }

        [JsonProperty("purposeOfRepairAlteration")]
        public ValueChangedTransport<string> PurposeOfRepairAlteration { get; set; }
    }

    public class SixFiftyThreeRegulatoryRequirement
    {
        [JsonProperty("databaseId")] public string DatabaseId { get; set; }

        [JsonProperty("jurisdictionRegulatoryAgency")]
        public ValueChangedTransport<string> JurisdictionRegulatoryAgency { get; set; }
    }

    public class SixFiftyThreeShellCourse
    {
        [JsonProperty("number")] public ValueChangedTransport<string?> Number { get; set; }

        [JsonProperty("materialSpecAndGrade")] public ValueChangedTransport<string> MaterialSpecAndGrade { get; set; }

        [JsonProperty("allowableStressAtTemp")]
        public ValueChangedTransport<double?> AllowableStressAtTemp { get; set; }

        [JsonProperty("nominalThickness")] public ValueChangedTransport<double?> NominalThickness { get; set; }

        [JsonProperty("corrosionAllowance")] public ValueChangedTransport<double?> CorrosionAllowance { get; set; }

        [JsonProperty("lengthOrHeight")] public ValueChangedTransport<double?> LengthOrHeight { get; set; }

        [JsonProperty("jointEfficiency")] public ValueChangedTransport<double?> JointEfficiency { get; set; }

        [JsonProperty("databaseId")] public string DatabaseId { get; set; }
    }

    public class SixFiftyThreeTankRoof
    {
        [JsonProperty("type")] public ValueChangedTransport<string[]> Type { get; set; }

        [JsonProperty("materialSpecAndGrade")] public ValueChangedTransport<string> MaterialSpecAndGrade { get; set; }

        [JsonProperty("nominalThickness")] public ValueChangedTransport<double?> NominalThickness { get; set; }

        [JsonProperty("corrosionAllowance")] public ValueChangedTransport<double?> CorrosionAllowance { get; set; }

        [JsonProperty("databaseId")] public string DatabaseId { get; set; }
    }

    public class SixFiftyThreeNozzle
    {
        [JsonProperty("databaseId")] public string DatabaseId { get; set; }

        [JsonProperty("number")] public ValueChangedTransport<string?> Number { get; set; }

        [JsonProperty("type")] public ValueChangedTransport<string> Type { get; set; }

        [JsonProperty("materialSpecAndGrade")] public ValueChangedTransport<string> MaterialSpecAndGrade { get; set; }

        [JsonProperty("pipeSize")] public ValueChangedTransport<string[]> PipeSize { get; set; }

        [JsonProperty("pipeSchedule")] public ValueChangedTransport<string[]> PipeSchedule { get; set; }

        [JsonProperty("flangeRating")] public ValueChangedTransport<string[]> FlangeRating { get; set; }

        [JsonProperty("reinforcementPadType")] public ValueChangedTransport<string> ReinforcementPadType { get; set; }

        [JsonProperty("reinforcementPadDimensions")]
        public ValueChangedTransport<string> ReinforcementPadDimensions { get; set; }

        [JsonProperty("reinforcementPadThickness")]
        public ValueChangedTransport<double?> ReinforcementPadThickness { get; set; }
    }

    public class SixFiftyThreeWalkDown
    {
        [JsonProperty("workOrderId")]
        public string WorkOrderId { get; set; }

        [JsonProperty("projectId")]
        public string ProjectId { get; set; }

        [JsonProperty("name")]
        public ValueChangedTransport<string> Name { get; set; }

        [JsonProperty("numberOrId")]
        public ValueChangedTransport<string> NumberOrId { get; set; }

        [JsonProperty("assetType")]
        public ValueChangedTransport<string> AssetType { get; set; }

        [JsonProperty("equipmentDescription")]
        public ValueChangedTransport<string> EquipmentDescription { get; set; }

        [JsonProperty("lastKnownInspectionDate")]
        public ValueChangedTransport<DateTime?> LastKnownInspectionDate { get; set; }

        [JsonProperty("location")]
        public ValueChangedTransport<string> Location { get; set; }

        [JsonProperty("latitude")]
        public ValueChangedTransport<double?> Latitude { get; set; }

        [JsonProperty("longitude")]
        public ValueChangedTransport<double?> Longitude { get; set; }

        [JsonProperty("designCode")]
        public ValueChangedTransport<string> DesignCode { get; set; }

        [JsonProperty("codeYear")]
        public ValueChangedTransport<string> CodeYear { get; set; }

        [JsonProperty("addendum")]
        public ValueChangedTransport<string> Addendum { get; set; }

        [JsonProperty("maximumFillHeight")] public ValueChangedTransport<double?> MaximumFillHeight { get; set; }

        [JsonProperty("diameter")] public ValueChangedTransport<double?> Diameter { get; set; }

        [JsonProperty("height")] public ValueChangedTransport<double?> Height { get; set; }

        [JsonProperty("tankVolumeInBBL")] public ValueChangedTransport<double?> TankVolumeInBBL { get; set; }

        [JsonProperty("constructionMethod")] public ValueChangedTransport<string[]> ConstructionMethod { get; set; }

        [JsonProperty("orientation")] public ValueChangedTransport<string> Orientation { get; set; }

        [JsonProperty("rt")] public ValueChangedTransport<string> RT { get; set; }

        [JsonProperty("installationDate")] public ValueChangedTransport<DateTime?> InstallationDate { get; set; }

        [JsonProperty("inServiceDate")] public ValueChangedTransport<DateTime?> InServiceDate { get; set; }

        [JsonProperty("pAndIdNumber")] public ValueChangedTransport<string> PAndIdNumber { get; set; }

        [JsonProperty("constructionDesignDrawingNumber")]
        public ValueChangedTransport<string> ConstructionDesignDrawingNumber { get; set; }

        [JsonProperty("lowestFlangeRating")] public ValueChangedTransport<string> LowestFlangeRating { get; set; }

        [JsonProperty("serviceProductContents")]
        public ValueChangedTransport<string> ServiceProductContents { get; set; }

        [JsonProperty("specificGravity")] public ValueChangedTransport<double?> SpecificGravity { get; set; }

        [JsonProperty("intendedService")] public ValueChangedTransport<string> IntendedService { get; set; }

        [JsonProperty("inspectionOpenings")] public SixFiftyThreeInspectionOpening[] InspectionOpenings { get; set; }

        [JsonProperty("inspectionCode")] public ValueChangedTransport<string> InspectionCode { get; set; }

        [JsonProperty("inspectionYear")] public ValueChangedTransport<string> InspectionYear { get; set; }

        [JsonProperty("inspectionAddendum")] public ValueChangedTransport<string> InspectionAddendum { get; set; }

        [JsonProperty("dataPlateAttached")] public ValueChangedTransport<string> DataPlateAttached { get; set; }

        [JsonProperty("dataPlateLegible")] public ValueChangedTransport<string> DataPlateLegible { get; set; }

        [JsonProperty("manufacturerName")] public ValueChangedTransport<string> ManufacturerName { get; set; }

        [JsonProperty("manufacturerDate")] public ValueChangedTransport<DateTime?> ManufacturerDate { get; set; }

        [JsonProperty("manufacturerSerialNumber")]
        public ValueChangedTransport<string> ManufacturerSerialNumber { get; set; }

        [JsonProperty("hasRepairOrAlterationPlate")]
        public ValueChangedTransport<string> HasRepairOrAlterationPlate { get; set; }

        [JsonProperty("repairOrAlterationPlateLegible")]
        public ValueChangedTransport<string> RepairOrAlterationPlateLegible { get; set; }

        [JsonProperty("repairs")] public SixFiftyThreeRepair[] Repairs { get; set; }

        [JsonProperty("currentService")] public ValueChangedTransport<string> CurrentService { get; set; }

        [JsonProperty("designTemperature")] public ValueChangedTransport<double?> DesignTemperature { get; set; }

        [JsonProperty("currentOperatingTemperature")]
        public ValueChangedTransport<int?> CurrentOperatingTemperature { get; set; }

        [JsonProperty("currentFillLevel")] public ValueChangedTransport<int?> CurrentFillLevel { get; set; }

        [JsonProperty("operationStatus")] public ValueChangedTransport<string> OperationStatus { get; set; }

        [JsonProperty("tankEquippedWithVRU")] public ValueChangedTransport<string> TankEquippedWithVRU { get; set; }

        [JsonProperty("tankEquippedWithLeakDetection")]
        public ValueChangedTransport<string> TankEquippedWithLeakDetection { get; set; }

        [JsonProperty("tankOutOfService")] public ValueChangedTransport<string> TankOutOfService { get; set; }

        [JsonProperty("regulatoryRequirements")]
        public SixFiftyThreeRegulatoryRequirement[] RegulatoryRequirements { get; set; }

        [JsonProperty("shellCourses")] public SixFiftyThreeShellCourse[] ShellCourses { get; set; }

        [JsonProperty("tankFloorType")] public ValueChangedTransport<string> TankFloorType { get; set; }

        [JsonProperty("tankFloorMaterialSpecAndGrade")]
        public ValueChangedTransport<string> TankFloorMaterialSpecAndGrade { get; set; }

        [JsonProperty("tankFloorAnnularRingNominalThickness")]
        public ValueChangedTransport<double?> TankFloorAnnularRingNominalThickness { get; set; }

        [JsonProperty("tankFloorSketchPlatesNominalThickness")]
        public ValueChangedTransport<double?> TankFloorSketchPlatesNominalThickness { get; set; }

        [JsonProperty("tankFloorInnerPlatesNominalThickness")]
        public ValueChangedTransport<double?> TankFloorInnerPlatesNominalThickness { get; set; }

        [JsonProperty("tankFloorCorrosionAllowance")]
        public ValueChangedTransport<double?> TankFloorCorrosionAllowance { get; set; }

        [JsonProperty("tankRoofs")] public SixFiftyThreeTankRoof[] TankRoofs { get; set; }

        [JsonProperty("nozzles")] public SixFiftyThreeNozzle[] Nozzles { get; set; }
    }

}