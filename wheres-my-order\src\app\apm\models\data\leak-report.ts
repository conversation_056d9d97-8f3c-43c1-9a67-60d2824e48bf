import { IAttribute } from '../../../shared/models/attributes';

export interface LeakReport {
    createdBy?: null | string;
    pendingStatus?: null;
    report: Report;
    workDetail: WorkDetail;
    apmNumber: IAttribute<string>;
    leakReportPhotos: LeakReportPhotos;
    id: string;
    displayName: string;
    status?: string;
}

export interface LeakReportPackage {
    leakReport: LeakReport;
    photos: unknown[];
}

export interface Report {
    attributeEquipment_ID: IAttribute<string>;
    attributeEquipment_Description: IAttribute<string>;
    attributeEquipment_ID_at_line_START: IAttribute<string>;
    attributeEquipment_ID_at_line_END: IAttribute<string>;
    attributePipe_Size: IAttribute<string>;
    attributePipe_Schedule: IAttribute<string>;
    attributeProcessService: IAttribute<string>;
    attributePipe_Cover: IAttribute<string>;
    attributeAffected_Length: IAttribute<number>;
    attributeDistance_between_tie_in_points: IAttribute<number>;
    attributeCorrosion_Type: PredefinedValueAttribute;
    attributeEstimated_Loss_Rate: IAttribute<number>;
    attributeExisting_clamp_count_same_line: AttributeExistingClampCountSameLineOrAttributeFeatureFittingCountSameLine;
    attributeFeatureFitting_count_same_line: AttributeExistingClampCountSameLineOrAttributeFeatureFittingCountSameLine;
    attributeObservation_Summary: IAttribute<string>;
    displayName: string;
}
export interface PhotosEntity {
    displayName: string;
    mediaName: string;
    extension: string;
    blobName: string;
    version: number;
    uploadedVersion: number;
    description: IAttribute<string>;
    imageData?: string;
    databaseId: string;
    createdBy: string;
    createdTime: string;
}

export interface OptionsEntity {
    isCommentRequired: boolean;
    value: string;
    description?: null;
}

export interface PredefinedValueAttribute {
    hasOtherOption: boolean;
    areCommentsRequired: boolean;
    attributeType: string;
    options?: OptionsEntity[] | null;
    valueChangeLog?: null;
    currentValue?: string[] | null;
    pendingValue?: string[] | null;
    displayName: string;
    databaseName: string;
    previewText: string;
    photos?: null[] | null;
    photoChangeLog?: null;
    commentChangeLog?: null;
    comment?: null;
}
export interface AttributeExistingClampCountSameLineOrAttributeFeatureFittingCountSameLine {
    areCommentsRequired: boolean;
    attributeType: string;
    allowNegatives: boolean;
    unit?: null;
    currentValue: number;
    pendingValue?: null;
    currentPendingOrValue: number;
    valueChangeLog?: null;
    displayName: string;
    databaseName: string;
    previewText: string;
    photos?: null[] | null;
    photoChangeLog?: null;
    commentChangeLog?: null;
    comment?: null;
}
export interface WorkDetail {
    attributeClient: IAttribute<string>;
    attributeCity: IAttribute<string>;
    attributeState: IAttribute<string>;
    attributePostal_Code: IAttribute<string>;
    attributeArea: IAttribute<string>;
    attributeLease: IAttribute<string>;
    attributeFacility: IAttribute<string>;
    attributeJob_Description: IAttribute<string>;
    attributeClient_Contact: IAttribute<string>;
    attributeClient_Contact_Number: IAttribute<string>;
    attributePurchase_OrderAFE: IAttribute<string>;
    attributeClient_Cost_Code: IAttribute<string>;
    attributeClient_Work_Order: IAttribute<string>;
    attributeTeam_District: IAttribute<string>;
    attributeTeam_Project_Number: AttributeTeamProjectNumber;
    attributeInspection_Reference: IAttribute<string>;
    attributeReference_EditionRevision: IAttribute<string>;
    attributeInspection_Type: PredefinedValueAttribute;
    attributeInspected_By: IAttribute<string>;
    attributeInspection_Date: IAttribute<string>;
    attributeInspector_Certificate_Number: IAttribute<string>;
    attributeReviewed_By: IAttribute<string>;
    attributeReviewer_Email: IAttribute<string>;
    attributeReviewer_Certificate_Number: IAttribute<string>;
    displayName: string;
}
export interface AttributeTeamProjectNumber {
    areCommentsRequired: boolean;
    attributeType: string;
    currentValue?: null;
    pendingValue?: null;
    currentPendingOrValue?: null;
    valueChangeLog?: null;
    displayName: string;
    databaseName: string;
    previewText: string;
    photos?: null[] | null;
    photoChangeLog?: null;
    commentChangeLog?: null;
    comment?: null;
}

export interface LeakReportPhotos {
    displayName: string;
    name: string;
    changeLog: LeakReportChangeLog;
    currentEntries?: LeakReportPhotoEntries[] | null;
    pendingEntries?: LeakReportPhotoEntries[] | null;
}
export interface LeakReportChangeLog {
    entries?: EntriesEntity[] | null;
    displayName: string;
}
export interface EntriesEntity {
    action: string;
    key: string;
    value: string;
    timeChanged: string;
    userName: string;
}
export interface LeakReportPhotoEntries {
    photos: Photos;
    description: IAttribute<string>;
    comment: IAttribute<string>;
    areaOfInterestCoordinate: IAttribute<string>;
    upstreamTieInCoordinate: IAttribute<string>;
    downstreamTieInCoordinate: IAttribute<string>;
    utHighMeasurment: IAttribute<number>;
    utLowMeasurment: IAttribute<number>;
    displayName: string;
    databaseId: string;
    createdBy: string;
    createdTime: string;
}
export interface Photos {
    areCommentsRequired: boolean;
    attributeType: string;
    displayName: string;
    databaseName?: null;
    previewText: string;
    photos?: PhotosEntity[] | null;
    photoChangeLog?: null;
    commentChangeLog?: null;
    comment?: null;
}
