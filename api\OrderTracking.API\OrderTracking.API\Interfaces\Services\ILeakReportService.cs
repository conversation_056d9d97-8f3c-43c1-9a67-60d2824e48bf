﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using APMWebDataInterface.DataModel.LeakReport;

namespace OrderTracking.API.Interfaces
{
    /// <summary>
    ///  interface for LeakReportService
    /// </summary>
    public interface ILeakReportService
    {
        Task<LeakReport> GetLeakReportAsync(string id, string email);
        Task<List<LeakReport>> GetLeakReportsAsync(string email);
        Task SaveLeakReportAsync(LeakReport report, string user);
        LeakReportPhoto GetLeakReportPhotoGroup(LeakReport report, string photoGroupId);
    }
}
