{"html.format.wrapAttributes": "force-aligned", "html.format.wrapLineLength": 80, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.tabSize": 4, "editor.wordWrapColumn": 80, "typescriptHero.imports.organizeOnSave": true, "typescriptHero.imports.organizeSortsByFirstSpecifier": true, "typescriptHero.imports.multiLineWrapThreshold": 80, "typescript.preferences.importModuleSpecifier": "relative", "typescript.preferences.quoteStyle": "single", "prettier.bracketSpacing": true, "prettier.trailingComma": "none", "prettier.semi": true, "prettier.singleQuote": true, "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}, "javascript.preferences.quoteStyle": "single", "[javascript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}, "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.tsdk": "./node_modules/typescript/lib", "emmet.triggerExpansionOnTab": true, "angular.enable-strict-mode-prompt": false, "search.exclude": {"**/.angular/*": true}}