import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrModule } from 'ngx-toastr';
import { BehaviorSubject } from 'rxjs';
import { ApmService } from '../../../apm/services';
import { UserProfile } from '../../../profile/models';
import { LeakReportingDetailsComponent } from './leak-reporting-details.component';

describe('LeakReportingDetailsComponent', () => {
    let component: LeakReportingDetailsComponent;
    let fixture: ComponentFixture<LeakReportingDetailsComponent>;
    let userSubject = new BehaviorSubject<UserProfile>(new UserProfile());
    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [HttpClientTestingModule, ToastrModule.forRoot()],
            providers: [{ provide: ApmService, useValue: {} }],
            declarations: [LeakReportingDetailsComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(LeakReportingDetailsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
