<div class="responsive-paddings content-block">
    <dx-data-grid [dataSource]="activitiesList"
                  [allowColumnReordering]="true"
                  [allowColumnResizing]="true"
                  [columnResizingMode]="'widget'"
                  (onSaving)="onSaving($event)"
                  (onSaved)="onSaved($event)"
                  (onEditingStart)="onEditingStart($event)"
                  (onInitNewRow)="onNewRowInit($event)"
                  (onEditCanceled)="onEditCanceled($event)"
                  (onToolbarPreparing)="onToolbarPreparing($event)">
        <!-- EDITING -->
        <dxo-editing mode="popup"
                     [allowAdding]="allowEditing"
                     [allowDeleting]="false"
                     [allowUpdating]="allowEditing">
            <dxo-popup [title]="'Activity'"
                       [showTitle]="true"
                       width="auto"
                       height="auto"
                       [onContentReady]="onPopupContentReady"></dxo-popup>
            <dxo-form>
                <dxi-item *ngIf="isInserting"
                          dataField="date"
                          dataType="date">
                    <dxi-validation-rule type="required"></dxi-validation-rule>
                </dxi-item>
                <dxi-item *ngIf="isInserting"
                          dataField="clientWorkOrder"
                          editorType="dxSelectBox"
                          [editorOptions]="{dataSource: projectActivityWorkOrderNumbers, searchEnabled: true, acceptCustomValue: true}">
                </dxi-item>
                <dxi-item *ngIf="isInserting"
                          dataField="technician"
                          editorType="dxSelectBox"
                          [editorOptions]="{dataSource: availableUsers, searchEnabled: true}">
                    <dxi-validation-rule type="required">
                    </dxi-validation-rule>
                </dxi-item>
                <dxi-item dataField="taskType"
                          editorType="dxSelectBox"
                          [editorOptions]="{dataSource: taskTypes}">
                    <dxi-validation-rule type="required">
                    </dxi-validation-rule>
                </dxi-item>
                <dxi-item dataField="duration">
                    <dxi-validation-rule type="required"></dxi-validation-rule>
                </dxi-item>
                <dxi-item *ngIf="showCount"
                          dataField="count">
                    <dxi-validation-rule type="required"></dxi-validation-rule>
                </dxi-item>
            </dxo-form>
        </dxo-editing>

        <dxo-group-panel [visible]="true"></dxo-group-panel>
        <dxo-scrolling [useNative]="true"></dxo-scrolling>
        <dxo-paging [pageSize]="5"></dxo-paging>
        <dxo-pager [showPageSizeSelector]="true"
                   [visible]="true"
                   [allowedPageSizes]="[5, 10, 25, 50]"></dxo-pager>
        <dxo-header-filter [visible]="true"></dxo-header-filter>
        <dxo-search-panel [visible]="true"
                          [highlightCaseSensitive]="true"></dxo-search-panel>
        <dxo-filter-panel [visible]="true"></dxo-filter-panel>
        <dxo-column-chooser [enabled]="true"
                            mode="dragAndDrop"></dxo-column-chooser>
        <dxo-state-storing [enabled]="true"
                           type="localStorage"
                           storageKey="apmProjectActivityTrackerState">
        </dxo-state-storing>
        <dxo-export [enabled]="true"
                    [allowExportSelectedData]="true"></dxo-export>

        <dxi-column dataField="date"
                    dataType="date"
                    [allowFiltering]="true">
            <dxi-validation-rule type="required"></dxi-validation-rule>
        </dxi-column>
        <dxi-column dataField="clientWorkOrder"
                    [allowFiltering]="true"></dxi-column>
        <dxi-column dataField="technician"
                    [allowFiltering]="true">
            <dxi-validation-rule type="required"></dxi-validation-rule>
        </dxi-column>
        <dxi-column dataField="taskType"
                    [allowFiltering]="true">
            <dxi-validation-rule type="required"></dxi-validation-rule>
        </dxi-column>
        <dxi-column dataField="duration"
                    [allowFiltering]="true">
            <dxi-validation-rule type="required"></dxi-validation-rule>
        </dxi-column>
        <dxi-column dataField="count"
                    [allowFiltering]="true">
            <dxi-validation-rule type="required"></dxi-validation-rule>
        </dxi-column>
    </dx-data-grid>
</div>
