﻿//using System;
//using System.Collections.Generic;
//using System.Globalization;
//using System.IO;
//using System.Linq;
//using System.Threading.Tasks;
//using Audit.Core;
//using Audit.SqlServer;
//using Audit.SqlServer.Providers;
//using ClientPortal.Shared.Extensions;
//using CsvHelper;
//using CsvHelper.Configuration.Attributes;
//using Dapper;
//using Dapper.Contrib.Extensions;
//using Microsoft.Data.SqlClient;
//using OrderTracking.API.Models.CRD;
//using Z.Dapper.Plus;

//namespace ChemicalReferenceDictionary.ConsoleApp
//{
//    internal class CRDEntriesRepository
//    {
//        /// <summary>
//        /// 
//        /// </summary>
//        /// <param name="connection"></param>
//        /// <param name="record"></param>
//        /// <param name="user"></param>
//        /// <returns></returns>
//        public static async Task InsertCRDEntry(SqlConnection connection, CRDEntry record, string user)
//        {
//            CRDEntry entry = null;
//            using (var scope = await AuditScope.CreateAsync("CRDEntry:Insert", () => entry))
//            {
//                var recordId = await connection.InsertAsync(record);
//                entry = record;
//                scope.SetCustomField("User", user);
//                scope.SetCustomField("RecordId", recordId);

//            }
//        }

//        /// <summary>
//        /// 
//        /// </summary>
//        /// <param name="connection"></param>
//        /// <param name="id"></param>
//        /// <param name="crdEntry">Updated in-memory entity</param>
//        /// <param name="user"></param>
//        /// <returns></returns>
//        public static async Task UpdateCRDEntry(SqlConnection connection, int id, CRDEntry crdEntry, string user)
//        {
//            var old = await connection.QuerySingleAsync<CRDEntry>("SELECT * FROM CRDEntries WHERE ID = @ID", new {ID = id});
//            using (var scope = await AuditScope.CreateAsync("CRDEntry:Update", () => old))
//            {
//                await connection.UpdateAsync(crdEntry);
//                old = crdEntry;
//                scope.SetCustomField("User", user);
//                scope.SetCustomField("RecordId", id);
//            }
//        }
//    }

//    internal class CRDInitialCreate
//    {
//        private static async Task Main(string[] args)
//        {
           
//            Audit.Core.Configuration.DataProvider = new SqlDataProvider
//            {
//                ConnectionString = "",
//                Schema = "dbo",
//                TableName = "AuditLog",
//                IdColumnName = "EventId",
//                JsonColumnName = "JsonData",
//                LastUpdatedDateColumnName = "LastUpdatedDate",
//                CustomColumns = new List<CustomColumn>
//                {
//                    new("EventType", ev => ev.EventType),
//                    new("User", ev => ev.CustomFields["User"]),
//                    new("RecordId", ev => ev.CustomFields["RecordId"])

//                }
//            };

//            /*

//            var connection =
//                new SqlConnection(
//                    );

//            // If the database doesn't exist, create it.
//            await CreateDatabaseIfNotExists(connection);
           

//            connection =
//                new SqlConnection(
//                  );

//            */

//            var connection =
//              new SqlConnection(
//                );

//            await CreateEntriesTableIfNotExists(connection);

//            await connection.TruncateTableAsync("CRDEntries");

//            await CreateAuditLogTableIfNotExists(connection);

//            await connection.TruncateTableAsync("AuditLog");

//            using var reader = new StreamReader(@"C:\Users\<USER>\Downloads\All Chemical CRD.csv");
//            using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);
//            var records = csv.GetRecords<CRDEntry>().ToList();

//            foreach (var record in records)
//            {
//                await CRDEntriesRepository.InsertCRDEntry(connection, record, "<EMAIL>");
//            }

//            var count = await connection.ExecuteScalarAsync("SELECT COUNT(ID) FROM CRDEntries");
//            Console.WriteLine("THIS IS HOW MANY ROWS WE HAVE: " + count);
//        }

//        private static async Task CreateAuditLogTableIfNotExists(SqlConnection connection)
//        {
//            var exists = await connection.TableExistsAsync("AuditLog");
//            if (!exists)
//                await connection.ExecuteAsync(@"
//CREATE TABLE [AuditLog]
//(
//    [EventId] BIGINT IDENTITY(1,1) NOT NULL,
//    [InsertedDate] DATETIME NOT NULL DEFAULT(GETUTCDATE()),
//    [LastUpdatedDate] DATETIME NULL,
//    [JsonData] NVARCHAR(MAX) NOT NULL,
//    [EventType] NVARCHAR(100) NOT NULL,
//    [User] NVARCHAR(100) NOT NULL,
//    [RecordId] INT NOT NULL,
//    CONSTRAINT PK_Event PRIMARY KEY (EventId)
//)
//");
//        }

//        private static async Task CreateEntriesTableIfNotExists(SqlConnection connection)
//        {
//            var exists = await connection.TableExistsAsync("CRDEntries");
//            if (!exists)
//                // Create the table
//                await connection.ExecuteAsync(@"
//CREATE TABLE [dbo].[CRDEntries] (
//    [ID] INT            IDENTITY (1, 1) NOT NULL,


//[Title] NVARCHAR (MAX) NULL,
//[NewNo] NVARCHAR (MAX) NULL,
//[CRDNo] NVARCHAR (MAX) NULL,
//[CCNo] NVARCHAR (MAX) NULL,
//[Formula] NVARCHAR (MAX) NULL,
//[PhysicalProperties] NVARCHAR (MAX) NULL,
//[LethalServiceReactive] NVARCHAR (MAX) NULL,
//[TEAMCritical] NVARCHAR (MAX) NULL,
//[Carcinogen] NVARCHAR (MAX) NULL,
//[MedicalSurveillance] NVARCHAR (MAX) NULL,
//[PSM] NVARCHAR (MAX) NULL,
//[FlashPoint] NVARCHAR (MAX) NULL,
//[AIT] NVARCHAR (MAX) NULL,
//[FlamabilityNote] NVARCHAR (MAX) NULL,
//[BP] NVARCHAR (MAX) NULL,
//[VD] NVARCHAR (MAX) NULL,
//[FrzTemp] NVARCHAR (MAX) NULL,
//[EightHRTWA] NVARCHAR (MAX) NULL,
//[STEL] NVARCHAR (MAX) NULL,
//[IDLH] NVARCHAR (MAX) NULL,
//[PurgeMedia] NVARCHAR (MAX) NULL,
//[VapourIsHeavierThanAir] NVARCHAR (MAX) NULL,
//[NonSparkToolingRequired] NVARCHAR (MAX) NULL,
//[RiskOfExplosionThroughShockFriction] NVARCHAR (MAX) NULL,
//[SCCCarbonSteelFasteners] NVARCHAR (MAX) NULL,
//[SCCStainlessSteelFasteners] NVARCHAR (MAX) NULL,
//[PWHTRecommended] NVARCHAR (MAX) NULL,
//[GroundingCableRequired] NVARCHAR (MAX) NULL,
//[Purge] NVARCHAR (MAX) NULL,
//[OxygenChlorineProcedures] NVARCHAR (MAX) NULL,
//[ReactsWithSteamWaterMoisture] NVARCHAR (MAX) NULL,
//[SpecialProcedures] NVARCHAR (MAX) NULL,
//[Toxicity] NVARCHAR (MAX) NULL,
//[FirstAid] NVARCHAR (MAX) NULL,
//[ButylAcidSuit] NVARCHAR (MAX) NULL,
//[ARButylGloves] NVARCHAR (MAX) NULL,
//[CRNitrileGloves] NVARCHAR (MAX) NULL,
//[ThirdManRS] NVARCHAR (MAX) NULL,
//[APRFilters] NVARCHAR (MAX) NULL,
//[CASNo] NVARCHAR (MAX) NULL,
//[MSDSNo] NVARCHAR (MAX) NULL,
//[NoExposure] NVARCHAR (MAX) NULL,
//[DripOrSeep] NVARCHAR (MAX) NULL,
//[DripOrSeepNotes] NVARCHAR (MAX) NULL,
//[MediumLeak] NVARCHAR (MAX) NULL,
//[MediumLeakNotes] NVARCHAR (MAX) NULL,
//[SevereLeak] NVARCHAR (MAX) NULL,
//[SevereLeakNotes] NVARCHAR (MAX) NULL,
//[OneB] NVARCHAR (MAX) NULL,
//[OneHalfA] NVARCHAR (MAX) NULL,
//[TwoA] NVARCHAR (MAX) NULL,
//[TwoAS] NVARCHAR (MAX) NULL,
//[TwoB] NVARCHAR (MAX) NULL,
//[TwoD] NVARCHAR (MAX) NULL,
//[TwoF] NVARCHAR (MAX) NULL,
//[TwoG] NVARCHAR (MAX) NULL,
//[ThreeA] NVARCHAR (MAX) NULL,
//[ThreeC] NVARCHAR (MAX) NULL,
//[FourA] NVARCHAR (MAX) NULL,
//[FiveA] NVARCHAR (MAX) NULL,
//[FiveB] NVARCHAR (MAX) NULL,
//[FiveD] NVARCHAR (MAX) NULL,
//[FiveE] NVARCHAR (MAX) NULL,
//[FiveC] NVARCHAR (MAX) NULL,
//[FiveO] NVARCHAR (MAX) NULL,
//[FiveX] NVARCHAR (MAX) NULL,
//[SixB] NVARCHAR (MAX) NULL,
//[SixC] NVARCHAR (MAX) NULL,
//[SevenC] NVARCHAR (MAX) NULL,
//[NineA] NVARCHAR (MAX) NULL,
//[NineC] NVARCHAR (MAX) NULL,
//[ElevenA] NVARCHAR (MAX) NULL,
//[ThirteenA] NVARCHAR (MAX) NULL,
//[ThirteenB] NVARCHAR (MAX) NULL,
//[FourteenA] NVARCHAR (MAX) NULL,
//[FourteenB] NVARCHAR (MAX) NULL,
//[FourteenC] NVARCHAR (MAX) NULL,
//[FourteenD] NVARCHAR (MAX) NULL,
//[FourteenE] NVARCHAR (MAX) NULL,
//[TenB] NVARCHAR (MAX) NULL,
//[TenC] NVARCHAR (MAX) NULL,
//[TenD] NVARCHAR (MAX) NULL,
//[TenE] NVARCHAR (MAX) NULL,
//[TwelveF] NVARCHAR (MAX) NULL,
//[TwelveS] NVARCHAR (MAX) NULL,
//[TwelveA] NVARCHAR (MAX) NULL,
//[TwelveL] NVARCHAR (MAX) NULL,
//[TwelveW] NVARCHAR (MAX) NULL,
//[FifteenF] NVARCHAR (MAX) NULL,
//[FifteenS] NVARCHAR (MAX) NULL,
//[FiftyNineD] NVARCHAR (MAX) NULL,
//[OneX] NVARCHAR (MAX) NULL,
//[TwoX] NVARCHAR (MAX) NULL,
//[TwoXH] NVARCHAR (MAX) NULL,
//[NumberFour] NVARCHAR (MAX) NULL,
//[NumberSix] NVARCHAR (MAX) NULL,
//[SixHT] NVARCHAR (MAX) NULL,
//[NumberTen] NVARCHAR (MAX) NULL,
//[NumberEleven] NVARCHAR (MAX) NULL,
//[OneHundredFiftyFiveK] NVARCHAR (MAX) NULL,
//[SixteenX] NVARCHAR (MAX) NULL,
//[EighteenX] NVARCHAR (MAX) NULL,
//[NineteenX] NVARCHAR (MAX) NULL,
//[VPAX] NVARCHAR (MAX) NULL,
//[VPB] NVARCHAR (MAX) NULL,
//[VPER] NVARCHAR (MAX) NULL,
//[VPEPX] NVARCHAR (MAX) NULL,
//[VPF] NVARCHAR (MAX) NULL,
//[VPGX] NVARCHAR (MAX) NULL,
//[VPLX] NVARCHAR (MAX) NULL,
//[VPOX] NVARCHAR (MAX) NULL,
//[BulkX] NVARCHAR (MAX) NULL,
//[FTen] NVARCHAR (MAX) NULL,
//[FElevenF] NVARCHAR (MAX) NULL,
//[FFourteen] NVARCHAR (MAX) NULL,
//[GFiber] NVARCHAR (MAX) NULL,
//[DBTwentyTwo] NVARCHAR (MAX) NULL,
//[DBTwentyThree] NVARCHAR (MAX) NULL,
//[ESOne] NVARCHAR (MAX) NULL,
//[TCFour] NVARCHAR (MAX) NULL,
//[XThirtySix] NVARCHAR (MAX) NULL,
//[RFThreeHundred] NVARCHAR (MAX) NULL,
//[BUNA] NVARCHAR (MAX) NULL,
//[HNBR] NVARCHAR (MAX) NULL,
//[EPDM] NVARCHAR (MAX) NULL,
//[VITON] NVARCHAR (MAX) NULL,
//[AFLAS] NVARCHAR (MAX) NULL,
//[NEOPRENE] NVARCHAR (MAX) NULL,
//[SILICONE] NVARCHAR (MAX) NULL,
//[FCXSixHundredFour] NVARCHAR (MAX) NULL,
//[ElevenS] NVARCHAR (MAX) NULL,
//[CarbonSteel] NVARCHAR (MAX) NULL,
//[StainlessSteel] NVARCHAR (MAX) NULL,
//[ChromeMoly] NVARCHAR (MAX) NULL,
//[PTFE] NVARCHAR (MAX) NULL,
//[CarbonGraphite] NVARCHAR (MAX) NULL,
//[ARAMID] NVARCHAR (MAX) NULL,
//[IMPRES] NVARCHAR (MAX) NULL,
//[BrassWire] NVARCHAR (MAX) NULL,
//[CopperTubing] NVARCHAR (MAX) NULL,
//[StainlessTubing] NVARCHAR (MAX) NULL,


//    CONSTRAINT [PK_dbo.CRDEntries] PRIMARY KEY CLUSTERED ([ID] ASC)
//);");
//        }

//        private static async Task<int> CreateDatabaseIfNotExists(SqlConnection connection) =>
//            await connection.ExecuteAsync(@"
//IF NOT EXISTS(SELECT * FROM sys.databases WHERE name = 'CRD')
//  BEGIN
//    CREATE DATABASE CRD
//  END");
//    }

//}