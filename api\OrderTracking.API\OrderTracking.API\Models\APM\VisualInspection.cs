﻿using System.Collections.Generic;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{

    /// <summary>
    /// 
    /// </summary>
    public class VisualInspection
    {
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName="projectId")]
        public string ProjectId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "workOrderId")]
        public string WorkOrderId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "InspectionType")]
        public string InspectionType { get; set; }

        /// <summary>
        ///     Database id of the task being updated
        /// </summary>
        [JsonProperty(PropertyName = "taskDatabaseId")]
        public string TaskDatabaseId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "sections")]
        public ICollection<VisualInspectionSections> Sections { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public class VisualInspectionSections
    {
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "title")]
        public string Title { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "questions")]
        public ICollection<InspectionResultsQuestion> Questions { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "databaseName")]
        public string DatabaseName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "subSections")]
        public ICollection<VisualInspectionSections> SubSections { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class InspectionResultsQuestion
    {

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "key")]
        public string Key { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "attributeType")]
        public string AttributeType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "text")]
        public string Text { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "response")]
        public string Response { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "comments")]
        public string Comments { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "commentsRequired")]
        public bool CommentsRequired { get; set; }
    }
}
