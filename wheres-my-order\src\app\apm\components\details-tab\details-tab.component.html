<div class="responsive-paddings content-block details-tab">
    <dx-form #form
             [(formData)]="project"
             [colCount]="5"
             [readOnly]="!isEditing"
             (onFieldDataChanged)="onFieldDataChanged($event)">

        <dxi-item itemType="group"
                  [colSpan]="1"
                  [colCount]="1">
            <dxi-item dataField="projectName"></dxi-item>
            <dxi-item dataField="apmProjectNumber"
                      [editorOptions]="{readOnly: true}">
                <dxo-label text="APM Project Number"></dxo-label>
            </dxi-item>
            <dxi-item dataField="location"
                      editorType="dxSelectBox"
                      [editorOptions]="{dataSource: locationsDataSource, displayExpr: 'name.currentValue', disabled: true}">
                <dxo-label text="Location Name"></dxo-label>
            </dxi-item>
            <dxi-item dataField="projectCity"></dxi-item>
            <dxi-item dataField="projectState"></dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  [colSpan]="1"
                  [colCount]="1">
            <dxi-item dataField="teamDistrictNumber">
                <dxo-label text="TEAM District Number"></dxo-label>
            </dxi-item>
            <dxi-item dataField="teamProjectNumber">
                <dxo-label text="TEAM Project Number"></dxo-label>
            </dxi-item>
            <dxi-item dataField="clientProjectNumber"></dxi-item>
        </dxi-item>

        <dxi-item itemType="group"
                  [colSpan]="1"
                  [colCount]="1">
            <dxi-item dataField="status"
                      editorType="dxSelectBox"
                      [editorOptions]="{dataSource: ['Scheduled', 'In Progress', 'Completed']}">
            </dxi-item>

            <dxi-item dataField="plannedStart"
                      editorType="dxDateBox"
                      dataType="date"></dxi-item>
            <dxi-item dataField="plannedEnd"
                      editorType="dxDateBox"
                      dataType="date"></dxi-item>
        </dxi-item>


        <dxi-item editorType="dxTextArea"
                  dataField="description"
                  [editorOptions]="{autoResizeEnabled: true}"></dxi-item>

        <dxi-item itemType="group"
                  [colSpan]="5">
            <dxi-item [template]="'buttons'"></dxi-item>
        </dxi-item>

        <div *dxTemplate="let data of 'buttons'"
             class="buttons">
            <dx-button *ngIf="!isEditing; else saveAndCancel"
                       text="Edit"
                       type="default"
                       [disabled]="!allowEditing"
                       (onClick)="onEditClicked($event)"></dx-button>
            <ng-template #saveAndCancel>
                <dx-button text="Cancel"
                           type="danger"
                           (onClick)="onCancelClicked($event, form)">
                </dx-button>
                <dx-button text="Save"
                           type="success"
                           [disabled]="isSaving |async"
                           (onClick)="onSaveClicked($event)"></dx-button>
            </ng-template>
        </div>
    </dx-form>

</div>
