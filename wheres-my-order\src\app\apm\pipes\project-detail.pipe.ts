import { Pipe, PipeTransform } from '@angular/core';
import { Location, ProjectDetail, ProjectVm } from '../models';

@Pipe({
    name: 'projectDetail'
})
export class ProjectDetailPipe implements PipeTransform {
    transform(project: ProjectVm, location: Location): ProjectDetail {
        return new ProjectDetail({
            apmProjectNumber: project?.apmProjectNumber,
            clientName: 'Chevron',
            clientProjectNumber: project?.clientProjectNumber,
            description: project?.description,
            location,
            projectCity: location?.city?.currentValue,
            projectState: location?.region?.currentValue,
            projectName: project?.name,
            teamDistrictNumber: project?.teamDistrictNumber,
            teamProjectNumber: project?.teamProjectNumber,
            status: project?.status,
            plannedEnd: project?.plannedEnd,
            plannedStart: project?.plannedStart,
            id: project?.id
        });
    }
}
