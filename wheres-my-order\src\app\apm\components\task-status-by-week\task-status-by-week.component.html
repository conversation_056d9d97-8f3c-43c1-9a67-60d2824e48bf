<div class="dx-card content-block responsive-paddings"
     style="position: relative">
    <dx-load-indicator *ngIf="loading"
                       style="position: absolute; left: calc(50% - 30px); top: calc(50% - 30px);"
                       height="60"
                       width="60"></dx-load-indicator>

    <app-asset-categories-selector (categories)="onCategoryChanged($event)">
    </app-asset-categories-selector>

    <dx-chart #chart
              [dataSource]="statusesByWeek">
        <dxo-size [height]="500"></dxo-size>
        <dxo-title [text]="'Weekly Task Tracker'"
                   [horizontalAlignment]="'center'"
                   [verticalAlignment]="'top'"></dxo-title>

        <dxi-value-axis>
            <dxo-title text="Count"></dxo-title>
        </dxi-value-axis>
        <dxo-common-series-settings argumentField="bin"
                                    type="stackedBar">
        </dxo-common-series-settings>

        <dxo-argument-axis [visualRange]="visualRange">
            <dxo-label overlappingBehavior="rotate"></dxo-label>
        </dxo-argument-axis>

        <dxo-legend position="outside"
                    horizontalAlignment="center"
                    verticalAlignment="top"></dxo-legend>
    </dx-chart>

    <h3 style="width: 100%; text-align: center; margin-top: 1rem;">{{axisLabel}}
    </h3>

    <dx-range-selector [dataSource]="statusesByWeek"
                       [(value)]="visualRange"
                       style="width: 102%">
        <dxo-size [height]="120"></dxo-size>
        <dxo-margin [left]="10"></dxo-margin>
        <dxo-scale [minorTickCount]="1"></dxo-scale>
        <dxo-behavior callValueChanged="onMoving"></dxo-behavior>
        <dxo-chart>
            <dxo-common-series-settings argumentField="bin">
            </dxo-common-series-settings>

            <dxi-series *ngFor="let series of chart.series"
                        [valueField]="series.valueField"
                        [color]="series.color"></dxi-series>
        </dxo-chart>
    </dx-range-selector>
</div>
