﻿using System;
using ClientPortal.Shared.Extensions;
using ClientPortal.Shared.Models;
using NUnit.Framework;

namespace ClientPortal.Shared.Test
{
    [TestFixture]
    public class UserProfileExtensionsTests
    {
        [Test]
        public void IsOnlyUserAgreementChanges_OnlyDisclaimerChanges_ReturnsTrue()
        {
            var originalUser = new UserProfile();
            var newUser = new UserProfile
            {
                AcceptedDisclaimerDate = DateTime.UtcNow,
                AcceptedClientDisclaimerVersion = "2"
            };

            var isOnlyUserAgreementChanges = originalUser.IsOnlyUserAgreementChanges(newUser);

            Assert.That(isOnlyUserAgreementChanges, Is.True);
        }

        [Test]
        public void IsOnlyUserAgreementChanges_MoreThanDisclaimerChanges_ReturnsFalse()
        {
            var originalUser = new UserProfile();
            var newUser = new UserProfile
            {
                Name = "Bob",
                AcceptedDisclaimerDate = DateTime.UtcNow,
                AcceptedClientDisclaimerVersion = "2"
            };

            var isOnlyUserAgreementChanges = originalUser.IsOnlyUserAgreementChanges(newUser);

            Assert.That(isOnlyUserAgreementChanges, Is.False);
        }

        [Test]
        public void IsOnlyPermittedChanges_ChangedPropertyNamesIsZero_True()
        {
            var user = new UserProfile();
            var newUser = new UserProfile
            {

            };
            var isOnlyPermittedChanges = user.IsOnlyPermittedChanges(newUser);

            Assert.That(isOnlyPermittedChanges, Is.True);
        }

        [Test]
        public void IsOnlyPermittedChanges_OnlyLastLoginDateChanged_True()
        {
            var user = new UserProfile();
            var newUser = new UserProfile()
            {
                LastLoginDate = new DateTime()
            };
            var isOnlyPermittedChanges = user.IsOnlyPermittedChanges(newUser);

            Assert.That(isOnlyPermittedChanges, Is.True);
        }

        [Test]
        public void IsOnlyPermittedChanges_OnlyLastClientPortalVersion_True()
        {
            var user = new UserProfile();
            var newUser = new UserProfile()
            {
                LastClientPortalVersion = "2"
            };
            var isOnlyPermittedChanges = user.IsOnlyPermittedChanges(newUser);

            Assert.That(isOnlyPermittedChanges, Is.True);
        }

        [Test]
        public void IsOnlyPermittedChanges_OnlyLastLoginDateAndLastClientPortalVersion_True()
        {
            var user = new UserProfile
            {
                LastLoginDate = new DateTime(),
                LastClientPortalVersion = "2"
            };
            var newUser = new UserProfile
            {
                LastLoginDate = new DateTime().AddHours(2),
                LastClientPortalVersion = "3"
            };

            var isOnlyPermittedChanges = user.IsOnlyPermittedChanges(newUser);

            Assert.That(isOnlyPermittedChanges, Is.True);
        }

        [Test]
        public void IsOnlyPermittedChanges_OtherPropertiesUpdated_False()
        {
            var user = new UserProfile
            {
                LastLoginDate = new DateTime(),
                LastClientPortalVersion = "2"
            };
            var newUser = new UserProfile
            {
                LastLoginDate = new DateTime().AddHours(2),
                LastClientPortalVersion = "3",
                Roles = {"App:Admin"}
            };

            var isOnlyPermittedChanges = user.IsOnlyPermittedChanges(newUser);

            Assert.That(isOnlyPermittedChanges, Is.False);
        }

        [Test]
        public void IsOnlyPermittedChanges_OnlyWrongPropertyUpdated_False()
        {
            var user = new UserProfile();
            var newUser = new UserProfile
            {
                Roles = {"App:Admin"}
            };

            var isOnlyPermittedChanges = user.IsOnlyPermittedChanges(newUser);

            Assert.That(isOnlyPermittedChanges, Is.False);
        }
    }
}