import { Component, EventEmitter, Input, Output } from '@angular/core';
import { AssetAccess } from '../../models';

@Component({
    selector: 'app-asset-access',
    templateUrl: './asset-access.component.html',
    styleUrls: ['./asset-access.component.scss']
})
export class AssetAccessComponent {
    private _assetAccess: AssetAccess | undefined;
    @Input() allowEditing: boolean;
    @Input() set assetAccess(value: AssetAccess) {
        this.isEditing = false;
        this._assetAccess = value;
    }
    get assetAccess(): AssetAccess {
        return this._assetAccess;
    }
    @Output() assetAccessUpdated = new EventEmitter<AssetAccess>();
    originalAssetAccess: AssetAccess;

    get allUnderFourFeet() {
        return this.assetAccess?.allComponentsUnder4FeetInHeight;
    }
    isEditing = false;
    isSaving = false;
    hasInsulationItems = ['Yes', 'No', 'Partial'];
    possibleAsbestosItems = ['Yes', 'No'];
    jacketingTypeItems = [
        'Aluminum',
        'Stainless',
        'Ferro Sheeting',
        'PVC',
        'None'
    ];
    insulationTypeItems = ['Bags/Blankets', 'Fiber', 'CalSil', 'Rock Wool'];
    heatTracingItems = [
        'Live Electrical',
        'Inactive Electrical',
        'Live Steam Tubing',
        'Inactive Steam Tubing',
        'None'
    ];
    coatingTypeItems = ['Paint', 'FBE', 'Bitumen', 'Wrap', 'None'];
    coatingConditionItems = ['Acceptable', 'Concern', 'N/A'];
    coatingConditionsObservedItems = [
        'Smooth',
        'Chalking',
        'Chipping',
        'Peeling',
        'Blistering',
        'Holiday'
    ];
    corrosionIdentifiedItems = ['Rust', 'Scale', 'Oxidation', 'None'];
    corrosionRemovalRecommendationItems = [
        'Wire Brush',
        'Power Tools',
        'Sandblasting',
        'None'
    ];
    ladderRequirementsItems = ['Step Ladder', 'Extension Ladder'];
    inspectionOpeningTypes = ['Manway', 'Handhole'];
    ventilationRequirementsItems = ['Open to Atmosphere', 'Air Mover'];
    cleaningRecommendations = [
        'Water Spray',
        'Pressure Wash',
        'Sandblasted',
        'Acid Wash',
        'Steam',
        'None'
    ];
    cleaningServiceReviewItems = ['Acceptable', 'Concern', 'N/A'];
    coatingLinerTypes = ['Concrete', 'Tile', 'Epoxy', 'Resin', 'None'];
    coatingLinerConditions = ['Acceptable', 'Concern', 'N/A'];
    coatingLinerConditionsObservedItems = [
        'Smooth',
        'Peeling',
        'Blistering',
        'Holiday'
    ];

    constructor() {}

    onEditClicked(e) {
        this.isEditing = !this.isEditing;
        this.originalAssetAccess = { ...this.assetAccess };
    }

    onCancelClicked(e) {
        this.isEditing = !this.isEditing;
        this.assetAccess = { ...this.originalAssetAccess };
    }

    onSaveClicked(e) {
        this.assetAccessUpdated.emit(this.assetAccess);
    }
}
