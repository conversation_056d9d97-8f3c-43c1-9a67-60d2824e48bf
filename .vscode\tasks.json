{"version": "2.0.0", "tasks": [{"label": "<PERSON><PERSON><PERSON>", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/api/OrderTracking.API/OrderTracking.API/OrderTracking.API.csproj"], "problemMatcher": "$msCompile"}, {"label": "buildap<PERSON>", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/api/OrderTracking.API/OrderTracking.API/OrderTracking.API.csproj"], "problemMatcher": "$msCompile", "dependsOn": ["<PERSON><PERSON><PERSON>"]}]}