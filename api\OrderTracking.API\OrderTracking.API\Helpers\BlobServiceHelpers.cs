﻿#region Copyright TEAM Inc. 2020

// www.TeamInc.com
// +1-800-662-8326
// 
// All rights are reserved. Reproduction or transmission in whole or in part, in
// any form or by any means, electronic, mechanical or otherwise, is prohibited
// without the prior written consent of the copyright owner.
// 
// File Created: -- 
// Updated:      2020-04-21 11:18 AM
// Created by:   <PERSON><PERSON><PERSON><PERSON>, Sean

#endregion

using System;
using System.Web;

namespace OrderTracking.API.Helpers
{
    /// <summary>
    ///     Helper class for Blob File related operations
    /// </summary>
    public static class BlobServiceHelpers
    {
        /// <summary>
        ///     Encode the Uri from the blob Name.
        /// </summary>
        /// <param name="blobName"></param>
        /// <returns></returns>
        public static string EncodeUri(string blobName)
        {
            var decodedBlobName = HttpUtility.UrlDecode(blobName);
            var encodedBlobName = Uri.EscapeDataString(decodedBlobName);
            return encodedBlobName;
        }

        /// <summary>
        ///     Resource path for a blob file, encoded.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="blobName"></param>
        /// <returns></returns>
        public static string FullBlobStringEncoded(string id, string blobName)
        {
            return $"{id}/{EncodeUri(blobName)}";
        }
    }
}