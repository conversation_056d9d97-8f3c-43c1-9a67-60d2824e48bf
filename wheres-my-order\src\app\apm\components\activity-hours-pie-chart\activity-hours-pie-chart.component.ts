import {
    ChangeDetectionStrategy,
    Component,
    Input,
    ViewChild
} from '@angular/core';
import { DxPieChartComponent } from 'devextreme-angular/ui/pie-chart';
import { BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { WorkTypePercentage } from '../../models';

@Component({
    selector: 'app-activity-hours-pie-chart',
    templateUrl: './activity-hours-pie-chart.component.html',
    styleUrls: ['./activity-hours-pie-chart.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ActivityHoursPieChartComponent {
    private readonly _activitySummary = new BehaviorSubject<
        WorkTypePercentage[]
    >([]);
    readonly totalHours$ = this._activitySummary.pipe(
        map((summary) => this.getTotalHours(summary))
    );

    @Input() loading: boolean = false;

    @Input() set activitySummary(value: WorkTypePercentage[]) {
        this._activitySummary.next(value);
    }
    get activitySummary(): WorkTypePercentage[] {
        return this._activitySummary.value;
    }

    @ViewChild(DxPieChartComponent) chart: DxPieChartComponent;

    constructor() {}

    customizeLabel(arg: { valueText: string; percentText: string }) {
        return `${arg.valueText} (${arg.percentText})`;
    }

    renderChart() {
        this.chart?.instance?.render({ force: true });
    }

    private getTotalHours(summary: WorkTypePercentage[]): number {
        return summary
            ?.map((activity) => activity.hours)
            ?.filter((hours) => Boolean(hours))
            ?.reduce(
                (previousValue, currentValue) => previousValue + currentValue,
                0
            );
    }
}
