<div class="dx-card responsive-paddings content-block"
     style="position: relative">
    <dx-load-indicator *ngIf="loading"
                       style="position: absolute; left: calc(50% - 30px); top: calc(50% - 30px)"
                       height="60"
                       width="60"></dx-load-indicator>

    <app-asset-categories-selector (categories)="onCategoryChanged($event)">
    </app-asset-categories-selector>

    <dx-chart [dataSource]="inspectionStatusesByMonth">
        <dxo-title [text]="'Monthly Task Tracker'"
                   [horizontalAlignment]="'center'"
                   [verticalAlignment]="'top'"></dxo-title>

        <dxi-value-axis>
            <dxo-title text="Count"></dxo-title>
        </dxi-value-axis>
        <dxo-common-series-settings argumentField="bin"
                                    type="stackedBar">
        </dxo-common-series-settings>
        <dxo-legend horizontalAlignment="right"
                    columnCount="2"
                    position="inside"></dxo-legend>
    </dx-chart>
    <h3 style="width: 100%; text-align: center;">{{axisLabel}}</h3>
</div>
