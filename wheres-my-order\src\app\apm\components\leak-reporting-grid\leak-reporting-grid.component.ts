import {
    Component,
    EventEmitter,
    Input,
    Output,
    ViewChild
} from '@angular/core';
import { DxDataGridComponent } from 'devextreme-angular';
import ArrayStore from 'devextreme/data/array_store';
import { ClickEvent } from 'devextreme/ui/button';
import { Properties } from 'devextreme/ui/radio_group';
import { map } from 'rxjs/operators';
import { ApmService } from '../../../apm/services';
import { DataGridService } from '../../../shared/services';
import { LeakReportGridRow } from '../../models';

@Component({
    selector: 'app-leak-reporting-grid',
    templateUrl: './leak-reporting-grid.component.html',
    styleUrls: ['./leak-reporting-grid.component.scss']
})
export class LeakReportingGridComponent {
    @ViewChild(DxDataGridComponent) dataGrid: DxDataGridComponent;
    dataSource = new ArrayStore({ data: [], key: 'id' });

    @Input()
    set rows(value: LeakReportGridRow[]) {
        this._rows = value;
    }

    get rows(): LeakReportGridRow[] {
        return this._rows;
    }
    private _rows: LeakReportGridRow[];

    @Output()
    addingLeakReport = new EventEmitter<LeakReportGridRow>();

    @Output()
    selectedReportID = new EventEmitter<string>();

    statusEditorOptions: Properties = {
        dataSource: ['active', 'closed'],
        layout: 'horizontal'
    };

    allowEditing$ = this._apm.allowEditing$;
    addRowButtonDisabled$ = this._apm.selectedBU$.pipe(
        map((bu) => bu === null || bu === undefined)
    );

    restoreGridDefaults = async (e: ClickEvent) => {
        const result = await this._grid.resetGridState(this.dataGrid);
        if (result) localStorage.removeItem('apmLeakReportsGridState');
    };

    constructor(
        private readonly _grid: DataGridService,
        private readonly _apm: ApmService
    ) {}

    onLeakReportCreating(e) {
        this.addingLeakReport.next(e.data);
    }

    onSelectionChanged(e) {
        this.selectedReportID.next(e.selectedRowKeys[0]);
    }
}
