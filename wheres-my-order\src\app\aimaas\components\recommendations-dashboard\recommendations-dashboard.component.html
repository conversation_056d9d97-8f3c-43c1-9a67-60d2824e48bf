<div class="dx-card content-block">
    <h2
        style="text-align: center; margin:0,10px,0,0; font-weight:500; font-size:20px">
        Anomalies and Recommendations</h2>
    <dx-popup [(visible)]="submissionPopupVisible"
              [width]="1200"
              [height]="700"
              [showTitle]="true"
              [dragEnabled]="false"
              [showCloseButton]="true">
        <dxi-toolbar-item toolbar="top"
                          [text]="submissionPopupTitle"
                          location="center"
                          locateInMenu="always"></dxi-toolbar-item>

        <ng-container *ngIf="submissionPopupVisible">

            <app-client-data-submission [initialAnomaly]="initialAnomaly"
                                        (clientSubmissionTitleValueChange)="clientSubmissionTitleValueChange($event)"
                                        (formSubmittedValueChange)="clientDataFormSubmitted($event)">
            </app-client-data-submission>
        </ng-container>
    </dx-popup>

    <div class="container1">
        <div class="item1">
            <div class="prioritylevel">
                <ul>
                    <b class="anomaly-heading"> Anomaly Priority Levels </b>
                    <li class="priority">
                        <div class="circle level-6"></div>
                        <span>6 - Major Anomaly / Code Requirement</span>
                    </li>
                    <li class="priority">
                        <div class="circle level-5"></div>
                        <span>5 - Minor Anomaly / Code Recommendation</span>
                    </li>
                    <li class="priority">
                        <div class="circle level-4"></div>
                        <span>4 - Monitor Anomaly / RAGAGEP</span>
                    </li>
                    <li class="priority">
                        <div class="circle level-3"></div>
                        <span>3 - Non-Code Suggestion</span>
                    </li>
                    <li class="priority">
                        <div class="circle level-2"></div>
                        <span>2 - No Concern</span>
                    </li>
                    <li class="priority">
                        <div class="circle level-1"></div>
                        <span>1 - Not Applicable</span>
                    </li>
                    <li class="priority">
                        <div class="circle level-0"></div>
                        <span>0 - Not Ranked</span>
                    </li>
                </ul>
            </div>
        </div>

        <div class="item2">
            <div class="prioritylevel">
                <ol class="square-list">
                    <b class="anomaly-heading"> Open Recommendations </b>
                    <div class="level">
                        <div class="square level-7"
                             style="background-color: #FFD9D9;padding-top: 5px;">
                            {{
                            priorityMetrics[6] }}
                        </div>
                        <span style="padding-left: 10px;padding-top: 5px;">Major
                            (P6)</span>
                    </div>
                    <div class="level">
                        <div class="square level-8"
                             style=" background-color: #fff3d9;padding-top: 5px;">
                            {{
                            priorityMetrics[5] }}
                        </div>
                        <span style="padding-left: 10px;">Minor (P5)</span>
                    </div>
                    <div class="level">
                        <div class="square level-9"
                             style="background-color: #fcfcca;padding-top: 5px;">
                            {{
                            priorityMetrics[4] }}
                        </div>
                        <span style="padding-left: 10px;">Monitor (P4)</span>
                    </div>
                    <!-- Aged Section with Dropdown -->
                    <div class="level">
                        <div class="square level-10"
                             style=" background-color: #edf5ff;padding-top: 5px;">
                            {{ agedMetric
                            }}</div>
                        <span
                              style="padding-left: 10px;padding-right: 15px;">Aged</span>
                        <div>
                            <dx-select-box id="selectBox2"
                                           [dataSource]="dropdownItems"
                                           [(value)]="selectedDropdownValue"
                                           (onValueChanged)="onDropdownValueChanged($event)"
                                           [width]="85"><dxo-drop-down-options
                                                       container="#selectBox2"></dxo-drop-down-options>
                            </dx-select-box>
                        </div>
                        <div style="padding-left: 10px;">
                            <dx-number-box [(value)]="timeInputValue"
                                           (onChange)="calculateAgedMetric(anomaliesData)"
                                           [showSpinButtons]="false"
                                           placeholder="Enter number of {{ selectedDropdownValue }}"
                                           [min]="1"
                                           [width]="90">
                            </dx-number-box>
                        </div>
                    </div>
                </ol>
            </div>
        </div>
        <div class="item3">
            <b class="anomaly-heading">P4, P5, P6 Priorities OPEN vs CLOSED</b>
            <dx-chart #OpenClosedChart
                      [dataSource]="pieChartData"
                      (onPointClick)="onPieChartClick($event)">
                <dxi-series argumentField="type"
                            name="Open"
                            valueField="Open"
                            color="#f5564a"
                            type="stackedBar">
                </dxi-series>
                <dxi-series argumentField="type"
                            name="Closed"
                            valueField="Closed"
                            color="#1db2f5"
                            type="stackedBar">
                </dxi-series>
                <dxi-value-axis position="left">
                    <dxo-title text="Number of Anomalies"></dxo-title>
                </dxi-value-axis>
                <dxo-argument-axis>
                    <dxo-title text="Priority Level"></dxo-title>
                </dxo-argument-axis>
                <dxo-legend verticalAlignment="bottom"
                            horizontalAlignment="center"
                            itemTextPosition="top">
                </dxo-legend>
                <dxo-tooltip [enabled]="true"></dxo-tooltip>
            </dx-chart>
        </div>


    </div>
    <div class="container"
         style="display: flex;flex-direction: column;">
        <div class="item3">
            <b class="anomaly-heading">Types of P6 Anomalies, grouped by status
                (all time) </b>
            <dx-chart #P6AnomaliesGroupedByStatusChart
                      id="chart"
                      [dataSource]="chartData"
                      (onPointClick)="onChartClick($event)">
                <dxi-series valueField="Recommended"
                            name="Recommended"
                            color="#1db2f5"></dxi-series>
                <dxi-series valueField="Resolved"
                            name="Resolved"
                            color="#97c95c"></dxi-series>
                <dxi-series valueField="Rejected"
                            name="Rejected"
                            color="#f5564a"></dxi-series>
                <dxi-value-axis position="left">
                    <dxo-title text="Anomaly Types"></dxo-title>
                </dxi-value-axis>
                <dxo-common-series-settings argumentField="type"
                                            type="stackedBar">
                </dxo-common-series-settings>
                <dxo-legend verticalAlignment="bottom"
                            horizontalAlignment="center"
                            itemTextPosition="top">
                </dxo-legend>
                <dxo-tooltip [enabled]="true"
                             location="edge"
                             [customizeTooltip]="customizeTooltip">
                </dxo-tooltip>
            </dx-chart>
        </div>

        <div class="item5">
            <b class="anomaly-heading"> Anomaly and Inspection Count Over Time
            </b>
            <dx-chart #AnomalyOperationChart
                      id="anomalyOperationChart"
                      (onPointClick)="onPointClick($event)"
                      [dataSource]="dataSource">
                <dxi-series *ngIf="isFirstLevel"
                            type="stackedBar"
                            argumentField="year"
                            valueField="openAnomalies"
                            title="Open Anomalies"
                            name="Open Anomalies"
                            color="#1db2f5"></dxi-series>
                <dxi-series *ngIf="isFirstLevel"
                            type="stackedBar"
                            argumentField="year"
                            title="Closed Anomalies"
                            valueField="closedAnomalies"
                            name="Closed Anomalies"
                            color="#f5564a"></dxi-series>
                <dxi-series *ngIf="isFirstLevel"
                            type="line"
                            argumentField="year"
                            valueField="operationCount"
                            name="Inspection"
                            axis="right"
                            color="#8051c6"></dxi-series>
                <dxi-series *ngIf="!isFirstLevel"
                            type="stackedBar"
                            argumentField="monthYear"
                            valueField="openAnomalies"
                            name="Open Anomalies"
                            color="#1db2f5"></dxi-series>
                <dxi-series *ngIf="!isFirstLevel"
                            type="stackedBar"
                            argumentField="monthYear"
                            valueField="closedAnomalies"
                            name="Closed Anomalies"
                            color="#f5564a"></dxi-series>
                <dxi-series *ngIf="!isFirstLevel"
                            type="line"
                            argumentField="monthYear"
                            valueField="operationCount"
                            name="Inspection"
                            axis="right"
                            color="#8051c6"></dxi-series>
                <dxi-value-axis name="left">
                    <dxo-title text="Anomalies"></dxo-title>
                </dxi-value-axis>
                <dxi-value-axis name="right"
                                position="right">
                    <dxo-title text="Inspections"></dxo-title>
                </dxi-value-axis>
                <dxo-common-series-settings *ngIf="isFirstLevel"
                                            argumentField="year"
                                            type="fullstackedbar">
                </dxo-common-series-settings>
                <dxo-common-series-settings *ngIf="!isFirstLevel"
                                            argumentField="monthYear"
                                            type="fullstackedbar">
                </dxo-common-series-settings>
                <dxo-tooltip [enabled]="true"></dxo-tooltip>
                <dxo-legend verticalAlignment="bottom"
                            horizontalAlignment="center"
                            itemTextPosition="top"></dxo-legend>
            </dx-chart>
            <dx-button class="button-container"
                       text="Back"
                       icon="chevronleft"
                       [visible]="!isFirstLevel"
                       (onClick)="onButtonClick()"></dx-button>

        </div>
    </div>
</div>


<dx-popup [(visible)]="anomalyPopupVisible"
          [width]="1200"
          [height]="700"
          [showTitle]="true"
          [dragEnabled]="false"
          [showCloseButton]="true">
    <dxi-toolbar-item toolbar="top"
                      location="center"
                      locateInMenu="always"></dxi-toolbar-item>
    <!-- [text]="submissionPopupTitle" -->
    <ng-container *ngIf="anomalyPopupVisible">

    </ng-container>
</dx-popup>