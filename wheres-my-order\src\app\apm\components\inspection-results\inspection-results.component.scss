.question {
    display: flex;
    margin-bottom: 1rem;
    .question-content {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        .response,
        .comments {
            display: flex;
            align-items: baseline;
            span {
                width: 70px;
            }
            dx-text-area {
                flex-grow: 1;
            }
            dx-tag-box {
                flex-grow: 0.5;
            }
        }
    }

    .gallery-wrapper{
        flex-basis: auto;
        margin-bottom: 20px;
    }

    dx-gallery {
        flex: 0 0 300px;
        ::ng-deep .dx-gallery-item-image {
            max-height: 100%;
        }
    }
}

::ng-deep .dx-multiview-item-hidden {
    top: -99999px;
}

::ng-deep .dx-gallery .dx-gallery-nav-button-prev,
::ng-deep .dx-gallery .dx-gallery-nav-button-next {
    width: 0% !important;
}

::ng-deep .popup-template {
    width: 100%;
    height: 100%;
    max-height: 100%;
    display: flex;
    flex-direction: row;
    white-space: pre-wrap;
    .image-container {
        flex-grow: 1;
        display: flex;
        height: 100%;
        dx-button {
            justify-self: flex-end;
        }
        img {
            height: 100%;
            width: 100%;
            object-fit: contain;
            justify-self: center;
            margin: auto;
        }
    }
    .info {
        margin-left: 1em;
        justify-content: space-between;
        height: 100%;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        .text-content {
            text-align: left;
            .description-buttons {
                display: flex;
                margin-bottom: 1em;
                margin-right: 1em;
                dx-button {
                    margin-top: 1em;
                    margin-left: 1em;
                    &:first-child {
                        margin-left: auto;
                    }
                }
            }
        }
        .buttons {
            margin-bottom: 1em;
            margin-right: 1em;
            align-self: flex-end;
            dx-button {
                margin-left: 1em;
            }
        }
    }
}

.button-container {
    display: flex;
    justify-content: flex-end;
    dx-button {
        margin-left: 1em;
    }
}
