//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Net;
//using System.Threading.Tasks;
//using Azure;
//using Azure.Storage.Blobs.Models;
//using Microsoft.AspNetCore.Http;
//using Microsoft.Azure.Cosmos;
//using Microsoft.Extensions.Options;
//using OrderTracking.API.Extensions;
//using OrderTracking.API.Interfaces;
//using OrderTracking.API.Models;
//using OrderTracking.API.Models.EDR;

//namespace OrderTracking.API.Services
//{
//    /// <summary>
//    ///     Service class for equipment requests
//    /// </summary>
//    public class EquipmentRequestsService : IEquipmentRequestsService
//    {
//        private readonly Container _container;
//        private readonly IEDRBlobStorageService _storage;

//        /// <summary>
//        ///     Constructs the EquipmentRequestsService class, injecting connection configuration and a EDR blob storage service
//        ///     instance
//        /// </summary>
//        /// <param name="client"></param>
//        /// <param name="options"></param>
//        /// <param name="storage"></param>
//        public EquipmentRequestsService(
//            ICosmosClientAdapter client,
//            IOptions<Connections> options,
//            IEDRBlobStorageService storage
//        )
//        {
//            if (client == null) throw new ArgumentNullException(nameof(client));
//            if (options == null) throw new ArgumentNullException(nameof(options));

//            _container = client.GetContainer(options.Value.Database, options.Value.EquipmentRequests);
//            _storage = storage;
//        }

//        /// <summary>
//        ///     Add an equipment request
//        /// </summary>
//        /// <param name="equipmentRequest"></param>
//        /// <returns></returns>
//        public async Task AddItemAsync(EquipmentRequest equipmentRequest)
//        {
//            if (equipmentRequest == null) throw new ArgumentNullException(nameof(equipmentRequest));
//            await _container.CreateItemAsync(equipmentRequest, new PartitionKey(equipmentRequest.JobNumber));
//        }

//        /// <summary>
//        ///     Delete an equipment request
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="pk"></param>
//        /// <returns></returns>
//        public async Task DeleteItemAsync(string id, string pk)
//        {
//            await _container.DeleteItemAsync<EquipmentRequest>(id, new PartitionKey(pk));
//        }

//        /// <summary>
//        ///     Get an equipment request
//        /// </summary>
//        /// <param name="id"></param>
//        /// <returns></returns>
//        public async Task<EquipmentRequest> GetItemAsync(string id)
//        {
//            try
//            {
//                using var iterator =
//                    _container.GetItemQueryIterator<EquipmentRequest>($"SELECT * FROM c WHERE c.id = '{id}'");
//                var results = new List<EquipmentRequest>();
//                while (iterator.HasMoreResults)
//                    results.AddRange(await iterator.ReadNextAsync());

//                var equipmentRequest = results.First();
//                return equipmentRequest;
//            }
//            catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
//            {
//                return null;
//            }
//        }

//        /// <summary>
//        ///     Get multiple equipment requests, dependent on the query string
//        /// </summary>
//        /// <param name="queryString"></param>
//        /// <returns></returns>
//        public async Task<IEnumerable<EquipmentRequest>> GetItemsAsync(string queryString)
//        {
//            using var iterator = _container.GetItemQueryIterator<EquipmentRequest>(new QueryDefinition(queryString));
//            var results = new List<EquipmentRequest>();
//            while (iterator.HasMoreResults)
//            {
//                var response = await iterator.ReadNextAsync();

//                results.AddRange(response.ToList());
//            }

//            return results;
//        }

//        /// <summary>
//        ///     Update an equipment request
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="equipmentRequest"></param>
//        /// <returns></returns>
//        public async Task UpdateItemAsync(string id, EquipmentRequest equipmentRequest)
//        {
//            if (equipmentRequest == null) throw new ArgumentNullException(nameof(equipmentRequest));
//            await _container.ReplaceItemAsync(equipmentRequest, equipmentRequest.Id);
//        }

//        /// <summary>
//        ///     Upload files to an equipment request.  If any of the files share the same name
//        ///     as references to files in the request document, the file contents are overwritten
//        ///     and Size, Type, UploadedAt, UploadedBy, and Id are updated.
//        /// </summary>
//        /// <param name="equipmentRequest"></param>
//        /// <param name="files"></param>
//        /// <param name="email"></param>
//        /// <returns></returns>
//        public async Task UploadFilesAsync(EquipmentRequest equipmentRequest, IFormFileCollection files, string email)
//        {
//            if (files == null) throw new ArgumentNullException(nameof(files));
//            if (files.Count <= 0) return;

//            // Get a fresh copy of the request
//            var blobFiles = files.Select(file => new EquipmentRequestBlobFile
//            {
//                Id = Guid.NewGuid().ToString(),
//                Name = file.FileName,
//                EquipmentRequestId = equipmentRequest.Id,
//                Size = file.Length,
//                Type = file.ContentType,
//                UploadedAt = DateTime.UtcNow,
//                UploadedBy = email
//            }).ToList();
//            var existingFiles = equipmentRequest?.Files;
//            var existingFilesToOverwrite = existingFiles?.Where(existingFile =>
//                blobFiles.Select(blobFile => blobFile.Name).Contains(existingFile.Name)).ToList();

//            // Upload files contents
//            foreach (var file in files)
//            {
//                if (equipmentRequest == null) continue;
//                var fileMetaData = equipmentRequest.Files.LastOrDefault(f => f.Name == file.FileName && f.Id == null);
//                // Actually upload the file to Azure Blob Storage
//                await _storage.UploadAttachmentAsync(equipmentRequest.Id, file);

//                equipmentRequest.Files ??= new List<EquipmentRequestBlobFile>();
//                equipmentRequest.Files.Add(new EquipmentRequestBlobFile
//                {
//                    Id = Guid.NewGuid().ToString(),
//                    Name = file.FileName,
//                    HasDataSheet = fileMetaData.HasDataSheet,
//                    HasOther = fileMetaData.HasOther,
//                    HasPJA = fileMetaData.HasPJA,
//                    HasShippingInfo = fileMetaData.HasShippingInfo,
//                    HasPhoto = fileMetaData.HasPhoto,
//                    EquipmentRequestId = equipmentRequest.Id,
//                    Size = file.Length,
//                    Type = file.ContentType,
//                    UploadedAt = DateTime.UtcNow,
//                    UploadedBy = email
//                });
//            }

//            if (existingFilesToOverwrite != null)
//                foreach (var file in existingFilesToOverwrite)
//                    equipmentRequest.Files.Remove(file);

//            await _container.ReplaceItemAsync(equipmentRequest, equipmentRequest.Id,
//                new PartitionKey(equipmentRequest.JobNumber));
//        }

//        /// <summary>
//        ///     Delete a file associated with an equipment request
//        /// </summary>
//        /// <param name="equipmentRequestId"></param>
//        /// <param name="fileId"></param>
//        /// <returns></returns>
//        public async Task DeleteFile(string equipmentRequestId, string fileId)
//        {
//            if (fileId == null) throw new ArgumentNullException(nameof(fileId));

//            var equipmentRequest = await GetItemAsync(equipmentRequestId);

//            // Remove any files that don't have a guid id
//            // TODO: Evaluate if this block of code is needed after some time.  Shouldn't be a 
//            // big performance hit because it'll only talk to cosmos if we have invalid files
//            // Also, we'll want to see if any other criteria make files invalid if this is still
//            // being used in the future.  This is needed right now because there was a small window
//            // of time where uploading files to EDR didn't set the id.
//            var validFiles = equipmentRequest.Files.Where(f => f.Id != null && f.Id != "null").ToList();
//            if (validFiles.Count != equipmentRequest.Files.Count)
//            {
//                equipmentRequest.Files = new List<EquipmentRequestBlobFile>(validFiles);
//                await _container.ReplaceItemAsync(equipmentRequest, equipmentRequestId,
//                    new PartitionKey(equipmentRequest.JobNumber));
//            }

//            var file = equipmentRequest.Files.SingleOrDefault(file => file.Id == fileId);

//            if (file != null)
//            {
//                // Remove from Blob Storage
//                try
//                {
//                    await _storage.DeleteBlobAsync(file.EquipmentRequestId, file.Name);
//                }
//                catch (RequestFailedException rfe)
//                {
//                    if (rfe.Status != 404) throw;
//                }

//                // Remove file from equipment request
//                var removed = equipmentRequest.Files.Remove(file);
//                if (removed)
//                    await _container.ReplaceItemAsync(equipmentRequest, equipmentRequestId,
//                        new PartitionKey(equipmentRequest.JobNumber));
//            }
//        }

//        /// <summary>
//        ///     Download a blob file associated with an equipment request
//        /// </summary>
//        /// <param name="routeId"></param>
//        /// <param name="blobName"></param>
//        /// <returns></returns>
//        public async Task<BlobDownloadInfo> DownloadBlobAsync(string routeId, string blobName) =>
//            await _storage.DownloadBlobAsync(routeId, blobName);

//        /// <summary>
//        ///     Delete multiple equipment demand requests
//        /// </summary>
//        /// <param name="ids"></param>
//        /// <returns></returns>
//        public async Task DeleteItemsAsync(string[] ids)
//        {
//            if (ids == null) throw new ArgumentNullException(nameof(ids));

//            foreach (var id in ids)
//            {
//                var request = await GetItemAsync(id);
//                await _container.DeleteItemAsync<EquipmentRequest>(id, new PartitionKey(request.JobNumber));
//            }
//        }
//    }
//}