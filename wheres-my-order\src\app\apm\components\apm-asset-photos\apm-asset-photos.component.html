<div *ngFor="let photoGroup of photoGroups">
    <h4>{{photoGroup.title}}</h4>
    <dx-tile-view *ngIf="photoGroup.stream | async as photos"
                  [baseItemHeight]="120"
                  [baseItemWidth]="185"
                  [itemMargin]="10"
                  [direction]="'vertical'"
                  [height]="(photos?.length > 0) ? 'auto' : 40">
        <dxi-item *ngFor="let photo of photos">
            <div class="image"
                 [style.background-image]="'url(' + photo.dataUrl + ')' | safe : 'style'">
            </div>
            <p #description
               style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
               (mouseenter)="showTooltip(description, tooltip)"
               (mouseleave)="hideTooltip(description, tooltip)">
                {{photo.description}}
            </p>
            <dx-tooltip #tooltip>
                <div *dxTemplate="let data = data of 'content'"
                     style="white-space: normal; max-width:300px; word-wrap: break-word;">
                    {{photo.description}}
                </div>
            </dx-tooltip>
        </dxi-item>
    </dx-tile-view>
</div>
