import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing';
import { MsalService } from '@azure/msal-angular';
import {
    DxButtonComponent,
    DxButtonModule
} from 'devextreme-angular/ui/button';
import { DxDataGridModule } from 'devextreme-angular/ui/data-grid';
import { DxListComponent, DxListModule } from 'devextreme-angular/ui/list';
import { DxSelectBoxModule } from 'devextreme-angular/ui/select-box';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';

import { UserProfile } from '../../../profile/models';
import { BreadcrumbsComponent } from '../../../shared/components';
import { AuthService, UsersService } from '../../../shared/services';
import { RolesComponent } from './roles.component';

describe('RolesComponent', () => {
    let component: RolesComponent;
    let fixture: ComponentFixture<RolesComponent>;

    const authServiceStub = {
        isLoggedIn: true,
        user: {
            userName: '<EMAIL>',
            idToken: { emails: ['<EMAIL>'] },
        },
    };

    beforeEach(
        waitForAsync(() => {
            TestBed.configureTestingModule({
                imports: [
                    HttpClientTestingModule,
                    ReactiveFormsModule,
                    RouterTestingModule,
                    ToastrModule.forRoot(),
                    DxButtonModule,
                    DxDataGridModule,
                    DxSelectBoxModule,
                    DxListModule,
                ],
                providers: [
                    {
                        provide: UsersService,
                        useValue: {
                            currentProfile$: of(new UserProfile({ roles: [] })),
                            getAll: () => {},
                        },
                    },
                    { provide: MsalService, useValue: {} },
                    {
                        provide: AuthService,
                        useValue: authServiceStub,
                    },
                ],
                declarations: [RolesComponent, BreadcrumbsComponent],
            }).compileComponents();
        })
    );

    beforeEach(() => {
        fixture = TestBed.createComponent(RolesComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    describe('Title', () => {
        it('should return Edit Role', () => {
            component.mode = 'editRole';
            const title = component.title;

            expect(title).toEqual('Edit Role');
        });

        it('should return New Role', () => {
            component.mode = 'newRole';
            const title = component.title;

            expect(title).toEqual('New Role');
        });

        it('should return Role Membership', () => {
            component.mode = 'none';
            const title = component.title;

            expect(title).toEqual('Role Membership');
        });
    });

    it('NonAppAdminWithWMOAdminSelected', () => {
        component.mode = 'roleSelected';
        component.roleOriginal = { id: 'wmo:admin' };

        fixture.detectChanges();
        const editButton = fixture.debugElement.query(
            By.css('dx-button#edit-role-button')
        ).componentInstance as DxButtonComponent;
        const addButton = fixture.debugElement.query(
            By.css('dx-button#add-role-button')
        ).componentInstance as DxButtonComponent;
        const deleteButton = fixture.debugElement.query(
            By.css('dx-button#delete-role-button')
        ).componentInstance as DxButtonComponent;
        const addUserButton = fixture.debugElement.query(
            By.css('dx-button#add-user-button')
        ).componentInstance as DxButtonComponent;
        const userList = fixture.debugElement.query(By.css('dx-list#userList'))
            .componentInstance as DxListComponent;

        expect(addButton.disabled).toBe(true);
        expect(editButton.disabled).toBe(true);
        expect(deleteButton.disabled).toBe(true);
        expect(addUserButton.disabled).toBe(false);
        expect(userList.allowItemDeleting).toBe(true);
    });

    it('NonAppAdminWithAppAdminSelected', () => {
        component.mode = 'roleSelected';
        component.roleOriginal = { id: 'app:admin' };

        fixture.detectChanges();
        const editButton = fixture.debugElement.query(
            By.css('dx-button#edit-role-button')
        ).componentInstance as DxButtonComponent;
        const addButton = fixture.debugElement.query(
            By.css('dx-button#add-role-button')
        ).componentInstance as DxButtonComponent;
        const deleteButton = fixture.debugElement.query(
            By.css('dx-button#delete-role-button')
        ).componentInstance as DxButtonComponent;
        const addUserButton = fixture.debugElement.query(
            By.css('dx-button#add-user-button')
        ).componentInstance as DxButtonComponent;
        const userList = fixture.debugElement.query(By.css('dx-list#userList'))
            .componentInstance as DxListComponent;

        expect(addButton.disabled).toBe(true);
        expect(editButton.disabled).toBe(true);
        expect(deleteButton.disabled).toBe(true);
        expect(addUserButton.disabled).toBe(true);
        expect(userList.allowItemDeleting).toBe(false);
    });
});

describe('RolesComponent as Admin', () => {
    let component: RolesComponent;
    let fixture: ComponentFixture<RolesComponent>;

    const authServiceStub = {
        isLoggedIn: true,
        user: {
            userName: '<EMAIL>',
            idToken: { emails: ['<EMAIL>'] },
        },
    };

    beforeEach(
        waitForAsync(() => {
            TestBed.configureTestingModule({
                imports: [
                    HttpClientTestingModule,
                    ReactiveFormsModule,
                    RouterTestingModule,
                    ToastrModule.forRoot(),
                    DxButtonModule,
                    DxDataGridModule,
                    DxSelectBoxModule,
                    DxListModule,
                ],
                providers: [
                    {
                        provide: UsersService,
                        useValue: {
                            currentProfile$: of(
                                new UserProfile({ roles: ['app:admin'] })
                            ),
                            getAll: () => {},
                        },
                    },
                    { provide: MsalService, useValue: {} },
                    {
                        provide: AuthService,
                        useValue: authServiceStub,
                    },
                ],
                declarations: [RolesComponent, BreadcrumbsComponent],
            }).compileComponents();
        })
    );

    beforeEach(() => {
        fixture = TestBed.createComponent(RolesComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('AppAdminWithAppAdminSelected', () => {
        component.mode = 'roleSelected';
        component.roleOriginal = { id: 'app:admin' };

        fixture.detectChanges();
        const editButton = fixture.debugElement.query(
            By.css('dx-button#edit-role-button')
        ).componentInstance as DxButtonComponent;
        const addButton = fixture.debugElement.query(
            By.css('dx-button#add-role-button')
        ).componentInstance as DxButtonComponent;
        const deleteButton = fixture.debugElement.query(
            By.css('dx-button#delete-role-button')
        ).componentInstance as DxButtonComponent;
        const addUserButton = fixture.debugElement.query(
            By.css('dx-button#add-user-button')
        ).componentInstance as DxButtonComponent;
        const userList = fixture.debugElement.query(By.css('dx-list#userList'))
            .componentInstance as DxListComponent;

        expect(addButton.disabled).toBe(false);
        expect(editButton.disabled).toBe(true);
        expect(deleteButton.disabled).toBe(true);
        expect(addUserButton.disabled).toBe(false);
        expect(userList.allowItemDeleting).toBe(true);
    });

    it('AppAdminWithWMOAdminSelected', () => {
        component.mode = 'roleSelected';
        component.roleOriginal = { id: 'wmo:admin' };

        fixture.detectChanges();
        const editButton = fixture.debugElement.query(
            By.css('dx-button#edit-role-button')
        ).componentInstance as DxButtonComponent;
        const addButton = fixture.debugElement.query(
            By.css('dx-button#add-role-button')
        ).componentInstance as DxButtonComponent;
        const deleteButton = fixture.debugElement.query(
            By.css('dx-button#delete-role-button')
        ).componentInstance as DxButtonComponent;
        const addUserButton = fixture.debugElement.query(
            By.css('dx-button#add-user-button')
        ).componentInstance as DxButtonComponent;
        const userList = fixture.debugElement.query(By.css('dx-list#userList'))
            .componentInstance as DxListComponent;

        expect(addButton.disabled).toBe(false);
        expect(editButton.disabled).toBe(false);
        expect(deleteButton.disabled).toBe(false);
        expect(addUserButton.disabled).toBe(false);
        expect(userList.allowItemDeleting).toBe(true);
    });
});
