import {
    Component,
    EventEmitter,
    Input,
    Output,
    ViewChild
} from '@angular/core';
import { DxTabPanelComponent } from 'devextreme-angular';
import {
    NewTask,
    ProjectUpdate,
    ProjectVm,
    TaskUpdate,
    WorkOrder,
    WorkOrderDetailTabInfo
} from '../../models';
import { ApmService } from '../../services';
import { DetailsTabComponent } from '../details-tab/details-tab.component';
import { WoDetailsTabComponent } from '../wo-details-tab/wo-details-tab.component';

@Component({
    selector: 'app-assets-details',
    templateUrl: './assets-details.component.html',
    styleUrls: ['./assets-details.component.scss']
})
export class AssetsDetailsComponent {
    @Input() workOrder: WorkOrder;
    @Input() project: ProjectVm;
    @Input() location: Location;
    @Input() loading: boolean;
    @Input() users: any[];

    allowEditing$ = this._apm.allowEditing$;

    tabs = [
        { title: 'Project Detail', template: 'project-details' },
        { title: 'Work Order Detail', template: 'details' },
        { title: 'Tasks', template: 'tasks' }
    ];
    @Output() woDetailsSave = new EventEmitter<
        Partial<WorkOrderDetailTabInfo>
    >();
    @Output() addTask = new EventEmitter<NewTask>();
    @Output() updateTask = new EventEmitter<TaskUpdate>();
    @Output() projectSave = new EventEmitter();
    @ViewChild(WoDetailsTabComponent) woDetailsTab: WoDetailsTabComponent;
    @ViewChild(DxTabPanelComponent) tabPanel: DxTabPanelComponent;
    @ViewChild(DetailsTabComponent) projectDetailsTab: DetailsTabComponent;

    constructor(private readonly _apm: ApmService) {}

    onWODetailsSave(update: Partial<WorkOrderDetailTabInfo>) {
        this.woDetailsSave.next(update);
    }

    onProjectUpdated(update: Partial<ProjectUpdate>) {
        this.projectSave.next(update);
    }

    newTask(e) {
        this.addTask.next(e);
    }

    taskUpdate(e) {
        this.updateTask.next(e);
    }
}
