<div class="dx-card content-block doughnut"
     style="height: 600px;">
    <h2
        style="text-align: center; margin:0,10px,0,0; font-weight:500; font-size:20px">
        Inspections
        Due</h2>
    <div class="flex-container2">
        <div style="display: flex; padding-left: 10px; align-items: center;">
            <div style="padding-right: 10px;">
                <label
                       class="dx-field-item-label dx-field-item-label-location-top">
                    <span class="dx-field-item-label-content">
                        <span class="dx-field-item-label-text">
                            Selected Asset Categories
                        </span>
                    </span>
                </label>
                <dx-tag-box id="selectBox"
                            #doughnutAssetOptions
                            [(value)]="selectedDoughnutCategories"
                            [items]="dropdownOptions"
                            [showSelectionControls]="true"
                            [maxDisplayedTags]="3"
                            [multiline]="false"
                            placeholder="Select options"
                            [searchEnabled]="true"
                            [width]="250"
                            (onValueChanged)="onDoughnutDropdownChange($event)">
                    <dxo-drop-down-options
                                           container="#selectBox"></dxo-drop-down-options>
                </dx-tag-box>
            </div>
            <div id="IncludeOutOfServiceCheckBox">

                <dx-check-box [(value)]="isincludeoutofservicechecked"
                              text="Include Out Of Service"
                              (onValueChanged)="onincludeoutofserviceValueChanged($event)"></dx-check-box>
            </div>
        </div>
        <!-- Doughnut Charts are supposed to NOT include complete inspections, according to Ahnna (JDS 4/8/2021) -->
        <div class="flex-container"
             style="height: auto;">
            <div class="doughnut-container"
                 #doughnutContainer>
                <dx-pie-chart #overdueInspectionsPie
                              class="doughnut-chart"
                              type="doughnut"
                              centerTemplate="centerTemplate"
                              palette="Soft Pastel"
                              sizeGroup="doughnutCharts"
                              [size]={height:400}
                              [innerRadius]="0.65"
                              [resolveLabelOverlapping]="resolveOverlappingTypes[0]"
                              [dataSource]="overdueInspections"
                              (onPointClick)="overduePointClicked($event)">

                    <dxo-tooltip [enabled]="true">
                    </dxo-tooltip>
                    <dxi-series argumentField="scheduletype"
                                valueField="count">
                        <dxo-label [visible]="true"
                                   [customizeText]="customizeLabel"
                                   [font]="{size:10}">
                            <dxo-connector [visible]="true"></dxo-connector>

                        </dxo-label>
                    </dxi-series>
                    <dxo-legend [visible]="false"></dxo-legend>
                    <svg *dxTemplate="let pieChart of 'centerTemplate'">
                        <text (click)="overdueCenterTextClicked($event)"
                              text-anchor="middle"
                              style="font-size: 18px"
                              x="100"
                              y="120"
                              fill="#494949">
                            <tspan style="font-weight:900; font-size: 36;"
                                   x="100">
                                {{overdueInspections | inspectionCount}}

                            </tspan>
                            <tspan style="font-size:15;"
                                   x="100"
                                   dy="20px">Overdue</tspan>
                            <tspan style="font-size: 15;"
                                   x="100"
                                   dy="20px">Inspections</tspan>
                        </text>
                    </svg>
                </dx-pie-chart>
            </div>
            <div class="doughnut-container">
                <dx-pie-chart #dueThisYearInspectionsPie
                              id="pieChart"
                              class="doughnut-chart"
                              type="doughnut"
                              centerTemplate="centerTemplate"
                              palette="Soft Pastel"
                              sizeGroup="doughnutCharts"
                              [size]={height:400}
                              [innerRadius]="0.65"
                              [dataSource]="dueThisYearInspections"
                              [resolveLabelOverlapping]="resolveOverlappingTypes[0]"
                              (onPointClick)="dueThisYearPointClicked($event)">

                    <dxo-tooltip [enabled]="true">
                    </dxo-tooltip>
                    <dxi-series argumentField="scheduletype"
                                valueField="count">
                        <dxo-label [visible]="true"
                                   [customizeText]="customizeLabel"
                                   [font]="{size:10}">
                            <dxo-connector [visible]="true"></dxo-connector>
                        </dxo-label>
                    </dxi-series>
                    <dxo-legend [visible]="false"></dxo-legend>

                    <svg *dxTemplate="let pieChart of 'centerTemplate'">
                        <text (click)="dueThisYearCenterTextClicked($event)"
                              text-anchor="middle"
                              style="font-size: 18px"
                              x="100"
                              y="120"
                              fill="#494949">
                            <tspan style="font-weight:900; font-size: 36;"
                                   x="100">
                                {{dueThisYearInspections | inspectionCount}}
                            </tspan>
                            <tspan style="font-size:15;"
                                   x="100"
                                   dy="20px">Inspections</tspan>
                            <tspan style="font-size:15;"
                                   x="100"
                                   dy="20px">Due This Year</tspan>
                        </text>
                    </svg>
                </dx-pie-chart>
            </div>
            <div class="doughnut-container">
                <dx-pie-chart #dueNextYearInspectionsPie
                              class="doughnut-chart"
                              type="doughnut"
                              centerTemplate="centerTemplate"
                              palette="Soft Pastel"
                              sizeGroup="doughnutCharts"
                              [size]={height:400}
                              [innerRadius]="0.65"
                              [dataSource]="dueNextYearInspections"
                              [resolveLabelOverlapping]="resolveOverlappingTypes[0]"
                              (onPointClick)="dueNextYearPointClicked($event)">
                    <dxo-tooltip [enabled]="true">
                    </dxo-tooltip>
                    <dxi-series argumentField="scheduletype"
                                valueField="count">
                        <dxo-label [visible]="true"
                                   [customizeText]="customizeLabel"
                                   [font]="{size:10}">
                            <dxo-connector [visible]="true"></dxo-connector>
                        </dxo-label>
                    </dxi-series>
                    <dxo-legend [visible]="false"></dxo-legend>

                    <svg *dxTemplate="let pieChart of 'centerTemplate'">
                        <text (click)="dueNextYearCenterTextClicked($event)"
                              text-anchor="middle"
                              style="font-size: 18px"
                              x="100"
                              y="120"
                              fill="#494949">
                            <tspan style="font-weight:900; font-size: 36;"
                                   x="100">
                                {{dueNextYearInspections | inspectionCount}}
                            </tspan>
                            <tspan style="font-size:15;"
                                   x="100"
                                   dy="20px">Inspections</tspan>
                            <tspan style="font-size:15;"
                                   x="100"
                                   dy="20px">Due Next Year</tspan>
                        </text>
                    </svg>
                </dx-pie-chart>
            </div>
        </div>
        <!-- </dx-scroll-view> -->
    </div>
</div>

<div class="dx-card content-block responsive-paddings"
     style="position: relative">
    <div>
        <div class="graphs"
             style="flex-direction: row; display: flex;">
            <div style="padding-right: 200px;">
                <label
                       class="dx-field-item-label dx-field-item-label-location-top">
                    <span class="dx-field-item-label-content">
                        <span class="dx-field-item-label-text">
                            Selected Asset Categories
                        </span>
                    </span>
                </label> <dx-tag-box id="selectBox2"
                            #currentYearAssetOptions
                            [(value)]="selectedBarGraphCategories"
                            [items]="dropdownOptions"
                            [showSelectionControls]="true"
                            [maxDisplayedTags]="3"
                            [multiline]="false"
                            placeholder="Select options"
                            [searchEnabled]="true"
                            [width]="250"
                            (onValueChanged)="onThisYearChartDropdownChange($event)">
                    <dxo-drop-down-options
                                           container="#selectBox2"></dxo-drop-down-options>
                </dx-tag-box>
            </div>
            <div style="padding-top: 25px;"> <dx-date-box #FromDate
                             id="selectBox4"
                             displayFormat="MMM dd yyyy"
                             type="date"
                             [acceptCustomValue]="false"
                             [visible]="true"
                             label="From"
                             labelMode="outside"
                             [(value)]="fromDate">
                    <dxo-drop-down-options
                                           container="#selectBox4"></dxo-drop-down-options></dx-date-box>
            </div>
            <!--             
            (onValueChanged)="onFromDateChange($event)" -->
            <div style="padding-left: 30px;padding-top: 25px;"> <dx-date-box
                             #ToDate
                             id="selectBox5"
                             displayFormat="MMM dd yyyy"
                             type="date"
                             [visible]="true"
                             [acceptCustomValue]="false"
                             label="To"
                             labelMode="outside"
                             [(value)]="toDate">
                    <dxo-drop-down-options
                                           container="#selectBox5"></dxo-drop-down-options></dx-date-box>
            </div>
            <div style="padding-left: 30px;padding-top: 27px;">
                <dx-button text="Go"
                           (onClick)="applyFilter()">
                </dx-button>
            </div>
            <div style="padding-left: 30px;padding-top: 27px;">
                <dx-button text="Clear"
                           (onClick)="clearFilter()">
                </dx-button>
            </div>
        </div>
    </div>
    <div class="barchart-container">
        <dx-chart #selectedInspectionsThisYearChart
                  [dataSource]="filteredDataThisYear"
                  title="Monthly Task Tracker"
                  (onPointClick)="onThisYearChartPointClicked($event)">
            <dxi-series valueField="complete"
                        name="Complete"
                        color="#97c95c"></dxi-series>
            <dxi-series valueField="overdue"
                        name="Overdue"
                        color="#f5564a"></dxi-series>
            <dxi-series valueField="upcoming"
                        name="Upcoming"
                        color="#1db2f5"></dxi-series>
            <dxo-common-series-settings argumentField="monthyear"
                                        type="stackedBar">
            </dxo-common-series-settings>
            <dxo-legend verticalAlignment="bottom"
                        horizontalAlignment="center"
                        itemTextPosition="top">
            </dxo-legend>
            <dxo-tooltip [enabled]="true"
                         location="edge">
            </dxo-tooltip>
        </dx-chart>
    </div>
</div>

<div class="flex-container dx-card content-block">


    <div class="card-content">
        <!-- Checkboxes Section -->
        <div class="checkbox-section">
            <h3 style="margin-bottom: 5px;">Management System Categories</h3>
            <div class="checkbox-column">
                <!-- Links for Select All and Unselect All -->
                <a href="javascript:void(0)"
                   (click)="selectAllManagementCategoryButtonClick()">
                    <strong>Select
                        All</strong></a>
                <a href="javascript:void(0)"
                   (click)="unSelectAllManagementCategoryButtonClick()"
                   style="margin-left: 10px;"><strong>Unselect All </strong>
                </a>
            </div>

            <!-- <div class="checkbox-column"> -->
            <div class="fields">

                <dx-check-box #noSystemCategory
                              [(value)]="noSystemCategoryChecked"
                              text="No Management System Categories (None)"
                              (onValueChanged)="onNoSystemCategoryChanged($event)"></dx-check-box>
            </div>
            <!-- <div class="fields">
                    <dx-check-box #selectAllCategories
                                  [(value)]="selectAllCategoriesChecked"
                                  text="Select All Management System Categories"
                                  (onValueChanged)="onSelectAllCategoriesChanged($event)"></dx-check-box>
                </div> -->
            <!-- </div> -->
            <div *ngFor="let category of systemManagementCategories">
                <div class="fields">

                    <dx-check-box #checkbox
                                  [(value)]="category.isChecked"
                                  [text]="category.systemcategory"
                                  (onValueChanged)="onSystemManagementCategoryChanged($event)"></dx-check-box>

                </div>
            </div>
        </div>

        <!-- Stacked Bar Chart Section -->
        <div class="chart-section">
            <h3>{{graphTitle}}</h3>
            <dx-chart #stackedChart
                      [dataSource]="stackedChartData"
                      (onPointClick)="stateInspectionsByYearPointClicked($event)">
                <dxi-series valueField="overdue"
                            name="Overdue"
                            color="#f5564a">
                </dxi-series>
                <dxi-series valueField="upcoming"
                            name="Upcoming"
                            color="#1db2f5">
                </dxi-series>
                <dxo-value-axis [tickInterval]="1"
                                [label]="{ format: 'decimal' }">
                </dxo-value-axis> <dxo-common-series-settings
                                            argumentField="year"
                                            type="stackedBar">
                </dxo-common-series-settings>
                <dxo-legend verticalAlignment="bottom"
                            horizontalAlignment="center"
                            itemTextPosition="top">
                </dxo-legend>
                <dxo-tooltip [enabled]="true"
                             location="edge">
                </dxo-tooltip>
            </dx-chart>

        </div>
    </div>
</div>