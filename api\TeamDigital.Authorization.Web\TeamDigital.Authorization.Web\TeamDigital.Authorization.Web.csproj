<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <PropertyGroup>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Extensions\**" />
    <Content Remove="Extensions\**" />
    <EmbeddedResource Remove="Extensions\**" />
    <None Remove="Extensions\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DevExpress.Document.Processor" Version="21.2.8" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.20.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.5" />
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="6.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="6.0.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="1.25.0" />
  </ItemGroup>

  <ItemGroup>

    <ProjectReference Include="..\..\OrderTracking.API\ClientPortal.Shared\ClientPortal.Shared.csproj" />

  </ItemGroup>


</Project>
