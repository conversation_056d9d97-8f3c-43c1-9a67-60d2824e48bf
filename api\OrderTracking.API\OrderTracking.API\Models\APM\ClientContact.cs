﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace OrderTracking.API.Models.APM
{
    public class ClientContact
    {
        [JsonProperty("contactEmail")] public string ContactEmail { get; set; }
        [<PERSON><PERSON><PERSON>roperty("contactName")] public string ContactName{ get; set; }
        [Json<PERSON>roperty("contactPhoneNumber")] public string ContactPhoneNumber { get; set; }
        [JsonProperty("contactTitle")] public string ContactTitle { get; set; }
        [JsonProperty("databaseId")] public string DatabaseId { get; set; }
    }
}
