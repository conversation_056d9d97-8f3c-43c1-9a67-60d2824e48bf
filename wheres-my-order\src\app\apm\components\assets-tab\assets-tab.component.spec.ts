import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DxButtonModule } from 'devextreme-angular/ui/button';
import { DxDataGridModule } from 'devextreme-angular/ui/data-grid';
import { DxPopupModule } from 'devextreme-angular/ui/popup';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import { ApmService } from '../../services';
import { AssetsTabComponent } from './assets-tab.component';

describe('AssetsTabComponent', () => {
    let component: AssetsTabComponent;
    let fixture: ComponentFixture<AssetsTabComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                DxDataGridModule,
                DxPopupModule,
                DxButtonModule,
                HttpClientTestingModule,
                ToastrModule.forRoot()
            ],
            declarations: [AssetsTabComponent],
            providers: [
                { provide: ApmService, useValue: { selectedBU$: of('123') } }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(AssetsTabComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
