using APMWebDataInterface.ExampleDataModel;
using APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Services.APM
{
    /// <summary>
    ///     Service class for working with <see cref="APMTask"/> instances
    /// </summary>
    public class TasksService : ITasksService
    {
        private readonly IProjectService _projects;

        public TasksService(IProjectService projects)
        {
            _projects = projects;
        }

        public TaskVM BuildTaskVM(APMTask t, string email)
        {
            var walkDown = t.workOrder.asset.walkDown;

            // Determine the equipment ID based on the walk down type (510, 570, 653)
            var equipmentId = walkDown switch
            {
                Section510_Asset_Walkdown_Details_F fiveTenWalkDown => fiveTenWalkDown
                    .sectionIdentification
                    .attributeNumber_or_ID
                    .CurrentValue,
                Section570_Asset_Walkdown_Details_F fiveSeventyWalkDown => fiveSeventyWalkDown
                    .sectionIdentification
                    .attributeNumber_or_Circuit_ID
                    .CurrentValue,
                Section653_Asset_Walkdown_Details_F sixFiftyThreeWalkDown => sixFiftyThreeWalkDown
                    .sectionIdentification
                    .attributeNumber_or_ID
                    .CurrentValue,
                _ => null
            };
         
            
            // Determine the equipment description based on the walk down type (510, 570, 653)
            var equipmentDescription = walkDown switch
            {
                Section510_Asset_Walkdown_Details_F fiveTenWalkDown => fiveTenWalkDown
                    .sectionIdentification
                    .attributeEquipment_Description
                    .CurrentValue,
                Section570_Asset_Walkdown_Details_F fiveSeventyWalkDown => fiveSeventyWalkDown
                    .sectionIdentification
                    .attributeEquipment_Description
                    .CurrentValue,
                Section653_Asset_Walkdown_Details_F sixFiftyThreeWalkDown => sixFiftyThreeWalkDown
                    .sectionIdentification
                    .attributeEquipment_Description
                    .CurrentValue,
                _ => null
            };

            // Get the project associated with the task
            var project = _projects.Get(t.projectId, email);

            return new TaskVM
            {
                ID = t.id,
                ProjectId = t.projectId,
                EquipmentId = equipmentId,
                Area = t.asset.area.CurrentValue,
                Unit = t.asset.unit.CurrentValue,
                EquipmentType = t.asset.assetCategory,
                EquipmentDescription = equipmentDescription,
                DueDate = t.dueDate.CurrentValueDateTime,
                TaskType = t.taskType,
                AssignedUsers = t.assignedUsers,
                TaskUpdatedDate = t.LastChangedTime,
                AFE = t.purchaseOrderAFE.CurrentValue,
                Supervisor = t.taskDetails.supervisor.CurrentValue,
                PlannedStart = t.plannedStart.CurrentValueDateTime,
                PlannedEnd = t.plannedEnd.CurrentValueDateTime,
                Status = t.status.CurrentValue,
                APMProjectNumber = project.accountingDetails.apmProjectNumber.CurrentValue,
                APMWorkOrderNumber = t.workOrder.apmWorkOrderNumber.CurrentValue,
                ProjectName = project.name.CurrentValue,
                TEAMProjectNumber = project.accountingDetails.teamProjectNumber.CurrentValue,
                APMTaskNumber = t.taskAPMNumber.CurrentValue,
                Client = "Chevron",
                WorkOrderId = t.workOrder.id,
                ClientWorkOrderNumber = t.clientWorkOrderNumber.CurrentValue,
                BusinessUnitId = t.businessUnitId.CurrentValue,
                AssetDatabaseId = t.workOrder.asset.id
            };
        }
    }

    public interface ITasksService
    {
        TaskVM BuildTaskVM(APMTask t, string email);
    }
}