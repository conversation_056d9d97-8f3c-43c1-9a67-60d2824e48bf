import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { SharedModule } from '../../shared';
import { AuthHistoryRoutingModule } from './auth-history-routing.module';
import { AuthHistoryComponent } from './auth-history.component';
import { DiffPipe } from './pipes/diff.pipe';

@NgModule({
    declarations: [AuthHistoryComponent, DiffPipe],
    imports: [CommonModule, AuthHistoryRoutingModule, SharedModule],
})
export class AuthHistoryModule {}
