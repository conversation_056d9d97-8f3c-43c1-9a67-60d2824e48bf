using System;

namespace OrderTracking.API.Models.EDR
{
    /// <summary>
    ///     A single note in the notes of an equipment request
    /// </summary>
    // ReSharper disable once ClassNeverInstantiated.Global (This is used for serialization/deserialization purposes)
    public class RequestNote
    {
        /// <summary>
        ///     Email of the user who created the note
        /// </summary>
        public string UserEmail { get; set; }

        /// <summary>
        ///     Name of the user who created the note
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        ///     When the note was created
        /// </summary>
        // ReSharper disable once UnusedAutoPropertyAccessor.Global (setter needed for serialization/deserialization)
        public DateTime Created { get; set; }

        /// <summary>
        ///     The note contents
        /// </summary>
        public string Note { get; set; }
    }
}