<div class="responsive-paddings content-block">
    <div *ngFor="let section of sections">
        <h6>{{section.title}}</h6>
        <dx-tile-view [items]="section.photos"
                      [baseItemHeight]="120"
                      [baseItemWidth]="185"
                      [itemMargin]="10"
                      [direction]="'horizontal'"
                      [noDataText]="'No photos in this section'"
                      [height]="
                      (section.photos?.length> 0) ? 180 : 40">
            <div *dxTemplate="let photo of 'item'">
                <div class="image"
                     [style.background-image]="'url(' + photo.path + ')' | safe : 'style'"
                     (dblclick)="imageClicked($event, photo, gallery)">
                </div>
                <p
                   style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis">
                    {{photo.description}}
                </p>
            </div>
        </dx-tile-view>
    </div>

</div>

<dx-popup [(visible)]="showPhotoPopup"
          [showTitle]="true"
          [showCloseButton]="true"
          [hideOnOutsideClick]="true"
          title="Photo Details">
    <div class="popup-outer-div">
        <dx-gallery #gallery
                    id="gallery"
                    [dataSource]="allPhotos"
                    height="90%"
                    width="100%"
                    style="align-self: center;"
                    [loop]="true"
                    [showNavButtons]="true"
                    [showIndicator]="false">
            <div *dxTemplate="let galleryItem of 'item'">
                <div class="gallery-item-outer-div">
                    <div
                         style="width: 50%; margin-left: 3rem; margin-right: .5rem">
                        <img class="gallery-image"
                             src="{{galleryItem.path}}" />
                    </div>
                    <div class="popup-right-div">
                        <div style="font-size: large;">Photo Location</div>
                        <div
                             style="font-size: large; color: gray; margin-bottom: .5rem;">
                            {{galleryItem.section}}
                        </div>
                        <div style="font-size: large;">Photo Comment</div>
                        <dx-text-area height="70%"
                                      [readOnly]="true"
                                      [value]="galleryItem.description">
                        </dx-text-area>
                    </div>
                </div>


            </div>
        </dx-gallery>
        <div class="popup-button-div">

            <dx-button style="margin-left:1rem; margin-right:1rem"
                       text="Edit"
                       icon="fa fa-pencil"
                       type="default"></dx-button>
            <dx-button text="Delete"
                       icon="fa fa-trash"
                       type="danger"></dx-button>
        </div>

    </div>
</dx-popup>
