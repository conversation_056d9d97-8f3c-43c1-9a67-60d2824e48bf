import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { environment } from '../../../environments/environment';
import { ExternalCustomer } from '../models';

@Injectable({
    providedIn: 'root',
})
export class CustomerAccountsService {
    constructor(private readonly _httpService: HttpClient) {}

    getCustomerAccounts(
        params: HttpParams
    ): Observable<{ data: ExternalCustomer[] }> {
        return this._httpService.get<{ data: ExternalCustomer[] }>(
            `${environment.api.url}/orders/customerAccounts`,
            { params }
        );
    }
}
