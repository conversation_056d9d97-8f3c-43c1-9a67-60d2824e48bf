import {
    Component,
    Input,
    OnChanges,
    OnInit,
    SimpleChanges
} from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import {
    ToastNotificationService,
    ToastType
} from '../../../shared/services/toast-notification.service';
import { Asset, ChamberData } from '../../models';
import { CredoSoftService } from '../../services/credo-soft.service';

@Component({
    selector: 'app-asset-generalinformation',
    templateUrl: './asset-generalinformation.component.html',
    styleUrls: ['./asset-generalinformation.component.scss']
})
export class AssetGeneralinformationComponent implements OnInit, OnChanges {
    @Input() asset: Asset;
    chamberData : ChamberData[];
    @Input() selectedAssetId: string;
    loadingVisible: boolean = false;
    imageloadingVisible: boolean=true;
    showChamberData: string = '';
    imageUrl: string | null = null; 
    safeLink: SafeHtml;
    constructor(
        private readonly credoService: CredoSoftService,
        private readonly _toasts: ToastNotificationService,
        private sanitizer: DomSanitizer
    ) {}
    
    ngOnChanges(changes: SimpleChanges): void {      
    }
    ngOnInit(): void {
        this.imageloadingVisible=true;
        this.loadingVisible = true;
        this.getChamberDataDetails();
        this.loadImage(this.asset.image);
        
    }
 
    getChamberDataDetails() {
        if (this.selectedAssetId) {
            this.loadingVisible = true;
            this.credoService.getChamberData(this.selectedAssetId).subscribe(
                (data) => {
                    this.chamberData = data;
                    this.loadingVisible = false;
                },
                (err) => {
                    console.error(err, 'error');
                    this.loadingVisible = false;
                }
            );
        } else {
            this.loadingVisible = false;
        }
       
    }
    convertHtmlToText(html: string): string {
        if (html == null) {
            return '';
        }
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return doc.documentElement.textContent ?? ' ';
    }
    
    getDigitalTwinLink(html: string ):SafeHtml{
        if(html==null){
            return'';
        }
        this.safeLink = this.sanitizer.bypassSecurityTrustHtml(html);
        return this.safeLink;
    }

    loadImage(data: any) {
        if (data) {
            let location: string = data;
            const newLocation = location.replace(/\\/g, '/');
            this.credoService
                .downloadAttachment(newLocation.replace(/\/\//g, '/'))
                .subscribe(
                    (file: any) => {
                        this.imageUrl = this.createBase64ImageUrl(file.data, file.filetype);
                    },
                    (err) => {
                        this._toasts.show(
                            ' ',
                            err.error.error,
                            ToastType.warning,
                            3000
                        );
                        console.error(err.error, 'Error while retrieving the file');
                    }
                );
                this.imageloadingVisible=false;  
        }
        else{
            this.imageloadingVisible=false;
        }
       
    }

    createBase64ImageUrl(base64String: string, filetype: string): string {
        return `data:${filetype};base64,${base64String}`; // Create a data URL
    }
     cleanIntegerString(input: string): string {
        if(input!==null){

            return input.replace(/,(?!\s*\d)/g, '').trim();
        }
        return input;
    }
    
    
    removeNullValues(value: string): string {
        if (
            value == 'null°F' ||
            value == undefined ||
            value.includes('null') ||
            value === ' psi'
        ) {
            return '';
        } else return value;
    }
    constructionYearValue(value: any): string {
        if (value === '0'|| value===0) {
            return '';
        } else return value;
    }
}
