﻿using System.Linq;
using System.Threading.Tasks;
using APMWebDataInterface.ExampleDataModel;
using APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F;
using OrderTracking.API.Extensions.APM;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Services.APM
{
    public interface IAssetService
    {
        /// <summary>
        ///     Creates an asset from a <see cref="NewAsset" />
        /// </summary>
        /// <param name="newAsset"></param>
        /// <param name="email"></param>
        /// <param name="businessUnitId"></param>
        Task<Asset> Create(NewAsset newAsset, string email, string businessUnitId);

        /// <summary>
        ///     Updates an asset from a <see cref="AssetAccessUpdate" />
        /// </summary>
        /// <param name="asset"></param>
        /// <param name="assetUpdate"></param>
        /// <param name="email"></param>
        Task Update(Asset asset, AssetAccessUpdate assetUpdate, string email);

        /// <summary>
        ///     Updates an asset from a <see cref="AssetPPEUpdate" />
        /// </summary>
        /// <param name="asset"></param>
        /// <param name="assetUpdate"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task Update(Asset asset, AssetPPEUpdate assetUpdate, string email);

        /// <summary>
        ///     Gets all assets
        /// </summary>
        /// <returns></returns>
        public Task<Asset[]> Get(string email);

        /// <summary>
        ///     Get an asset by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<Asset> Get(string id, string email);

        /// <summary>
        ///     Get assets associated with a project by project id(s)
        /// </summary>
        /// <param name="email"></param>
        /// <param name="projectIds"></param>
        /// <returns></returns>
        Task<Asset[]> GetByProjects(string email, params string[] projectIds);
        
        AssetVM BuildAssetVM(Asset asset);

        /// <summary>
        ///     Updates an asset from a <see cref="AssetVM" />
        /// </summary>
        /// <param name="asset"></param>
        /// <param name="vm"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        Task Update(Asset asset, AssetVM vm, string email);
    }

    public class AssetService : IAssetService
    {
        private readonly APM_WebDataInterface _apm;
        private readonly ILocationService _locations;
        private readonly IProjectService _projects;

        public AssetService(APM_WebDataInterface apm, ILocationService locations, IProjectService projects)
        {
            _apm = apm;
            _locations = locations;
            _projects = projects;
        }

        /// <summary>
        ///     Creates an asset from a <see cref="NewAsset" />
        /// </summary>
        /// <param name="newAsset"></param>
        /// <param name="email"></param>
        /// <param name="businessUnitId"></param>
        public async Task<Asset> Create(NewAsset newAsset, string email, string businessUnitId)
        {
            var asset = new Asset(newAsset.Category, newAsset.LocationId);

            if (string.Equals(newAsset.Category, "Vessel"))
            {
                var walkDown = asset.walkDown as Section510_Asset_Walkdown_Details_F;
                if (newAsset.AssetName != null)
                    walkDown.sectionIdentification.attributeName.SetValue(newAsset.AssetName);

                if (newAsset.AsssetId != null)
                    walkDown.sectionIdentification.attributeNumber_or_ID.SetValue(newAsset.AsssetId);

                walkDown.sectionIdentification.attributeGIS_Location.SetLocation(
                    newAsset?.StartLat,
                    newAsset?.StartLong
                );
            }
            else if (string.Equals(newAsset.Category, "Tank"))
            {
                var walkDown = asset.walkDown as Section653_Asset_Walkdown_Details_F;
                if (newAsset.AssetName != null)
                    walkDown.sectionIdentification.attributeName.SetValue(newAsset.AssetName);

                if (newAsset.AsssetId != null)
                    walkDown.sectionIdentification.attributeNumber_or_ID.SetValue(newAsset.AsssetId);

                walkDown.sectionIdentification.attributeGIS_Location.SetLocation(
                    newAsset?.StartLat,
                    newAsset?.StartLong
                );
            }
            else if (string.Equals(newAsset.Category, "Piping"))
            {
                var walkDown = asset.walkDown as Section570_Asset_Walkdown_Details_F;
                if (newAsset.AssetName != null)
                    walkDown.sectionIdentification.attributeName.SetValue(newAsset.AssetName);

                if (newAsset.AsssetId != null)
                    walkDown.sectionIdentification.attributeNumber_or_Circuit_ID.SetValue(newAsset.AsssetId);

                walkDown.sectionIdentification.attributeStart_GIS_Location.SetLocation(
                    newAsset?.StartLat,
                    newAsset?.StartLong
                );

                walkDown.sectionIdentification.attributeEnd_GIS_Location.SetLocation(
                    newAsset?.EndLat,
                    newAsset?.EndLong
                );
            }

            asset.businessUnitId.SetValue(businessUnitId);

            await asset.SavePendingChanges(email);

            return asset;
        }

        /// <summary>
        ///     Updates an asset from a <see cref="AssetVM" />
        /// </summary>
        /// <param name="asset"></param>
        /// <param name="vm"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task Update(Asset asset, AssetVM vm, string email)
        {
            asset.Update(vm);
            await asset.SavePendingChanges(email);
        }

        /// <summary>
        ///     Updates an asset from a <see cref="AssetAccessUpdate" />
        /// </summary>
        /// <param name="asset"></param>
        /// <param name="assetUpdate"></param>
        /// <param name="email"></param>
        public async Task Update(Asset asset, AssetAccessUpdate assetUpdate, string email)
        {
            asset.Update(assetUpdate);
            await asset.SavePendingChanges(email);
        }

        /// <summary>
        ///     Updates an asset from a <see cref="AssetPPEUpdate" />
        /// </summary>
        /// <param name="asset"></param>
        /// <param name="assetUpdate"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task Update(Asset asset, AssetPPEUpdate assetUpdate, string email)
        {
            asset.Update(assetUpdate);
            await asset.SavePendingChanges(email);
        }

        /// <summary>
        ///     Gets all assets
        /// </summary>
        /// <returns></returns>
        public async Task<Asset[]> Get(string email)
        {
            var projects = _projects.Get(email);
            var projectIds = projects.Select(p => p.id).ToArray();
            var assets = await GetByProjects(email, projectIds);
            return assets;
        }

        /// <summary>
        ///     Get an asset by id
        /// </summary>
        /// <param name="id"></param>
        /// /// <param name="email"></param>
        /// <returns></returns>
        public async Task<Asset> Get(string id, string email)
        {
            var asset = await _apm.GetAsset(id, email);
            return asset;
        }

        /// <summary>
        ///     Get assets associated with a project by project id(s)
        /// </summary>
        /// /// <param name="email"></param>
        /// <param name="projectIds"></param>
        /// <returns></returns>
        public async Task<Asset[]> GetByProjects(string email, params string[] projectIds) =>
            await _apm.GetAssetsForProjects(projectIds, email);
        
        public AssetVM BuildAssetVM(Asset asset)
        {
            var walkDown = asset.walkDown;
            
            var (equipmentId, assetName, assetType, assetDescription, gisLocation) = walkDown switch
            {
                Section510_Asset_Walkdown_Details_F fiveTenWalkDown => (
                    fiveTenWalkDown.sectionIdentification.attributeNumber_or_ID.CurrentValue,
                    fiveTenWalkDown.sectionIdentification.attributeName.CurrentValue,
                    fiveTenWalkDown.sectionIdentification.attributeAsset_Type.CurrentValue,
                    fiveTenWalkDown.sectionIdentification.attributeEquipment_Description.CurrentValue,
                    fiveTenWalkDown.sectionIdentification.attributeGIS_Location.CurrentValue),
                Section570_Asset_Walkdown_Details_F fiveSeventyWalkDown => (
                    fiveSeventyWalkDown.sectionIdentification.attributeNumber_or_Circuit_ID.CurrentValue,
                    fiveSeventyWalkDown.sectionIdentification.attributeName.CurrentValue,
                    fiveSeventyWalkDown.sectionIdentification.attributeAsset_Type.CurrentValue,
                    fiveSeventyWalkDown.sectionIdentification.attributeEquipment_Description.CurrentValue,
                    $"{fiveSeventyWalkDown.sectionIdentification.attributeStart_GIS_Location.CurrentValue} - {fiveSeventyWalkDown.sectionIdentification.attributeEnd_GIS_Location.CurrentValue}"),
                Section653_Asset_Walkdown_Details_F sixFiftyThreeWalkDown => (
                    sixFiftyThreeWalkDown.sectionIdentification.attributeNumber_or_ID.CurrentValue,
                    sixFiftyThreeWalkDown.sectionIdentification.attributeName.CurrentValue,
                    sixFiftyThreeWalkDown.sectionIdentification.attributeAsset_Type.CurrentValue,
                    sixFiftyThreeWalkDown.sectionIdentification.attributeEquipment_Description.CurrentValue,
                    sixFiftyThreeWalkDown.sectionIdentification.attributeGIS_Location.CurrentValue),
                _ => (null, null, null, null, null)
            };

            return new AssetVM
            {
                Id = asset.id,
                AssetCategory = asset.assetCategory,
                EquipmentId = equipmentId,
                AssetName = assetName,
                Area = asset.area.CurrentValue,
                Unit = asset.unit.CurrentValue,
                AssetType = assetType,
                AssetDescription = assetDescription,
                GISLocation = gisLocation,
                WorkOrderIds = asset.workOrdersContainedIn,
                BusinessUnitId = asset.businessUnitId.CurrentValue,
                LocationId = asset.locationId
            };
        }
    }
}