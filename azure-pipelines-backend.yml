# Azure DevOps Pipeline for Backend API
# Replaces Google Cloud Build (cloudbuild.yaml)

trigger:
  branches:
    include:
      - main
      - develop
  paths:
    include:
      - api/OrderTracking.API/OrderTracking.API/*

pool:
  vmImage: 'ubuntu-latest'

variables:
  buildConfiguration: 'Release'
  imageRepository: 'cpa-backend'
  containerRegistry: 'krakenacr.azurecr.io'
  dockerfilePath: 'api/OrderTracking.API/OrderTracking.API/Dockerfile'
  tag: '$(Build.BuildId)'
  azureSubscription: 'Kraken-Azure-Connection'
  resourceGroupName: 'rg-kraken-$(Environment.Name)'
  containerAppName: 'ca-cpa-backend-$(Environment.Name)'

stages:
- stage: Build
  displayName: 'Build and Push Docker Image'
  jobs:
  - job: Build
    displayName: 'Build Docker Image'
    steps:
    - task: Docker@2
      displayName: 'Build and push Docker image'
      inputs:
        command: 'buildAndPush'
        repository: '$(imageRepository)'
        dockerfile: '$(dockerfilePath)'
        containerRegistry: '$(containerRegistry)'
        tags: |
          $(tag)
          latest
        arguments: '--build-arg FEED_ACCESSTOKEN=$(System.AccessToken)'

- stage: DeployDev
  displayName: 'Deploy to Development'
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
  variables:
    Environment.Name: 'dev'
  jobs:
  - deployment: DeployToDev
    displayName: 'Deploy to Development Environment'
    environment: 'kraken-dev'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureCLI@2
            displayName: 'Deploy to Azure Container Apps'
            inputs:
              azureSubscription: '$(azureSubscription)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az containerapp update \
                  --name $(containerAppName) \
                  --resource-group $(resourceGroupName) \
                  --image $(containerRegistry)/$(imageRepository):$(tag) \
                  --set-env-vars \
                    AZURE_CLIENT_ID="$(AZURE_CLIENT_ID)" \
                    KeyVault__VaultName="$(KeyVault__VaultName)" \
                    ConnectionStrings__DefaultConnection="$(ConnectionStrings__DefaultConnection)"

- stage: DeployStaging
  displayName: 'Deploy to Staging'
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  variables:
    Environment.Name: 'stg'
  jobs:
  - deployment: DeployToStaging
    displayName: 'Deploy to Staging Environment'
    environment: 'kraken-staging'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureCLI@2
            displayName: 'Deploy to Azure Container Apps'
            inputs:
              azureSubscription: '$(azureSubscription)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az containerapp update \
                  --name $(containerAppName) \
                  --resource-group $(resourceGroupName) \
                  --image $(containerRegistry)/$(imageRepository):$(tag) \
                  --set-env-vars \
                    AZURE_CLIENT_ID="$(AZURE_CLIENT_ID)" \
                    KeyVault__VaultName="$(KeyVault__VaultName)" \
                    ConnectionStrings__DefaultConnection="$(ConnectionStrings__DefaultConnection)"

- stage: DeployProduction
  displayName: 'Deploy to Production'
  dependsOn: DeployStaging
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  variables:
    Environment.Name: 'prod'
  jobs:
  - deployment: DeployToProduction
    displayName: 'Deploy to Production Environment'
    environment: 'kraken-production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureCLI@2
            displayName: 'Deploy to Azure Container Apps'
            inputs:
              azureSubscription: '$(azureSubscription)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az containerapp update \
                  --name $(containerAppName) \
                  --resource-group $(resourceGroupName) \
                  --image $(containerRegistry)/$(imageRepository):$(tag) \
                  --set-env-vars \
                    AZURE_CLIENT_ID="$(AZURE_CLIENT_ID)" \
                    KeyVault__VaultName="$(KeyVault__VaultName)" \
                    ConnectionStrings__DefaultConnection="$(ConnectionStrings__DefaultConnection)"
