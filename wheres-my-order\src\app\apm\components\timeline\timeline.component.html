<div class="dx-card content-block responsive-paddings"
     style="position: relative">
    <dx-load-indicator *ngIf="loading"
                       style="position: absolute; left: calc(50% - 30px); top: calc(50% - 30px); z-index: 1"
                       height="60"
                       width="60"></dx-load-indicator>

    <div *ngIf="tasks">
        <dx-scheduler #scheduler
                      [height]="1000"
                      [dataSource]="tasks"
                      [currentDate]="currentDate"
                      [editing]="false"
                      currentView="timelineMonth"
                      startDateExpr="dueDate"
                      endDateExpr="dueDate"
                      textExpr="status"
                      [groups]="['equipmentType']"
                      [maxAppointmentsPerCell]="3"
                      appointmentTooltipTemplate="tooltip-template"
                      (onAppointmentFormOpening)="onAppointmentFormOpening($event)">

            <!-- RESOURCES -->
            <dxi-resource fieldExpr="status"
                          [dataSource]="statuses"
                          [useColorAsDefault]="true"
                          label="Status"></dxi-resource>

            <dxi-resource fieldExpr="taskType"
                          [dataSource]="taskTypes"
                          label="Task Type"></dxi-resource>

            <dxi-resource fieldExpr="projectId"
                          [dataSource]="projects"
                          label="Project"
                          displayExpr="name"
                          valueExpr="id"></dxi-resource>

            <dxi-resource fieldExpr="equipmentType"
                          [dataSource]="equipmentTypeResources"
                          label="Equipment Type"></dxi-resource>

            <!-- VIEWS -->
            <dxi-view type="timelineMonth"
                      appointmentTemplate="appointment-template"></dxi-view>
            <dxi-view type="month"
                      appointmentTemplate="appointment-template"></dxi-view>
            <dxi-view type="agenda"></dxi-view>

            <!-- CUSTOM TEMPLATES -->
            <div *dxTemplate="let model of 'appointment-template'">
                <strong class="equipmentId">
                    {{model.targetedAppointmentData.equipmentId}}</strong>
                <div class="area-and-unit">
                    {{model.targetedAppointmentData.area}},
                    {{model.targetedAppointmentData.unit}}</div>
                <div class="taskType">{{model.targetedAppointmentData.taskType}}
                </div>
                <div>
                    <em class="status">{{model.targetedAppointmentData.status}}
                    </em>: <span
                          class="assignedUsers">{{model.targetedAppointmentData.assignedUsers}}</span>
                </div>
            </div>

            <div *dxTemplate="let model of 'tooltip-template'">
                <div class="responsive-paddings">
                    <table class="task-details">
                        <tr>
                            <th>Project Name:</th>
                            <td> {{model.appointmentData.projectName}}</td>
                        </tr>
                        <tr>
                            <th>Asset ID:</th>
                            <td>{{model.appointmentData.equipmentId}}</td>
                        </tr>
                        <tr>
                            <th>Area, Unit</th>
                            <td>{{model.appointmentData.area}},
                                {{model.appointmentData.unit}}</td>
                        </tr>
                        <tr>
                            <th>APM Task Number:</th>
                            <td>{{model.appointmentData.apmTaskNumber}}</td>
                        </tr>
                        <tr>
                            <th>Task Type:</th>
                            <td>{{model.appointmentData.taskType}}</td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>{{model.appointmentData.status}}</td>
                        </tr>
                        <tr>
                            <th>Assigned To:</th>
                            <td>
                                <div
                                     *ngFor="let user of model.appointmentData.assignedUsers">
                                    {{user}}
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>

            </div>

        </dx-scheduler>
    </div>

</div>
