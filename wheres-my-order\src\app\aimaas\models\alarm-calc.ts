export class AlarmCalc {
    alY_OM_VERT_HORIZ: string;
    diM_CONE_APEX_ANGLE: number;
    diM_DESIGNFACTOR: number;
    diM_DIAMINSIDE: number;
    diM_DIAMOUTSIDE_: number;
    diM_DIAMUNITS: string;
    diM_JOINTQUAL: number;
    diM_LENGTHUNIT_: string;
    diM_LENGTH_: number;
    diM_MAWT: number;
    diM_MAWT_STRUCTURAL: number;
    diM_MAWT_STRUCTURAL_UNIT: string;
    diM_MAWT_UNIT: string;
    diM_RADIUSINSIDE: number;
    diM_RADIUSOUTSIDE: number;
    diM_RADIUS_CROWN_INSIDE: number;
    diM_RADIUS_CROWN_OUTSIDE: number;
    diM_RADIUS_UNIT: string;
    diM_TALARM1_CALCCODE: string;
    diM_TALARM1_FACTORX: number;
    diM_TALARM1_VAL: number;
    diM_TALARM2_CALCCODE: string;
    diM_TALARM2_FACTORX: number;
    diM_TALARM2_VAL: number;
    diM_TALARM3_CALCCODE: string;
    diM_TALARM3_FACTORX: number;
    diM_TALARM3_VAL: number;
    diM_TALARM4_CALCCODE: string;
    diM_TALARM4_VAL: number;
    diM_TALARM5_CALCCODE: string;
    diM_TALARM5_FACTORX: number;
    diM_TALARM5_VAL: number;
    diM_TALARM_K: number;
    diM_TALARM_L: number;
    diM_TALARM_M: number;
    diM_TALARM_NOTE01: string;
    diM_TALARM_Y: number;
    diM_TANK_FILL_HEIGHT_M: number;
    diM_TCORRALLOW: number;
    diM_TCORRALLOW_UNIT: string;
    diM_TNOMINAL: number;
    diM_TNOMINAL_UNIT: string;
    diM_TTOLERANCEPERCENT: number;
    eqdesigncodE_: string;
    eqmanufdate: Date;
    matgradE_: string;
    matspeC_: string;
    objcommission: Date;
    objdesC_: string;
    objid: number;
    objservice: string;
    objtypecodE_: string;
    pressdesmaX_: number;
    pressopernorM_: number;
    pressunitS_: string;
    prssrelieF_: number;
    rmaT_DAMAGE_GROUP_: string;
    tM_PV_CONFIG_GEOM_: string;
    tM_PV_SPEC_GRAVITY_: string;
    tempdesmaX_: number;
    tempopernorM_: number;
    tempunitS_: string;
    rsitE_RID: number;

    constructor(options?: Partial<AlarmCalc>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }
    }
}
