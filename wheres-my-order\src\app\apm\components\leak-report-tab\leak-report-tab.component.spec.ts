import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrModule } from 'ngx-toastr';
import { PhotoSasPipe } from '../../pipes';
import { ApmService } from '../../services';
import { LeakReportTabComponent } from './leak-report-tab.component';

describe('LeakReportTabComponent', () => {
    let component: LeakReportTabComponent;
    let fixture: ComponentFixture<LeakReportTabComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [HttpClientTestingModule, ToastrModule.forRoot()],
            providers: [{ provide: ApmService, useValue: {} }, PhotoSasPipe],
            declarations: [LeakReportTabComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(LeakReportTabComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
