﻿namespace AIMaaS.Models
{
    /// <summary>
    ///     Result record of an Asset Management Site from CredoSoft
    /// </summary>
    public class AssetManagementSite
    {
#pragma warning disable CA1707 // Identifiers should not contain underscores
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
        public long RSITE_RID { get; set; }


        public string RSITE_GROUP { get; set; }

        public string RSITE_NAME { get; set; }
#pragma warning disable CA1707 // Identifiers should not contain underscores
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
    }
}