{"html.format.wrapAttributes": "force-aligned", "html.format.wrapLineLength": 80, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.tabSize": 4, "editor.wordWrapColumn": 80, "typescriptHero.imports.organizeOnSave": true, "typescriptHero.imports.organizeSortsByFirstSpecifier": true, "typescriptHero.imports.multiLineWrapThreshold": 80, "typescript.preferences.importModuleSpecifier": "relative", "typescript.preferences.quoteStyle": "single", "prettier.bracketSpacing": true, "prettier.trailingComma": "es5", "prettier.semi": true, "prettier.singleQuote": true, "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}, "javascript.preferences.quoteStyle": "single", "[javascript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "cSpell.words": ["Microtasks"], "typescript.tsdk": "./wheres-my-order/node_modules/typescript/lib", "csharp.format.enable": true}