import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { DxDataGridModule } from 'devextreme-angular/ui/data-grid';
import { of } from 'rxjs';

import { UserProfile } from '../../../profile/models';
import { BreadcrumbsComponent } from '../../../shared/components';
import { UsersService } from '../../../shared/services';
import {
    AssetDetailsComponent,
    AttachmentsComponent,
    CalculationsViewComponent
} from '../../components';
import { SiteLabelPipe } from '../../pipes';
import {
    FixedEquipmentInspectionDrilldownComponent
} from './fixed-equipment-inspection-drilldown.component';

describe('FixedEquipmentInspectionDrilldownComponent', () => {
    let component: FixedEquipmentInspectionDrilldownComponent;
    let fixture: ComponentFixture<FixedEquipmentInspectionDrilldownComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                HttpClientTestingModule,
                RouterTestingModule,
                DxDataGridModule,
            ],
            declarations: [
                FixedEquipmentInspectionDrilldownComponent,
                BreadcrumbsComponent,
                AssetDetailsComponent,
                AttachmentsComponent,
                CalculationsViewComponent,
            ],
            providers: [
                SiteLabelPipe,

                {
                    provide: UsersService,
                    useValue: { currentProfile$: of(new UserProfile()) },
                },
            ],
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(
            FixedEquipmentInspectionDrilldownComponent
        );
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should show asset attachments tab when inspection selected', () => {
        component.inspectionSelectionChanged({
            selectedRowsData: [
                {} /** TODO: Change this to a test instantion for an inspection when we have a model */,
            ],
        });

        expect(component.tabs).toContain({
            title: 'Asset Attachments',
            template: 'asset-attachments',
        });
    });

    it('should show inspection attachments tab when inspection selected', () => {
        component.inspectionSelectionChanged({
            selectedRowsData: [
                {} /** TODO: Change this to a test instantion for an inspection when we have a model */,
            ],
        });

        expect(component.tabs).toContain({
            title: 'Inspection Attachments',
            template: 'inspection-attachments',
        });
    });
});
