import { HttpClient } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { DxDataGridComponent } from 'devextreme-angular/ui/data-grid';
import { alert } from 'devextreme/ui/dialog';
import { saveAs } from 'file-saver';
import { shareReplay } from 'rxjs/operators';

import { environment } from '../../../environments/environment';
import { UserProfile } from '../../profile/models';
import { Role } from '../models';
import { ChangeEvent } from './models/change-event';

function isRole(object: Role | UserProfile): object is Role {
    return (
        (object as Role)?.roleName !== undefined &&
        (object as Role)?.group !== undefined
    );
}
function isUserProfile(object: Role | UserProfile): object is UserProfile {
    return (
        (object as UserProfile)?.customerAccounts !== undefined &&
        (object as UserProfile)?.districtIds !== undefined &&
        (object as UserProfile)?.email !== undefined
    );
}

@Component({
    selector: 'app-auth-history',
    templateUrl: './auth-history.component.html',
    styleUrls: ['./auth-history.component.scss']
})
export class AuthHistoryComponent implements OnInit {
    @ViewChild('historyGrid') historyGrid: DxDataGridComponent;
    dataSource: ChangeEvent<Role | UserProfile>[];
    rangeStart: Date;
    rangeEnd: Date;
    valueStart: Date;
    valueEnd: Date;
    showDiff: boolean;
    currentChange: ChangeEvent<Role | UserProfile>;

    selected;

    constructor(private readonly _http: HttpClient) {}

    ngOnInit(): void {
        const authHistory = this._http
            .get<ChangeEvent<Role | UserProfile>[]>(
                `${environment.api.url}/AuthHistory`
            )
            .pipe(shareReplay());
        authHistory.subscribe((history) => {
            this.dataSource = history.map((change) => {
                return {
                    ...change,
                    type: isRole(change.new ?? change.old)
                        ? 'Role'
                        : isUserProfile(change.new ?? change.old)
                        ? 'UserProfile'
                        : '?'
                };
            });
            const beginningOfToday = new Date();
            beginningOfToday.setHours(0, 0, 0, 0);
            const endOfToday = new Date();
            endOfToday.setHours(23, 59, 59, 999);
            const dates = this.dataSource.map(
                (change) => new Date(change.createdAt)
            );
            const sorted = [...dates, beginningOfToday, endOfToday].sort(
                (a, b) => (a < b ? -1 : a > b ? 1 : 0)
            );
            this.rangeStart = sorted[0];
            this.rangeStart.setHours(0, 0, 0, 0);

            this.rangeEnd = sorted[sorted.length - 1];
            this.rangeEnd.setHours(23, 59, 59, 999);

            this.valueStart = new Date();
            this.valueStart.setHours(0, 0, 0, 0);

            this.valueEnd = new Date();
            this.valueEnd.setHours(23, 59, 59, 999);
        });
    }

    onRangeSelectorValueChanged(event) {
        this.selected = this.dataSource.filter((change) => {
            return (
                +new Date(change.createdAt) >= +event.value[0] &&
                +new Date(change.createdAt) <= +event.value[1]
            );
        });
    }

    onRangeSelectorInitialized(event) {
        const value = event.component.option('value');
        this.selected = this.dataSource.filter(
            (change) =>
                +new Date(change.createdAt) >= +value[0] &&
                +new Date(change.createdAt) <= +value[1]
        );
    }

    diffClicked = (e) => {
        this.currentChange = e.row.data;
        this.showDiff = true;
    };

    onToolbarPreparing(e) {
        e.toolbarOptions.items.unshift({
            location: 'after',
            widget: 'dxButton',
            options: {
                icon: 'exportselected',
                onClick: this.exportAsJSON.bind(this)
            }
        });
    }

    async exportAsJSON(e) {
        let selected = await this.historyGrid.instance.getSelectedRowsData();
        if (selected.length > 0) {
            selected = selected.map((item) => ({
                ...item,
                summary: ChangeEvent.diff(item.old, item.new)
            }));
            saveAs(
                new Blob([JSON.stringify(selected)], {
                    type: 'application/json'
                }),
                'auth-history.json'
            );
        } else {
            alert(
                'No rows selected.  Select the changes you would like to export',
                'No changes selected'
            );
        }

        e.cancel = true;
    }

    calculateId = (rowData) => {
        try {
            return rowData.old
                ? rowData.old?.id
                    ? rowData.old?.id
                    : rowData.old?.email
                : rowData.new?.id
                ? rowData.new?.id
                : rowData.new?.email;
        } catch (e) {
            console.error(e, rowData);
        }
    };

    calculateUserId = (rowData) => {
        try {
            return !!rowData.user?.email
                ? rowData.user?.email
                : rowData.user?.id;
        } catch (e) {
            console.error(e, rowData);
        }
    };
}
