import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { DxSelectBoxModule } from 'devextreme-angular';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import { ApmService } from '../../../apm/services';
import { UserProfile } from '../../../profile/models';
import { UsersService } from '../../../shared/services';
import { BusinessUnitSelectorComponent } from './business-unit-selector.component';

describe('BusinessUnitSelectorComponent', () => {
    let component: BusinessUnitSelectorComponent;
    let fixture: ComponentFixture<BusinessUnitSelectorComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                HttpClientTestingModule,
                ToastrModule.forRoot(),
                RouterTestingModule,
                DxSelectBoxModule
            ],
            declarations: [BusinessUnitSelectorComponent],
            providers: [
                {
                    provide: ApmService,
                    useValue: { getMyBusinessUnits: () => {} }
                },
                {
                    provide: UsersService,
                    useValue: {
                        currentProfile$: of(
                            new UserProfile({ selectedBusinessUnit: '123' })
                        )
                    }
                }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(BusinessUnitSelectorComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
