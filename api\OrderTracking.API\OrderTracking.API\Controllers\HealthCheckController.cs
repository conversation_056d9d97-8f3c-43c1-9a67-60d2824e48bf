using Microsoft.AspNetCore.Mvc;
using System.Threading;

namespace OrderTracking.API.Controllers
{
    /// <summary>
    ///     Controller to facilitate anonymous requests to the root route.
    ///     Currently, application is deployed to Azure App Service, and when
    ///     the AlwaysOn setting for the App Service is on, every 5 minutes
    ///     a request is made to the root of the application to check the
    ///     health of the application and to keep the application "alive."
    /// </summary>
    [ApiController]
    public class HealthCheckController
    {
        /// <summary>
        ///     Root route to facilitate health check-like requests
        /// </summary>
        /// <returns></returns>
        [Route("/")]
        [HttpGet]
        public string Get() 
        {
            //This 970ms is used to increase the response time, we are using to make sure that our 
            //auto-healing rule of 1s works, the rule states that if this request takes more than
            //1s to respond, the app is restarted, so since this endpoint returns in less than 1ms,
            //it is ok to add this 970ms, our rule will only be broken if it returns in more than 30ms
            Thread.Sleep(970);
            return "I feel healthy";
        }
    }
}