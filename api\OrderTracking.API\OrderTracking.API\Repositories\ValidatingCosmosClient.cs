﻿using System;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    ///     Inherits from CosmosClient and provides a new helper method to retrieve
    ///     a Container, but verifies that it exists and provides the Partition Key path
    /// </summary>
    public class ValidatingCosmosClient : CosmosClient
    {
        #region Constructors

        /// <summary>
        ///     Constructs a ValidatingCosmosClient using endpoint and auth key
        /// </summary>
        /// <param name="accountEndpoint"></param>
        /// <param name="authKey"></param>
        /// <param name="options"></param>
        public ValidatingCosmosClient(string accountEndpoint, string authKey, CosmosClientOptions options) :
            base(accountEndpoint, authKey, options)
        {
        }

        /// <summary>
        ///     Constructs a ValidatingCosmosClient using connection string
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="options"></param>
        public ValidatingCosmosClient(string connectionString, CosmosClientOptions options) :
            base(connectionString, options)
        {
        }

        #endregion

        #region Public Methods

        /// <summary>
        ///     Gets the Container and performs a ReadAsync to verify that the container exists
        ///     as well as retrieve the partition key path.
        /// </summary>
        /// <param name="databaseId"></param>
        /// <param name="containerId"></param>
        /// <param name="partitionKeyPath"></param>
        /// <returns></returns>
        public Container GetContainer(string databaseId, string containerId, out string partitionKeyPath)
        {
            Container container;
            ContainerResponse response = null;

            try
            {
                container = base.GetContainer(databaseId, containerId);
                response = container.ReadContainerAsync().GetAwaiter().GetResult();
            }
            catch (CosmosException e)
            {
                if (e.StatusCode == HttpStatusCode.NotFound)
                    throw new ArgumentException(
                        $"Unable to find container '{containerId}' in database ({databaseId}).  Verify that the container has been created and try again.");
                if (e.StatusCode == HttpStatusCode.TooManyRequests)
                {
                    // Go ahead and retry if a very short retry interval is given
                    if (e.RetryAfter < TimeSpan.FromMilliseconds(1000))
                    {
                        Task.Delay(e.RetryAfter.Value.Milliseconds).GetAwaiter().GetResult();
                        return GetContainer(databaseId, containerId, out partitionKeyPath);
                    }

                    throw new Exception(
                        $"Number of RUs on the Cosmos Database have been exceeded.  Specified retry interval is ${e.RetryAfter?.Milliseconds} ms.");
                }

                throw;
            }

            partitionKeyPath = response.Resource.PartitionKeyPath;
            return container;
        }

        #endregion
    }
}