import { Component, Input, OnInit } from '@angular/core';
import { CredoSoftService } from '../../services';

@Component({
    selector: 'app-general-analysis',
    templateUrl: './general-analysis.component.html',
    styleUrls: ['./general-analysis.component.scss']
})
export class GeneralAnalysisComponent implements OnInit {
    //@Input()
    generalanalysis: any;
    @Input() assetId: string;
    loadingVisible: boolean = false;
    constructor(private readonly credoService: CredoSoftService) {}

    ngOnInit(): void {
        this.getGeneralAnalysis();
    }

    getGeneralAnalysis() {
        if (this.assetId) {
            this.loadingVisible = true;
            this.credoService.getAllGeneralAnalysis(this.assetId).subscribe(
                (data) => {
                    this.generalanalysis = data;
                    this.loadingVisible = false;
                },
                (err) => {
                    console.error(err, 'error');
                    this.loadingVisible = false;
                }
            );
        } else {
            this.loadingVisible = false;
        }
    }

    calculateCorrosionRate(value: string, retirementDate: string): string {
        if (!retirementDate) return '';
        else return value ? parseFloat(value).toFixed(4) + ' in/year' : ' ';
    }
    calculateThickness(value: string, retirementDate: string): string {
        if (!retirementDate) return '';
        else return value ? parseFloat(value).toFixed(3) + ' in' : ' ';
    }
    calculateRemainingLife(value: string, retirementDate: string): string {
        if (!retirementDate) return 'Cannot calculate';
        else if (value && retirementDate) {
            const remainingValue = parseFloat(value);
            const currentDate = new Date();
            const expiration = new Date(retirementDate);
            if (remainingValue < 0 || expiration < currentDate) {
                return 'PAST RETIREMENT';
            }
            const remainingLife = remainingValue.toFixed(2) + ' years';
            return remainingLife;
        } else return '';
    }
}
