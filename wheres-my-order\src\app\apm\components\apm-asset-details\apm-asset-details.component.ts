import { Component, EventEmitter, Input, Output } from '@angular/core';
import { GalleryInspectionPhoto } from '..';
import { AssetDetailsPhotoTransport, WalkDown, WorkOrder } from '../../models';

@Component({
    selector: 'app-apm-asset-details',
    templateUrl: './apm-asset-details.component.html',
    styleUrls: ['./apm-asset-details.component.scss']
})
export class ApmAssetDetailsComponent {
    allPhotos: GalleryInspectionPhoto[];
    selectedAssetType: string;
    assetId: string;
    projectId: string;
    workOrderId: string;
    walkdown: WalkDown;

    @Output() photoDelete = new EventEmitter<AssetDetailsPhotoTransport>();
    @Output() photoDescriptionUpdate =
        new EventEmitter<AssetDetailsPhotoTransport>();
    @Input() photoSas: string;
    @Input() allowEditing: boolean;
    private _workOrder: WorkOrder;
    @Input() set workOrder(value: WorkOrder) {
        this._workOrder = value;
        if (this._workOrder === null || this._workOrder === undefined) return;
        this.workOrderId = this.workOrder?.id;
        this.assetId = this.workOrder?.asset?.id;
        this.projectId = this.workOrder?.projectId;
        this.walkdown = this.workOrder?.asset?.walkDown;
    }
    get workOrder() {
        return this._workOrder;
    }

    constructor() {}

    onPhotoDescriptionUpdate(e) {
        this.photoDescriptionUpdate.next(e);
    }

    onPhotoDelete(e) {
        this.photoDelete.next(e);
    }
}
