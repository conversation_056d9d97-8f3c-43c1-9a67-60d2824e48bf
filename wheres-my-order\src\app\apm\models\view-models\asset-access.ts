export interface AssetAccess {
    id: string;
    hasInsulation: string;
    possibleAsbestos: boolean;
    possibleAsbestosComment: string;
    jacketingType: string[];
    insulationType: string[];
    insulationTypeComment: string;
    insulationRemovalRequired: boolean;
    insulationRemovalRequiredComment: string;
    heatTracing: string[];
    heatTracingComment: string;
    existingInspectionPorts: boolean;
    insulationPlugsMissing: boolean;
    insulationPlugsMissingComment: string;
    additionalPortsNeeded: boolean;
    additionalPortsNeededComment: string;
    coatingType: string[];
    coatingTypeComment: string;
    coatingCondition: string[];
    coatingConditionsObserved: string[];
    coatingConditionsObservedComment: string;
    coatingRemovalRequired: boolean;
    coatingRemovalRequiredComment: string;
    corrosionIdentified: string[];
    corrosionIdentifiedComment: string;
    corrosionRemovalRecommendation: string[];
    corrosionRemovalRecommendationComment: string;
    fixedEquipmentLaddersStairwaysPlatformsInstalled: boolean;
    fixedEquipmentLaddersStairwaysPlatformsInstalledComment: string;
    allComponentsUnder4FeetInHeight: boolean;
    allComponentsUnder4FeetInHeightComment: string;
    ladderRequirements: string[];
    ladderRequirementsComment: string;
    aerialLiftNeeded: boolean;
    aerialLiftNeededComment: string;
    accessForAerialLiftForAllLocationsAtHeight: boolean;
    gasPoweredPermitted: boolean;
    batteryPoweredPermitted: boolean;
    clientRequiredProofOfTraining: boolean;
    clientProvidedOperator: boolean;
    estimatedDistanceToAnyLiveElectricalOverheadLines: number;
    scaffoldingRequired: boolean;
    scaffoldingRequiredComment: string;
    ropeAccessRequired: boolean;
    ropeAccessRequiredComment: string;
    assetOutOfService: boolean;
    inspectionOpeningsPresent: boolean;
    inspectionOpeningTypes: string[];
    inspectionOpeningTypesComment: string;
    sizeOfAllAccessOpenings: string;
    ventilationRequirements: string[];
    ventilationRequirementsComment: string;
    cleaningRecommendations: string[];
    cleaningRecommendationsComment: string;
    cleaningServiceReview: string;
    cleaningServiceReviewComment: string;
    coatingLinerType: string[];
    coatingLinerTypeComment: string;
    coatingLinerConditions: string[];
    coatingLinerConditionsObserved: string[];
    coatingLinerConditionsObservedComment: string;
}
