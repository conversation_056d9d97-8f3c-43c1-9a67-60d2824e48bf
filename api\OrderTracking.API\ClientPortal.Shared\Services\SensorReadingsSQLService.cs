﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Models.RemoteMonitoring;
using Dapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Z.Dapper.Plus;

namespace ClientPortal.Shared.Services
{
    /// <summary>
    ///     Implementation of service class for making SQL operations on SensorReadings
    /// </summary>
    public class SensorReadingsSQLService : ISensorReadingsSQLService
    {
        private readonly IConfiguration _config;
        private readonly ILogger<SensorReadingsSQLService> _logger;

        public SensorReadingsSQLService(ILogger<SensorReadingsSQLService> logger, IConfiguration config)
        {
            _logger = logger;
            _config = config;

            // Make sure we are allowed to use Dapper Plus for the bulk insert
            var licenseName = config["ZDapperPlus:LicenseName"];
            var licenseKey = config["ZDapperPlus:LicenseKey"];
            DapperPlusManager.AddLicense(licenseName, licenseKey);
        }

        public void MergeSensorReadings(IEnumerable<SensorReading> sensorReadings)
        {
            try
            {
                var connectionString = _config.GetConnectionString("RemoteMonitoring");

                // Use a connection to sql server
                using var connection = new SqlConnection(connectionString);

                _logger.LogInformation(
                    "bulk inserting sensor data, inserting only if the record doesn't already exist.");

                connection
                    // Want to make sure that we only insert what we don't already have
                    .UseBulkOptions(options => options.InsertIfNotExists = true)
                    // Attempt to insert all sensor readings
                    .BulkInsert(sensorReadings);
            }
            catch (Exception e)
            {
                _logger.LogError(e.Message);
                throw;
            }
        }

        public async Task<IEnumerable<SensorReading>> GetSensorReadingsAsync()
        {
            try
            {
                var connectionString = _config.GetConnectionString("RemoteMonitoring");

                // Use a connection to sql server
                await using var connection = new SqlConnection(connectionString);

                _logger.LogInformation("Getting all sensor readings");
                
                var readings = await connection.QueryAsync<SensorReading>("SELECT * FROM dbo.SensorReadings");
                var orderedReadings = readings.Select(r =>
                {
                    // Make sure that we mark these as UTC coming out of the database so that they get serialized
                    // and sent to the front end as such.
                    r.DateTimeUTC = DateTime.SpecifyKind(r.DateTimeUTC, DateTimeKind.Utc);
                    return r;
                }).OrderBy(r => r.DateTimeUTC);
                return orderedReadings;
            }
            catch (Exception e)
            {
                _logger.LogError(e.Message);
                throw;
            }
        }

        public async Task<IEnumerable<SensorReading>> GetSensorReadingsForUserAsync(UserProfile user)
        {
            try
            {
                var connectionString = _config.GetConnectionString("RemoteMonitoring");

                // Use a connection to sql server
                await using var connection = new SqlConnection(connectionString);

                _logger.LogInformation("Getting all sensor readings");
                // if the total length of the query string ever exceeds 4096 characters we will run into an error and have to update this method
                var sites ="'"+String.Join("','", user.RemoteMonitoringSiteIds)+"'";
                var readings = await connection.QueryAsync<SensorReading>($"SELECT * FROM dbo.SensorReadings WHERE Site IN ({sites}) ");
                var orderedReadings = readings.Select(r =>
                {
                    // Make sure that we mark these as UTC coming out of the database so that they get serialized
                    // and sent to the front end as such.
                    r.DateTimeUTC = DateTime.SpecifyKind(r.DateTimeUTC, DateTimeKind.Utc);
                    return r;
                }).OrderBy(r => r.DateTimeUTC);
                return orderedReadings;
            }
            catch (Exception e)
            {
                _logger.LogError(e.Message);
                throw;
            }
        }
    }
}