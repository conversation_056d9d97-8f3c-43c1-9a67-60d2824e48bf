﻿using System.Collections.Generic;
using System.Linq;
using NUnit.Framework;
using OrderTracking.API.Models.EDR;

namespace OrderTracking.API.Tests
{
    [TestFixture]
    public class EquipmentRequestTests
    {
        [Test]
        public void CheckForAcknowledged_IsNew_ReturnsFalse()
        {
            // Arrange
            var requestOne = new EquipmentRequest();
            var requestTwo = new EquipmentRequest();
            requestOne.JobTypes = new List<JobType> { new JobType { RequestStatus = "New" } };
            requestTwo.JobTypes = new List<JobType> { new JobType { RequestStatus = "New" } };

            // Act/Assert
            Assert.That(requestOne.JobTypes.First().CheckForAcknowledged(requestTwo), Is.False);
        }


        [Test]
        public void CheckForAcknowledged_RequestAcknowledged_ReturnsTrue()
        {
            // Arrange
            var requestOne = new EquipmentRequest();
            var requestTwo = new EquipmentRequest();
            requestOne.JobTypes = new List<JobType> { new JobType { RequestStatus = "New" } };
            requestTwo.JobTypes = new List<JobType> { new JobType { RequestStatus = "Request Acknowledged" } };

            // Act/Assert
            Assert.That(requestOne.JobTypes.First().CheckForAcknowledged(requestTwo), Is.True);
        }

        [Test]
        public void CheckForDelay_NoDelay_ReturnsFalse()
        {
            // Arrange
            var requestOne = new EquipmentRequest();
            var requestTwo = new EquipmentRequest();
            requestOne.JobTypes = new List<JobType> { new JobType { RequestStatus = "New", Delayed = false } };
            requestTwo.JobTypes = new List<JobType>
                { new JobType { RequestStatus = "Request Acknowledged", Delayed = false } };

            // Act/Assert
            Assert.That(requestOne.JobTypes.First().CheckForDelay(requestTwo), Is.False);
        }


        [Test]
        public void CheckForDelay_BothDelay_ReturnsFalse()
        {
            // Arrange
            var requestOne = new EquipmentRequest();
            var requestTwo = new EquipmentRequest();
            requestOne.JobTypes = new List<JobType> { new JobType { RequestStatus = "New", Delayed = true } };
            requestTwo.JobTypes = new List<JobType>
                { new JobType { RequestStatus = "Request Acknowledged", Delayed = true } };

            // Act / Assert
            Assert.That(requestOne.JobTypes.First().CheckForDelay(requestTwo), Is.False);
        }

        [Test]
        public void CheckForDelay_NewDelay_ReturnsTrue()
        {
            // Arrange
            var requestOne = new EquipmentRequest();
            var requestTwo = new EquipmentRequest();
            requestOne.JobTypes = new List<JobType> { new JobType { RequestStatus = "New", Delayed = false } };
            requestTwo.JobTypes = new List<JobType>
                { new JobType { RequestStatus = "Request Acknowledged", Delayed = true } };

            // Act / Assert
            Assert.That(requestOne.JobTypes.First().CheckForDelay(requestTwo), Is.True);
        }

        [Test]
        public void CheckForReadyForPickup_NoUpdate_ReturnsFalse()
        {
            // Arrange
            var requestOne = new EquipmentRequest();
            var requestTwo = new EquipmentRequest();
            requestOne.JobTypes = new List<JobType> { new JobType { RequestStatus = "Building Equipment" } };
            requestTwo.JobTypes = new List<JobType>
                { new JobType { RequestStatus = "Building Equipment" } };

            // Act / Assert
            Assert.That(requestOne.JobTypes.First().CheckForReadyForPickup(requestTwo), Is.False);
        }

        [Test]
        public void CheckForReadyForPickup_IsReady_ReturnsTrue()
        {
            // Arrange
            var requestOne = new EquipmentRequest();
            var requestTwo = new EquipmentRequest();
            requestOne.JobTypes = new List<JobType> { new JobType { RequestStatus = "Building Equipment" } };
            requestTwo.JobTypes = new List<JobType>
                { new JobType { RequestStatus = "Ready For Pickup" } };

            // Act / Assert
            Assert.That(requestOne.JobTypes.First().CheckForReadyForPickup(requestTwo), Is.True);
        }


        [Test]
        public void CheckForDelayUpdate_noUpdate_ReturnsFalse()
        {
            // Arrange
            var requestOne = new EquipmentRequest();
            var requestTwo = new EquipmentRequest();
            requestOne.JobTypes = new List<JobType>
            {
                new JobType
                {
                    RequestStatus = "New", Delayed = true,
                    Notes = new List<RequestNote> { new RequestNote { Note = "this is a new note" } }
                }
            };
            requestTwo.JobTypes = new List<JobType>
            {
                new JobType
                {
                    RequestStatus = "New", Delayed = true,
                    Notes = new List<RequestNote> { new RequestNote { Note = "this is a new note" } }
                }
            };


            // Act / Assert
            Assert.That(requestOne.JobTypes.First().CheckForDelayUpdate(requestTwo), Is.False);
        }


        [Test]
        public void CheckForDelayUpdate_Update_ReturnsTrue()
        {
            // Arrange
            var requestOne = new EquipmentRequest();
            var requestTwo = new EquipmentRequest();
            requestOne.JobTypes = new List<JobType>
            {
                new JobType
                {
                    RequestStatus = "New", Delayed = true,
                    Notes = new List<RequestNote> { new RequestNote { Note = "this is a new note" } }
                }
            };
            requestTwo.JobTypes = new List<JobType>
            {
                new JobType
                {
                    RequestStatus = "New", Delayed = true,
                    Notes = new List<RequestNote>
                    {
                        new RequestNote { Note = "this is a new note" },
                        new RequestNote { Note = "this is a second note" }
                    }
                }
            };

            // Act / Assert
            Assert.That(requestOne.JobTypes.First().CheckForDelayUpdate(requestTwo), Is.True);
        }

        [Test]
        public void GetMissingShippingInfo_WhenFieldsAreLeftEmpty_PopulatesMissingFields()
        {
            // Arrange
            var request = new EquipmentRequest();

            // Act
            var missingShippingInfo = request.GetMissingShippingInfo();

            // Assert
            Assert.That(missingShippingInfo, Is.Not.Null);
        }

        [Test]
        public void GetMissingShippingInfo_NonEmptyFields_AreNotInMissingFields()
        {
            // Arrange
            var request = new EquipmentRequest
            {
                ShippingAddress = new Address
                {
                    State = "a State",
                    ZipCode = "1234"
                }
            };

            // Act
            var missingShippingInfo = request.GetMissingShippingInfo();

            // Assert
            Assert.That(missingShippingInfo, Does.Not.Contain("State"));
        }

        [Test]
        public void GetMissingShippingInfo_EmptyFields_AreInMissingFields()
        {
            // Arrange
            var request = new EquipmentRequest
            {
                ShippingAddress = new Address
                {
                    State = "a State",
                    ZipCode = "1234"
                }
            };

            //Act
            var missingShippingInfo = request.GetMissingShippingInfo();

            // Assert
            Assert.That(missingShippingInfo, Does.Contain("City"));
        }

        [Test]
        public void GetMissingShippingInfo_EmptyStringFields_AreInMissingFields()
        {
            // Arrange
            var request = new EquipmentRequest
            {
                ShippingAddress = new Address
                {
                    State = "a State",
                    ZipCode = "1234",
                    City = ""
                }
            };

            //Act
            var missingShippingInfo = request.GetMissingShippingInfo();

            // Assert
            Assert.That(missingShippingInfo, Does.Contain("City"));
        }

        [Test]
        public void GetMissingShippingInfo_NullFiles_ShippingAttachmentNotMissing()
        {
            // Arrange
            var request = new EquipmentRequest
            {
                Files = null
            };

            // Act
            var missingShippingInfo = request.GetMissingShippingInfo();

            // Assert
            Assert.That(missingShippingInfo, Does.Not.Contain("Shipping Info Attachment"));
        }

        [Test]
        public void GetMissingShippingInfo_EmptyFiles_ShippingAttachmentNotMissing()
        {
            // Arrange
            var request = new EquipmentRequest
            {
                Files = new List<EquipmentRequestBlobFile>()
            };

            // Act
            var missingShippingInfo = request.GetMissingShippingInfo();

            // Assert
            Assert.That(missingShippingInfo, Does.Not.Contain("Shipping Info Attachment"));
        }

        [Test]
        public void GetMissingShippingInfo_FilesWithNoShipping_ShippingAttachmentNotMissing()
        {
            // Arrange
            var request = new EquipmentRequest
            {
                Files = new List<EquipmentRequestBlobFile>
                {
                    new() { HasShippingInfo = false, HasDataSheet = true },
                    new() { HasShippingInfo = false, HasPJA = true }
                }
            };

            // Act
            var missingShippingInfo = request.GetMissingShippingInfo();

            // Assert
            Assert.That(missingShippingInfo, Does.Not.Contain("Shipping Info Attachment"));
        }

        [Test]
        public void GetMissingShippingInfo_FilesWithShipping_NotMissingShippingAttachment()
        {
            // Arrange
            var request = new EquipmentRequest
            {
                Files = new List<EquipmentRequestBlobFile>
                {
                    new EquipmentRequestBlobFile { HasShippingInfo = true, HasDataSheet = true, HasPJA = true }
                }
            };

            // Act
            var missingShippingInfo = request.GetMissingShippingInfo();

            // Assert
            Assert.That(missingShippingInfo, Does.Not.Contain("Shipping Info Attachment"));
        }
    }
}