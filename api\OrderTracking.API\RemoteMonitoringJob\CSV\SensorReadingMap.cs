﻿using ClientPortal.Shared.Models.Helpers;
using ClientPortal.Shared.Models.RemoteMonitoring;
using CsvHelper.Configuration;

namespace RemoteMonitoringJob.CSV
{
    /// <summary>
    ///     Fluent style class map from CSV to C# model
    /// </summary>
    // ReSharper disable once ClassNeverInstantiated.Global
    public sealed class SensorReadingMap : ClassMap<SensorReading>
    {
        public SensorReadingMap()
        {
            Map(m => m.DateTimeUTC)
                .Index(0)
                .TypeConverter<AsUTCDateTimeConverter>();
            Map(m => m.ThicknessAlarmState)
                .Name("Thickness Alarm State");
            Map(m => m.CorrosionAlarmState)
                .Name("Corrosion Alarm State");
            Map(m => m.Thickness)
                .Name("Thickness in")
                .TypeConverter<FloatingPointNumberConverter>();
            Map(m => m.TempCompensatedThickness)
                .Name("Temp. Compensated Thickness in")
                .TypeConverter<FloatingPointNumberConverter>();
            Map(m => m.MaterialTemperature)
                .Name("Material Temperature deg F")
                .TypeConverter<FloatingPointNumberConverter>();
            Map(m => m.DSITemperature)
                .Name("DSI Temperature deg F")
                .TypeConverter<FloatingPointNumberConverter>();
            Map(m => m.ReferenceVelocity)
                .Name("Reference Velocity in/usec")
                .TypeConverter<FloatingPointNumberConverter>();
            Map(m => m.TempCompensatedVelocity)
                .Name("Temp. Compensated Velocity in/usec")
                .TypeConverter<FloatingPointNumberConverter>();
            Map(m => m.CorrosionRateShortTerm)
                .Name("Corrosion Rate (short term)in/yr")
                .TypeConverter<FloatingPointNumberConverter>();
            Map(m => m.EndOfLifeShortTerm)
                .Name("End of Life (short term)")
                .TypeConverter<EndOfLifeConverter>();
            Map(m => m.CorrosionRateLongTerm)
                .Name("Corrosion Rate (long term)in/yr")
                .TypeConverter<FloatingPointNumberConverter>();
            Map(m => m.EndOfLifeLongTerm)
                .Name("End of Life (long term)")
                .TypeConverter<EndOfLifeConverter>();
            Map(m => m.ThicknessAlarmThreshold)
                .Name("Thickness Alarm Threshold")
                .TypeConverter<FloatingPointNumberConverter>();
            Map(m => m.ThicknessWarningThreshold)
                .Name("Thickness Warning Threshold")
                .TypeConverter<FloatingPointNumberConverter>();
            Map(m => m.CorrosionAlarmThreshold)
                .Name("Corrosion Alarm Threshold")
                .TypeConverter<FloatingPointNumberConverter>();
            Map(m => m.CorrosionWarningThreshold)
                .Name("Corrosion Warning Threshold")
                .TypeConverter<FloatingPointNumberConverter>();
            Map(m => m.Company)
                .Name("Company");
            Map(m => m.Site)
                .Name("Site");
            Map(m => m.Plant)
                .Name("Plant");
            Map(m => m.Asset)
                .Name("Asset");
            Map(m => m.CollectionPoint)
                .Name("Collection Point");
            Map(m => m.TML)
                .Name("TML");
        }
    }
}