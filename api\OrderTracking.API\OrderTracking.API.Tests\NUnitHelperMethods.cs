﻿using System.Linq;
using Microsoft.AspNetCore.Mvc;
using NUnit.Framework;

namespace OrderTracking.API.Tests
{
    internal static class NUnitHelperMethods
    {
        #region Public Methods

        internal static bool MethodHasStatusCode(this ControllerBase controller, string method, int statusCode)
        {
            var possibleMethods = controller.GetType().GetMethods();
            var attributes = possibleMethods
                .Where(m => m.Name == method)
                .SelectMany(
                (m) => m.CustomAttributes,
                (m, c) => c);

            var responseAttributes = attributes
                .Where(a => a.AttributeType == typeof(ProducesResponseTypeAttribute))
                .Select(a => (int)a.ConstructorArguments[0].Value);

            Assert.That(responseAttributes, Contains.Item(statusCode));
            
            return true;
        }

        #endregion
    }
}