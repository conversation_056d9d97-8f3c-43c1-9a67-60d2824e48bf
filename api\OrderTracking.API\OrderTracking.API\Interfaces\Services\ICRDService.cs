﻿//using OrderTracking.API.Models.CRD;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;

//namespace OrderTracking.API.Interfaces.Services
//{
//    public interface ICRDService
//    {
//        /// <summary>
//        /// 
//        /// </summary>
//        /// <param name="id"></param>
//        /// <returns></returns>
//        public Task<CRDEntry> GetCRDEntry(int id);

//        /// <summary>
//        /// </summary>
//        /// <returns></returns>
//        public Task<IEnumerable<CRDEntry>> GetAllCRDEntries();

//        /// <summary>
//        /// </summary>
//        /// <returns></returns>
//        public Task<IEnumerable<CRDAuditEntry>> GetAllCRDAuditRecords();

//        /// <summary>
//        /// </summary>
//        /// <returns></returns>
//        public Task<IEnumerable<CRDEntry>> GetCRDAuditRecordsById(int id);

//        /// <summary>
//        /// 
//        /// </summary>
//        /// <param name="record"></param>
//        /// <param name="user"></param>
//        /// <returns></returns>
//        public Task<CRDEntry> InsertCRDEntry(CRDEntry record, string user);

//        /// <summary>
//        /// 
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="crdEntry">Updated in-memory entity</param>
//        /// <param name="user"></param>
//        /// <returns></returns>
//        public Task<CRDEntry> UpdateCRDEntry(int id, CRDEntry crdEntry, string user);

//        /// <summary>
//        /// 
//        /// </summary>
//        /// <param name="id"></param>
//        /// <param name="user"></param>
//        /// <returns></returns>
//        public Task<bool> DeleteCRDEntry(int id, string user);

//    }
//}
