const { data } = require("cypress/types/jquery");

const useradded = "APM:Admin";
const Description = "Entering description for testing  purpose";
const Role1 = "testing:test";

class Base1 {
  elements = {
    Login_button: () => cy.xpath("//span[text()='Login']"),
    username_button: () => cy.get("#logonIdentifier"),
    password_button: () => cy.get("#password"),
    Signin: () => cy.get("#next"),
    signout: () => cy.xpath('//div[@class="user-name"]'),
    Logout_button: () => cy.xpath("//span[text()='Logout']"),
    AITool_click: () => cy.xpath("//span[text()='AI Tools']"),
    ESG_button: () => cy.xpath("//span[text()='ESG']"),
    Search_button: () => cy.get("#searchWidgetTrigger"),
    Type_search: () => cy.xpath("//input[@placeholder='Search here']"),
    Requestaccesstab: () => cy.xpath("//i[@class='dx-icon fa fa-unlock-alt']"),
    Select_all_option: () => cy.xpath("(//span[@class='dx-checkbox-icon'])[1]"),
    companyname_and_site: () =>
      cy.xpath('(//input[@class="dx-texteditor-input"])[1]'),
    Team_contact: () => cy.xpath("(//input[@class='dx-texteditor-input'])[2]"),
    Request_Accessbutton: () => cy.get(".dx-button-text"),
    Successfully_submission: () =>
      cy.xpath('(//span[@class="dx-button-text"])[2]'),
    Administration_module: () => cy.xpath("//*[text()='Administration']"),
    User_Tab: () => cy.xpath("//*[text()='Users']"),
    Search_user: () => cy.xpath("( //input[@class='dx-texteditor-input'])[1]"),
    click_user: () => cy.xpath("(//td[@role='gridcell'])[1]"),
    select_roles: () =>
      cy.xpath(
        "//div[@class='dx-texteditor-input-container dx-tag-container']"
      ),
    Select_rolesfromoptions: () => cy.get(":nth-child(14) > .dx-item-content"),
    Update_button: () => cy.xpath("//span[text()='Update']"),
    Toast_message: () => cy.get(".toast-message"),
    Role_tab: () => cy.get("li[data-item-id='/admin/roles']"),
    Add_button: () => cy.get("#add-role-button"),
    Enter_rolekey: () => cy.xpath("(//*[@class='dx-texteditor-input'])[2]"),
    Enter_description: () => cy.xpath("(//*[@class='dx-texteditor-input'])[3]"),
    create_Button: () => cy.xpath("//*[text()='Create']"),
    Select_user_to_add_button: () => cy.get("#userSelectBox"),
    Typein_selectuser_to_add_button: () =>
      cy.xpath("(//input[@class='dx-texteditor-input'])[2]"),
    Delete_role: () => cy.get("#delete-role-button"),
    Deletion_confirmation: () => cy.xpath("//span[text()='Yes']"),
    search_function: () =>
      cy.xpath("//input[@aria-label='Search in the data grid']"),
    Select_a_role: () => cy.xpath('//div[@role="presentation"]'),
    ok_button_user: () => cy.xpath("//span[text()='OK']"),
    Edit_role: () => cy.get("#edit-role-button"),
    Update_role_button: () => cy.xpath("//*[text()='Update']"),
    Refresh_Icon: () => cy.xpath("//*[@icon='fa fa-refresh']"),
    Name_refreshed_check: () => cy.xpath("//span[@class='name']"),
    User_audit_Tab: () => cy.xpath("//*[@data-item-id='/admin/user-audit']"),
    User_audit_checkbox: () =>
      cy.get(
        ".dx-header-row > .dx-command-select > .dx-widget > .dx-checkbox-container > .dx-checkbox-icon"
      ),
    Export_Excel_button: () => cy.get(".dx-icon-spindown"),
    Export_all_rows: () =>
      cy.get('[title="Export all data to Excel"] > .dx-item-content'),
    user_audit_singlecheckbox: () =>
      cy.get(
        '[aria-rowindex="1"] > .dx-command-select > .dx-widget > .dx-checkbox-container > .dx-checkbox-icon'
      ),
    Export_selected_rows: () =>
      cy.get('[title="Export selected rows to Excel"] > .dx-item-content'),
    search: () => cy.xpath('(//input[@role="textbox"])[1]'),
    Tableof_useraudit: () =>
      cy.xpath("//*[@role='gridcell' and @aria-colindex='4']"),
    Check_roles: () => cy.xpath("//*[@role='gridcell' and @aria-colindex='6']"),
    History_tab: () => cy.xpath("//span[text()='History']"),
    selected_Area: () => cy.xpath('(//*[@transform="translate(0,0)"])[12]'),
    checkbox_historypage: () =>
      cy.xpath("(//div[@class='dx-checkbox-container'])[2]"),
    Export_Excel_History: () => cy.xpath("(//div[@class='dx-inkripple'])[3]"),
    search_Text_History: () =>
      cy.xpath("//input[@aria-label='Search in the data grid']"),
    Tableof_History: () =>
      cy.xpath("//*[@role='gridcell' and @aria-colindex='5']"),
    Link_History: () =>
      cy.get(
        ".dx-datagrid-content-fixed > .dx-datagrid-table > tbody > [aria-rowindex='1'] > .dx-command-edit > .dx-link"
      ),
    Checking_background: () =>
      cy.get(".dx-scrollview-content > :nth-child(1) > span"),
    Precheck_history: () => cy.get('[style="flex-grow: 1; width: 50%;"] > pre'),
    Postcheck_history: () => cy.get('[style="flex-grow: 1;"] > pre'),
    T_box4a: () => cy.get("div[role='option']"),
    History_checking: () =>
      cy.xpath("//td[@role='gridcell' and @aria-colindex='5']"),
    AssetManagement_tab: () => cy.xpath('//span[text()="Asset Management"]'),
    RemoteAssetMonitoring_tab: () =>
      cy.get('li[data-item-id="/remote-asset-monitoring"]'),
    SelectingAnAsset: () =>
      cy.get(
        '[data-item-id="State%20Kate%20Olson%2057-T2-17X8%20B204H"] > .dx-widget > .dx-checkbox-container > .dx-checkbox-icon'
      ),
    PlantSelected: () => cy.xpath('//dt[text()="Plants selected"]'),
    AssetSelected: () => cy.xpath('//dt[text()="Assets selected"]'),
    CollectionPointSelected: () =>
      cy.xpath('//dt[text()="Collection points selected"]'),
    TMLsSelected: () => cy.xpath('//dt[text()="TMLs selected"]'),
    Minimum_Thickness: () => cy.xpath('//span[text()="MINIMUM THICKNESS"]'),
    ThicknessTemperatureoverTime: () =>
      cy.xpath('//*[text()="Thickness, Temperature Over Time"]'),
    CorrisionRateOvertime: () =>
      cy.xpath('//*[text()="Corrosion Rate Over Time"]'),
    ThicknessInGraph: () => cy.xpath('(//*[text()="Thickness"])[1]'),
    TemperatureInGraph: () => cy.xpath('(//*[text()="Temperature"])[1]'),
    TemperatureInsecondGraph: () => cy.xpath('(//*[text()="Temperature"])[2]'),
    CorrisonInGraph: () => cy.xpath('//*[text()="Corrosion"]'),
    DatadetailsTab: () => cy.xpath('//span[text()="Data Detail"]'),
    ClickChekbox: () =>
      cy.get(
        '[aria-rowindex="1"] > .dx-command-select > .dx-widget > .dx-checkbox-container > .dx-checkbox-icon'
      ),
    Exportbutton: () => cy.get(".dx-icon-export"),
    ExportSelectedData: () =>
      cy.get('[title="Export selected rows to Excel"] > .dx-item-content'),
    ExportAllData: () =>
      cy.get('[title="Export all data to Excel"] > .dx-item-content'),
    RemoteSearchData: () =>
      cy.get(
        ".dx-datagrid-search-panel > .dx-texteditor-container > .dx-texteditor-input-container > .dx-texteditor-input"
      ),
    CheckingSearchedData: () =>
      cy.xpath('//td[@role="gridcell" and @aria-colindex="7"]'),
  };

  login() {
    cy.visit(
      "https://run-clientportal-frontend-stg-usc1-2atiipqgyq-uc.a.run.app/"
    );
    cy.clearAllCookies();
    this.elements.Login_button().click();
    cy.wait(15000);
    this.elements.username_button().type(Cypress.env("Client_username"));
    this.elements.password_button().type(Cypress.env("Client_password"));
    this.elements.Signin().click({ force: true });
    cy.wait(20000);
  }

  logout() {
    this.elements.signout().click({ force: true });
    this.elements.Logout_button().click();
  }

  AI_TOOL() {
    this.elements.AITool_click().click();
    this.elements.ESG_button().click();
    this.elements.Search_button().dblclick({ force: true }, { timeout: 1000 });
  }

  Request_access_module() {
    this.elements.Requestaccesstab().click();
    this.elements.Select_all_option().click();
    this.elements.companyname_and_site().click().type("team1");
    this.elements.Team_contact().click().type("Mangalagayathri nagaraju");
    this.elements.Request_Accessbutton().click();
    this.elements.Successfully_submission().click();
  }

  Administration_tab_user() {
    cy.fixture("example.json").then((Data) => {
      const Searchedtext = Data.searchtext;
      const Role_1 = Data.Role;
      this.elements.Administration_module().click();
      this.elements.User_Tab().click();
      this.elements.Search_user().type(Searchedtext);
      cy.wait(2000);
      this.elements.click_user().click();
      this.elements.select_roles().click();
      this.elements.select_roles().click();
      this.elements.T_box4a().each(function ($ele, index, list) {
        if ($ele.text() == useradded) {
          cy.wrap($ele).click({ force: true });
        } else {
        }
      });
      this.elements.ok_button_user().click({ force: true });
      cy.wait(2000);
      this.elements.Update_button().click();
      cy.wait(2000);
      this.elements.Toast_message({ timeout: 10000 }).should(($element) => {
        const text = $element.text();
        expect(text).to.include("User successfully updated");
      });

      this.elements.Administration_module().click();
      this.elements.User_audit_Tab().click();
      this.elements.User_audit_checkbox().click();
      this.elements.Export_Excel_button().click();
      this.elements.Export_all_rows().click();
      this.elements.User_audit_checkbox().click();
      this.elements.user_audit_singlecheckbox().click();
      this.elements.Export_selected_rows().click({ force: true });
      this.elements.search().type(Searchedtext);
      this.elements.Tableof_useraudit().should("have.text", Searchedtext);
      this.elements
        .Check_roles()
        .should("have.text", `App:Admin, ${useradded}`);
    });
  }

  Administration_tab_Roles_AddingRole() {
    cy.fixture("example.json").then((Data) => {
      const Role_1 = Data.Role;
      this.elements.Administration_module().click();
      this.elements.Role_tab().click();
      this.elements.Add_button().click();
      this.elements.Enter_rolekey({ timeout: 10000 }).type(Role_1);
      this.elements.Enter_description().type(Description);
      this.elements.create_Button().click();
      this.elements.Toast_message().should(($element) => {
        const text = $element.text();
        expect(text).to.include("Role successfully added");
      });
    });
  }

  Administration_tab_Roles_DeletingRole() {
    cy.fixture("example.json").then((Data) => {
      const Role_1 = Data.Role;
      this.elements.Administration_module().click();
      this.elements.Role_tab().click();
      this.elements.search_function().type(Role_1).type("{enter}");
      this.elements.Select_a_role().contains(Role_1).click();
      this.elements.Delete_role().click({ force: true });
      this.elements.Deletion_confirmation().click({ force: true });
      cy.wait(1000);
      this.elements.Toast_message().should(($element) => {
        const text = $element.text();
        expect(text).to.include("Role successfully deleted");
      });
    });
  }

  Administration_tab_Roles_EditingRole() {
    cy.fixture("example.json").then((Data) => {
      const Role_1 = Data.Role;
      this.elements.Administration_module().click();
      this.elements.Role_tab().click();
      this.elements.search_function().type(Role_1).type("{enter}");
      this.elements.Select_a_role().contains(Role_1).click();
      this.elements.Edit_role().click();
      this.elements.Enter_rolekey({ timeout: 10000 }).type("04");
      this.elements.Update_role_button().click();
      this.elements.Toast_message().should(($element) => {
        const text = $element.text();
        expect(text).to.include("Role successfully updated");
      });
      this.elements.Refresh_Icon().click();
      cy.wait(2000);
    });
  }

  Administration_tab_HistoryPage() {
    cy.fixture("example.json").then((Data) => {
      const Role_1 = Data.Role;
      this.elements.Administration_module().click();
      this.elements.History_tab().click();
      cy.wait(80000);
      this.elements.selected_Area().click();
      this.elements.checkbox_historypage().click();
      this.elements.Export_Excel_History().click({ force: true });
      this.elements.search_Text_History().type(Role_1);
      this.elements.Tableof_History().should("be.visible");
      this.elements.Tableof_History().contains(Role_1).should("exist");
      this.elements.Link_History().click();
      this.elements.Checking_background().should("be.visible");
      this.elements
        .Checking_background()
        .contains("<EMAIL>")
        .should("exist");
      this.elements.History_checking().contains(Role_1).should("exist");
    });
  }

  RemoteAssetMonitoring_Page() {
    this.elements.AssetManagement_tab().click({ force: true });
    this.elements.RemoteAssetMonitoring_tab().click();
    cy.wait(20000);
    this.elements.SelectingAnAsset().click();
    cy.wait(3000);
    this.elements.PlantSelected().should("be.visible");
    this.elements.PlantSelected().should("have.text", "Plants selected");
    this.elements.AssetSelected().should("be.visible");
    this.elements.AssetSelected().should("have.text", "Assets selected");
    this.elements.CollectionPointSelected().should("be.visible");
    this.elements
      .CollectionPointSelected()
      .should("have.text", "Collection points selected");
    this.elements.TMLsSelected().should("be.visible");
    this.elements.TMLsSelected().should("have.text", "TMLs selected");
    this.elements.Minimum_Thickness().should("be.visible");
    this.elements.Minimum_Thickness().should("have.text", "MINIMUM THICKNESS");
    cy.wait(4000);
    this.elements.ThicknessTemperatureoverTime().then(($el) => {
      if ($el.is(":visible")) {
        cy.wrap($el).scrollIntoView();
      }
    });

    this.elements
      .ThicknessTemperatureoverTime()
      .should("have.text", "Thickness, Temperature Over Time");
    this.elements.ThicknessInGraph().should("be.visible");
    this.elements.ThicknessInGraph().should("have.text", "Thickness");
    this.elements.TemperatureInGraph().should("be.visible");
    this.elements.TemperatureInGraph().should("have.text", "Temperature");
    cy.wait(6000);
    this.elements.CorrisionRateOvertime().then(($el) => {
      if ($el.is(":visible")) {
        cy.wrap($el).scrollIntoView();
      }
    });

    this.elements
      .CorrisionRateOvertime()
      .should("have.text", "Corrosion Rate Over Time");
    this.elements.CorrisonInGraph().then(($el) => {
      if ($el.is(":visible")) {
        cy.wrap($el).scrollIntoView();
      }
    });
    this.elements.CorrisonInGraph().should("have.text", "Corrosion");
    this.elements.TemperatureInsecondGraph().then(($el) => {
      if ($el.is(":visible")) {
        cy.wrap($el).scrollIntoView();
      }
    });

    this.elements.TemperatureInsecondGraph().should("have.text", "Temperature");
    this.elements.DatadetailsTab().click();
    this.elements.ClickChekbox().click();
    this.elements.Exportbutton().click();
    this.elements.ExportSelectedData().click();
    cy.wait(1000);
    this.elements.ClickChekbox().click();
    this.elements.Exportbutton().click();
    this.elements.ExportAllData().click();
    cy.wait(3000);
    this.elements.RemoteSearchData().type("10.364");
    cy.wait(2000);
    const Finaldata = this.elements.CheckingSearchedData().then(($el) => {
      if ($el.is(":visible")) {
        cy.wrap($el).scrollIntoView();
      }
    });
    Finaldata.should("have.text", "10.364");
  }
}
module.exports = new Base1();
