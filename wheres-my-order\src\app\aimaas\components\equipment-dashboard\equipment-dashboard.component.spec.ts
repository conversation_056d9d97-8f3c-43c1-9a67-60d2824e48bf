import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { DxChartModule } from 'devextreme-angular/ui/chart';
import { DxPieChartModule } from 'devextreme-angular/ui/pie-chart';

import { OverviewBoxComponent } from '../../../shared/components';
import { EquipmentDashboardComponent } from './equipment-dashboard.component';

describe('EquipmentDashboardComponent', () => {
    let component: EquipmentDashboardComponent;
    let fixture: ComponentFixture<EquipmentDashboardComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [RouterTestingModule, DxPieChartModule, DxChartModule],
            declarations: [EquipmentDashboardComponent, OverviewBoxComponent],
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(EquipmentDashboardComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
