<dx-form [colCount]="2"
         [formData]="location"
         [readOnly]="!isEditing"
         [showValidationSummary]="true">
    <dxi-item dataField="name">
        <dxo-label text="Location Name"></dxo-label>
        <dxi-validation-rule type="required"></dxi-validation-rule>
    </dxi-item>
    <dxi-item dataField="description">
        <dxo-label text="Description"></dxo-label>
    </dxi-item>

    <dxi-item itemType="group"
              caption="Address">
        <dxi-item dataField="city">
            <dxo-label text="City"></dxo-label>
            <dxi-validation-rule type="required"></dxi-validation-rule>
        </dxi-item>
        <dxi-item dataField="region">
            <dxo-label text="Region (State)"></dxo-label>
        </dxi-item>
        <dxi-item dataField="street1">
            <dxo-label text="Street 1"></dxo-label>
        </dxi-item>
        <dxi-item dataField="street2">
            <dxo-label text="Street 2"></dxo-label>
        </dxi-item>
        <dxi-item dataField="postalCode">
            <dxo-label text="Postal Code (Zip)"></dxo-label>
        </dxi-item>
    </dxi-item>
    <dxi-item itemType="group"
              caption="GIS Location">
        <dxi-item dataField="latitude">
            <dxo-label text="Latitude"></dxo-label>
        </dxi-item>
        <dxi-item dataField="longitude">
            <dxo-label text="Longitude"></dxo-label>
        </dxi-item>
    </dxi-item>
</dx-form>

<div class="content-block responsive-paddings form-buttons">
    <dx-button *ngIf="!isEditing"
               text="Edit Location"
               type="default"
               hint="Edit Location"
               [disabled]="editButtonDisabled$ | async"
               (onClick)="editClicked()">
    </dx-button>
    <dx-button *ngIf="isEditing"
               type="normal"
               text="Cancel"
               hint="cancel"
               [disabled]="cancelButtonDisabled$ | async"
               (onClick)="cancelEditClicked()"></dx-button>
    <dx-button *ngIf="isEditing"
               text="Save"
               type="success"
               hint="save"
               [disabled]="saveButtonDisabled$ | async"
               (onClick)="saveEditLocationClicked()"></dx-button>
</div>
