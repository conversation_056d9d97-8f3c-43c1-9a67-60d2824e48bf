﻿using System;
using System.Linq;
using APMWebDataInterface.ExampleDataModel;
using OrderTracking.API.Models.APM;
using OrderTracking.API.Services;

namespace OrderTracking.API.Extensions.APM
{
    public static class TaskExtensions
    {
        public static void Update(this APMTask task, ActivityUpdate update)
        {
            if (string.IsNullOrWhiteSpace(update.DatabaseId))
            {
                var activity = task.activities.AddNewItem();

                if (update.Technician != null)
                    activity.user.SetValue(update.Technician);

                if (update.Date != null)
                    activity.date.SetValue(DateTime.Parse(update.Date));

                //if (update.ClientWorkOrder != null)
                // TODO: Is it on purpose that task activities don't have a client work order number?
                // Because they don't, unlike project activities.

                var activityItem = activity.activities.FirstOrDefault(item => item.DisplayName == update.TaskType);
                if (activityItem == null) return;

                if (update.Duration != null)
                    activityItem.SetValue(update.Duration.Value);
            }
            else
            {
                var activity =
                    task.activities.CurrentEntries.FirstOrDefault(activity => activity.DatabaseId == update.DatabaseId);
                if (activity == null) throw new TasksNotFoundException(update.DatabaseId);

                var activityItem = activity.activities.FirstOrDefault(item => item.DisplayName == update.TaskType);
                if (activityItem != null)
                {
                    if (update.Duration == null && activityItem.CurrentValue != null)
                    {
                        activityItem.SetValue(null);
                    }else if (update.Duration != null && activityItem.CurrentValue == null)
                    {
                        activityItem.SetValue(update.Duration);
                    }
                    else
                    {
                        if (update.Duration != null && activityItem.CurrentValue != null)
                        {
                            if (Math.Abs(activityItem.CurrentValue.Value - update.Duration.Value) > 0.00001)
                                activityItem.SetValue(update.Duration);
                        }
                    }
                }

                if (string.Equals(update.PreviousTaskType, update.TaskType) || update.IsInserting) return;
             
                var previousActivityItem = activity.activities.FirstOrDefault(item =>
                    string.Equals(item.DisplayName, update.PreviousTaskType));

                previousActivityItem?.SetValue(null);
            }
        }

        public static void Update(this APMTask task, TaskUpdate taskUpdate)
        {
            if (taskUpdate.Status != null && taskUpdate.Status != task.status.CurrentValue)
            {
                var status = task.StatusFromString(taskUpdate.Status);
                task.ChangeStatus(status);
            }

            if (taskUpdate.ClientWorkOrderNumber != null &&
                taskUpdate.ClientWorkOrderNumber != task.clientWorkOrderNumber.CurrentValue)
                task.clientWorkOrderNumber.SetValue(taskUpdate.ClientWorkOrderNumber);
            if (taskUpdate.ClientWorkOrderDescription != null && taskUpdate.ClientWorkOrderDescription !=
                task.clientWorkOrderDescription.CurrentValue)
                task.clientWorkOrderDescription.SetValue(taskUpdate.ClientWorkOrderDescription);
            if (taskUpdate.LeadTechnician != null && taskUpdate.LeadTechnician != task.leadTech.CurrentValue)
                task.leadTech.SetValue(taskUpdate.LeadTechnician);
            var parsed = DateTime.TryParse(task.plannedEnd.CurrentValue, out var plannedEndParsed);
            if (taskUpdate.PlannedEnd != null &&
                (parsed && task.plannedEnd.CurrentValue != null || !parsed && task.plannedEnd.CurrentValue == null) &&
                taskUpdate.PlannedEnd != plannedEndParsed)
                task.plannedEnd.SetValue(taskUpdate.PlannedEnd);
            parsed = DateTime.TryParse(task.dueDate.CurrentValue, out var dueDateParsed);
            if (taskUpdate.DueDate != null &&
                (parsed && task.dueDate.CurrentValue != null || !parsed && task.dueDate.CurrentValue == null) &&
                taskUpdate.DueDate != dueDateParsed)
                task.dueDate.SetValue(taskUpdate.DueDate);
            parsed = DateTime.TryParse(task.plannedStart.CurrentValue, out var plannedStartParsed);
            if (taskUpdate.PlannedStart != null &&
                (parsed && task.plannedStart.CurrentValue != null ||
                 !parsed && task.plannedStart.CurrentValue == null) && taskUpdate.PlannedStart != plannedStartParsed)
                task.plannedStart.SetValue(taskUpdate.PlannedStart);
            if (taskUpdate.Supervisor != null && taskUpdate.Supervisor != task.taskDetails.supervisor.CurrentValue)
                task.taskDetails.supervisor.SetValue(taskUpdate.Supervisor);
            if (taskUpdate.TaskAssignees != null && (taskUpdate.TaskAssignees.Length != task.assignedUsers.Length ||
                                                     taskUpdate.TaskAssignees.Any(u =>
                                                         !task.assignedUsers.Contains(u))))
                task.assignedUsers = taskUpdate.TaskAssignees;

            if (taskUpdate.PurchaseOrderAFE != null &&
                taskUpdate.PurchaseOrderAFE != task.purchaseOrderAFE.CurrentValue)
                task.purchaseOrderAFE.SetValue(taskUpdate.PurchaseOrderAFE);

            if (taskUpdate.ClientCostCode != null && taskUpdate.ClientCostCode != task.clientCostCode.CurrentValue)
                task.clientCostCode.SetValue(taskUpdate.ClientCostCode);
        }

        public static APMTask.Statuses StatusFromString(this APMTask task, string statusString)
        {
            var status = statusString switch
            {
                "Not Started" => APMTask.Statuses.NotStarted,
                "In Progress" => APMTask.Statuses.InProgress,
                "Completed" => APMTask.Statuses.Completed,
                "Canceled" => APMTask.Statuses.Canceled,
                "Scheduled" => APMTask.Statuses.Scheduled,
                "On Hold" => APMTask.Statuses.OnHold,
                _ => throw new InvalidOperationException($"Invalid task status: {statusString}")
            };

            return status;
        }
    }
}