FROM mcr.microsoft.com/dotnet/core/aspnet:3.1-buster-slim AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/core/sdk:3.1-buster AS build

WORKDIR /src

RUN curl -L https://raw.githubusercontent.com/Microsoft/artifacts-credprovider/master/helpers/installcredprovider.sh  | sh

COPY . .

ARG FEED_ACCESSTOKEN
ENV NUGET_CREDENTIALPROVIDER_SESSIONTOKENCACHE_ENABLED true
ENV VSS_NUGET_EXTERNAL_FEED_ENDPOINTS \
    "{\"endpointCredentials\": [{\"endpoint\":\"https://pkgs.dev.azure.com/teaminc/TeamDigital/_packaging/TeamDigitalFeed/nuget/v3/index.json\", \"username\":\"docker\", \"password\":\"${FEED_ACCESSTOKEN}\"}]}"

RUN dotnet restore "./TeamDigital.Authorization.Web/TeamDigital.Authorization.Web/TeamDigital.Authorization.Web.csproj" --configfile ./OrderTracking.API/NuGet.Config
RUN dotnet restore "./OrderTracking.API/OrderTracking.API/OrderTracking.API.csproj"
RUN dotnet restore "./OrderTracking.API/ClientPortal.Shared/ClientPortal.Shared.csproj"

RUN dotnet build "./OrderTracking.API/ClientPortal.Shared/ClientPortal.Shared.csproj" -c Release -o /app/build
RUN dotnet build "./TeamDigital.Authorization.Web/TeamDigital.Authorization.Web/TeamDigital.Authorization.Web.csproj" -c Release -o /app/build
RUN dotnet build "./OrderTracking.API/OrderTracking.API/OrderTracking.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "./OrderTracking.API/OrderTracking.API/OrderTracking.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "OrderTracking.API.dll"]