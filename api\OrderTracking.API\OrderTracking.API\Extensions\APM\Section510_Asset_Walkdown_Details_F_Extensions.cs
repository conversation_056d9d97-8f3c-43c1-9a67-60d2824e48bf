using System;
using System.Linq;
using APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F;
using OrderTracking.API.Models.APM;

namespace OrderTracking.API.Extensions.APM
{
    /// <summary>
    ///     Extension methods to update an APM 510 Asset WalkDown (<see cref="Section510_Asset_Walkdown_Details_F"/>) type
    /// </summary>
    public static class Section510_Asset_Walkdown_Details_F_Extensions
    {
        /// <summary>
        ///     Updates a 510 asset walk down with a <see cref="FiveTenAssetDetailsUpdate"/>
        /// </summary>
        /// <param name="walkdown"></param>
        /// <param name="assetDetails"></param>
        public static void Update(this Section510_Asset_Walkdown_Details_F walkdown, FiveTenAssetDetailsUpdate assetDetails)
        {
            if (assetDetails.AssetType != null)
                walkdown.sectionIdentification.attributeAsset_Type.SetValue(assetDetails.AssetType.Value);

            if (assetDetails.DataPlateAttached != null)
                walkdown.sectionGeneralInformation.sectionDataPlate.attributeAttached.SetValue(assetDetails.DataPlateAttached.Value == true ? "Yes" : "No");

            if (assetDetails.DataPlateLegible != null)
                walkdown.sectionGeneralInformation.sectionDataPlate.attributeLegible.SetValue(assetDetails.DataPlateLegible.Value == true ? "Yes" : "No");

            if (assetDetails.DesignAddendum != null)
                walkdown.sectionGeneralInformation.sectionDesign.attributeAddendum.SetValue(assetDetails.DesignAddendum.Value);

            if (assetDetails.DesignCode != null)
                walkdown.sectionGeneralInformation.sectionDesign.attributeDesign_Code.SetValue(assetDetails.DesignCode.Value);

            if (assetDetails.DesignConditionsOperatingTemp != null)
                walkdown.sectionOperatingDesignConditions.attributeOperating_Temperature.SetValue(assetDetails.DesignConditionsOperatingTemp.Value);

            if (assetDetails.DesignDrawingNumber != null)
                walkdown.sectionGeneralInformation.attributeConstructionDesign_Drawing_Number.SetValue(assetDetails.DesignDrawingNumber.Value);

            if (assetDetails.DesignYear != null)
                walkdown.sectionGeneralInformation.sectionDesign.attributeCode_Year.SetValue(assetDetails.DesignYear.Value);

            if (assetDetails.Diameter != null)
                walkdown.sectionOperatingDesignConditions.sectionDimensions.attributeDiameter.SetValue(assetDetails.Diameter.Value);

            if (assetDetails.DiameterComments != null)
                walkdown.sectionOperatingDesignConditions.sectionDimensions.attributeDiameter.SetComment(assetDetails.DiameterComments.Value);

            if (assetDetails.DiameterMeasurement != null)
                walkdown.sectionOperatingDesignConditions.sectionDimensions.attributeDiameter_Measurement.SetValue(assetDetails.DiameterMeasurement.Value);

            if (assetDetails.EquipmentDescription != null)
                walkdown.sectionIdentification.attributeEquipment_Description.SetValue(assetDetails.EquipmentDescription.Value);

            // Potentially throws an InvalidLocationException
            walkdown.sectionIdentification.attributeGIS_Location.SetLocation(
                assetDetails.GisLocationLat,
                assetDetails.GisLocationLong);

            if (assetDetails.HasMultipleDiameters != null)
                walkdown.sectionOperatingDesignConditions.sectionDimensions.attributeDoes_the_shell_have_multiple_diameters.SetValue(assetDetails.HasMultipleDiameters.Value == true ? "Yes" : "No");

            if (assetDetails.HasRepairOrAlterationPlate != null)
                walkdown.sectionGeneralInformation.sectionRepairRecord.attributeDoes_the_asset_have_a_repair_or_alteration_plate.SetValue(assetDetails.HasRepairOrAlterationPlate.Value == true ? "Yes" : "No");

            if (assetDetails.HasToriconicalSections != null)
                walkdown.sectionOperatingDesignConditions.sectionDimensions.attributeAre_there_toriconical_transition_sections_in_the_shell.SetValue(assetDetails.HasToriconicalSections.Value == true ? "Yes" : "No");

            if (assetDetails.HydroTestPressure != null)
                walkdown.sectionGeneralInformation.attributeHydro_Test_Pressure.SetValue(assetDetails.HydroTestPressure.Value);

            if (assetDetails.IdentificationName != null)
                walkdown.sectionIdentification.attributeName.SetValue(assetDetails.IdentificationName.Value);

            if (assetDetails.IdentificationNumber != null)
                walkdown.sectionIdentification.attributeNumber_or_ID.SetValue(assetDetails.IdentificationNumber.Value);

            if (assetDetails.InServiceDate != null)
                walkdown.sectionGeneralInformation.attributeIn_service_Date.SetValue(assetDetails.InServiceDate.Value);

            if (assetDetails.InspectionCode != null)
                walkdown.sectionGeneralInformation.sectionInspection.attributeInspection_Code.SetValue(assetDetails.InspectionCode.Value);

            if (assetDetails.InspectionYear != null)
                walkdown.sectionGeneralInformation.sectionInspection.attributeYear.SetValue(assetDetails.InspectionYear.Value);

            if (assetDetails.InspectionAddendum != null)
                walkdown.sectionGeneralInformation.sectionInspection.attributeAddendum.SetValue(assetDetails.InspectionAddendum.Value);

            if (assetDetails.InstallationDate != null)
                walkdown.sectionGeneralInformation.attributeInstallation_Date.SetValue(assetDetails.InstallationDate.Value);

            if (assetDetails.LastKnownInspectionDate != null)
                walkdown.sectionIdentification.attributeLast_known_inspection_date.SetValue(assetDetails.LastKnownInspectionDate.Value);

            if (assetDetails.Location != null)
                walkdown.sectionIdentification.attributeLocation.SetValue(assetDetails.Location.Value);

            if (assetDetails.LowestFlangeRating != null)
                walkdown.sectionGeneralInformation.attributeLowest_Flange_Rating.SetValue(assetDetails.LowestFlangeRating.Value);

            if (assetDetails.ManufacturerDate != null)
                walkdown.sectionGeneralInformation.sectionManufacturer.attributeDate.SetValue(assetDetails.ManufacturerDate.Value);

            if (assetDetails.ManufacturerName != null)
                walkdown.sectionGeneralInformation.sectionManufacturer.attributeName.SetValue(assetDetails.ManufacturerName.Value);

            if (assetDetails.ManufacturerSerialNumber != null)
                walkdown.sectionGeneralInformation.sectionManufacturer.attributeSerial_Number.SetValue(assetDetails.ManufacturerSerialNumber.Value);

            if (assetDetails.NationalBoardNumber != null)
                walkdown.sectionGeneralInformation.sectionManufacturer.attributeNational_Board_Number.SetValue(assetDetails.NationalBoardNumber.Value);

            if (assetDetails.OperationStatus != null)
                walkdown.sectionOperatingDesignConditions.attributeOperation_Status.SetValue(assetDetails.OperationStatus.Value);

            if (assetDetails.Orientation != null)
                walkdown.sectionGeneralInformation.attributeOrientation.SetValue(assetDetails.Orientation.Value);

            if (assetDetails.OverallLengthHeight != null)
                walkdown.sectionOperatingDesignConditions.sectionDimensions.attributeOverall_Length_or_Height.SetValue(assetDetails.OverallLengthHeight.Value);

            if (assetDetails.PIDNumber != null)
                walkdown.sectionGeneralInformation.attributePID_Number.SetValue(assetDetails.PIDNumber.Value);

            if (assetDetails.PWHT != null)
                walkdown.sectionGeneralInformation.attributePost_Weld_Heat_Treatment.SetValue(assetDetails.PWHT.Value);

            if (assetDetails.RatingChanged != null)
                walkdown.sectionGeneralInformation.attributeHas_the_equipment_been_de_rated_or_re_rated.SetValue(assetDetails.RatingChanged.Value);

            if (assetDetails.RepairOrAlterationPlateLegible != null)
                walkdown.sectionGeneralInformation.sectionRepairRecord.attributeRepairAlteration_Plates_Legible.SetValue(assetDetails.RepairOrAlterationPlateLegible.Value);

            if (assetDetails.Rt != null)
                walkdown.sectionGeneralInformation.attributeRT.SetValue(assetDetails.Rt.Value);

            if (assetDetails.RtNumber != null)
                walkdown.sectionGeneralInformation.sectionManufacturer.attributeRT_Number.SetValue(assetDetails.RtNumber.Value);

            if (assetDetails.ServiceProductContents != null)
                walkdown.sectionGeneralInformation.sectionService.attributeServiceProductContents.SetValue(assetDetails.ServiceProductContents.Value);

            if (assetDetails.ShellSideDesignMAWP != null)
                walkdown.sectionOperatingDesignConditions.sectionShellSide.attributeDesign_MAWP.SetValue(assetDetails.ShellSideDesignMAWP.Value);

            if (assetDetails.ShellSideDesignTemp != null)
                walkdown.sectionOperatingDesignConditions.sectionShellSide.attributeDesign_Temperature.SetValue(assetDetails.ShellSideDesignTemp.Value);

            if (assetDetails.ShellSideOperatingTemp != null)
                walkdown.sectionOperatingDesignConditions.sectionShellSide.attributeOperating_Temperature.SetValue(assetDetails.ShellSideOperatingTemp.Value);

            if (assetDetails.ShellSideSetPressure != null)
                walkdown.sectionOperatingDesignConditions.sectionShellSide.attributePRV_Set_Pressure.SetValue(assetDetails.ShellSideSetPressure.Value);

            if (assetDetails.ShellSideOperatingPressure != null)
                walkdown.sectionOperatingDesignConditions.sectionShellSide.attributeOperating_Pressure.SetValue(assetDetails.ShellSideOperatingPressure.Value);

            if (assetDetails.IsFiredPressureVessel != null)
                walkdown.sectionGeneralInformation.attributeIs_this_a_fired_pressure_vessel.SetValue(assetDetails.IsFiredPressureVessel.Value);

            if (assetDetails.SpecificGravity != null)
                walkdown.sectionGeneralInformation.sectionService.attributeSpecific_Gravity.SetValue(assetDetails.SpecificGravity.Value);

            if (assetDetails.TubeSideDesignMAWP != null)
                walkdown.sectionOperatingDesignConditions.sectionTubeSide.attributeDesign_MAWP.SetValue(assetDetails.TubeSideDesignMAWP.Value);

            if (assetDetails.TubeSideOperatingTemp != null)
                walkdown.sectionOperatingDesignConditions.sectionTubeSide.attributeOperating_Temperature.SetValue(assetDetails.TubeSideOperatingTemp.Value);

            if (assetDetails.TubeSideOperatingPressure != null)
                walkdown.sectionOperatingDesignConditions.sectionTubeSide.attributeOperating_Pressure.SetValue(assetDetails.TubeSideOperatingPressure.Value);

            if (assetDetails.TubeSideDesignTemp != null)
                walkdown.sectionOperatingDesignConditions.sectionTubeSide.attributeDesign_Temperature.SetValue(assetDetails.TubeSideDesignTemp.Value);

            if (assetDetails.TubeSideSetPressure != null)
                walkdown.sectionOperatingDesignConditions.sectionTubeSide.attributePRV_Set_Pressure.SetValue(assetDetails.TubeSideSetPressure.Value);

            if (assetDetails.TypeOfConstruction != null)
                walkdown.sectionGeneralInformation.attributeType_of_construction.SetValue(assetDetails.TypeOfConstruction.Value);

            foreach (var channelUpdate in assetDetails.Channels)
            {
                var channel =
                    walkdown.sectionComponents.sectionChannels.CurrentEntries.FirstOrDefault(entry =>
                        entry.DatabaseId == channelUpdate.DatebaseId);

                if (channel == null)
                    continue;

                if (channelUpdate.Location != null)
                    channel.attributeLocation.SetValue(channelUpdate.Location.Value);

                if (channelUpdate.Length != null)
                    channel.attributeLength_or_Height.SetValue(channelUpdate.Length.Value);

                if (channelUpdate.AllowableStressAtTemp != null)
                    channel.attributeAllowable_Stress_at_Temperature.SetValue(channelUpdate.AllowableStressAtTemp.Value);

                if (channelUpdate.CorrosionAllowance != null)
                    channel.attributeCorrosion_Allowance.SetValue(channelUpdate.CorrosionAllowance.Value);

                if (channelUpdate.JointEfficiency != null)
                    channel.attributeJoint_Efficiency.SetValue(channelUpdate.JointEfficiency.Value);

                if (channelUpdate.MaterialSpecAndGrade != null)
                    channel.attributeMaterial_Spec_and_Grade.SetValue(channelUpdate.MaterialSpecAndGrade.Value);

                if (channelUpdate.NominalThickness != null)
                    channel.attributeMaterial_Spec_and_Grade.SetValue(channelUpdate.MaterialSpecAndGrade.Value);

                if (channelUpdate.Number != null)
                    channel.attributeNumber.SetValue(channelUpdate.Number.Value);
            }

            foreach (var headUpdate in assetDetails.Heads)
            {
                var head = walkdown.sectionComponents.sectionHeads.CurrentEntries.FirstOrDefault(entry =>
                    entry.DatabaseId == headUpdate.DatebaseId);

                if (head == null)
                    continue;

                if (headUpdate.Location != null)
                    head.attributeLocation.SetValue(headUpdate.Location.Value);

                if (headUpdate.MaterialSpecAndGrade != null)
                    head.attributeMaterial_Spec_and_Grade.SetValue(headUpdate.MaterialSpecAndGrade.Value);

                if (headUpdate.AllowableStressAtTemp != null)
                    head.attributeAllowable_Stress_at_Temperature.SetValue(headUpdate.AllowableStressAtTemp.Value);

                if (headUpdate.CorrosionAllowance != null)
                    head.attributeCorrosion_Allowance.SetValue(headUpdate.CorrosionAllowance.Value);

                if (headUpdate.Geometry != null)
                    head.attributeGeometry.SetValue(headUpdate.Geometry.Value);

                if (headUpdate.GeometryComments != null)
                    head.attributeGeometry.SetComment(headUpdate.GeometryComments.Value);

                if (headUpdate.JointEfficiency != null)
                    head.attributeJoint_Efficiency.SetValue(headUpdate.JointEfficiency.Value);

                if (headUpdate.NominalThickness != null)
                    head.attributeNominal_Thickness.SetValue(headUpdate.NominalThickness.Value);

                if (headUpdate.Number != null)
                    head.attributeNumber.SetValue(headUpdate.Number.Value);
            }

            foreach (var openingUpdate in assetDetails.InspectionOpenings)
            {
                var opening =
                    walkdown.sectionGeneralInformation.sectionInspectionOpenings.CurrentEntries.FirstOrDefault(entry =>
                        entry.DatabaseId == openingUpdate.DatabaseId);
                if (opening == null)
                    continue;

                if (openingUpdate.Number != null)
                    opening.attributeOpening_Number.SetValue(openingUpdate.Number.Value);

                if (openingUpdate.Size != null)
                    opening.attributeOpening_Size.SetValue(openingUpdate.Size.Value);

                if (openingUpdate.Type != null)
                    opening.attributeOpening_Type.SetValue(openingUpdate.Type.Value);
            }

            foreach (var nozzleUpdate in assetDetails.Nozzles)
            {
                var nozzle =
                    walkdown.sectionComponents.sectionNozzles.CurrentEntries.FirstOrDefault(entry =>
                        entry.DatabaseId == nozzleUpdate.DatebaseId);

                if (nozzle == null)
                    continue;

                if (nozzleUpdate.MaterialSpecAndGrade != null)
                    nozzle.attributeMaterial_Spec_and_Grade.SetValue(nozzleUpdate.MaterialSpecAndGrade.Value);

                if (nozzleUpdate.FlangeRating != null)
                    nozzle.attributeFlange_Rating.SetValue(nozzleUpdate.FlangeRating.Value);

                if (nozzleUpdate.Number != null)
                    nozzle.attributeNumber.SetValue(nozzleUpdate.Number.Value);

                if (nozzleUpdate.PipeSchedule != null)
                    nozzle.attributePipe_Schedule.SetValue(nozzleUpdate.PipeSchedule.Value);

                if (nozzleUpdate.PipeSize != null)
                    nozzle.attributePipe_Size.SetValue(nozzleUpdate.PipeSize.Value);

                if (nozzleUpdate.ReinforcementPadDimensions != null)
                    nozzle.attributeReinforcement_pad_dimensions.SetValue(nozzleUpdate.ReinforcementPadDimensions.Value);

                if (nozzleUpdate.ReinforcementPadThickness != null)
                    nozzle.attributeReinforcement_pad_thickness.SetValue(nozzleUpdate.ReinforcementPadThickness.Value);

                if (nozzleUpdate.ReinforcementPadType != null)
                    nozzle.attributeReinforcement_pad_type.SetValue(nozzleUpdate.ReinforcementPadType.Value);

                if (nozzleUpdate.Type != null)
                    nozzle.attributeType.SetValue(nozzleUpdate.Type.Value);
            }

            foreach (var repairUpdate in assetDetails.Repairs)
            {
                var repair =
                    walkdown.sectionGeneralInformation.sectionRepairRecord.sectionRepairs.CurrentEntries.FirstOrDefault(
                        entry => entry.DatabaseId == repairUpdate.DatebaseId);

                if (repair == null)
                    continue;

                if (repairUpdate.DateRepairedOrAltered != null)
                {
                    if (repairUpdate.DateRepairedOrAltered.Value == null)
                    {
                        repair.attributeDate_Repaired_or_Altered.SetValue(null);
                    }
                    else
                    {
                        repair.attributeDate_Repaired_or_Altered.SetValue(
                            DateTime.Parse(repairUpdate.DateRepairedOrAltered.Value));
                    }
                }

                if (repairUpdate.IsNBFormR1Available != null)
                    repair.attributeIs_NB_Form_R_1_Available.SetValue(repairUpdate.IsNBFormR1Available.Value == true ? "Yes" : "No");

                if (repairUpdate.IsNBFormR2Available != null)
                    repair.attributeIs_NB_Form_R_2_Available.SetValue(repairUpdate.IsNBFormR2Available.Value == true ? "Yes" : "No");

                if (repairUpdate.NbRCertificateNumber != null)
                    repair.attributeNB_R_Certificate_Number.SetValue(repairUpdate.NbRCertificateNumber.Value);

                if (repairUpdate.RepairAlterationOrganization != null)
                    repair.attributeRepairAlteration_organization.SetValue(repairUpdate.RepairAlterationOrganization.Value);

                if (repairUpdate.PurposeOfRepairAlteration != null)
                    repair.attributePurpose_of_repairalteration.SetValue(repairUpdate.PurposeOfRepairAlteration.Value);
            }

            foreach (var shellUpdate in assetDetails.ShellCourses)
            {
                var shell = walkdown.sectionComponents.sectionShellCourses.CurrentEntries.FirstOrDefault(entry =>
                    entry.DatabaseId == shellUpdate.DatebaseId);

                if (shellUpdate.MaterialSpecAndGrade != null)
                    shell.attributeMaterial_Spec_and_Grade.SetValue(shellUpdate.MaterialSpecAndGrade.Value);

                if (shellUpdate.CorrosionAllowance != null)
                    shell.attributeCorrosion_Allowance.SetValue(shellUpdate.CorrosionAllowance.Value);

                if (shellUpdate.AllowableStressAtTemp != null)
                    shell.attributeAllowable_Stress_at_Temperature.SetValue(shellUpdate.AllowableStressAtTemp.Value);

                if (shellUpdate.JointEfficiency != null)
                    shell.attributeJoint_Efficiency.SetValue(shellUpdate.JointEfficiency.Value);

                if (shellUpdate.LengthOrHeight != null)
                    shell.attributeLength_or_Height.SetValue(shellUpdate.LengthOrHeight.Value);

                if (shellUpdate.NominalThickness != null)
                    shell.attributeNominal_Thickness.SetValue(shellUpdate.NominalThickness.Value);

                if (shellUpdate.Number != null)
                    shell.attributeNumber.SetValue(shellUpdate.Number.Value);
            }
        }
    }
}