import { DOCUMENT } from '@angular/common';
import { Component, Inject, OnInit, Renderer2 } from '@angular/core';

@Component({
  selector: 'app-esg',
  templateUrl: './esg.component.html',
  styleUrls: ['./esg.component.scss']
})
export class EsgComponent implements OnInit {

  constructor(private _renderer2: Renderer2, 
    @Inject(DOCUMENT) private _document: Document) { }

  ngOnInit(): void {
    let script = this._renderer2.createElement('script');
        script.src = "https://cloud.google.com/ai/gen-app-builder/client"
        this._renderer2.appendChild(this._document.body, script);
  }
}