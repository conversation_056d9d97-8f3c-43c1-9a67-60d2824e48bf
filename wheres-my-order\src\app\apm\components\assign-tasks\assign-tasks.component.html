<div class="dx-card content-block responsive-paddings"
     style="max-width: 360px;">
    <dx-select-box [dataSource]="businessUnits"
                   displayExpr="name"
                   keyExpr="id"
                   valueExpr="id"
                   label="Business Unit"
                   labelMode="floating"
                   [searchEnabled]="true"
                   searchMode="contains"
                   searchExpr="name"
                   style="width: 85%; display: inline-block"
                   (onSelectionChanged)="onBusinessUnitChanged($event)">
        <dxo-state-storing [enabled]="true"
                           type="localStorage"
                           storageKey="storage"></dxo-state-storing>
    </dx-select-box>
    <div *ngIf="loadingBusinessUnits"
         [@inOutAnimation]
         style="margin-left: 18px; display: inline-block">
        <dx-load-indicator class="centered"
                           id="large-indicator"
                           height="20"
                           width="20"></dx-load-indicator>
    </div>
</div>

<div id="maincontainer"
     *ngIf="!loadingBusinessUnits"
     [@inOutAnimation]
     class="dx-card responsive-paddings content-block"
     style="padding: 30px;">
    <h4 style="display: inline-block; padding-right: 20px;">Assets
    </h4>
    <div *ngIf="loadingAssets"
         [@inOutAnimation]
         style="display: inline-block; vertical-align: middle;">
        <dx-load-indicator class="centered"
                           id="large-indicator"
                           height="20"
                           width="20"></dx-load-indicator>
    </div>
    <dx-data-grid id="assets-datagrid"
                  [dataSource]="assetsList"
                  [columnChooser]="{enabled: true}"
                  [searchPanel]="{visible: true}"
                  [loadPanel]="{ enabled: false }"
                  [paging]="{enabled: true}"
                  [filterRow]="{visible: true}"
                  [columnAutoWidth]="true"
                  [hoverStateEnabled]="true"
                  (onRowClick)="onAssetChanged($event)"
                  noDataText="No assets"
                  [allowColumnResizing]="true">
        <dxo-toolbar>
            <dxi-item name="groupPanel"></dxi-item>
            <dxi-item name="columnChooserButton"></dxi-item>
            <dxi-item name="searchPanel"></dxi-item>
        </dxo-toolbar>
        <dxo-paging [pageSize]="5"
                    [pageIndex]="0">
            <!-- Shows the second page -->
        </dxo-paging>
        <dxo-state-storing [enabled]="true"
                           type="localStorage"
                           storageKey="storage">

        </dxo-state-storing>
        <dxi-column dataField="id"
                    [visible]="false"
                    [showInColumnChooser]="false">
        </dxi-column>
        <dxi-column dataField="workOrderIds"
                    [visible]="false"
                    [showInColumnChooser]="false">
        </dxi-column>
        <dxi-column dataField="category">
        </dxi-column>
        <dxi-column dataField="businessUnitId"
                    [visible]="false"
                    [showInColumnChooser]="false">
        </dxi-column>
        <dxi-column dataField="type"
                    [visible]="false"
                    [showInColumnChooser]="false">
        </dxi-column>
        <dxi-column dataField="assetName">
        </dxi-column>

        <dxo-paging [pageSize]="5"></dxo-paging>
        <dxo-pager [showPageSizeSelector]="true"
                   [visible]="true"
                   [showInfo]="true"
                   infoText="({2} assets)"
                   [allowedPageSizes]="[5, 10, 25, 50]"></dxo-pager>
        <dxo-selection mode="single"></dxo-selection>
    </dx-data-grid>
    <div style="padding-top: 20px"></div>
    <h4 style="display: inline-block; padding-right: 20px;">Tasks
    </h4>
    <div *ngIf="loadingTasks"
         style="display: inline-block; vertical-align: middle;">
        <dx-load-indicator class="centered"
                           id="large-indicator"
                           height="20"
                           width="20"></dx-load-indicator>
    </div>
    <dx-data-grid [dataSource]="tasksList"
                  [columnChooser]="{enabled: true}"
                  [searchPanel]="{visible: true}"
                  [paging]="{enabled: true}"
                  [filterRow]="{visible: true}"
                  [columnAutoWidth]="true"
                  [hoverStateEnabled]="true"
                  [allowColumnResizing]="true"
                  (onRowUpdated)="onRowUpdated($event)"
                  noDataText="No tasks">

        <dxo-editing mode="cell"
                     [allowUpdating]="(currentUser$ | async | hasRoles : 'app:admin' : 'apm:qaqc' : 'apm:admin' : 'apm:edit')">
        </dxo-editing>
        <dxo-toolbar>
            <dxi-item name="columnChooserButton"></dxi-item>
            <dxi-item name="searchPanel"></dxi-item>
        </dxo-toolbar>
        <dxi-column dataField="id"
                    [visible]="false"
                    [showInColumnChooser]="false">
        </dxi-column>
        <dxi-column dataField="workOrderId"
                    [visible]="false"
                    [showInColumnChooser]="false">
        </dxi-column>
        <dxi-column dataField="taskType"
                    editCellTemplate="typeTemplate"
                    [allowEditing]="false">
        </dxi-column>
        <dxi-column dataField="apmNumber">
        </dxi-column>
        <dxi-column dataField="supervisor"
                    editCellTemplate="supervisorTemplate">
        </dxi-column>
        <dxi-column dataField="assignedUsers"
                    [customizeText]="customizeText"
                    editCellTemplate="assigneesTemplate">
        </dxi-column>
        <dxi-column dataField="status"
                    editCellTemplate="statusTemplate">
        </dxi-column>
        <dxi-column type="buttons"
                    caption="Results"
                    [width]="'10%'">
            <dxi-button icon="fa fa-tasks"
                        hint="View results"
                        [onClick]="tasksResultsClicked">
            </dxi-button>
        </dxi-column>

        <div *dxTemplate="let cellInfo of 'supervisorTemplate'">
            <dx-select-box [dataSource]="users"
                           width="300"
                           [value]="cellInfo.value"
                           (onValueChanged)="cellInfo.setValue($event.value)"
                           [showSelectionControls]="true"
                           [searchEnabled]="true"
                           applyValueMode="useButtons">
            </dx-select-box>
        </div>
        <div *dxTemplate="let cellInfo of 'assigneesTemplate'">
            <dx-tag-box [dataSource]="users"
                        [showMultiTagOnly]="false"
                        width="300"
                        [value]="cellInfo.value"
                        searchEnabled="true"
                        (onValueChanged)="cellInfo.setValue($event.value)"
                        (onSelectionChanged)="cellInfo.component.updateDimensions()"
                        applyValueMode="useButtons">
            </dx-tag-box>
        </div>

        <div *dxTemplate="let cellInfo of 'typeTemplate'">
            <dx-select-box [dataSource]="availableTypes"
                           width="300"
                           [value]="cellInfo.value"
                           (onValueChanged)="cellInfo.setValue($event.value)"
                           [showSelectionControls]="true"
                           [searchEnabled]="true"
                           applyValueMode="useButtons">
            </dx-select-box>
        </div>

        <div *dxTemplate="let cellInfo of 'statusTemplate'">
            <dx-select-box [dataSource]="availableStatus"
                           (onValueChanged)="cellInfo.setValue($event.value)"
                           noDataText="Loading...">
            </dx-select-box>
        </div>
    </dx-data-grid>
</div>