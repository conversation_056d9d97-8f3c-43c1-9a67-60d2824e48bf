import { Component, Input } from '@angular/core';
import { DxGalleryComponent } from 'devextreme-angular/ui/gallery';

export class InspectionPhotoSection {
    title: string;
    photos: InspectionPhoto[];
}

export class InspectionPhoto {
    path: string;
    description: string;
    uniqueId: number;
}

export class GalleryInspectionPhoto {
    path: string;
    description: string;
    uniqueId: number;
    section: string;
}

@Component({
    selector: 'app-inspection-photos-tab',
    templateUrl: './inspection-photos-tab.component.html',
    styleUrls: ['./inspection-photos-tab.component.scss'],
})
export class InspectionPhotosTabComponent {
    allPhotos: GalleryInspectionPhoto[];
    private _sections: InspectionPhotoSection[];

    @Input() set sections(value: InspectionPhotoSection[]) {
        this._sections = value;
        this.allPhotos = value.flatMap((section) => {
            return section.photos.map((photo) => {
                return { ...photo, section: section.title };
            });
        });
    }
    get sections() {
        return this._sections;
    }

    showPhotoPopup = false;

    constructor() {}

    imageClicked(
        e: MouseEvent,
        photo: InspectionPhoto,
        gallery: DxGalleryComponent
    ) {
        this.showPhotoPopup = true;
        gallery.selectedIndex = this.allPhotos.findIndex(
            (somePhoto) => somePhoto.uniqueId === photo.uniqueId
        );
    }
}
