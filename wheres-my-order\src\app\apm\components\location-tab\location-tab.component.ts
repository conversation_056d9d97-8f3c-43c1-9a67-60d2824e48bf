import { Component, Input, ViewChild } from '@angular/core';
import { DxFormComponent } from 'devextreme-angular';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { finalize, map } from 'rxjs/operators';
import { ProjectLocationTabItem, ProjectVm } from '../../models';
import { ApmService } from '../../services';

@Component({
    selector: 'app-location-tab',
    templateUrl: './location-tab.component.html',
    styleUrls: ['./location-tab.component.scss']
})
export class LocationTabComponent {
    private _location: ProjectLocationTabItem;
    private _selectedProject: ProjectVm;
    private _originalLocation: ProjectLocationTabItem | undefined;
    private _isSaving = new BehaviorSubject(false);
    private _allowEditing = new BehaviorSubject(false);

    @ViewChild(DxFormComponent) form: DxFormComponent;
    @Input() set allowEditing(value: boolean) {
        this._allowEditing.next(value);
    }
    get allowEditing(): boolean {
        return this._allowEditing.value;
    }
    @Input()
    set location(loc: ProjectLocationTabItem) {
        if (
            this._location === null ||
            this._location === undefined ||
            this._location.id !== loc.id
        ) {
            this.isEditing = false;
        }
        this._location = loc;
    }
    get location() {
        return this._location;
    }

    @Input()
    set selectedProject(proj: ProjectVm) {
        this._selectedProject = proj;
    }
    get selectedProject() {
        return this._selectedProject;
    }

    isEditing = false;
    isSaving$ = this._isSaving.asObservable();
    saveButtonDisabled$ = combineLatest([
        this.isSaving$,
        this._apm.selectedBU$
    ]).pipe(
        map(([isSaving, selectedBusinessUnit]) => {
            return (
                isSaving ||
                selectedBusinessUnit === null ||
                selectedBusinessUnit === undefined
            );
        })
    );
    cancelButtonDisabled$ = this.saveButtonDisabled$;
    editButtonDisabled$ = combineLatest([
        this._allowEditing.asObservable(),
        this._apm.selectedBU$
    ]).pipe(
        map(
            ([allowEditing, bu]) =>
                !allowEditing || bu === undefined || bu === null
        )
    );

    constructor(
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService
    ) {}

    editClicked() {
        this._originalLocation = new ProjectLocationTabItem(this._location);
        this.isEditing = true;
    }

    cancelEditClicked() {
        this._location = new ProjectLocationTabItem(this._originalLocation);
        this.isEditing = false;
    }

    saveEditLocationClicked() {
        const result = this.form.instance.validate();
        if (result.isValid) {
            this._isSaving.next(true);
            this._apm
                .putLocation(this.location)
                .pipe(
                    finalize(() => {
                        this._isSaving.next(false);
                        this.isEditing = false;
                    })
                )
                .subscribe((x) => {
                    this._toasts.success(
                        'Updated Location successfully',
                        'Success'
                    );
                });
        }
    }
}
