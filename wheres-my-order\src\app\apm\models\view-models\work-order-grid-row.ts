export class WorkOrderGridRow {
    id: string;
    projectId: string;
    assetDatabaseId: string;
    assetId: string;
    apmWorkOrderNumber: string;
    assetCategory: string;
    dueDate: Date;
    facilityName: string;
    plannedEnd: Date;
    plannedStart: Date;
    status: string;

    constructor(options?: Partial<WorkOrderGridRow>) {
        if (options)
            for (const key of Object?.keys(options)) {
                this[key] = options[key];
            }
    }
}
