﻿// DEPRECATED: This class has been completely replaced by ValidatingCosmosClient for Azure Cosmos DB
// Commented out during Firebase-to-Azure migration cleanup
/*
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
// DEPRECATED: Migrated from Google Cloud Firestore to Azure Cosmos DB
// using Google.Cloud.Firestore;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    /// DEPRECATED: Firestore client - replaced by Azure Cosmos DB client
    /// This class is kept for backward compatibility during migration
    /// </summary>
    [Obsolete("Use Azure Cosmos DB client instead. This class will be removed after migration is complete.")]
    public class ValidatingFirestoreClient
    {
        // TODO: Replace with Azure Cosmos DB client
        // private readonly FirestoreDb _firestoreDb;

        public ValidatingFirestoreClient(string projectId, string databaseId)
        {
            // TODO: This class is obsolete and will be removed after migration is complete
            // _firestoreDb = new FirestoreDbBuilder
            // {
            //     ProjectId = projectId,
            //     DatabaseId = databaseId
            // }.Build();
            throw new NotImplementedException("This class is obsolete. Use Azure Cosmos DB client instead.");
        }

        // TODO: Replace with Azure Cosmos DB container reference
        // public CollectionReference GetCollection(string collectionId, out string partitionKeyPath)
        // {
        //     partitionKeyPath = GetPartitionKeyPath(collectionId);
        //     return GetCollection(collectionId);
        // }
        public object GetCollection(string collectionId, out string partitionKeyPath)
        {
            partitionKeyPath = GetPartitionKeyPath(collectionId);
            throw new NotImplementedException("Replace with Azure Cosmos DB container");
        }


        // TODO: Replace with Azure Cosmos DB container reference
        // public CollectionReference GetCollection(string collectionId)
        // {
        //     var collection = _firestoreDb.Collection(collectionId);
        //     var querySnapshot = collection.GetSnapshotAsync().GetAwaiter().GetResult();
        //
        //     if (querySnapshot == null || querySnapshot.Count == 0)
        //     {
        //         throw new ArgumentException($"Unable to find collection '{collectionId}'. Verify that the collection has been created and try again.");
        //     }
        //     return collection;
        // }
        public object GetCollection(string collectionId)
        {
            throw new NotImplementedException("Replace with Azure Cosmos DB container");
        }

        private string GetPartitionKeyPath(string collectionId)
        {
            if (collectionId == null) throw new ArgumentNullException(nameof(collectionId));

            var pathSegments = collectionId.Split('/');
            return pathSegments.LastOrDefault();
        }
    }
}
*/
