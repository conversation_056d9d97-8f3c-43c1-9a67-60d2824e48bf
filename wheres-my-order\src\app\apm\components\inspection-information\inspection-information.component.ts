import { Component, EventEmitter, Input, Output } from '@angular/core';
import cloneDeep from 'clone-deep';
import { DxAccordionComponent } from 'devextreme-angular/ui/accordion';
import { DxButtonComponent } from 'devextreme-angular/ui/button';
import dxAccordion from 'devextreme/ui/accordion';
import dxButton from 'devextreme/ui/button';
import { InspectionInfo } from '../../models';

const collapseButtonText = 'Collapse All';
const expandButtonText = 'Expand All';

@Component({
    selector: 'app-inspection-information',
    templateUrl: './inspection-information.component.html',
    styleUrls: ['./inspection-information.component.scss']
})
export class InspectionInformationComponent {
    private _inspectionInfo: InspectionInfo | undefined;
    private _original: InspectionInfo | undefined;

    @Input() set inspectionInfo(value: InspectionInfo) {
        this.isEditing = false;
        this._inspectionInfo = value;
        this._original = cloneDeep(this._inspectionInfo);
    }
    get inspectionInfo(): InspectionInfo {
        return this._inspectionInfo;
    }
    @Output() save = new EventEmitter<InspectionInfo>();

    items = [
        { title: 'Job Scope', dataField: 'jobScope' },
        {
            title: 'Inspection Summary/Observation(s)',
            dataField: 'inspectionSummary'
        },
        {
            title: 'Applicable Damage Mechanism(s)',
            dataField: 'applicableDamage'
        },
        {
            title: 'Relevant Indications/Finding(s)',
            dataField: 'releventIndications'
        },
        { title: 'Recommendation(s)', dataField: 'recommendations' }
    ];

    isEditing = false;
    isSaving = false;
    @Input() allowEditing: boolean;

    private _firstContentReadyEvent = true;

    constructor() {}

    onContentReady(e: { component: dxAccordion }) {
        if (!this.items) return;
        // contentReady event fires more than once.  This just needs to happen once
        // so that the items that are applicable all start out as expanded.
        if (this._firstContentReadyEvent) {
            this._firstContentReadyEvent = false;
            for (let i = 0; i < this.items.length; i++) {
                e.component.expandItem(i);
            }
        }
    }

    toggleExpandCollapseAll(
        e: { component: dxButton },
        accordion: DxAccordionComponent
    ) {
        const isCollapsing = e.component.option('text') === collapseButtonText;

        // Toggle the button text
        if (isCollapsing) {
            e.component.option('text', expandButtonText);
        } else {
            e.component.option('text', collapseButtonText);
        }

        // Expand or collapse sections based on whether the user is expanding all or collapsing all
        accordion.items.forEach((_, index: number) => {
            if (isCollapsing) accordion.instance.collapseItem(index);
            else accordion.instance.expandItem(index);
        });
    }

    onSelectionChanged(
        accordion: DxAccordionComponent,
        expandCollapseButton: DxButtonComponent
    ) {
        // When a section is expanded or collapsed, the selected items change.
        // `selectedItems` equals the sections that are expanded.  Change the
        // button text based on whether any sections are expanded or not.
        if (accordion.selectedItems.length <= 0) {
            expandCollapseButton.text = expandButtonText;
        } else {
            expandCollapseButton.text = collapseButtonText;
        }
    }

    onEditClicked(e) {
        this.isEditing = true;
    }

    onCancelClicked(e) {
        this.isEditing = false;
        this.inspectionInfo = this._original;
    }

    onSaveClicked(e) {
        this.save.next(this._inspectionInfo);
        this._original = this._inspectionInfo;
    }
}
